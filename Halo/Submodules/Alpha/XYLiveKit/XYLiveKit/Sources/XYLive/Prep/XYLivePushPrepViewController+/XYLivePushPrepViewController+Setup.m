//
//  XYLivePrepPlanAViewController.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/10/24.
//

#import "XYLivePushPrepViewController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepPrivateExt+Core.h"
#import "XYLivePushPrepPrivateExt+Recovery.h"
@import XYConfigCenter;
@import XYLiveFoundation;
@implementation XYLivePushPrepViewController (Setup)

- (XYLiveLoginParam *)setup_basicLoginParam {
    XYLiveLoginParam *param = [XYLivePushPrepDataSource loginParamWithRoomInfo:self.roomInfo cover:self.prepVM.cover title:self.prepVM.title notice:self.prepVM.notice keepSecret:self.prepVM.keepSecret];
    param.joinLimit = self.prepVM.joinLimit;
    return param;
}

- (void)setup_insertPCPrepView {
    [self navigationModule_createPrepNav];
}

- (void)seutp_switchClearModel:(BOOL)clear {
    self.containerView.hidden = clear;
}

- (void)setup_layoutBaseUI {
    BOOL enableV9 = [XYLiveExperimentConfig enableCapaV9];
    CGFloat y = [XYLiveExperimentConfig enableCapaV9] ? [UIApplication safeAreaTop] : ([UIDevice currentDevice].xypk_isiPhoneX ? 44 : 0);
    CGFloat safeBottom = [UIApplication safeAreaBottom];
    // capa传高度，老样式tabbarHeight为0走老逻辑，新样式不为0时候用tabbarHeight，非全面屏不变
    CGFloat bottomXHeight = (XYConfigCenter().boolForKey(@"ios_pre_bottom_height", NO) && self.tabbarHeight != 0) ? (self.tabbarHeight - safeBottom) : 67;
    CGFloat bottomHeight = [UIDevice currentDevice].xypk_isiPhoneX ? bottomXHeight : 0;
    CGFloat width = DeviceUtility.screenWidth;
    CGSize resizeTo = CGSizeMake(9, 16);
    CGFloat height = DeviceUtility.screenHeight - y - bottomHeight - safeBottom;
    CGRect cameraRect = CGRectMake(0, y, width, height);
    CGRect containerRect = CGRectMake(0, y, width, height);
    
    if (enableV9) {
        height = DeviceUtility.screenHeight - self.tabbarHeight;
        cameraRect = CGRectMake(0, 0, width, height);
        containerRect = CGRectMake(0, 0, width, height);
    }

    
    self.cameraView.frame = cameraRect;
    self.containerView.frame = containerRect;
    
    self.prepView.frame = self.containerView.bounds;
    self.panelContainerView.frame = self.containerView.bounds;
    self.privacyTipView.frame = self.containerView.bounds;
    
    int radius = enableV9 ? 0 : 20;
    [self.cameraView live_updateMaskWithRoundedRect:self.cameraView.bounds topLeftRadius:0 topRightRadius:0 bottomLeftRadius:radius bottomRightRadius:radius];
    [self.containerView live_updateMaskWithRoundedRect:self.containerView.bounds topLeftRadius:0 topRightRadius:0 bottomLeftRadius:radius bottomRightRadius:radius];
}

- (void)setupUI {
    self.view.backgroundColor = Theme.color.black;
    
    [self.view addSubview:self.cameraView];
    [self.view addSubview:self.containerView];
    [self.containerView addSubview:self.prepView];
    [self.containerView addSubview:self.panelContainerView];
    [self.containerView addSubview:self.privacyTipView];
        
    [self setup_layoutBaseUI];
}

- (void)setup_resetParameters {
    self.eventActing = NO;
    self.coverSelected = NO;
    self.isStarting = NO;
    self.normalRetryChance = requestRetryCount;
    self.recoveryRetryChance = requestRetryCount;
}

@end

