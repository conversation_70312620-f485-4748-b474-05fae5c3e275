//
//  XYLivePushPrepViewController+PaidCourse.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/1/17.
//  Copyright © 2023 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepPrivateExt+Core.h"

@implementation XYLivePushPrepViewController (PaidCourse)

- (void)paidCourse_updateOnSettingLessonId:(NSString *)paidCourseLessonID {
    self.prepVM.paidCourseLessonID = paidCourseLessonID;
    if (paidCourseLessonID.length > 0) {
        // 有lessonId的时候，应该关闭开课弹唱提醒的检测
        self.prepVM.shouldCheckToShowStartLessonAlert = NO;
        NSString *content = [NSString stringWithFormat:@"绑定了lessonId = %@", paidCourseLessonID];
        [XYLogCollector xyLiveLogTag:@"alphaPaidCourse" content:content];
    } else {
        // 应该置为开始检测，后续再通过viewmodel里是否有开课信息继续校验
        self.prepVM.shouldCheckToShowStartLessonAlert = YES;
        NSString *content = NSLocalizedString(@"用户取消了绑定", @"");
        [XYLogCollector xyLiveLogTag:@"alphaPaidCourse" content:content];
    }
}

- (void)bindStartLessonInfoWithPaidCourseLessonId:(NSString *)lessonId
                                 withBizPushStyle:(XYLiveRoomBizPushStyle)bizPushStyle
                                withPrivateLesson:(BOOL)privateLesson {
    self.paidCourseLessonID = lessonId;
    self.bizPushStyle = bizPushStyle;
    if (privateLesson) {
        // 只有私密课程的时候，绑定/解绑专栏操作才会影响本场直播的可见范围
        XYLivePushPrepBroadcastConfigStyle style = privateLesson ? XYLivePushPrepBroadcastConfigStyleShareOnly : XYLivePushPrepBroadcastConfigStyleNormal;
        [self.prepView configBroadcastStatus:style];
    }
}

- (void)paidCourse_setupVcActionBlock {
    __weak typeof(self) wself = self;
    self.paidCourseListVC.handleActionBtnBlock = ^(XYLivePaidCourseStartLessonModel * _Nonnull model, NSString * _Nullable paidCourseLessonID, XYLiveRoomBizPushStyle bizPushStyle, BOOL privateLesson, BOOL isBind) {
        wself.prepVM.startLessonModel = model;
        wself.paidCourseLessonID = paidCourseLessonID;
        wself.bizPushStyle = bizPushStyle;
        if (privateLesson) {
            // 只有私密课程的时候，绑定/解绑专栏操作才会影响本场直播的可见范围
            XYLivePushPrepBroadcastConfigStyle style = isBind ? XYLivePushPrepBroadcastConfigStyleShareOnly : XYLivePushPrepBroadcastConfigStyleNormal;
            [wself.prepView configBroadcastStatus:style];
        }
    };
}

@end

