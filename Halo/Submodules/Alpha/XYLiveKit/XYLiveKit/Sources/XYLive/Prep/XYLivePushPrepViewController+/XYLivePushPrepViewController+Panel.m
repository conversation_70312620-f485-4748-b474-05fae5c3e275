//
//  XYLivePushPrepViewController+Panel.m
//  XYLiveKit
//
//  Created by 周博立 on 2021/1/28.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveKit-Swift.h>

#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import "XYLiveEnterConfig.h"
#import "XYLiveWebViewController.h"
#import "XYLivePushUserCardService.h"
#import "XYLiveFansClubNetworkHandler.h"
#import <XYLiveKit/XYLiveShoppingFansListViewController.h>
#import "XYLiveAlertView.h"

@import KVOController;

@implementation XYLivePushPrepViewController (Panel)

- (void)panel_setupBlock {
    __weak typeof(self) wself = self;
    
    self.moreSettingVC.didDismissed = ^{
        [wself capa_showLiveTabMenu:YES];
        wself.moreSettingVC.autoDismiss = YES;
    };
    
    self.moreSettingVC.didDisplayPanelItem = ^(XYLivePushPanelItem * _Nonnull item) {
        XYLivePushPanelItemType type = item.type;
        switch (type) {
            case XYLivePushPanelItemTypeGamePipChatPush: {
                [XYTrackLiveShoppingPage eventActionID9911WithAnchorID:XYLiveManagerSharedInstance.hostInfo.userID tabName:@"comment_push"];
            }
                break;
            default:
                break;
        }
        [wself trackEventActionID9911WithType:type];
    };
    
    self.moreSettingVC.holdAutoDismiss = ^BOOL(XYLivePushPanelItem * _Nonnull item) {
        BOOL hold = NO;
        switch (item.type) {
            case XYLivePushPanelItemTypeOfficialActivity:
                if ([XYLiveEnterConfig isVideoLive]) {
                    hold = ![XYLiveEnterConfig hasVideoActivity];
                } else if ([XYLiveEnterConfig isScreenLive]) {
                    hold = ![XYLiveEnterConfig hasGameActivity];
                }
                break;
            case XYLivePushPanelItemTypeShoppingClubSetting:
            case XYLivePushPanelItemTypeCommonClubSetting:
                hold = YES;
                break;
            default:
                break;
        }
        return hold;
    };
    
    self.moreSettingVC.didSelectPanelItem = ^(XYLivePushPanelItem * _Nonnull item) {
        XYLivePushPanelItemType type = item.type;
        [wself handlePanelAction: item];
        [wself trackEventActionID2961WithType:type];
    };
}

- (void)handlePanelAction:(XYLivePushPanelItem * _Nonnull)item {
    __weak typeof(self) wself = self;
    XYLivePushPanelItemType type = item.type;
    switch (type){
        case XYLivePushPanelItemTypeCustomBlock: {
            [self capa_showLiveTabMenu:NO];
            [self.shieldWordVC showInViewController:wself];
        }
            break;
        case XYLivePushPanelItemTypeScreen: {
            if ([XYLiveScreenIntroduceViewController canShow]) {
                XYLiveScreenIntroduceViewController *screenIntroduceViewController = [XYLiveScreenIntroduceViewController new];
                [self capa_showLiveTabMenu:NO];
                screenIntroduceViewController.showSettingBlock = ^{
                    [wself seutp_switchClearModel:YES];
                    [wself.screenSettingVC showWithVc:wself];
                };
                screenIntroduceViewController.cancelBlock = ^{
                    [wself capa_showLiveTabMenu:YES];
                };
                [screenIntroduceViewController showWithVc:wself];
                [XYLiveScreenIntroduceViewController showOnce];
            } else {
                [self capa_showLiveTabMenu:NO];
                [self seutp_switchClearModel:YES];
                [self.screenSettingVC showWithVc:wself];
            }
            [[NSUserDefaults standardUserDefaults] setBool:YES forKey:XYLiveGuidanceConfig.screenButtonTappedkey];
        }
            break;
        case XYLivePushPanelItemTypeGoodsCollection:
            break;
        case XYLivePushPanelItemTypePaidControl: {
            self.prepVM.notifyViewToReloadFuncPanel = YES;
            [self capa_showLiveTabMenu:NO];
            [self.moreSettingVC showHostPrepMorePanelIn:wself moreItems:[wself.prepVM moreDetailForItem:item.funcItem] moreTitle:@"付费功能设置"];
        }
            break;
            
        case XYLivePushPanelItemTypeGiftControl: {
            [self.livePreparationLoadingItem show];
            
            [item.funcItem buttonReverse];
            [XYLiveFuncConfigNetworkHandler updateFuncFor:item.funcItem hostID:CURRENT_USER.userId completion:^(BOOL success, NSError * _Nullable error) {
                [wself.livePreparationLoadingItem hide];
                if (success) {
                    [wself.moreSettingVC reverseSettingWithItem:item];
                    // 修改本地config
                    XYLiveEnterConfigStatus configStatus = item.buttonSwitchOn ? XYLiveEnterConfigStatusOn : XYLiveEnterConfigStatusDisabled;
                    [XYLiveEnterConfig updateGiftHistoryStatus:configStatus];
                    [XYLiveEnterConfig updateGiftStatus:configStatus];
                } else {
                    [item.funcItem buttonReverse];
                }
            }];
        }
            break;
        case XYLivePushPanelItemTypeFansClubControlV2: {
            [self.livePreparationLoadingItem show];
            [XYLiveFansGroupLog log:XYLiveFansGroupLogTypeSetting content:[NSString stringWithFormat:@"allow join cur switch %@", @(item.funcItem.buttonSwitch)]];
            [item.funcItem buttonReverse];
            [XYLiveFuncConfigNetworkHandler updateFuncFor:item.funcItem hostID:CURRENT_USER.userId completion:^(BOOL success, NSError * _Nullable error) {
                [wself.livePreparationLoadingItem hide];
                if (success) {
                    [wself.moreSettingVC reverseSettingWithItem:item];
                    [XYLiveEnterConfig updateFansClubStatus:item.buttonSwitchOn ? XYLiveEnterConfigStatusOn : XYLiveEnterConfigStatusOff];
                } else {
                    [item.funcItem buttonReverse];
                }
            }];
        }
            break;
        case XYLivePushPanelItemTypeGamePipChatPush: {
            self.prepVM.notifyViewToReloadFuncPanel = YES;
            XYLiveGameChatSettingViewController *gameVc = [XYLiveGameChatSettingViewController new];
            [wself capa_showLiveTabMenu:NO];
            [gameVc showInViewController:wself dismiss:^{
                [wself capa_showLiveTabMenu:YES];
            }];
        }
            break;
        case XYLivePushPanelItemTypeRoomVisible: {
            self.prepVM.notifyVCToSelectVisible = YES;
        }
            break;
        case XYLivePushPanelItemTypeOfficialActivity: {
            if ([XYLiveEnterConfig isVideoLive]) {
                if ([XYLiveEnterConfig hasVideoActivity]) {
                    [self capa_showLiveTabMenu:NO];
                    XYLiveOfficialActivityViewController *activityVC = [XYLiveOfficialActivityViewController new];
                    activityVC.curBizNaviType = self.prepVM.bizNaviType;
                    activityVC.didSelectItem = ^(NSInteger bizNaviType, NSArray<XYLiveActivityDetailModel *> * _Nonnull activitys) {
                        XYLiveManagerSharedInstance.roomInfo.videoActivitys = activitys;
                    };
                    [activityVC showInViewController:wself height:XYLiveStandardContentHeight.height264 dismiss:^{
                        [wself capa_showLiveTabMenu:YES];
                    } completion:^(BOOL result) {
                        
                    }];
                } else {
                    [[XYAlert createTextItemWithTextOnMiddle:@"暂无活动可选"] show];
                }
            } else if ([XYLiveEnterConfig isScreenLive]) {
                if ([XYLiveEnterConfig hasGameActivity]) {
                    [self capa_showLiveTabMenu:NO];
                    XYLiveOfficialActivityViewController *activityVC = [XYLiveOfficialActivityViewController new];
                    activityVC.curBizNaviType = self.prepVM.bizNaviType;
                    activityVC.didSelectItem = ^(NSInteger bizNaviType, NSArray<XYLiveActivityDetailModel *> * _Nonnull activitys) {
                        XYLiveManagerSharedInstance.roomInfo.gameActivitys = activitys;
                    };
                    [activityVC showInViewController:self height:XYLiveStandardContentHeight.height264 dismiss:^{
                        [wself capa_showLiveTabMenu:YES];
                    } completion:^(BOOL result) {
                        
                    }];
                } else {
                    [[XYAlert createTextItemWithTextOnMiddle:@"暂无活动可选"] show];
                }
            }
        }
            break;
        case XYLivePushPanelItemTypeFansClubSetting: {
            [self capa_showLiveTabMenu:NO];
            self.moreSettingVC.autoDismiss = NO;
            [self.moreSettingVC showPrepMorePanelIn:self moreItems:[self.prepVM moreDetailForItem:item.funcItem] moreTitle:@"粉丝团设置" height:XYLiveStandardContentHeight.height50PercentOfScreen];
        }
            break;
        case XYLivePushPanelItemTypeShoppingClubSetting: {
            // 如果已经是购物粉丝团，不做处理
            if (![XYLiveEnterConfig isShoppingClub]) {
                XYLivePushShoppingClubSwitchViewController *switchVC = [XYLivePushShoppingClubSwitchViewController new];
                switchVC.vieweModel = [XYLiveFansClubViewModel new];
                switchVC.isFromPushPreView = YES;
                switchVC.joinSuccess = ^{
                    XYLiveEventPackage *package = [wself.eventManager packageByKey:@(XYLivePushPrePackageTypeNonRecover)];
                    id<XYLiveEventListenerProtocol> preEvent = [package.dispatcher getEventListenerWithEventType:@(XYLivePushPreChannelEventTypeFeature)];
                    [preEvent tickWithInfo:package.dispatcher.info condition:nil];
                    [wself.moreSettingVC dismiss:^{
                        [wself capa_showLiveTabMenu:YES];
                    }];
                };
                [switchVC showInVc:self];
            }
        }
            break;
        case XYLivePushPanelItemTypeCommonClubSetting: {
            // 如果已经是互动粉丝团，不做处理
            if ([XYLiveEnterConfig isShoppingClub]) {
                // 1是间内。4是预直播页
                [XYLiveFansGroupSwitchAlert showAlertWithSwitchToType:XYLiveRoomFansGroupTypeCommon source:4 completion:^(BOOL success, NSError * _Nullable error) {
                    if (success) {
                        [XYLiveManager sharedManager].fansClubInfo.fansGroupType = XYLiveRoomFansGroupTypeCommon;
                        [XYLiveManager sharedManager].roomInfo.userFansClubInfo.fansGroupType = XYLiveRoomFansGroupTypeCommon;
                        [XYLiveManager sharedManager].configInfo.fansGroupType = XYLiveRoomFansGroupTypeCommon;
                        
                        XYLiveEventPackage *package = [wself.eventManager packageByKey:@(XYLivePushPrePackageTypeNonRecover)];
                        id<XYLiveEventListenerProtocol> preEvent = [package.dispatcher getEventListenerWithEventType:@(XYLivePushPreChannelEventTypeFeature)];
                        [preEvent tickWithInfo:package.dispatcher.info condition:nil];

                        [XYAlert live_showTextItemWithText:@"切换成功"];
                        [wself.moreSettingVC dismiss:^{
                            [wself capa_showLiveTabMenu:YES];
                        }];
                    } else {
                        [XYAlert live_showTextItemWithError:error];
                    }
                    [XYLiveFansGroupLog log:XYLiveFansGroupLogTypeSetting content:[NSString stringWithFormat:@"prepush switch to common %@", @(success)]];
                }];
            }
        }
            break;
        case XYLivePushPanelItemTypeShop:
            self.prepVM.notifyVCToShowShoppingList = YES;
            break;
        case XYLivePushPanelItemTypeCourse:
            self.prepVM.notifyVCToShowPaidCourseList = YES;
            break;
        case XYLivePushPanelItemTypeSelectGame:
            self.prepVM.notifyVCToSelectCategory = YES;
            break;
        case XYLivePushPanelItemTypeLocation:
            self.prepVM.currentItem = item.funcItem;
            self.prepVM.notifyVCToSelectLocation = YES;
            break;
        case XYLivePushPanelItemTypeFansClubManage: {
            if ([XYLiveEnterConfig isShoppingClub]) {
                [wself showHostShoppingFansDetailVC];
            } else {
                [wself showHostCommonFansDetailVC];
            }
        }
            break;
        case XYLivePushPanelItemTypeFansClubMode: {
            // 打开切换粉丝团面板
            XYLiveFansClubTypeSettingPanel *panel = [XYLiveFansClubTypeSettingPanel new];
            WEAK_REF(panel)
            panel.didSelectClubType = ^(XYLiveRoomFansGroupType type) {
                BOOL isShoppingClub = type == XYLiveRoomFansGroupTypeShopping;
                if ([XYLiveEnterConfig isShoppingClub] == isShoppingClub) {
                    return;
                }
                
                // 1是间内。4是预直播页
                [XYLiveFansGroupSwitchAlert showAlertWithSwitchToType:type source:4 completion:^(BOOL success, NSError * _Nullable error) {
                    if (success) {
                        [XYLiveManager sharedManager].fansClubInfo.fansGroupType = type;
                        [XYLiveManager sharedManager].roomInfo.userFansClubInfo.fansGroupType = type;
                        [XYLiveManager sharedManager].configInfo.fansGroupType = type;
                        
                        XYLiveEventPackage *package = [wself.eventManager packageByKey:@(XYLivePushPrePackageTypeNonRecover)];
                        id<XYLiveEventListenerProtocol> preEvent = [package.dispatcher getEventListenerWithEventType:@(XYLivePushPreChannelEventTypeFeature)];
                        [preEvent tickWithInfo:package.dispatcher.info condition:nil];
                        
                        [wself.moreSettingVC reloadData];
                        [XYAlert live_showTextItemWithText:@"切换成功"];
                        [weakpanel live_dimiss];
                    } else {
                        [XYAlert live_showTextItemWithError:error];
                    }
                    [XYLiveFansGroupLog log:XYLiveFansGroupLogTypeSetting content:[NSString stringWithFormat:@"prepush switch to common %@", @(success)]];
                }];
                
            };
            [panel showWithVc:self.moreSettingVC];
        }
            break;
        case XYLivePushPanelItemTypeDefinition: {
            [self showDefinitionSelectVC];
        }
            break;
        default:
            break;
    }
}

- (void)showDefinitionSelectVC {
    if (!XYAlphaSwitch.enablePushMultiLevel) { return; }
    [self capa_showLiveTabMenu:NO];
    XYLiveSubtitleListViewController *list = [[XYLiveSubtitleListViewController alloc] init];
    __weak typeof(self) wself = self;
    __weak typeof(list) wlist = list;
    BOOL video = self.bizNaviType == XYLivePushPrepBizNaviTypeVideo;
    BOOL game = self.bizNaviType == XYLivePushPrepBizNaviTypeGame;
    
    NSArray *title = [NSArray new];
    NSArray *subTitle = [NSArray new];
    NSInteger selectIndex = 0;
    NSInteger recommendIndex = 0;
    if (video) {
        title = [self.roomInfo.pushDispatchInfo videoLevels];
        subTitle = [self.roomInfo.pushDispatchInfo videoLevelsDescription];
        selectIndex = [self.roomInfo.pushDispatchInfo videoLevelIndex];
        recommendIndex = [self.roomInfo.pushDispatchInfo videoRecommendLevelIndex];
    } else if (game) {
        title = [self.roomInfo.pushDispatchInfo gameLevels];
        subTitle = [self.roomInfo.pushDispatchInfo gameLevelsDescription];
        selectIndex = [self.roomInfo.pushDispatchInfo gameLevelIndex];
        recommendIndex = [self.roomInfo.pushDispatchInfo gameRecommendLevelIndex];
    }
    [XYLogCollector xyLiveLogTag:@"prep_definition_select_vc" content:[NSString stringWithFormat:@"titles = %@, subtitles = %@, index = %@",title, subTitle, @(selectIndex)]];
    list.itemClickedCallback = ^(NSInteger index, NSString *name) {
        // 本地存储对应的编码参数
        [XYLogCollector xyLiveLogTag:@"prep_definition_select_vc" content:[NSString stringWithFormat:@"item click name = %@, index = %@",name, @(index)]];
        if (selectIndex == index) { return; }
        if (video) {
            [PushDispatchMultiLevelUtil setVideoLevel:name];
            [wself.roomInfo.pushDispatchInfo restoreSavedLevelForVideo:name];
        } else if (game) {
            [PushDispatchMultiLevelUtil setGameLevel:name];
            [wself.roomInfo.pushDispatchInfo restoreSavedLevelForGame:name];
        }
        
        [XYAlertCenter live_showTextItemWithText:[NSString stringWithFormat:@"切换「%@」成功",name]];
        [wlist dismiss];
    };
    
    [list showInViewController:self
                           tip:@"清晰度越高，用户的观看体验更好"
                          with:title
            subtitleSelections:subTitle
                   selectIndex:selectIndex
                recommendIndex:recommendIndex
                       dismiss:^{
        [wself capa_showLiveTabMenu:YES];
    }];
}

- (void)showHostCommonFansDetailVC {
    __weak typeof(self) wself = self;
    NSString *hostId = [XYLiveManager sharedManager].hostInfo.userID;
    if (hostId.length <= 0) {
        hostId = CURRENT_USER.userId;
    }
    XYLiveFansClubHostDetailViewContoller *detailVC = [[XYLiveFansClubHostDetailViewContoller alloc] initWithRoomId:[XYLiveManager sharedManager].roomInfo.roomIDStr hostId:hostId source:@"normal"];
    WEAK_REF(detailVC)
    detailVC.ruleButtonClosure = ^{
        CGFloat height = weakdetailVC.liveContentView.xy_height;
        [XYLiveWebViewController showFansClubProtocolInViewController:wself height:height animated:YES completion:nil];
    };
    
    detailVC.editButtonClosure = ^(NSString * _Nonnull fansClubName) {
        // 跳转到编辑页面
        [wself openFansClubNameEditVC:fansClubName];
    };
    
    detailVC.guideButtonClosure = ^(NSString * _Nonnull actionUrl) {
        [wself.moreSettingVC dismiss:^{
            [wself capa_showLiveTabMenu:YES];
        }];
        [wself.actionRouteManager performWith:actionUrl];
    };
    
    [detailVC showWithVc:self.moreSettingVC];
}

- (void)showHostShoppingFansDetailVC {
    XYLiveFansClubViewModel *viewModel = [XYLiveFansClubViewModel new];
    __weak typeof(self) wself = self;
    WEAK_REF(viewModel)
    [viewModel requestFansClubInfoIsHost:YES complete:^(XYLiveFansClubInfo * _Nonnull fansClubInfo, NSError * _Nonnull error) {
        XYLiveShoppingFansListViewController *listVC = [XYLiveShoppingFansListViewController new];
        listVC.isHost = YES;
        listVC.roomID = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
        listVC.hostID = XYLiveManagerSharedInstance.roomInfo.hostInfo.userID;
        [listVC bindViewModel:weakviewModel];
        [listVC showInViewController:wself.moreSettingVC];
    }];
}

- (void)panel_handldFuncItem:(XYLivePrepFuncItem *)item {
    switch (item.type) {
        case XYLivePrepFuncItemTypeEffect:
            self.prepVM.notifyVCToPresentEffectPanel = YES;
            break;
        case XYLivePrepFuncItemTypeFlip:
            self.prepVM.notifyVCToFlipCamera = YES;
            break;
        case XYLivePrepFuncItemTypeForenotice:
            self.prepVM.notifyVCToShowForenotice = YES;
            break;
        case XYLivePrepFuncItemTypeShop:
            self.prepVM.notifyVCToShowShoppingList = YES;
            break;
        case XYLivePrepFuncItemTypeCollection:
            break;
        case XYLivePrepFuncItemTypeCourse:
            self.prepVM.notifyVCToShowPaidCourseList = YES;
            break;
        case XYLivePrepFuncItemTypeMore:
            self.prepVM.notifyVCToShowMore = YES;
            break;
        case XYLivePrepFuncItemTypeLandscape: {
            [self.livePreparationLoadingItem show];
            [item buttonReverse];
            [XYLiveFuncConfigNetworkHandler updateFuncFor:item hostID:CURRENT_USER.userId completion:^(BOOL success, NSError * _Nullable error) {
                [self.livePreparationLoadingItem hide];
                if (error) {
                    [item buttonReverse];
                } else {
                    NSString *tt = item.buttonOn ? @"已切换为横屏模式，画面横向展示" : @"已切换为竖屏模式，画面纵向展示";
                    [XYAlertCenter live_showTextItemWithText:tt];
                }
                XYLivePushPrepParamCache.pushLandscape = item.buttonOn;
            }];
        }
            break;
        case XYLivePrepFuncItemTypeGameSelect: {
            self.prepVM.notifyVCToSelectCategory = YES;
            NSString *tabName = @"choose_game";
            NSString *liveOrChat = @"live";
            [XYTrackLivePreparePage eventActionID2961WithChannelTabName:tabName tabID:self.prepVM.channelTabID liveOrChat:liveOrChat];
        }
            break;
        default:
            break;
    }
}

- (void)trackEventActionID9911WithType:(XYLivePushPanelItemType)type {
    NSString *liveOrChat = @"live";
    if (self.prepVM.bizNaviType == XYLivePushPrepBizNaviTypeRHVoice) {
        liveOrChat = @"chat";
    }
    if (type == XYLivePushPanelItemTypeRoomVisible) {
        NSString *tabName = XYLiveEnterConfig.roomVisibleStatus ? @"range_share" : @"range_all";
        [XYTrackLivePreparePage eventActionID9911WithTabName:tabName channelTabID:self.prepVM.channelTabID vidoeOrChat:liveOrChat];
    } else if (type == XYLivePushPanelItemTypeOfficialActivity) {
        [XYTrackLivePreparePage eventActionID9911WithTabName:@"official_activity" channelTabID:self.prepVM.channelTabID vidoeOrChat:liveOrChat];
    } else if (type == XYLivePushPanelItemTypeLocation) { // 添加地点
        [XYTrackLivePreparePage eventActionID9911WithTabName:@"add_location" channelTabID:self.prepVM.channelTabID vidoeOrChat:liveOrChat];
    }
}

- (void)trackEventActionID2961WithType:(XYLivePushPanelItemType)type {
    NSString *liveOrChat = @"live";
    if (self.prepVM.bizNaviType == XYLivePushPrepBizNaviTypeRHVoice) {
        liveOrChat = @"chat";
    }
    if (type == XYLivePushPanelItemTypeRoomVisible) {
        NSString *tabName = XYLiveEnterConfig.roomVisibleStatus ? @"range_share" : @"range_all" ;
        [XYTrackLivePreparePage eventActionID2961WithChannelTabName:tabName tabID:self.prepVM.channelTabID liveOrChat:liveOrChat];
    } else if (type == XYLivePushPanelItemTypeOfficialActivity) {
        [XYTrackLivePreparePage eventActionID2961WithChannelTabName:@"official_activity" tabID:self.prepVM.channelTabID liveOrChat:liveOrChat];
    } else if (type == XYLivePushPanelItemTypeLocation) {
        // 原来tabID = "视频"感觉有问题,先埋点要求埋的是phone 不过曝光埋的是视频,搞不明白了...
        NSString *tabID = [XYLivePrepNewTracker trackIndexWithMode:self.prepVM.bizNaviType];
        [XYTrackLivePreparePage eventActionID2961WithChannelTabName:@"add_location" tabID:tabID liveOrChat:liveOrChat];
    }
}

- (void)openFansClubNameEditVC:(NSString *)fansClubName {
    XYLiveFansClubNameViewController *clubNameVC = [[XYLiveFansClubNameViewController alloc] initWithClubName:fansClubName];
    [clubNameVC showInViewController:self dismiss:^{
        
    }];
    __weak typeof(self) wself = self;
    __weak typeof(clubNameVC) wClubNameVC = clubNameVC;
    clubNameVC.onSubmitEvent = ^(NSString *name) {
        if (name.length == 0) {
            [XYAlertCenter live_showTextItemWithText:@"还未输入团名称"];
            return;
        }
        NSString *subTitle = [NSString stringWithFormat:@"确定将粉丝团名称修改为%@吗？30天内只可修改一次哦~", name];
        XYLiveAlertView *alert = [XYLiveAlertView alertViewWithTitle:@"修改团名称" subTitle:subTitle];
        alert.rightBtnBlock = ^(UIButton * _Nonnull sender, BOOL timeout) {
            [XYLiveFansClubNetworkHandler updateCommonFansClubName:name complete:^(BOOL success) {
                if (success) {
                    [XYAlertCenter live_showTextItemWithText:@"团名称设置成功，等待审核中"];
                    XYExecuteOnMainQueue(^{
                        [wClubNameVC dismiss];
                    });
                }
            }];
        };
        [alert showInWindow:[[UIApplication sharedApplication] keyWindow] animated:YES];
    };
}

@end
