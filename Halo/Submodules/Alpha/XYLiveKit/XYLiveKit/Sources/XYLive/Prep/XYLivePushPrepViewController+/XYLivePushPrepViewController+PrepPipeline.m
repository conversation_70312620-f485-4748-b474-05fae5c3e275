//
//  XYLivePushPrepViewController+PrepPipeline.m
//  XYLiveKit
//
//  Created by 周博立 on 2021/9/25.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import "XYLivePushPrepPrivateExt+Recovery.h"
#import "XYTrackLiveBroadcastPage.h"
#import "XYLiveCountdownView.h"
#import "XYLivePushGambitConfigCenter.h"
#import "XYLivePushGambitHelper.h"

@import XYLiveFoundation;
@import XYConfigCenter;
@import XYEvolver;
@import XYLiveFoundation;
@import XYMacroConfig;
@import XYLiveCore;
@import XYTracker;
@import XYStorageCore_Linker;
@import XYAlphaUtils;
@import KasaSDK;
@import XYRoutesManager;

@implementation XYLivePushPrepViewController (PrepPipeline)

- (void)pipeline_shoppingInfoCheckOnLoginParam {
    self.loginParam = [self setup_basicLoginParam];
    self.loginParam.goodsUploadItems = [self.prepVM newGoodsUploadItems];
}

- (void)pipeline_loginParamCheckOnRecoveryWithRoom:(XYLiveRoomInfo *)roomInfo {
    //带货的商品
    self.loginParam = [self setup_basicLoginParam];
    self.loginParam.roomInfo = roomInfo;
    self.loginParam.hasGoods = roomInfo.hasGoods;
    self.loginParam.liveRecovered = YES;
}

- (void)pipeline_mappingContentTypeForRoomInfo:(XYLiveRoomInfo *)roomInfo {
    // 续播维持上次类型
    if (roomInfo.isRecoveryRoom) {
        return;
    }
    // 否则进行映射
    self.roomInfo.contentType = [XYLiveRoomEnumUtils convertBizNaviToContentTypeWithType:self.bizNaviType];
    if (self.bizNaviType != XYLivePushPrepBizNaviTypePC) {
        self.roomInfo.obsType = XYLiveRoomBizPushOBSTypeMobile;
    } else {
        self.roomInfo.obsType = self.roomInfo.allowNormalOBS ? XYLiveRoomBizPushOBSTypeOBS : XYLiveRoomBizPushOBSTypePC;
    }
}

- (void)pipeline_normalPushForCurrentRoom {
    [self pipeline_mappingContentTypeForRoomInfo:self.roomInfo];
    self.roomInfo.bizStyle = self.bizPushStyle;
    
    
    // V8.51，话题活动信息携带至开播页进行上报
    id<XYLivePushPrepGambitNodeServiceProtocol> gambitService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushPrepGambitNodeServiceProtocol)];
    XYLiveManagerSharedInstance.cacheGambit = [gambitService currentGambit];
    
    // V8.65，将分类（游戏）转为话题并携带至开播页
    if ([XYLivePushGambitConfigCenter enableNewGambit]) {
        if (self.prepVM.category) {
            XYLiveManagerSharedInstance.cacheGambit = [XYLivePushGambitHelper convertCacheModelFromCategory:self.prepVM.category];
        }
    } else if (self.isScreenLive && self.prepVM.category) {
        self.roomInfo.categories = @[self.prepVM.category];
    }
    
    // 群畅聊标识传递到开播页
    self.roomInfo.joinLimit = self.loginParam.joinLimit;
    
    self.loginParam.roomInfo = self.roomInfo;
    __weak typeof(self) wself = self;
    [XYLiveManagerSharedInstance startLivePushProcessWithLoginParam:self.loginParam 
                                                       bizPushStyle:self.bizPushStyle
                                                           lessonID:self.paidCourseLessonID
                                                     liveInfoResult:^(XYLiveRoomInfo * _Nonnull roomInfo, NSError * _Nonnull error) {
        XYExecuteOnMainQueue(^{
            // 是否更改了封面
            if (XYLiveManagerSharedInstance.loginParam.coverLocalPath.length) {
                [wself uploadCoverClarity];
            }
            wself.roomInfo.curRoomName = wself.prepVM.title;
            wself.eventActing = NO;
            [wself.livePreparationLoadingItem hide];
            [XYTrackLiveBroadcastPage startLiveEvent:@"push_prepare_process" isOBS:wself.isOBSMode success:!error roomID:roomInfo.roomID pushUrl:wself.roomInfo.streamURLInfo.livePushURL errorMessage:error.localizedDescription];
            if (!error) {
                [wself pipeline_normalPushOnRoomResp];
            } else {
                [wself handlePushProcessError:error retryNormalPush:YES];
            }
        });
    }];
}

// 封面图清晰度打分上报
- (void)uploadCoverClarity {
    BOOL open = XYConfigCenter().boolForKey(@"ios_cover_fraction_upload", NO);
    BOOL slient = (XYConfigCenter().boolForKey(@"ios_live_slient_upload_cover", NO));   // 异步上传封面后门
    if (open && !slient) {
        dispatch_async(dispatch_get_global_queue(0, 0), ^{
            if (self.prepVM.coverImage) {
                XYCRRenderHandle *handle;
                handle = [[XYCRRenderHandle alloc] initWithRenderType:XYCRRenderTypePhoto bizID:ZSBIZ_LIVE_VIDEO];
                
                NSString *clarity = [NSString stringWithFormat:@"%f", [handle detectClarityWithImage:self.prepVM.coverImage]];
                NSString *userId = XYLiveManagerSharedInstance.roomInfo.hostInfo.userID ?: @"";
                NSString *coverId = XYLiveManagerSharedInstance.loginParam.cover ?: @"";
                NSString *roomId = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
                // 点位名称：sns_live_cover_fraction_upload
                [XYTracker track:^(XYAPMContext *context) {
                    context.snsLiveCoverFractionUpload
                    .sampleRate(1.0)
                    .fraction(clarity)
                    .userId(userId)
                    .coverId(coverId)
                    .roomId(roomId);
                }];
                [XYLogCollector xyLiveLogTag:@"cover_clarity" content:@"upload_succeed"];
            } else {
                [XYLogCollector xyLiveLogTag:@"cover_clarity" content:@"cover_download_failed"];
            }
        });
    }
}

- (void)startNormalLivePushProcess {
    [XYLiveManagerSharedInstance reset];
    [self pipeline_loginParamCheck];
    if (self.roomInfo) {
        [XYLogCollector xyLiveLogTag:@"push_prep_start" content:@"pipeline normalPushForCurrentRoom"];
        [self.livePreparationLoadingItem show];
        [self pipeline_normalPushForCurrentRoom];
    } else {
        [XYLogCollector xyLiveLogTag:@"push_prep_start" content:@"no roomInfo"];
        [[XYAlertCenter live_createTextItemWithText:@"数据获取异常，请退出重试！"] show];
        [self resetDecorationIndeed];
    }
}

- (void)pipeline_normalPushOnRoomResp {
    [XYTrackLiveBroadcastPage startLiveEvent:@"jump_to_pushroom" isOBS:self.isOBSMode success:YES roomID:self.roomInfo.roomID pushUrl:self.roomInfo.streamURLInfo.livePushURL errorMessage:@""];
    
    [self pipeline_prepDone];
    [self pipeline_shareCheck];
}

- (void)pipeline_pushBeforeCheckOnRoom:(XYLiveRoomInfo *)roomInfo countdown:(BOOL)cd {
    if (!roomInfo) {
        self.prepView.hidden = NO;
        [self capa_showLiveTabMenu:YES];
        return;
    }
    
    [self pipeline_filterCheck];
    [self pipeline_cameraFrontCheck];
    
    [self capa_showLiveTabMenu:NO];
    
    [self pipeline_countToPush:cd roomInfo:roomInfo];
}

#pragma mark <Pipeline>

- (void)pipeline_loginParamCheck {
    self.loginParam = [self setup_basicLoginParam];
    self.loginParam.goodsUploadItems = [self.prepVM newGoodsUploadItems];
    if (self.coverSelected && self.coverLocalPath.length) {
        self.loginParam.coverLocalPath = self.coverLocalPath;
    }
}

- (void)pipeline_prepDone {
    if (self.prepDoneBlock) {
        self.prepDoneBlock();
    }
}

- (void)pipeline_shareCheck {
    [XYTrackLiveBroadcastPage startLiveEvent:@"jump_to_pushroom" isOBS:self.isOBSMode success:YES roomID:self.roomInfo.roomID pushUrl:self.roomInfo.streamURLInfo.livePushURL errorMessage:@""];
    if (self.prepVM.selectedShareType != XYLiveShareTypeNone) {
        //起loading,准备好跳转，分享结束返回app后开始推流
        [self.livePreparationLoadingItem show];
        __weak typeof(self) wself = self;
        XYSPlatformAssembly assembly = [wself.prepVM getPlatformAssembly];
        
        [XYLiveShareCenter shareWithRoomId:self.roomInfo.roomIDStr contentType:self.prepVM.bizNaviType assembly:assembly callback:^(XYSharingRequest * _Nullable request, XYLiveShareInfo * _Nonnull sinfo, NSError * _Nullable error) {
            if (!error) {
                XYSharingTask *task = [XYSocialCenter.center shareTaskWithRequest:request];
                task.delegate = (id<XYSocialTaskDelegate>)wself;
                [task resume];
            } else {
                [wself.livePreparationLoadingItem hide];
                [[XYAlertCenter live_createTextItemWithText:@"分享资源下载失败"] show];
                [wself pipeline_pushBeforeCheckOnRoom:wself.roomInfo countdown:YES];
            }
        }];
    } else {
        [self pipeline_pushBeforeCheckOnRoom:self.roomInfo countdown:YES];
    }
}

- (void)pipeline_filterCheck {
    if ([XYLiveFilterConfigManager fetchCurrentFilterConfigInfo].count == 0) {
        [XYLiveFilterConfigManager fetchAndUpdateFilterConfigInfoWithCompleteHandler:NULL];
    }
}

- (void)pipeline_cameraFrontCheck {
    // V7.80，续播不再保留本次直播的采集方向
    BOOL front = [XYLiveManagerSharedInstance.coreManager.media isCameraFront];
    [[XYSCKVMigrateTool defaultUserDefaultTool_v2] setBool:front forKey:XYLivePushFrontCamera];
}

- (void)pipeline_countToPush:(BOOL)countdown roomInfo:(XYLiveRoomInfo *)roomInfo {
    [XYLogCollector xyLiveLogTag:@"rtc" content:[NSString stringWithFormat:@"start_push rtc_switch: %@", @(roomInfo.pushType)]];
    if (countdown) {
        __weak typeof(self) wself = self;
        [self showCountdownViewWithRoomInfo:roomInfo completion:^{
            [wself pipeline_startPush:roomInfo needcountdown:countdown];
        }];
    } else {
        [self pipeline_startPush:roomInfo needcountdown:countdown];
    }
}

- (void)pipeline_startPush:(XYLiveRoomInfo *)roomInfo needcountdown:(BOOL)needcountdown {
    self.shouldKeepIMManagerWhenVCDealloc = YES;
    self.isStarting = YES;
    [self pipeline_jumpToPushWithRoomInfo:roomInfo needcountdown:needcountdown];
}

- (void)pipeline_jumpToPushWithRoomInfo:(XYLiveRoomInfo *)roomInfo needcountdown:(BOOL)needcountdown {
    // 判定contentType
    [self pipeline_mappingContentTypeForRoomInfo:roomInfo];
    // 特定业务场景关闭采集
    BOOL noLocalPush = [XYLiveRoomEnumUtils judgeIfShouldStopPushWithBizStyle:self.bizPushStyle];
    BOOL screenPush = (self.roomInfo.contentType == XYLiveRoomPushContentTypeScreenCap);
    BOOL noPreview = noLocalPush || screenPush;
    
    if (noLocalPush || screenPush) {
        [XYLiveManagerSharedInstance.coreManager.media stopPreview];
    }
    
    [PushPrepUtilites showDebugOrAdhocLogToastWithType:roomInfo.pushType];
    
    // 调用capa服务清理空间
    NSTimeInterval startTime = CFAbsoluteTimeGetCurrent() * 1000;
    [self cleanMemoryForLivePushIfNeeded];
    NSTimeInterval endTime = CFAbsoluteTimeGetCurrent() * 1000;
    [XYLogCollector xyLiveLogTag:@"ClearCapaMemory" content:[NSString stringWithFormat:@"use time: %@ ms", @(endTime - startTime)]];
    // 截图
    UIImageView *screenshotView = nil;
    if (noLocalPush && XYConfigCenter().justOnceBoolForKey(@"ios_live_push_fix_nolocalpush_open_carema", NO)) {
        screenshotView = [self screenshotForLivePushLoadingWithView];
    } else {
        if (self.roomInfo.contentType == XYLiveRoomPushContentTypeVideo) {
            screenshotView = [self screenshotForLivePushLoadingWithViewOpt];
        } else {
            screenshotView = [self screenshotForLivePushLoadingWithView];
        }
    }
    
    [XYLiveManagerSharedInstance.consumeService memoryGenerate:XYLiveConsumeMemOptionPreDiff];
    
    [self updateApmV2BasicParams];
    
    if (self.isOBSMode) {
        if ([self canApplyRTC]) {
            [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventPushObs recovery:roomInfo.isRecovery encodeInfo:[RTCVideoEncodeInfo encodeInfoWithResolution:self.roomInfo.encodedResolution vendorType:self.rtcSession.vendorType]];
        } else {
            [self updateApmRtmpParam];
            [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventPushObs recovery:roomInfo.isRecovery encodeInfo:[XYLiveRtmpPusher presetVideoEncodeInfo]];
        }
    }
    
    XYLivePushRoomViewController *pushRoomVC = nil;
    if (XYConfigCenter().boolForKey(@"all_gop_use_dispatch", NO) == NO) {
        self.roomInfo.pushDispatchInfo.encode.gop = XYExperiments().integerForKey(@"Alpha_Encoder_Gop");
        self.roomInfo.pushDispatchInfo.gameEncode.gop = XYExperiments().integerForKey(@"Alpha_Encoder_Gop");
        self.roomInfo.pushDispatchInfo.talkEncode.gop = XYExperiments().integerForKey(@"Alpha_Encoder_Gop");
    }
    
    if (self.roomInfo.contentType == XYLiveRoomPushContentTypeRedHouse) {
        if (!self.rtcSession) {
            self.rtcSession = [RTCChannelSession sessionWithRoomInfo:self.roomInfo userInfo:self.loginParam.userInfo passport:XYLiveManagerSharedInstance.imConfigParam.passport role:RTCMemberRoleHost audienceRtcLinkReference:XYLiveManagerSharedInstance.linkmicManager.audiencePushProtocolType];
        }
        
        [MultiLinkmicCommService getPushConfigV2WithContentType: XYLiveRoomPushContentTypeRedHouse otherRoomId:@"" userId:self.loginParam.userInfo.userID completion:^(XYLivePushDispatchInfoEncode * encode, NSError * error) {
            // 在开播前需要调用before_client_link确保畅聊连线状态，目前在这里打接口，正常流程应该在预直播页在进入畅聊tab时去打。
            NSLog(@"startLink redhouse encode PushType: %ld", (long)encode.pushType);
        }];
        
        if (XYConfigCenter().boolForKey(@"ios_live_push_prep_chat_dispatch", YES)) {
            XYLivePushType pushType =    self.roomInfo.pushDispatchInfo.talkEncode.pushType;
            switch (pushType) {
                case XYLivePushTypeUndefine:
                    break;
                case XYLivePushTypeRtmp:
                    break;
                case XYLivePushTypeTrtc:
                    self.rtcSession.vendorType = RTCVendorTypeTRTC;
                    break;
                case XYLivePushTypeKasa:
                    self.rtcSession.vendorType = RTCVendorTypeKasa;
                    break;
            }
        }
        pushRoomVC = [[XYLivePushRoomViewController alloc] initWithRoomInfo:roomInfo loginParam:self.loginParam liveSession:self.rtcSession bizPushStyle:self.bizPushStyle screenshot:screenshotView];
    } else {
        if ([self canApplyRTC]) {
            BOOL noSession = (self.rtcSession == nil);
            BOOL needSwitchTrtc = (screenPush && (self.rtcSession.vendorType != RTCVendorTypeTRTC));
            needSwitchTrtc = false;
            if (noSession || needSwitchTrtc) {
                self.rtcSession = [RTCChannelSession sessionWithRoomInfo:self.roomInfo userInfo:self.loginParam.userInfo passport:XYLiveManagerSharedInstance.imConfigParam.passport role:RTCMemberRoleHost audienceRtcLinkReference:XYLiveManagerSharedInstance.linkmicManager.audiencePushProtocolType];
                [XYLogCollector xyLiveLogTag:@"screen_capture" content:@"start push in pushtype switch"];
            }
            
            if (XYLiveLinkConfig.applyRTCCore && self.roomInfo.contentType != XYLiveRoomPushContentTypeAudio && (self.roomInfo.pushType != XYLivePushTypeKasa || (self.roomInfo.pushType == XYLivePushTypeKasa && XYLiveLinkConfig.kasaSupportRTCCore))) {
                
                if (roomInfo.contentType == XYLiveRoomPushContentTypeScreenCap) {
                    if (self.roomInfo.pushType == XYLivePushTypeKasa) {
                        self.rtcSession.vendorType = RTCVendorTypeKasa;
                    } else if (self.roomInfo.pushType == XYLivePushTypeTrtc) {
                        self.rtcSession.vendorType = RTCVendorTypeTRTC;
                    }
                }
                if ([XYAlphaSwitch enablePushMultiLevelV2]) {
                    // 点击开播取消网速探测
                    [[KasaLive Instance] stopEchoTest];
                    [[KasaLive Instance] terminate];
                    [XYLogCollector xyLiveLogTag:@"live_multi_level" content:@"stop echo test by click start live"];
                    if (self.rtcSession.vendorType == RTCVendorTypeKasa) {
                        NSString *roomID = self.roomInfo.roomIDStr;
                        if (self.roomInfo.contentType == XYLiveRoomPushContentTypeVideo) {
                            NSString *level = self.roomInfo.pushDispatchInfo.encode.levelName;
                            NSString *bitrate = [NSString stringWithFormat:@"%ld",self.roomInfo.pushDispatchInfo.encode.bitrate];
                            NSString *framerate = [NSString stringWithFormat:@"%ld",self.roomInfo.pushDispatchInfo.encode.fps];
                            NSString *resolution = [NSString stringWithFormat:@"%ld",self.roomInfo.pushDispatchInfo.encode.resolution];
                            NSString *contentType = [NSString stringWithFormat:@"%ld",XYLiveRoomPushContentTypeVideo];
                            NSString *path = @"推荐";
                            // 实验新样式 视频
                            id<XYLivePrepVideoNodeServiceProtocol> videoService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePrepVideoNodeServiceProtocol)];
                            if ([XYAlphaSwitch videoEchoTestResultApply]) {
                                if (videoService.echoTestResultApply) {
                                    path = @"网速";
                                }
                            } else {
                                if (videoService.echoTested) {
                                    path = @"网速";
                                }
                            }
                            
                            if ([PushDispatchMultiLevelUtil videoLevel].length > 0) {
                                path = @"主动";
                            }
                            if ([XYAlphaSwitch reportRecommendLevel]) {
                                [XYAPMLiteTracker track:^(XYAPMLiteContext * _Nonnull context) {
                                    context.eventName(@"live_prep_multi_level_abr")
                                        .sampleRate(1)
                                        .appendEventData(@"level_name", level)
                                        .appendEventData(@"default_level_name", self.roomInfo.pushDispatchInfo.videoRecommendedLevelEncode.name ?: @"")
                                        .appendEventData(@"bitrate", bitrate)
                                        .appendEventData(@"framerate", framerate)
                                        .appendEventData(@"resolution", resolution)
                                        .appendEventData(@"path", path)
                                        .appendEventData(@"content_type", contentType)
                                        .appendEventData(@"room_id", roomID);
                                }];
                            } else {
                                [XYAPMLiteTracker track:^(XYAPMLiteContext * _Nonnull context) {
                                    context.eventName(@"live_prep_multi_level_abr")
                                        .sampleRate(1)
                                        .appendEventData(@"level_name", level)
                                        .appendEventData(@"bitrate", bitrate)
                                        .appendEventData(@"framerate", framerate)
                                        .appendEventData(@"resolution", resolution)
                                        .appendEventData(@"path", path)
                                        .appendEventData(@"content_type", contentType)
                                        .appendEventData(@"room_id", roomID);
                                }];
                            }
                            
                        } else if (self.roomInfo.contentType == XYLiveRoomPushContentTypeScreenCap) {
                            NSString *level = self.roomInfo.pushDispatchInfo.gameEncode.levelName;
                            NSString *bitrate = [NSString stringWithFormat:@"%ld",self.roomInfo.pushDispatchInfo.gameEncode.bitrate];
                            NSString *framerate = [NSString stringWithFormat:@"%ld",self.roomInfo.pushDispatchInfo.gameEncode.fps];
                            NSString *resolution = [NSString stringWithFormat:@"%ld",self.roomInfo.pushDispatchInfo.gameEncode.resolution];
                            NSString *contentType = [NSString stringWithFormat:@"%ld",XYLiveRoomPushContentTypeScreenCap];
                            NSString *path = @"推荐";
                            // 实验新样式  游戏
                            id<XYLivePrepGameNodeServiceProtocol> gameService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePrepGameNodeServiceProtocol)];
                            if ([XYAlphaSwitch gameEchoTestResultApply]) {
                                if (gameService.echoTestResultApply) {
                                    path = @"网速";
                                }
                            } else {
                                if (gameService.echoTested) {
                                    path = @"网速";
                                }
                            }
                            
                            if ([PushDispatchMultiLevelUtil gameLevel].length > 0) {
                                path = @"主动";
                            }
                            if ([XYAlphaSwitch reportRecommendLevel]) {
                                [XYAPMLiteTracker track:^(XYAPMLiteContext * _Nonnull context) {
                                    context.eventName(@"live_prep_multi_level_abr")
                                        .sampleRate(1)
                                        .appendEventData(@"level_name", level)
                                        .appendEventData(@"default_level_name", self.roomInfo.pushDispatchInfo.gameRecommendedLevelEncode.name ?: @"")
                                        .appendEventData(@"bitrate", bitrate)
                                        .appendEventData(@"framerate", framerate)
                                        .appendEventData(@"resolution", resolution)
                                        .appendEventData(@"path", path)
                                        .appendEventData(@"content_type", contentType)
                                        .appendEventData(@"room_id", roomID);
                                }];
                            } else {
                                [XYAPMLiteTracker track:^(XYAPMLiteContext * _Nonnull context) {
                                    context.eventName(@"live_prep_multi_level_abr")
                                        .sampleRate(1)
                                        .appendEventData(@"level_name", level)
                                        .appendEventData(@"bitrate", bitrate)
                                        .appendEventData(@"framerate", framerate)
                                        .appendEventData(@"resolution", resolution)
                                        .appendEventData(@"path", path)
                                        .appendEventData(@"content_type", contentType)
                                        .appendEventData(@"room_id", roomID);
                                }];
                            }
                        }
                    }
                }
                pushRoomVC = [[XYLivePushRoomViewController alloc] initWithRoomInfo:roomInfo loginParam:self.loginParam liveSession:noLocalPush ? nil : self.rtcSession bizPushStyle:self.bizPushStyle screenshot:screenshotView];
            } else {
                pushRoomVC = [[XYLivePushRoomViewController alloc] initWithRoomInfo:roomInfo loginParam:self.loginParam session: noLocalPush ? nil : self.rtcSession bizPushStyle:self.bizPushStyle screenshot:screenshotView];
            }
        } else if ([self canApplyKasa]) {
            XYLiveKasaPusher *kasaPusher = noLocalPush ? nil : [XYLiveKasaPusher new];
            kasaPusher.isAudioLive = roomInfo.isVoiceLive;
            if (kasaPusher) {
                RTCVideoEncodeInfo *encodeInfo = [RTCVideoEncodeInfo encodeInfoWithDispatchInfo:self.roomInfo.pushDispatchInfo vendorType:RTCVendorTypeNone];
                [kasaPusher reloadVideoEncoderParam:encodeInfo userId: XYLiveManagerSharedInstance.userInfo.userID];
            }
            pushRoomVC = [[XYLivePushRoomViewController alloc] initWithRoomInfo:roomInfo loginParam:self.loginParam kasaPusher:kasaPusher bizPushStyle:self.bizPushStyle screenshot:screenshotView];
        } else {
            XYLiveRtmpPusher *pusher = noLocalPush ? nil : [XYLiveRtmpPusher new];
            pusher.isAudioLive = roomInfo.isVoiceLive;
            if (pusher) {
                RTCVideoEncodeInfo *encodeInfo = [RTCVideoEncodeInfo encodeInfoWithDispatchInfo:self.roomInfo.pushDispatchInfo vendorType:RTCVendorTypeNone];
                [pusher reloadVideoEncoderParam: encodeInfo];
            }
            pushRoomVC = [[XYLivePushRoomViewController alloc] initWithRoomInfo:roomInfo loginParam:self.loginParam pusher:pusher bizPushStyle:self.bizPushStyle screenshot:screenshotView];
        }
    }
    
    id<XYLivePrepMoreNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePrepMoreNodeServiceProtocol)];
    [service saveLiveMode];
    
    [self launchIntoPushVc:pushRoomVC needcountdown:needcountdown];
    [XYLiveFastVcCache addInFindCacheWithVc:pushRoomVC];
}

- (void)launchIntoPushVc:(XYLivePushRoomViewController *)pushRoomVC needcountdown:(BOOL)needcountdown {
    UINavigationController *(^fetchCurrentNavBlock)(void) = ^UINavigationController *{
        UINavigationController *nav = [XYPHRoutesManager getRootNavi];
        if (!nav || !nav.view.window) {
            nav = [XYPHRoutesManager getTempNavi];
        }
        return nav;
    };
    
    if (self.presented) {
        [self dismissLivePrepPushViewAnimated:NO needTransition:YES completion:^{
            UINavigationController *nav = fetchCurrentNavBlock();
            if (!nav) {
                return;
            }
            XYPHNavigationViewController *containerNav = [[XYPHNavigationViewController alloc] initWithRootViewController:pushRoomVC];
            containerNav.modalPresentationStyle = UIModalPresentationFullScreen;
            __weak __typeof(pushRoomVC) wpushRoomVC = pushRoomVC;
            [nav presentViewController:containerNav animated:NO completion:^{
                if (needcountdown) {
                    [wpushRoomVC showCountdownView:nil];
                }
            }];
        }];
    } else {
        UINavigationController *nav = fetchCurrentNavBlock();
        if (!nav) {
            return;
        }
        [nav pushViewController:pushRoomVC animated:NO];
        NSMutableArray *viewControllersArray = [self.navigationController.viewControllers mutableCopy];
        if (self) {
            [viewControllersArray removeObject:self];
            if (viewControllersArray.count > 0) {
                self.navigationController.viewControllers = viewControllersArray;
            }
        }
    }
}

#pragma mark <Helper>

- (UIImageView *)screenshotForLivePushLoadingWithView {
    UIView *view = self.view;
    UIGraphicsImageRendererFormat *format = nil;
    if (@available(iOS 11.0, *)) {
        format = [UIGraphicsImageRendererFormat preferredFormat];
    } else {
        format = [UIGraphicsImageRendererFormat defaultFormat];
    }
    format.opaque = YES;
    format.scale = [[UIScreen mainScreen] scale];
    UIGraphicsImageRenderer *renderer = [[UIGraphicsImageRenderer alloc] initWithSize:DeviceUtility.screenSize format:format];
    UIImage *viewImage = [renderer imageWithActions:^(UIGraphicsImageRendererContext * _Nonnull rendererContext) {
        if ([view respondsToSelector:@selector(drawViewHierarchyInRect:afterScreenUpdates:)]) {
            [view drawViewHierarchyInRect:view.bounds afterScreenUpdates:NO];
        } else {
            CGContextRef context = UIGraphicsGetCurrentContext();
            [view.layer renderInContext:context];
        }
    }];
    
    UIImageView *imageView = [UIImageView new];
    [CATransaction begin];
    [CATransaction setDisableActions:YES];
    imageView.frame = DeviceUtility.screenBounds;
    [CATransaction commit];
    imageView.image = viewImage;
    UIWindow *window = [UIApplication sharedApplication].keyWindow;
    [window addSubview:imageView];
    XYAlertItem *loadingItem = [XYAlertCenter createLoadingItemInView:imageView];
    [loadingItem show];
    
    return imageView;
}

- (UIImageView *)screenshotForLivePushLoadingWithViewOpt {
    UIImageView *imageView = [UIImageView new];
    [CATransaction begin];
    [CATransaction setDisableActions:YES];
    imageView.frame = DeviceUtility.screenBounds;
    [CATransaction commit];
    UIWindow *window = [UIApplication sharedApplication].keyWindow;
    [window addSubview:imageView];
    [XYLiveManagerSharedInstance.coreManager.media startPreview: imageView];
    
    XYAlertItem *loadingItem = [XYAlertCenter createLoadingItemInView:imageView];
    [loadingItem show];
    
    return imageView;
}

- (void)cleanMemoryForLivePushIfNeeded {
    if (self.needCleanMemory) {
        XYLogDebug(@"[lothar]%s, %d", __PRETTY_FUNCTION__, __LINE__);
        id<XYCapaServiceProtocol> service = [XYOldRouter serviceForProtocol:@protocol(XYCapaServiceProtocol)];
        if ([service respondsToSelector:@selector(cleanMemoryForLivePush)]) {
            [service cleanMemoryForLivePush];
        }
    }
}

- (void)showCountdownViewWithRoomInfo:(XYLiveRoomInfo *)roomInfo completion:(void(^)(void))completion {
    if ([XYLiveConfigCenter pushSupportCountdownWithContentType:roomInfo.contentType]) {
        EXECUTE_BLOCK_SAFELY(completion);
        return;
    }
    
    XYLiveCountdownView *cdView = [XYLiveCountdownView new];
    [self.view addSubview:cdView];
    cdView.frame = self.view.bounds;
    __weak __typeof(cdView) wcdView = cdView;
    [cdView playWithCompletion:^{
        if (completion) {
            [wcdView removeFromSuperview];
            completion();
        }
    }];
}

- (void)dismissLivePrepPushViewAnimated:(BOOL)flag completion:(void (^)(void))completion {
    if (self.dismissalHandler) {
        self.dismissalHandler(self, flag, completion);
    }
    else {
        // !!!:Self-closed view controller should know the stack of presented view controllers very well.
        [self dismissViewControllerAnimated:flag completion:completion];
    }
}

- (void)dismissLivePrepPushViewAnimated:(BOOL)flag needTransition:(BOOL)needTransition completion:(void (^)(void))completion {
    if (!XYConfigCenter().boolForKey(@"ios_live_push_prep_transition", NO)) {
        [self dismissLivePrepPushViewAnimated:flag completion:completion];
        return;
    }
    
    if (!needTransition) {
        [self dismissLivePrepPushViewAnimated:flag completion:completion];
        return;
    }
    
    UINavigationController *nav = [XYPHRoutesManager getRootNavi];
    if (!nav) {
        [self dismissLivePrepPushViewAnimated:flag completion:completion];
        return;
    }
    
    UIViewController *empty = [[UIViewController alloc] init];
    [nav pushViewController:empty animated:NO];
    
    [self dismissLivePrepPushViewAnimated:flag completion:^{
        completion();
        if (needTransition) {
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                [nav popViewControllerAnimated:NO];
            });
        }
    }];
}

- (void)resetDecorationIndeed {
    self.prepView.hidden = NO;
    [self capa_showLiveTabMenu:YES];
}

- (void)startLiveButtonPressedAction {
    self.eventActing = YES;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        self.eventActing = NO;
    });
    self.normalRetryChance = requestRetryCount;
    self.hasRecoveringRoom = NO;
    [XYTrackLiveBroadcastPage startLiveEvent:@"start_button_clicked" isOBS:self.isOBSMode success:YES roomID:0 pushUrl:@"" errorMessage:@""];
    if (self.isOBSMode) {
        BOOL robs = self.roomInfo.allowNormalOBS != 1;
        [XYLogCollector xyLiveLogTag:@"push_prep_start" content:[NSString stringWithFormat:@"obs %@ live start btn pressed", @(robs)]];
    } else {
        [XYLogCollector xyLiveLogTag:@"push_prep_start" content:@"normal live start btn pressed"];
    }
    [self startNormalLivePushProcess];
}

@end
