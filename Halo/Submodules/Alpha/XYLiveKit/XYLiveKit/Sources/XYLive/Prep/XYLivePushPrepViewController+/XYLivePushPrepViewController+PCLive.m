//
//  XYLivePushPrepViewController+PCLive.m
//  XYLiveKit
//
//  Created by gongyidemac on 2021/7/29.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
@import XYLiveUIKit;
@import XYLivePlayManager;
@import XYLiveFoundation;
@import XYLiveCore;

@implementation XYLivePushPrepViewController (PCLive)

- (void)startPCLiveActionForPartnerMode:(BOOL)isPartner {
    xyLiveSharedAPMManager.startApmInfo.bizNaviType = self.prepVM.bizNaviType;
    
    if (isPartner) {// 直播助手复用recovery链路
        [self sdk_prepRoomWithExtra:@{@"scence" : @"redObs"}];
    } else {// obs复用start链路
        __weak typeof(self) wself = self;
        [self auth_checkStartLiveConditionWithRoomID:self.roomInfo.roomIDStr completion:^(BOOL enable) {
            if (!enable) {
                return;
            }
            XYLivePushPrepParamCache.didTriggerStartLiveSuccess = YES;
            wself.bizPushStyle = XYLiveRoomBizPushStyleOBS;
            // 协议校验
            [wself checkProtocol:^{
                [wself startLiveButtonPressedAction];
            }];
        }];
    }
    
    id<XYLivePrepMoreNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePrepMoreNodeServiceProtocol)];
    [service saveLiveMode];
}

- (void)handlePCExistingLiveRoomForRoomInfo:(XYLivePreRoomInfo *)roomInfo hasRoom:(BOOL)hasRoom error:(NSError *)err {
    XYExecuteOnMainQueue(^{
        if (!err && hasRoom && (roomInfo.bizStyle == XYLiveRoomBizPushStyleOBS)) {
            [self startLiveRecoveryInNoPushModeWithRoomInfo:roomInfo recoverBizStyle:XYLiveRoomBizPushStyleOBS];
            NSString *content = [NSString stringWithFormat:@"has_recovery_in_partner_mode, room_id: %@", roomInfo.roomIDStr];
            [XYLogCollector xyLiveLogTag:@"obs" content:content];
        } else {
            [XYAlertCenter live_showTextItemWithText:@"请先在直播助手开播"];
        }
    });
}

- (BOOL)pcLive_judgePartnerExistingRoom:(XYLivePreRoomInfo *)roomInfo {
    if (roomInfo.isRecoveryRoom && (roomInfo.bizStyle == XYLiveRoomBizPushStyleOBS)) {
        NSString *content = [NSString stringWithFormat:@"has recovery in partner mode, room_id: %@", roomInfo.roomIDStr];
        [XYLogCollector xyLiveLogTag:@"obs" content:content];
        return YES;
    }
    [XYAlertCenter live_showTextItemWithText:@"请先在直播助手开播"];
    return NO;
}

@end
