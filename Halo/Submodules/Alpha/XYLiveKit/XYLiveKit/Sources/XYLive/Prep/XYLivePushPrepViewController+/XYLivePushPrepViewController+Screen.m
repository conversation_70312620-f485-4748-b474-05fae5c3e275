//
//  XYLivePushPrepViewController+Screen.m
//  XYLiveKit
//
//  Created by sida on 2022/1/12.
//  Copyright © 2022 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLiveEnterConfig.h"
@import KVOController;

@implementation XYLivePushPrepViewController (Screen)

- (void)screen_setup {
    @weakify(self)
    [self.prepVM fetchRoomAuthCompletion:^(XYLiveRoomAuth * _Nonnull roomAuth, NSError * _Nonnull error) {
        @strongify(self)
        if ([XYLiveConfigCenter greenScreenEntrance] && [XYLiveEnterConfig liveBackgropEntrance]) {
            [[XYLiveScreenManager sharedInstance] fetchAllScreenResource];
        }
    }];
    
    [self.KVOController observe:[XYLiveScreenManager sharedInstance] keyPath:NSStringFromSelector(@selector(sourceUpdated)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        @strongify(self)
        XYLiveScreenModel *curSelectedScreen = [XYLiveScreenManager sharedInstance].currentChosenScreenModel;
        if (curSelectedScreen) {
            [self screen_configChromakeyWithColor:curSelectedScreen.colorValue alpha:curSelectedScreen.alphaValue path:curSelectedScreen.filePath];
        }
    }];
}

- (void)screen_setupBlock {
    @weakify(self)
    self.screenSettingVC.dismissBlock = ^ {
        @strongify(self)
        [self seutp_switchClearModel:NO];
        [self capa_showLiveTabMenu:YES];
    };
    self.screenSettingVC.cameraSwitchBlock = ^{
        @strongify(self)
        self.prepVM.notifyVCToFlipCamera = YES;
    };
    self.screenSettingVC.screenSettingBlock = ^(CGFloat colorValue, CGFloat alphaValue, NSString *imagePath){
        @strongify(self)
         [self screen_configChromakeyWithColor:colorValue alpha:alphaValue path:imagePath];
    };
}

- (void)screen_configChromakeyWithColor:(CGFloat)colorValue alpha:(CGFloat)alphaValue path:(NSString *)imagePath {
    XYAVEffectChromakeyModel * model = nil;
    [XYLogCollector xyLiveLogTag:@"[chromakey]-prep-param-bg" content:imagePath];
    if (imagePath.length > 0 && [[NSFileManager defaultManager] fileExistsAtPath:imagePath]) {
        model = [XYAVEffectChromakeyModel new];
        model.similarity = colorValue;
        model.alpha = alphaValue;
        model.chromakeyPath = imagePath;
        [XYLogCollector xyLiveLogTag:@"[chromakey]-prep-param" content:[NSString stringWithFormat:@"alpha=%lf,simi=%lf", alphaValue, colorValue]];
    }
    [XYLiveManagerSharedInstance.coreManager.media applyChromakey:model];
}

@end
