//
//  XYLivePushPrepViewController+ViewHierarchy.m
//  XYLiveKit
//
//  Created by 侯逸仙 on 2021/12/21.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import <XYLiveKit/XYLiveKit-Swift.h>

@implementation XYLivePushPrepViewController (ViewHierarchy)

- (void)registerViewHierarchies {
    #if DEBUG
    NSAssert(self.view && self.prepView && self.panelContainerView, @"[alpha viewManager]基础视图缺失");
    #endif
    
    NSDictionary<NSNumber *, UIView *> * registeredViews = [[NSDictionary alloc] initWithObjectsAndKeys:
                                                            self.view, [NSNumber numberWithInteger:HierarchyRoot],
                                                            self.prepView, [NSNumber numberWithInteger:XYLivePushPrepViewHierarchyPrep],
                                                            self.panelContainerView, [NSNumber numberWithInteger:XYLivePushPrepViewHierarchyPanel],
                                                            nil];
    XYLiveManagerSharedInstance.prepViewHierarchyManager = [[XYLiveBaseViewHierarchyManager alloc] initWithRegisteredViews:registeredViews vc:self isCustom: true];
}

@end
