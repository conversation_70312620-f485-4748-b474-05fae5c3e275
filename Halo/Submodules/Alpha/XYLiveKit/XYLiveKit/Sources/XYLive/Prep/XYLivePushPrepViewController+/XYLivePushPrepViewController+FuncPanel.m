//
//  XYLivePushPrepViewController+FuncPanel.m
//  XYLiveKit
//
//  Created by 周博立 on 2021/2/9.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
@import KVOController;
#import "XYLiveEnterConfig.h"

@implementation XYLivePushPrepViewController (FuncPanel)

- (void)funcPanel_setupKVO {
    __weak typeof(self) wself = self;
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyViewToShowPaidCourseEntrance)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (wself.prepVM.notifyViewToShowPaidCourseEntrance) {
            [wself fetchPaidCourseList];
        } else {
            [wself checkPanelShowScreenBubble];
        }
    }];
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToChangeNoGoodsFilter)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        // 1.处理滤镜互斥逻辑
        BOOL current = [XYLiveFilterConfigManager noGoodsFilterSwitchOn];
        [XYLiveFilterConfigManager updateNoGoodsFilterSwitchOn:!current];
        // 2、关闭商品无滤镜的时候，使用上一次的滤镜
        XYAVEffectFilterModel *filter = [[XYLiveFilterConfigManager fetchLatestUserFilterConfigModel] generateAVFilter];
        [XYLiveManagerSharedInstance.coreManager.media applyFilter:filter];
        // 3.设置商品无滤镜
        [XYLiveManagerSharedInstance.coreManager.media applyNoGoodsFilterSwitchOn:current];
    }];
}

- (void)checkPanelShowScreenBubble {
    __weak typeof(self) wself = self;
    [self.prepVM fetchRoomAuthCompletion:^(XYLiveRoomAuth * _Nonnull roomAuth, NSError * _Nonnull error) {
        if ([XYLiveConfigCenter greenScreenEntrance] && [XYLiveEnterConfig liveBackgropEntrance]) {
            
        }
    }];
}

-(void)fetchPaidCourseList {
    __weak typeof(self) wself = self;
    [self.prepVM fetchPaidCourseListCompletion:^(NSArray<XYLivePaidCourseStartLessonModel *> * _Nonnull list, NSError * _Nonnull error) {
        if (list.count > 0 && wself.paidCourseLessonID.length <= 0) {
            // 有待开课程，应该展示弹窗逻辑
            wself.prepVM.startLessonModel = [list firstObject];
            wself.prepVM.shouldCheckToShowStartLessonAlert = YES;
        } else {
            wself.prepVM.shouldCheckToShowStartLessonAlert = NO;
            [wself checkPanelShowScreenBubble];
        }
    }];
}

@end
