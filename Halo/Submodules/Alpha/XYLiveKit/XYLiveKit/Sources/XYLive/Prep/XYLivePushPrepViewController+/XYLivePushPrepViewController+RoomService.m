//
//  XYLivePushPrepViewController+RoomService.m
//  XYLiveKit
//
//  Created by 周博立 on 2021/4/12.
//  Copyright © 2021 XingIn. All rights reserved.
//

@import XYWebImage;

#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import "XYLivePushPrepPrivateExt+Recovery.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
@import XYConfigCenter;
@import XYFoundation;
@import XYLiveFoundation;
@import XYLiveUIKit;
@import XYMacroConfig;
@import XYStorageCore_Linker;

@implementation XYLivePushPrepViewController (RoomService)

- (XYLivePushPrepBizNaviType)bizNaviType {
    return self.prepVM.bizNaviType;
}

- (BOOL)isVideoLive {
    return self.bizNaviType == XYLivePushPrepBizNaviTypeVideo;
}

//- (BOOL)isVoiceLive {
//    return self.bizNaviType == XYLivePushPrepBizNaviTypeVoice;
//}

- (BOOL)isScreenLive {
    return self.bizNaviType == XYLivePushPrepBizNaviTypeGame;
}

- (BOOL)isChatLive {
    return self.bizNaviType == XYLivePushPrepBizNaviTypeRHVoice;
}

- (void)handlePreRoomInfoConfirmed:(XYLivePreRoomInfo *)roomInfo {
    self.roomInfo = roomInfo;
    [XYLogCollector xyLiveLogTag:@"rtc" content:[NSString stringWithFormat:@"login_res rtc_switch: %@", @(roomInfo.pushType)]];
    
    [self pusher_setupChannelSession];
    
    // 普通直播
    if (self.coverSelected) {
        
    } else if (self.roomInfo.lastCoverURL.length > 0) {
        [XYLogCollector xyLiveLogTag:@"AuditStatus" content:[NSString stringWithFormat:@"self.prepVM.auditStatus = %@", @(self.roomInfo.auditStatus)]];
        self.prepVM.auditStatus = self.roomInfo.auditStatus;
        self.prepVM.lastCoverURL = [NSURL URLWithString:self.roomInfo.lastCoverURL];
        self.prepVM.lastCoverURLStr = self.roomInfo.lastCoverURL;
        self.prepVM.showLowQualityTip = (roomInfo.highQualityCoverFlag == 0);
        self.prepVM.lowQualityBubbleTip = roomInfo.lowQualityTip;
        self.prepVM.coverQualityFlag = roomInfo.highQualityCoverFlag;
        self.prepVM.lowClickRate = roomInfo.lowClickRate;
        self.prepVM.clickRateDesc = roomInfo.clickRateDesc;
    } else {
        //        self.prepVM.notifyViewToShowDefaultImage = YES;
    }
    if (self.roomInfo.roomName.length) {
        [self.prepVM updateRoomTitle:self.roomInfo.roomName];
    }
    
    // 畅聊
    XYLivePushPrepChatModel *chatModel = [XYLivePushPrepChatModel new];
    chatModel.coverUrl = self.roomInfo.lastCoverURL;
    chatModel.roomTitle = self.roomInfo.roomName;
    chatModel.coverQuality = self.prepVM.coverQualityFlag;
    chatModel.relateToGroupChat = [self relateToGroupChat];
    self.prepVM.chatParam = chatModel;
    
    // 面板配置 & 面板权限
    [self fetchHostGoodsAuth];
}

- (void)fetchHostLiveCount {
    @weakify(self)
    [XYLivePushPrepNetworkHandler fetchHostLiveCountInfo:^(NSInteger liveCount, NSError * _Nullable error) {
        @strongify(self)
        if (!error && liveCount == 0) {
            XYLiveManagerSharedInstance.firstTimeBroadcast = YES;
        } else {
            XYLiveManagerSharedInstance.firstTimeBroadcast = NO;
        }
    }];
}

- (void)roomService_handleLoginResponseWithRoomInfo:(XYLivePreRoomInfo *)roomInfo error:(NSError *)error completion:(void (^)(XYLivePreRoomInfo *roomInfo, NSError *error))completion {
    if (roomInfo) {
        self.roomInfo = roomInfo;
    }
    void(^selectRoomComplete)(void) = ^{
        NSString *content = [NSString stringWithFormat:@"login_im: %@, room_id: %@", error ?: @"none", @(roomInfo.roomID)];
        [XYLogCollector xyLiveLogTag:@"push_prep" content:content];
        [self setup_insertPCPrepView];
        self.prepVM.notice = roomInfo.roomNotice;
        [self handlePreRoomInfoConfirmed:roomInfo];
        self.prepVM.cover = roomInfo.lastCoverURL;
        
        if (completion) {
            completion(roomInfo, error);
        }
    };
    selectRoomComplete();
}

- (void)handlePushProcessError:(NSError *)error retryNormalPush:(BOOL)retry {
    [XYLogCollector xyLiveLogTag:@"push_err" content:[NSString stringWithFormat:@"error: %@", error]];
    XYExecuteOnMainQueue((^{
        if (error.code == XYLivePushServerRoomExpired || //room expired code from app server
            error.code == XYLiveTIMGroupExpired) { //room expired code from timsdk
            [[XYAlertCenter live_createTextItemWithText:@"续播超时，请重新开播"] show];
            [self cancelLiveRecovery:self.roomInfo];
            return;
        } else if (error.code == XYLiveTIMAccountNotLogin ||
                   error.code == XYLiveTIMAccountKicked ||
                   error.code == XYLiveTIMGroupInvalid) {
            if (self.hasRecoveringRoom) {
                if (self.recoveryRetryChance > 0) {
                    self.recoveryRetryChance --;
                    [self startLiveRecoveryProcessWithRoomInfo:self.roomInfo];
                }
            } else {
                if (retry) {
                    if (self.normalRetryChance > 0) {
                        self.normalRetryChance --;
                        [XYLogCollector xyLiveLogTag:@"push_prep" content:@"push_proc_retry"];
                        [self startNormalLivePushProcess];
                    }
                }
            }
            return;
        }
        
        self.roomInfo = nil;
        
        self.prepView.hidden = NO;
        [self capa_showLiveTabMenu:YES];
        
        NSInteger errcode = error.code;
        NSString *txt = @"直播初始化失败";
        if (error.localizedDescription.length) {
            txt = [NSString stringWithFormat:@"%@ (%@)", error.localizedDescription, @(errcode)];
        } else {
            txt = [NSString stringWithFormat:@"%@ (%@)", txt, @(errcode)];
        }
        [[XYAlertCenter live_createTextItemWithText:txt] show];
    }));
}

- (BOOL)canApplyRTC {
    // 录屏开播篡改推流类型
    if (XYLiveRoomPushContentTypeScreenCap == self.roomInfo.contentType) {
        self.roomInfo.pushType = self.roomInfo.pushDispatchInfo.gameEncode.pushType;
        return YES;
    }
    
    if (XYMacroDefine.debugORAdhoc) {
        if ([[XYSCKVMigrateTool defaultUserDefaultTool_v2] containsKey:kXYLiveOdysseySwitchPusherType]) {
            NSInteger exceptedPushType = [[XYSCKVMigrateTool defaultUserDefaultTool_v2] intForKey:kXYLiveOdysseySwitchPusherType];
            if (exceptedPushType >= 0 && exceptedPushType <= 3) {
                self.roomInfo.pushType = (XYLivePushType)exceptedPushType;
            }
//            if (XYLiveRoomPushContentTypeScreenCap == self.roomInfo.contentType) {
//                self.roomInfo.pushType = XYLivePushTypeTrtc;
//            }
        }
        if (XYConfigCenter().boolForKey(@"ios_use_rtc_engine_kasa", YES)) {
            return (self.roomInfo.pushType > XYLivePushTypeRtmp);
        } else {
            return (self.roomInfo.pushType > XYLivePushTypeRtmp && self.roomInfo.pushType != XYLivePushTypeKasa);
        }

    }

    BOOL enable = XYConfigCenter().boolForKey(@"ios_live_pre_read_room_info_push_type", YES);
    if (enable) {
        if (XYConfigCenter().boolForKey(@"ios_use_rtc_engine_kasa", YES)) {
            return (self.roomInfo.pushType > XYLivePushTypeRtmp);
        } else {
            return (self.roomInfo.pushType > XYLivePushTypeRtmp && self.roomInfo.pushType != XYLivePushTypeKasa);
        }
    }
    
    return RTCSwitch;
}

- (BOOL)canApplyKasa {
    return self.roomInfo.pushType == XYLivePushTypeKasa && !XYConfigCenter().boolForKey(@"ios_use_rtc_engine_kasa", YES);
}

@end
