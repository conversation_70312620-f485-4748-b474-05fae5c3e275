//
//  XYLivePushPrepViewController+Location.swift
//  XYLiveKit
//
//  Created by   李勇 on 2023/12/21.
//  Copyright © 2023 XingIn. All rights reserved.
//

import UIKit
import XYLocationManager
import XYSessionManager
import XYStorageCore
import XYConfigCenter

extension XYLivePushPrepViewController {
    
    /// 处理定位的弹框
    @objc
    public func disposeLocationAlertView(item: [PrepFuncItem]) {
        
        let locationItems = item.filter { item in
            item.type == .locationVisible
        }
        guard let locationItem = locationItems.first else { return }
        let locationVisibel = locationItem.buttonOn ? 1 : 2
        let roomInfo = XYLiveManager.shared().roomInfo
        if roomInfo != nil {
            roomInfo.locationVisible = locationVisibel
        }
        
        if locationItem.buttonOn == true { // 后台返回的显示地理位置的逻辑
            if self.isHasLocationPermissionAuthorization() { // 用户开启了位置权限 弹二次确认的弹框,只谈一次
                showSecondConfirmlocationHintAlertView()
                disposePoi()
            } else {
                if getLocationStatus() == .notDetermined { // 定位权限没有确定
                    // 展示系统的定位弹框
                    showSystemLocationAlertView()
                } else { // 拒绝了定位权限
                    disposeRejectLocationPerssion()
                    handleLocationConfirmed()
                }
            }
        } else { // 隐藏我的地理位置的逻辑
            if isHasLocationPermissionAuthorization() { // 有定位权限
                disposePoi()
            }
            handleLocationConfirmed()
        }
    }
    
    /// 添加定位权限变换的通知
    private func addLocationAuthStatusChangeNoti() {
        let name = Notification.Name(kChangeLocationAuthStatusNotificationNameKey)
        NotificationCenter.default.addObserver(forName: name, object: nil, queue: nil) { [weak self] _ in
            guard let self = self else { return }
            if self.getLocationStatus() != .notDetermined {
                if self.isHasLocationPermissionAuthorization() { // 用户开启了位置权限 弹框
                    self.showSecondConfirmlocationHintAlertView()
                    self.updateUserPoiLocation()
                } else { // 拒绝了定位权限
                    self.disposeRejectLocationPerssion()
                    self.handleLocationConfirmed()
                }
            }
        }
    }
    
}

// MARK: - 弹框
extension XYLivePushPrepViewController {
    
    var liveId: String {
        if self.roomInfo == nil || self.roomInfo.roomIDStr.count <= 0 {
            return ""
        } else {
            return self.roomInfo.roomIDStr
        }
    }
    
    /// 展示系统的定位弹框
    func showSystemLocationAlertView() {
        if getLocationStatus() != .notDetermined { return }
        // 添加定位权限变换的通知
        addLocationAuthStatusChangeNoti()
        XYLocationManagerImp.sharedInstance().requestLocation(withDesiredAccuracy: .neighborhood, timeout: 0, source: kLocationSourceAlphaXYLivekit) { _ in }
    }
    
    /// 展示地理位置 让更多人看到你的actionSheet
    @objc
    public func showSelectLocationVC() {
        let vc = CommonSelectionListViewController()
        vc.listTitle = NSAttributedString(string: "展示地理位置 让更多人看到你")
        vc.bgAlpha = 0
        vc.itemClickedCallback = { [weak self, weak vc] index in
            self?.displayOrHideActionSheetClick(index: index)
            vc?.dismiss()
        }
        let titles = [PrepLocationStatus.display.description(), PrepLocationStatus.hide.description()]
        let colors = XYLiveManager.shared().roomInfo.locationVisibleStatus ? [XYLiveTokenColor.primary, XYLiveTokenColor.title] : [XYLiveTokenColor.title, XYLiveTokenColor.primary]
        let fonts = [UIFont(name: "PingFangSC-Regular", size: 16) ?? Theme.font16, UIFont(name: "PingFangSC-Regular", size: 16) ?? Theme.font16]
        self.capa_showLiveTabMenu(false)
        vc.showIn(viewController: self, with: titles, color: colors, font: fonts)
        vc.live_dismissCompletion = { [weak self] in
            self?.capa_showLiveTabMenu(true)
        }
        
        XYTrackLivePreparePage.eventActionID46634WitLiveId(liveId)
    }
    
    /// 点击展示我的地理位置 还是隐藏我的地理位置
    /// - Parameter index: 0 代表展示 1代表隐藏
    private func displayOrHideActionSheetClick(index: Int) {
        // 点击了显示位置如果没有开启定位权限就弹框提示跳转到设置页面
        if index == 0 && !isHasLocationPermissionAuthorization() {
            showNoLocationPersionAlertView()
        } else {
            requestUpdateFunc(index: index)
        }
        
        let buttonName = index == 0 ? PrepLocationStatus.display.description() : PrepLocationStatus.hide.description()
        XYTrackLivePreparePage.eventActionID46635WitLiveId(liveId, buttonName: buttonName)
        
    }
    
    /// 开启定位的二次确认弹框 只会弹出一次
    private func showSecondConfirmlocationHintAlertView() {
        let key = "showPreLiveLocationHintAlertView2"
        let isShowPreLiveLocationHintAlert = XYStorageCenter.default().permanent().getBoolForKey(key, defaultValue: false)
        if !isShowPreLiveLocationHintAlert {
            showNoLocationPersionAlertView {
                XYStorageCenter.default().permanent().setBool(true, forKey: key)
            }
        } else {
            handleLocationConfirmed()
        }
    }
    
    /// 用户的二次确认弹框和跳转到设置页面的弹框
    private func showNoLocationPersionAlertView(completion: (() -> Void)? = nil) {
        let enable = LiveConfigCenter.enablePushPrepLocationTextUpdate()
        let title = "“小红书”想获取您的位置权限"
        let message = enable ? "用于在同城、地图中展示你的位置与用户距离，让更多用户看到你的直播" :"授权直播获取你的位置，用于直播在地图上的展示，可在“更多”选择隐藏"
        let confirmButtonName = enable ? "同意" :"确认"
        let cancleButtonName = "拒绝"
        
        let alert = XYAlert.createDefaultRedModal(title: title, plainDesc: message, mainActionTitle: confirmButtonName, secondaryActionTitle: cancleButtonName, completion: { [weak self] confirm in
            if confirm { // 点击确定
                self?.confirmButtonClick(confirmButtonName)
            } else {
                self?.cancleButtonClick(cancleButtonName)
            }
            self?.handleLocationConfirmed()
        })
        
        alert.xyat_registerCompletionTrackerBlock { index, _ in
            if index == 0 {
                return XYTrackLivePreparePage.eventActionID46701WitLocationPermissionButtonName(cancleButtonName)
            } else if index == 1 {
                return XYTrackLivePreparePage.eventActionID46701WitLocationPermissionButtonName(confirmButtonName)
            }
            return nil
        }
        alert.show()
        
        completion?()
        
        XYTrackLivePreparePage.eventActionID46700()
        
    }
    
    private func confirmButtonClick(_ buttonName: String) {
        if !isHasLocationPermissionAuthorization() {
            if getLocationStatus() == .notDetermined {
                showSystemLocationAlertView()
            } else {
                openAppSettings()
            }
        }
        requestUpdateFunc(index: 0)
        
    }
    
    private func cancleButtonClick(_ buttonName: String) {
        requestUpdateFunc(index: 1)
    }
    
    private func handleLocationConfirmed() {
        if PushPrepBeginnerTipsConfigCenter.displayAfterLocationAlert {
            DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 0.5) {
                PushPrepBeginnerTipsConfigCenter.showTipsIfNeeded()
            }
        }
        
        if LiveConfigCenter.enablePushPrepLocationEvent() {
            let eventService = self.serviceManager?.getServiceWith(LiveRoomEventServiceProtocol.self) as? LiveRoomEventServiceProtocol
            eventService?.eventPrepLocationAuthorityConfirmed?()
            
        }
    }
}

extension XYLivePushPrepViewController {
    /// 定位权限变化后更新定位的location
    @objc
    public func updateUserPoiLocation() {
        if getLocationStatus() == .authorizedAlways || getLocationStatus() == .authorizedWhenInUse {
            self.disposePoi()
        } else if getLocationStatus() == .denied || getLocationStatus() == .restricted {
            disposeRejectLocationPerssion()
        } else { // 没有操作定位权限
            showSystemLocationAlertView()
        }
        
    }
    
    /// 点击了拒绝开启定位权限 啥逻辑都不做了,跟安卓一致
    private func disposeRejectLocationPerssion() {
        XYLiveManager.shared().roomInfo.locationVisible = 2
    }
    
    /// 请求poi信息的逻辑
    func disposePoi(longitude: Float? = nil, latitude: Float? = nil) {
        var longitude = longitude
        var latitude = latitude
        if longitude == nil || latitude == nil {
            longitude = XYLocationManagerImp.sharedInstance().cacheLastLocation?.longitude as? Float
            latitude = XYLocationManagerImp.sharedInstance().cacheLastLocation?.latitude as? Float
            if longitude == nil || latitude == nil {
                XYLocationManagerImp.sharedInstance().requestLocation(withDesiredAccuracy: .neighborhood, timeout: 0.0, source: kLocationSourceAlphaXYLivekit) { [weak self] location in
                    let longitude = location.coreLocation.coordinate.longitude
                    let latitude = location.coreLocation.coordinate.latitude
                    guard let self = self, latitude != 0, longitude != 0 else { return }
                    self.requestPoi(longitude: Float(longitude), latitude: Float(latitude))
                }
            } else {
                requestPoi(longitude: longitude, latitude: latitude)
            }
        } else {
            requestPoi(longitude: longitude, latitude: latitude)
        }
    }
    
    /// 调用update接口 index == 0 代表展示我的地理位置
    private func requestUpdateFunc(index: Int) {
        let item = prepVM.currentItem
        guard let item = item else { return }
        let usrId = XYSessionManager.shared().user?.userId
        item.buttonSwitch = index
        LiveFuncConfigNetworkHandler.updateFunc(for: item, hostID: usrId) { success, _ in
            if success {
                let locationVisibel = (index == 0 ? 1 : 2)
                XYLiveManager.shared().roomInfo.locationVisible = locationVisibel
            }
        }
    }
    
    /// 请求poi接口
    func requestPoi(longitude: Float? = nil, latitude: Float? = nil) {
        guard let longitude = longitude else { return }
        guard let latitude = latitude else { return }
        LiveFuncConfigNetworkHandler.getRecentPoiInfo(longitude: longitude, latitude: latitude) { poi_name, poi_context, error in
            if error == nil {
                if let poi_name = poi_name, let poi_context = poi_context {
                    let roomInfo = XYLiveManager.shared().roomInfo
                    if roomInfo != nil {
                        roomInfo.locationPoiName = poi_name
                        roomInfo.locationPoiContext = poi_context
                    }
                }
            }
        }
    }
    
    
}


extension XYLivePushPrepViewController {
    
    private func getLocationStatus() -> CLAuthorizationStatus {
        let status: CLAuthorizationStatus
        let useNewApi = ConfigCenter.shared.bool(forKey: "ios_alpha_prep_fetch_location_use_newapi", defalut: false)
        if #available(iOS 14.0, *), useNewApi {
            let manager = CLLocationManager()
            status = manager.authorizationStatus
        } else {
            status = CLLocationManager.authorizationStatus()
        }
        return status
    }
    
    /// 是否开启了定位权限
    func isHasLocationPermissionAuthorization() -> Bool {
        let status = getLocationStatus()
        if status == .authorizedAlways || status == .authorizedWhenInUse {
            return true
        } else {
            return false
        }
    }
    
    /// 跳转到设置页面
    private func openAppSettings() {
        guard let settingsURL = URL(string: UIApplication.openSettingsURLString) else {
            return
        }
        
        if UIApplication.shared.canOpenURL(settingsURL) {
            UIApplication.shared.open(settingsURL, options: [:], completionHandler: nil)
        }
    }
}
