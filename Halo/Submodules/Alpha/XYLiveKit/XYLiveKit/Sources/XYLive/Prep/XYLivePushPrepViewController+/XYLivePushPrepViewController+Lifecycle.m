//
//  XYLivePrepPlanAViewController.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/10/24.
//

#import "XYLivePushPrepViewController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepPrivateExt+Core.h"
#import "XYLivePushPrepPrivateExt+Recovery.h"
#import "XYLivePushPrepHierarchyManager.h"
#import "XYLivePushPrepPrivateExt.h"
#import <XYMacroConfig/XYMacroDefine.h>
@import XYConfigCenter;

@implementation XYLivePushPrepViewController (Lifecycle)

- (void)lifecycle_viewDidLoad {
    [XYLiveStartPushApmReport recordPrePushMemory];
    XYLiveManagerSharedInstance.prepSource = self.source;
    [XYLiveManagerSharedInstance.consumeService memoryGenerate:XYLiveConsumeMemOptionPreMem];
    self.shouldKeepIMManagerWhenVCDealloc = NO;
    [XYLogCollector xyLiveLogTag:@"rtc" content:[NSString stringWithFormat:@"vdl rtc_switch: %@", @(RTCSwitch)]];
    [XYLivePushPreviewLogger.shared setupWithToken:@"prepvc"];
    XYLivePushPreviewLogger.shared.prefix = @"prepvc";
    [self updateBeautyConfigIfNeeded];// 美颜参数获取
    [self setup_resetParameters];
    BOOL config = XYConfigCenter().boolForKey(@"ios_live_privacy_order_change", NO);
    if (!config) {
        [self camera_initSetup];
    }

    [self sdk_setupListener];
    
    [self setupUI];
    [self setupKVO];
    
    // 视图层级
    XYLivePushPrepHierarchyManager *hierarchyManager = (XYLivePushPrepHierarchyManager *)self.hierarchyManager;
    [hierarchyManager mapHierarchyView:self.view renderView:self.cameraView bussinessView:self.prepView cleanView:self.prepView.interactView];
    [self.nodesLoader loadBizNodes];

    [self.nodesLoader viewDidLoad];
    // 添加视图
    [hierarchyManager setupHierarchyAdapters:self.nodesLoader.nodes];
    [hierarchyManager triggerSetupUI];
    
    [self setupNotificationObservation];
    [self setupFilter];
    [self setupStyleMakeup];
    [self setupBody];
    [self registerViewHierarchies];
    [self screen_setup];
    [self presetupActionLinkRegister];
    __weak typeof(self) wself = self;
    [[XYLiveAlfredInterfaceCenter center] bindEntranceClickWithAction:^{
        [wself alfred_showPanel];
    }];
    // 提前下载本地迁移至云端的资源文件包括 xyLiveAnimation 和 xyLiveLotteryDraw
    [XYLiveFileManager checkAndUpdateAllCloudResource];
    
    xyLiveSharedAPMManager.startApmInfo.enterPrepTs = [NSDate date].timeIntervalSince1970;
    
    [XYLiveClassPropertiesUtil logProperties: self.class];
    if ([XYMacroDefine debugORAdhoc]) {
        [XYLivePushDegradeDebugTool shared];
    }
    
    if ([XYLivePushSentryInfo enableUpdate]) {
        NSDictionary* info = @{@"liveType": @"pre",
                               @"livepushOpenTime": [NSString stringWithFormat:@"%@",@(XYLivePushSentryInfo.livepushOpenTime)],
                               @"source": self.source ?: @""};
        [XYLivePushSentryInfo updateSentryRecordLivePushInfo:@"-1" info:info];
    } else {
        if ([XYLivePushSentryInfo enable]) {
            [XYLivePushSentryInfo sentryRecordLivePushInfo:@"-1" liveType:@"pre"];
        }
    }
}

@end



