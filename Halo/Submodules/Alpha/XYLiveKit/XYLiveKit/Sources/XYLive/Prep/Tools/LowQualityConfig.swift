//
//  MultiLinkConfig.swift
//  XYLiveKit
//
//  Created by 张清(王帅) on 2025/4/18.
//  Copyright © 2025 XingIn. All rights reserved.
//
@objcMembers
@objc(XYLiveLowQualityWrapperConfig)
public class LowQualityWrapperConfig: XYLiveCodableModel {
    public var video: LowQualityLocalConfig?
    public var game: LowQualityLocalConfig?
    
    public override class func modelCustomPropertyMapper() -> [String: Any]? {
        return [
            "video": "video",
            "game": "game"
        ]
    }
}

@objcMembers
@objc(XYLiveLowQualityLocalConfig)
public class LowQualityLocalConfig: XYLiveCodableModel {
    public var availableUpBandwidth: Int64 = 0
    public var networkResult: Int = 0
    public var resolution: Int = 0
    
    public override class func modelCustomPropertyMapper() -> [String: Any]? {
        return [
            "availableUpBandwidth": "availableUpBandwidth",
            "networkResult": "networkResult",
            "resolution": "resolution"
        ]
    }
}

@objcMembers
@objc(XYLiveLowQualityConfig)
public class LowQualityConfig: NSObject {
    public static var videoConfig: LowQualityLocalConfig?
    public static var gameConfig: LowQualityLocalConfig?
    
    static func remoteConfig() -> [String: Any] {
        ConfigCenter.shared.dictionary(forKey: "ios_live_prep_low_quality_config")
    }
    
    public static func getVideoConfig() -> LowQualityLocalConfig {
        guard let config = videoConfig else {
            let configDictionary = remoteConfig()
            if let configModel = LowQualityWrapperConfig.xy_model(with: configDictionary),
               let video = configModel.video {
                videoConfig = video
                return video
            } else {
                let defaultConfig = LowQualityLocalConfig()
                defaultConfig.availableUpBandwidth = 4000000
                defaultConfig.networkResult = 4
                defaultConfig.resolution = 1
                return defaultConfig
            }
        }
        return config
    }
    
    public static func getGameConfig() -> LowQualityLocalConfig {
        guard let config = gameConfig else {
            let configDictionary = remoteConfig()
            if let configModel = LowQualityWrapperConfig.xy_model(with: configDictionary),
               let game = configModel.game {
                gameConfig = game
                return game
            } else {
                let defaultConfig = LowQualityLocalConfig()
                defaultConfig.availableUpBandwidth = 4000000
                defaultConfig.networkResult = 4
                defaultConfig.resolution = 1
                return defaultConfig
            }
        }
        return config
    }
}
