//
//  XYLivePushPrepViewController+Protocol.m
//  XYLiveKit
//
//  Created by 大远 on 2025/4/8.
//  Copyright © 2025 XingIn. All rights reserved.
//  主播协议弹窗

#import "XYLivePushPrepViewController.h"
#import <XYLiveKit/XYLiveNavigationChildController.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <TTTAttributedLabel/TTTAttributedLabel.h>
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLiveKit/XYLivePushPrepPrivateExt+Core.h>
#import <XYStorageCore/XYStorageCenter.h>
#import <XYLiveKit/XYTrackLivePreparePage.h>

// 用户uid维度
#define kXYLivePushPrepProtocolKey [NSString stringWithFormat:@"prep-protocol-%@", XYSessionManager.sharedManager.user.userId]

// 协议面板
@interface XYLivePushPrepProtocolPanelVC : XYLiveBaseChildViewController<XYLiveNavigationChildControllerProtocol, TTTAttributedLabelDelegate>

@property (nonatomic, weak)   XYLivePushPrepViewModel *viewModel;
// 点击同意
@property (nonatomic, copy)   dispatch_block_t didTapAgreeHandler;
// 关闭按钮
@property (nonatomic, strong) UIButton *closeBtn;
// 关闭按钮
@property (nonatomic, strong) TTTAttributedLabel *detailLbl;
// 同意按钮
@property (nonatomic, strong) UIButton *agreeBtn;

@end

@implementation XYLivePushPrepProtocolPanelVC

- (instancetype)initWithViewModel:(XYLivePushPrepViewModel *)viewModel {
    if (self = [super init]) {
        _viewModel = viewModel;
    }
    return self;
}

- (void)setupSubViews {
    [super setupSubViews];
    // 创建关闭按钮
    [self setupCloseBtn];
    // 创建内容标题
    [self setupDetailLbl];
    // 创建同意按钮
    [self setupAgreenBtn];
    
    // 布局
    [self.detailLbl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(36);
        make.right.equalTo(self.contentView).offset(-36);
        make.top.equalTo(self.contentView).offset(8);
    }];
    
    [self.agreeBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(16);
        make.right.equalTo(self.contentView).offset(-16);
        make.height.mas_equalTo(40);
        make.bottom.equalTo(self.contentView).offset(-8);
    }];
    WS
    [self.agreeBtn xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
        SS
        return [XYTrackLivePreparePage eventActionId78921];
    }];
}

#pragma mark - 创建子视图

- (void)setupCloseBtn {
    UIButton *closeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeBtn setImage:Theme.icon.close_b.color(XYLiveTokenColor.title).size(20).image forState:UIControlStateNormal];
    [closeBtn addTarget:self action:@selector(didTapClose:) forControlEvents:UIControlEventTouchUpInside];
    self.closeBtn = closeBtn;
}

- (void)setupDetailLbl {
    TTTAttributedLabel *detailLbl = [[TTTAttributedLabel alloc] initWithFrame:CGRectZero];
    detailLbl.delegate = self;
    detailLbl.numberOfLines = 0;
    detailLbl.textColor = XYLiveTokenColor.paragraph;
    detailLbl.font = [UIFont fontWithName:@"PingFangSC-Regular" size:14];
    detailLbl.textAlignment = NSTextAlignmentLeft;
    detailLbl.linkAttributes = @{
        NSForegroundColorAttributeName: XYLiveTokenColor.blue,
        NSFontAttributeName: [UIFont fontWithName:@"PingFangSC-Regular" size:14],
    };
    detailLbl.activeLinkAttributes = @{
        NSForegroundColorAttributeName: XYLiveTokenColor.blue,
        NSFontAttributeName: [UIFont fontWithName:@"PingFangSC-Regular" size:14]
    };
    detailLbl.extendsLinkTouchArea = YES;
    detailLbl.lineSpacing = 6;
    detailLbl.text = @"根据平台相关要求，在您开播前，请先阅读并同意《小红书直播服务协议》《小红书直播规范》";
    NSRange leftRange = [detailLbl.text rangeOfString:@"《小红书直播服务协议》"];
    if (leftRange.location != NSNotFound) {
        [detailLbl addLinkToURL:[NSURL URLWithString:@"https://oa.xiaohongshu.com/h5/terms/ZXXY20220401002/-1"] withRange:leftRange];
    }
    NSRange rightRange = [detailLbl.text rangeOfString:@"《小红书直播规范》"];
    if (rightRange.location != NSNotFound) {
        [detailLbl addLinkToURL:[NSURL URLWithString:@"https://www.xiaohongshu.com/hina/basic-criterion"] withRange:rightRange];
    }
    
    [self.contentView addSubview:detailLbl];
    self.detailLbl = detailLbl;
}

- (void)setupAgreenBtn {
    UIButton *agreeBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [agreeBtn setBackgroundColor:XYLiveTokenColor.primary];
    [agreeBtn setTitleColor:XYLiveTokenColor.white forState:UIControlStateNormal];
    agreeBtn.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:16];
    [agreeBtn setTitle:@"同意协议并开播" forState:UIControlStateNormal];
    agreeBtn.layer.cornerRadius = 20;
    agreeBtn.layer.masksToBounds = YES;
    [agreeBtn addTarget:self action:@selector(didTapAgree:) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:agreeBtn];
    self.agreeBtn = agreeBtn;
}


#pragma mark - XYLiveNavigationChildControllerProtocol

- (NSString *)XYLiveNav_title {
    return @"直播协议";
}

- (NSArray<UIButton *> *)XYLiveNav_rightButtons {
    return @[self.closeBtn];
}

#pragma mark - Event

// 点击关闭
- (void)didTapClose:(UIButton *)sender {
    // 关闭面板
    [self.navigationChildController dismissWithAnimated:YES complete:nil];
}

// 点击同意
- (void)didTapAgree:(UIButton *)sender {
    // 数据持久化
    [[XYStorageCenter defaultCenter].permanent setBool:YES forKey:kXYLivePushPrepProtocolKey];
    // 执行回调
    self.didTapAgreeHandler ? self.didTapAgreeHandler() : nil;
}

#pragma mark - TTTAttributedLabelDelegate

- (void)attributedLabel:(TTTAttributedLabel *)label didSelectLinkWithURL:(NSURL *)url {
    if (!url.absoluteString.length) { return; }
    self.viewModel.notifyVCToShowHalfWebviewOnTopWithURL = url.absoluteString;
}

@end


@implementation XYLivePushPrepViewController (Protocol)

- (void)checkProtocol:(void (^)(void))completion {
    BOOL disableShowPanel = [[XYStorageCenter defaultCenter].permanent getBoolForKey:kXYLivePushPrepProtocolKey defaultValue:NO];
    BOOL enablePrepPushUpdateProtocol = [XYAlphaSwitch enablePrepPushUpdateProtocol];
    [XYLogCollector xyLiveLogTag:@"push_prep" content:[NSString stringWithFormat:@"check protocol,{disableShowPanel:%d, enablePrepPushUpdateProtocol:%d}", disableShowPanel, enablePrepPushUpdateProtocol]];
    if (!disableShowPanel && enablePrepPushUpdateProtocol) {
        // 隐藏工具条
        [self capa_showLiveTabMenu:NO];
        // 展示web协议面板
        [self showWebProtocolPanelVCWithCompletion:completion];
        // 埋点上报
        [XYTrackLivePreparePage eventActionId78920];
    } else {
        completion ? completion() : nil;
    }
}

// 展示web协议面板
- (void)showWebProtocolPanelVCWithCompletion:(void(^)(void))completion {
    XYLivePushPrepProtocolPanelVC *protocolPanelVC = [[XYLivePushPrepProtocolPanelVC alloc] initWithViewModel:self.prepVM];
    protocolPanelVC.didTapAgreeHandler = completion;
    XYLiveNavigationChildController *nav = [[XYLiveNavigationChildController alloc] initWithRootChildController:protocolPanelVC];
    nav.portraitHeight = ^CGFloat{ return 188; };
    WS
    [self showPopBottomVC:nav showComplete:nil dismissComplete:^(XYLiveBaseChildViewController * _Nullable vc) {
        SS
        [self capa_showLiveTabMenu:YES];
    }];
    nav.didClickViewBlock = nil;
}

@end





