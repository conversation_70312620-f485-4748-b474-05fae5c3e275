//
//  XYLivePushPrepView+Setup.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/7/7.
//  Copyright © 2023 XingIn. All rights reserved.
//

@import XYDevice;

#import "XYLivePushPrepView.h"
#import "XYLivePushPrepPrivateExt.h"
#import <XYLiveKit/XYLiveKit-Swift.h>

@implementation XYLivePushPrepView (Setup)

- (void)setupUI {
    if (!self.fullScreen) {
        [self.layer addSublayer:self.bgLayer];
        [self.bgLayer addSublayer:self.gradienLayer];
    }
    [self addSubview:self.interactView];
}

- (void)setupSubviewLayout {
    if (!self.fullScreen) {
        if (self.isHidden) { return; }
        CGFloat selfwidth = CGRectGetWidth(self.frame);
        
        self.bgLayer.frame = self.bounds;
        self.gradienLayer.frame = CGRectMake(0, 0, selfwidth, 160);
    }
    
    self.interactView.frame = self.bounds;
    
    if (self.onLayoutBlock) {
        self.onLayoutBlock(self);
    }
}

@end

