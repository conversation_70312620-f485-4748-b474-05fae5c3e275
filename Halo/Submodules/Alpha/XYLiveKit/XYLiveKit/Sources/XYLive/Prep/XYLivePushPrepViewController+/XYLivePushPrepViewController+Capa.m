//
//  XYLivePushPrepViewController+Capa.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/1/18.
//  Copyright © 2023 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepPrivateExt+Core.h"

@import XYConfigCenter;
@import XYPrivacy;

@implementation XYLivePushPrepViewController (Capa)

- (void)capa_showLiveTabMenu:(BOOL)show {
    if (self.showScollMenu && self.shown) {
        self.showScollMenu(show);
    }
}

- (void)resignLive {
    if (self.resignLiveBlock) {
        self.resignLiveBlock();
    }
}


- (void)capa_handlingOnViewWillDisappear {
    if ([XYLiveConfigCenter useStopCameraStrategy] && !self.isStarting) {
        BOOL config = XYConfigCenter().boolForKey(@"ios_live_privacy_order_change", NO);
        if (config) {
            __weak typeof(self) wself = self;
            XYPRCameraPrivacyManager *pm = XYPRCameraPrivacyManager.new;
            XYPRPrivacyStatus stats = pm.authorizeStatus;
            if (stats == XYPRPrivacyStatusAuthorized) {
                [wself camera_stopPreview];
            }
        } else {
            [self camera_stopPreview];
        }
    }
}

@end

