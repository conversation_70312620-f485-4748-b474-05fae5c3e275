//
//  XYLivePushPrepViewModel+IMHandler.m
//  XYLiveKit
//
//  Created by sida on 2021/4/9.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewModel.h"
#import <XYLiveKit/XYLiveKit-Swift.h>

@implementation XYLivePushPrepViewModel (IMHandler)

- (void)setupListeners {
    [self.msgParser setupImListener];
    self.msgParser.delegate = self;
}

- (void)registHostIm {
    
}

@end
