//
//  XYLivePushPrepViewController+ContainerDelegate.m
//  XYLiveKit
//
//  Created by 王帅 on 2021/7/26.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"

@import XYLiveFoundation;
@import XYLiveUIKit;

@implementation XYLivePushPrepViewController (ContainerDelegate)

- (void)didStartPushInPCMode:(BOOL)pcAssistant obsConfig:(XYLiveConfigInfo *)config {
    if (self.bizPushStyle == XYLiveRoomBizPushStyleVideoLesson) {
        [XYAlertCenter live_showTextItemWithText:@"录播课不支持电脑直播"];
        return;
    }
    
    if (!pcAssistant) {
        self.roomInfo.configInfo = config;// 仅obs开播需要使用obs接口的config信息
    }
    
    [self startPCLiveActionForPartnerMode:pcAssistant];
    
    [XYLogCollector xyLiveLogTag:@"obs" content:pcAssistant ? @"pcAssistant" : @"obs"];
}

- (void)funcPanelDidTapWithItem:(XYLivePrepFuncItem *)item {
    [self panel_handldFuncItem:item];
}

@end
