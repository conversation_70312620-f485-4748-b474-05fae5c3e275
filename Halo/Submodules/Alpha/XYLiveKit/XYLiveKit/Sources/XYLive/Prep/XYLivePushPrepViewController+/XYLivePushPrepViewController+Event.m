//
//  XYLivePushPrepViewController+Event.m
//  XYLiveKit
//
//  Created by 刘晨阳 on 2022/8/19.
//  Copyright © 2022 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import <XYLiveKit/XYLiveKit-Swift.h>

@import XYConfigCenter;

@implementation XYLivePushPrepViewController (Event)

- (void)fireRecoverNetPackage: (XYLivePreRoomInfo *)roomInfo completion:(void(^)(XYLivePreRoomInfo *roomInfo))completion {
    XYLiveEventPackage *package = nil;
    if (XYConfigCenter().justOnceBoolForKey(@"ios_live_prep_roomid_use_package_cache", NO)) {
        package = [self.eventManager packageByKey:@(XYLivePushPrePackageTypeRecover)];
        if (package) {
            [package reset];
            
            XYLivePreEndPointEvent *endPoint = (XYLivePreEndPointEvent *)[package.dispatcher getEventListenerWithEventType:@(XYLivePushPreChannelEventTypeEndPoint)];
            
            __weak typeof(self) wself = self;
            endPoint.onReadyBlock = ^(XYLivePreRoomInfo * _Nonnull roomInfo) {
                if (completion) {
                    completion(roomInfo);
                }
                [wself generateCameraMem];
            };
            [package start];
            return;
        }
    }
        
    package = [[XYLiveEventPackage alloc] initWithInfo:roomInfo];
    
    XYLiveEventDisptacher *dispatcher = package.dispatcher;
    // 预直播功能
    XYLivePreFeatureEvent *featureEvent = [XYLivePreFeatureEvent new];
    [dispatcher registerEventListener:featureEvent requireParentTypes:nil];
    
    //线索广告信息
    XYLivePreAdsLeadsEvent *adsLeadsEvent = [XYLivePreAdsLeadsEvent new];
    [dispatcher registerEventListener:adsLeadsEvent requireParentTypes:nil];
    
    // 主播信息
    XYLivePreHostInfoEvent *hostInfoEvent = [XYLivePreHostInfoEvent new];
    [dispatcher registerEventListener:hostInfoEvent requireParentTypes:nil];
    
    // 直播间开关
    XYLivePreSwitchEvent *switchEvent = [XYLivePreSwitchEvent new];
    [dispatcher registerEventListener:switchEvent requireParentTypes:nil];
    
    // 基础信息
    XYLivePreBaseInfoEvent *baseInfoEvent = [XYLivePreBaseInfoEvent new];
    [dispatcher registerEventListener:baseInfoEvent requireParentTypes:nil];
    
    // 直播课获取
    XYLivePreCourseInfoEvent *courseEvent = [XYLivePreCourseInfoEvent new];
    [dispatcher registerEventListener:courseEvent requireParentTypes:nil];
    
    // 连线信息
    XYLivePreLinkMicInfoEvent *linkMicEvent = [XYLivePreLinkMicInfoEvent new];
    [dispatcher registerEventListener:linkMicEvent requireParentTypes:nil];
    
    // 结束连线
    XYLivePreLinkMicStopEvent *linkMicStopEvent = [XYLivePreLinkMicStopEvent new];
    [dispatcher registerEventListener:linkMicStopEvent requireParentTypes:@[@(XYLivePushPreChannelEventTypeLinkMic)]];
    
    // 聊天信息
    XYLivePreChatInfoEvent *chatEvent = [XYLivePreChatInfoEvent new];
    [dispatcher registerEventListener:chatEvent requireParentTypes:nil];
    
    // 统计数据
    XYLivePreStatisticEvent *statisticsEvent = [XYLivePreStatisticEvent new];
    [dispatcher registerEventListener:statisticsEvent requireParentTypes:nil];
    
    // roomAuth
    XYLivePreAuthEvent *authEvent = [XYLivePreAuthEvent new];
    [dispatcher registerEventListener:authEvent requireParentTypes:nil];
    
    // 粉丝团信息
    XYLivePreFansGroupInfoEvent *fansGroupInfoEvent = [XYLivePreFansGroupInfoEvent new];
    [dispatcher registerEventListener:fansGroupInfoEvent requireParentTypes:nil];
    
    XYLivePreEndPointEvent *endPoint = [XYLivePreEndPointEvent new];
    
    __weak typeof(self) wself = self;
    endPoint.onReadyBlock = ^(XYLivePreRoomInfo * _Nonnull roomInfo) {
        if (completion) {
            completion(roomInfo);
        }
        [wself generateCameraMem];
    };
    NSArray <NSNumber *> *requiredTypes = @[
        @(XYLivePushPreChannelEventTypeFeature),
        @(XYLivePushPreChannelEventTypeHostInfo),
        @(XYLivePushPreChannelEventTypeSwitchInfo),
        @(XYLivePushPreChannelEventTypeBaseInfo),
        @(XYLivePushPreChannelEventTypeLinkMicStop),
        @(XYLivePushPreChannelEventTypeCourseInfo),
        @(XYLivePushPreChannelEventTypeStatistic),
        @(XYLivePushPreChannelEventTypeChatInfo),
        @(XYLivePushPreChannelEventTypeRoomAuth),
        @(XYLivePushPreChannelEventTypeAdsLeads),
    ];
    [dispatcher registerEventListener:endPoint
                   requireParentTypes:requiredTypes];
    [self.eventManager registerPackageWithPackage:package
                                          withKey:@(XYLivePushPrePackageTypeRecover)];
    
    [package start];
}

- (void)generateCameraMem {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, 2.0 * NSEC_PER_SEC),dispatch_get_main_queue(),^{
        [XYLiveManagerSharedInstance.consumeService memoryGenerate:XYLiveConsumeMemOptionCameraDiff];
    });
}

- (void)fireNonRecoverNetPackage:(XYLivePreRoomInfo *)roomInfo completion:(void(^)(XYLivePreRoomInfo *roomInfo))completion {
    XYLiveEventPackage *package = nil;
    if (XYConfigCenter().justOnceBoolForKey(@"ios_live_prep_roomid_use_package_cache", NO)) {
        [self.eventManager packageByKey:@(XYLivePushPrePackageTypeNonRecover)];
        if (package) {
            [package reset];
            
            XYLivePreEndPointEvent *endPoint = (XYLivePreEndPointEvent *)[package.dispatcher getEventListenerWithEventType:@(XYLivePushPreChannelEventTypeEndPoint)];
            
            __weak typeof(self) wself = self;
            endPoint.onReadyBlock = ^(XYLivePreRoomInfo * _Nonnull roomInfo) {
                if (completion) {
                    completion(roomInfo);
                }
                [wself generateCameraMem];
            };
            [package start];
            return;
        }
    }
    
    package = [[XYLiveEventPackage alloc] initWithInfo:roomInfo];
    
    XYLiveEventDisptacher *dispatcher = package.dispatcher;
    
    // 预直播功能
    XYLivePreFeatureEvent *featureEvent = [XYLivePreFeatureEvent new];
    [dispatcher registerEventListener:featureEvent requireParentTypes:nil];
    
    //线索广告信息
    XYLivePreAdsLeadsEvent *adsLeadsEvent = [XYLivePreAdsLeadsEvent new];
    [dispatcher registerEventListener:adsLeadsEvent requireParentTypes:nil];
    
    // 主播信息
    XYLivePreHostInfoEvent *hostInfoEvent = [XYLivePreHostInfoEvent new];
    [dispatcher registerEventListener:hostInfoEvent requireParentTypes:nil];
    
    // 直播间开关
    XYLivePreSwitchEvent *switchEvent = [XYLivePreSwitchEvent new];
    [dispatcher registerEventListener:switchEvent requireParentTypes:nil];
    
    // 直播间上一场信息
    XYLivePreDefaultInfoEvent *defaultInfoEvent = [XYLivePreDefaultInfoEvent new];
    [dispatcher registerEventListener:defaultInfoEvent requireParentTypes:nil];
    
    // roomAuth
    XYLivePreAuthEvent *authEvent = [XYLivePreAuthEvent new];
    [dispatcher registerEventListener:authEvent requireParentTypes:nil];
    
    // 粉丝团信息
    XYLivePreFansGroupInfoEvent *fansGroupInfoEvent = [XYLivePreFansGroupInfoEvent new];
    [dispatcher registerEventListener:fansGroupInfoEvent requireParentTypes:nil];
    
    XYLivePreEndPointEvent *endPoint = [XYLivePreEndPointEvent new];
    __weak typeof(self) wself = self;
    endPoint.onReadyBlock = ^(XYLivePreRoomInfo * _Nonnull roomInfo) {
        if (completion) {
            completion(roomInfo);
        }
        [wself generateCameraMem];
    };
    NSArray <NSNumber *> *requiredTypes = @[
        @(XYLivePushPreChannelEventTypeFeature),
        @(XYLivePushPreChannelEventTypeHostInfo),
        @(XYLivePushPreChannelEventTypeSwitchInfo),
        @(XYLivePushPreChannelEventTypeDefaultInfo),
        @(XYLivePushPreChannelEventTypeRoomAuth),
        @(XYLivePushPreChannelEventTypeAdsLeads),
    ];
    [dispatcher registerEventListener:endPoint
                   requireParentTypes:requiredTypes];
    
    [self.eventManager registerPackageWithPackage:package
                                          withKey:@(XYLivePushPrePackageTypeNonRecover)];
    [package start];
}

@end
