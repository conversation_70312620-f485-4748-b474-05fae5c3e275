//
//  XYLivePushPrepViewModel+Guide.swift
//  XYLiveKit
//
//  Created by gongyidemac on 2022/11/28.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation

extension XYLivePushPrepViewModel {
    @objc
    public func fetchPrepGuideInfo(completion comp: ((_ guide: CommonGuideModel?) -> Void)?) {
        CommonGuideCenter.fetchPrepGuide { list in
            var info: CommonGuideModel?
            if !list.isEmpty {
                info = list.first
            }
            comp?(info)
        }
    }
    
    @objc
    public func handleGuideShown(info i: CommonGuideModel) {
        CommonGuideCenter.handlePrepGuideDisplayed(model: i)
    }
}
