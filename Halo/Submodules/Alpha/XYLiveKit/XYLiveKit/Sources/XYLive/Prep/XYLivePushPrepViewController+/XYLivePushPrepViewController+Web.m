//
//  XYLivePushPrepViewController+Web.m
//  XYLiveKit
//
//  Created by sida on 2022/11/17.
//  Copyright © 2022 XingIn. All rights reserved.
//

@import XYSessionManager;

#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import <XYLiveUIKit/XYLiveSilentWebViewController.h>
@import XYConfigCenter;

static void *XYLivePushPrepViewControllerSilentWebVCKey = "XYLivePushPrepViewControllerSilentWebVCKey";

@interface XYLivePushPrepViewController() <XYLiveSilentWebViewControllerDelegate>

@property (nonatomic, nullable, strong) XYLiveSilentWebViewController *silentWebVC;

@end

@implementation XYLivePushPrepViewController (Web)

- (void)showSilentWebVCWithAddParamUrl:(NSString *)url {
    [self showSilentWebVCWithUrl:[self processWithBaseParametersForURLStr:url]];
}

- (void)showSilentWebVCWithUrl:(NSString *)url {
    [self destroySilentWebVC];
    if ([RBPreRequestManager.shared getIsPreRrequestEnable] && XYConfigCenter().boolForKey(@"ios_live_silent_web_prefetch", YES)) {
        [RBPreRequestManager.shared preRequestWithLink:url];
    }
    self.silentWebVC = [XYLiveSilentWebViewController new];
    self.silentWebVC.delegate = self;
    self.silentWebVC.linkURL = url;
    [self.silentWebVC presentWithFullScreenStyle];
}

- (void)showSilentWebVCOnSelfWithUrl:(NSString *)url {
    [self destroySilentWebVC];
    if ([RBPreRequestManager.shared getIsPreRrequestEnable] && XYConfigCenter().boolForKey(@"ios_live_silent_web_prefetch", YES)) {
        [RBPreRequestManager.shared preRequestWithLink:url];
    }
    self.silentWebVC = [XYLiveSilentWebViewController new];
    self.silentWebVC.delegate = self;
    self.silentWebVC.linkURL = url;
    [self.silentWebVC showInSuperView:self.view];
}

- (void)showSilentWebVCOnSelfWithAddParamUrl:(NSString *)url {
    [self showSilentWebVCOnSelfWithUrl:[self processWithBaseParametersForURLStr:url]];
}

- (void)destroySilentWebVC {
    self.silentWebVC.delegate = nil;
    [self.silentWebVC.view removeFromSuperview];
    self.silentWebVC = nil;
}

#pragma mark - XYLiveSilentWebViewControllerDelegate

- (void)liveSilentWebViewControllerDidFinishContentLoading:(XYLiveSilentWebViewController *)webViewController {
}

- (void)liveSilentWebViewControllerDidExit:(XYLiveSilentWebViewController *)webViewController {
    [self destroySilentWebVC];
}

#pragma mark - Getter & Setter

- (XYLiveSilentWebViewController *)silentWebVC {
    return objc_getAssociatedObject(self, &XYLivePushPrepViewControllerSilentWebVCKey);
}

- (void)setSilentWebVC:(XYLiveSilentWebViewController *)silentWebVC {
    objc_setAssociatedObject(self, &XYLivePushPrepViewControllerSilentWebVCKey, silentWebVC, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (NSString *)processWithBaseParametersForURLStr:(NSString *)URLStr {
    NSURL *linkURL = [NSURL URLWithString:URLStr];
    NSDictionary *queryDic = [NSURL URLQueryParameters:linkURL];
    if (!queryDic[@"room_id"]) {
        linkURL = [linkURL URLByAppendingQueryParameters:@{@"room_id" : XYLiveManagerSharedInstance.roomInfo.roomIDStr ?: @""}];
    }
    
    // 预直播页room_role 与 host_id 进行 hard code
    if (!queryDic[@"room_role"]) {
        linkURL = [linkURL URLByAppendingQueryParameters:@{@"room_role" : @"5"}];
    }
    if (!queryDic[@"host_id"]) {
        linkURL = [linkURL URLByAppendingQueryParameters:@{@"host_id" : CURRENT_USER.userId ?: @""}];
    }
    URLStr = linkURL.absoluteString;
    return URLStr ?: @"";
}

@end
