//
//  PushPrepDataSource.swift
//  XYLiveKit
//
//  Created by 周博立 on 2022/6/1.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation
import XYLiveFoundation
import XYSessionManager
import XYLivePusher

@objcMembers
@objc(XYLivePushPrepDataSource)
public class PushPrepDataSource: NSObject {
    public static var baseLoginParam: XYLiveLoginParam {
        let session: XYSessionManager = XYSessionManager.shared()
        let nickname: String = session.user?.name ?? ""
        let uid: String = session.user?.userId ?? ""
        let avatar: String = session.user?.avatar ?? ""
        let desc: String = session.user?.desc ?? ""
        let fans_count: Int = session.user?.fansCount ?? 0
        let note_count: Int = session.user?.notesCount ?? 0
        let red_official_verify_type: Int = session.user?.redOfficialVerifyType ?? 0
        let collect_count: Int = session.user?.collectedCount ?? 0
        let like_num: Int = session.user?.likedCount ?? 0
        
        let userInfo: XYLiveUserInfo = XYLiveUserInfo()
        userInfo.userID = uid
        userInfo.avatar = avatar
        userInfo.nickname = nickname
        userInfo.totalFansNum = fans_count
        userInfo.totalNotesNum = note_count
        userInfo.desc = desc
        userInfo.totalCollectsNum = collect_count
        userInfo.totalLikesNum = like_num
        userInfo.redOfficialVerifyType = red_official_verify_type
        userInfo.roomRole = .host
        
        let appID: String = XYLiveEnterConfig.currentImSdkAppId()
        let trtcAppID: String = RTCPusher.currentTrtcSdkAppId()
        let loginParam: XYLiveLoginParam = XYLiveLoginParam(appID: appID, userInfo: userInfo, role: .host)
        loginParam.trtcAppID = trtcAppID
        
        return loginParam
    }
    
    public static func loginParam(withRoomInfo roomInfo: XYLiveRoomInfo?, cover: String?, title: String?, notice: String?, keepSecret: Bool) -> XYLiveLoginParam {
        let param = PushPrepDataSource.baseLoginParam
        param.cover = cover ?? ""
        param.title = title ?? ""
        param.notice = notice ?? ""
        param.keepSecret = keepSecret
        if let info = roomInfo {
            param.roomInfo = info
        }
        return param
    }
}
