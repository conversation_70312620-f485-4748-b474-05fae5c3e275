//
//  XYLivePushPrepViewModel+Track.swift
//  XYLiveKit
//
//  Created by wenguoning on 2023/7/17.
//  Copyright © 2023 XingIn. All rights reserved.
//

import Foundation

extension XYLivePushPrepViewModel {
    @objc
    public func channelTabID() -> String {
        var channelTabID = ""
        switch (self.bizNaviType.rawValue) {
            case 0:
                channelTabID = "视频"
            case 1:
                channelTabID = "语音"
            case 2:
                channelTabID = "手游"
            case 4:
                channelTabID = "电脑"
            case 5:
                channelTabID = "畅聊"
            default:
                break
        }
        return channelTabID
    }
}
