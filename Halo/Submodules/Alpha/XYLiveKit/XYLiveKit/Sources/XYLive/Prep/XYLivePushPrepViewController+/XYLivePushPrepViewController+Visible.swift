//
//  XYLivePushPrepViewController+Visible.swift
//  XYLiveKit
//
//  Created by hulong on 2023/2/27.
//  Copyright © 2023 XingIn. All rights reserved.
//

import Foundation
import XYLiveUIKit
import XYUITheme

extension XYLivePushPrepViewController {
    @objc
    public func showSelectVisibleVC() {
        if relateToGroupChat() {
            showSelectVisibleVCForChatGroup()
            return
        }
        let vc = CommonSelectionListViewController()
        vc.listTitle = NSAttributedString(string: "谁可以看")
        vc.bgAlpha = 0
        vc.itemClickedCallback = { [weak self, weak vc] index in
            XYLiveEnterConfig.updateRoomVisibleStatus(index == 1)
            self?.changeNeedBroadcast(index == 0)
            vc?.dismiss()
        }
        let titles = [PrepVisibleStatus.all.description(), PrepVisibleStatus.shared.description()]
        let colors = XYLiveEnterConfig.roomVisibleStatus() == false ? [XYLiveTokenColor.primary, XYLiveTokenColor.title] : [XYLiveTokenColor.title, XYLiveTokenColor.primary]
        let fonts = [UIFont(name: "PingFangSC-Regular", size: 16) ?? Theme.font16, UIFont(name: "PingFangSC-Regular", size: 16) ?? Theme.font16]
        self.capa_showLiveTabMenu(false)
        vc.showIn(viewController: self, with: titles, color: colors, font: fonts)
        vc.live_dismissCompletion = { [weak self] in
            self?.capa_showLiveTabMenu(true)
        }
        vc.itemClickedAutoTrackContext = { index in
            if index == 1 {
                return XYAnalyticsOrganizer._
                    .channelTab.channelTabName("cancel_share_to_fans")
                    .page.pageInstance(.livePreparePage)
                    .event.targetType(.channelTabTarget).action(.gotoChannelTab).pointId(2961).isGoto(0).moduleId(1399)
            } else {
                return XYAnalyticsOrganizer._
                    .channelTab.channelTabName("share_to_fans")
                    .page.pageInstance(.livePreparePage)
                    .event.targetType(.channelTabTarget).action(.gotoChannelTab).pointId(2961).isGoto(0).moduleId(1399)
            }
        }
    }
    
    private func showSelectVisibleVCForChatGroup() {
        let vc = CommonSelectionListViewController()
        vc.listTitle = NSAttributedString(string: "谁可以看")
        vc.bgAlpha = 0
        vc.itemClickedCallback = { [weak self, weak vc] index in
            XYLiveEnterConfig.updateRoomVisibleStatus(index == 1)
            self?.changeNeedBroadcast(index == 0)
            self?.changeJoinLimit(isOnlyChatGroup: index == 1)
            vc?.dismiss()
        }
        var titles = [PrepVisibleStatus.all.description(), PrepVisibleStatus.onlyChatGroup.description()]
        let colors = XYLiveEnterConfig.roomVisibleStatus() == false ? [XYLiveTokenColor.primary, XYLiveTokenColor.title] : [XYLiveTokenColor.title, XYLiveTokenColor.primary]
        let fonts = [UIFont(name: "PingFangSC-Regular", size: 16) ?? Theme.font16, UIFont(name: "PingFangSC-Regular", size: 16) ?? Theme.font16]
        self.capa_showLiveTabMenu(false)
        vc.showIn(viewController: self, with: titles, color: colors, font: fonts)
    }
    
    private func changeJoinLimit(isOnlyChatGroup: Bool) {
        self.prepVM.joinLimit = isOnlyChatGroup
    }
}
