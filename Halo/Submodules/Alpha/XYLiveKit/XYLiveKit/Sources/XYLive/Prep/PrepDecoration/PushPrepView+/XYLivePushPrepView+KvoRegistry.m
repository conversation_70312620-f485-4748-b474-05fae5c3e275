//
//  XYLivePushPrepView+KvoRegistry.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/7/7.
//  Copyright © 2023 XingIn. All rights reserved.
//

#import "XYLivePushPrepView.h"
#import "XYLivePushPrepPrivateExt.h"
#import <XYLiveKit/XYLiveKit-Swift.h>

@implementation XYLivePushPrepView (KvoRegistry)

- (void)setupKVO {
    __weak typeof(self) wself = self;
    [self.KVOController observe:self.viewModel keyPath:NSStringFromSelector(@selector(coverImage)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        XYExecuteOnMainQueue(^{
            NSString *tip = XYConfigCenter().stringForKey(@"ios_live_cover_tip", @"");
            if (tip.length > 0) {
                BOOL blurry = [wself.viewModel.coverImage blurry];
                if (blurry) {
                    [XYAlertCenter live_showTextItemWithText:tip];
                }
            }
        });
    }];
        
    [self.KVOController observe:self.viewModel keyPath:NSStringFromSelector(@selector(notifyVCToCheckShopOrSelectionAuthorityEntrance)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself setNeedsLayout];
        XYLivePrepFuncItem *shopItem = [wself.viewModel checkToShowShopEntrance];
        [XYTrackLiveShoppingPage eventActionID9911WithAnchorID:XYLiveManagerSharedInstance.hostInfo.userID tabName:@"collect_goods_entry"];
        [XYTrackLiveShoppingPage eventActionID9911WithAnchorID:XYLiveManagerSharedInstance.hostInfo.userID tabName:@"goods_selection"];
    }];
    
    [self.KVOController observe:self.viewModel keyPath:NSStringFromSelector(@selector(notifyViewToShowPaidCourseEntrance)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (wself.viewModel.notifyViewToShowPaidCourseEntrance) {
            [wself setNeedsLayout];
            XYLivePrepFuncItem *courseItem = [wself.viewModel checkToShowPaidCourseEntrance];
            courseItem.highlighted = wself.viewModel.paidCourseLessonID.length > 0; //deeplink 进入直播间需要
        }
    }];
    
    [self.KVOController observe:self.viewModel keyPath:NSStringFromSelector(@selector(paidCourseLessonID)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        BOOL on = wself.viewModel.paidCourseLessonID.length > 0;
//        wself.videoLiveStartButtonTitle = on ? NSLocalizedString(@"开播专栏", @"") : NSLocalizedString(@"开始视频直播", @"");
    }];
    
    [self hostTask_setupKVO];
}

@end

