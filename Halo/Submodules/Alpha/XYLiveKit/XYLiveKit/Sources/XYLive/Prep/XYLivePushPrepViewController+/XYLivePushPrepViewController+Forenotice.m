//
//  XYLivePushPrepViewController+Forenotice.m
//  XYLiveKit
//
//  Created by jxx on 2022/6/8.
//  Copyright © 2022 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
@import KVOController;
@import XYStorageCore_Linker;

@implementation XYLivePushPrepViewController (Forenotice)

- (void)forenotice_setupKVO {
    __weak typeof(self) wself = self;
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToShowForenotice)) options:NSKeyValueObservingOptionNew | NSKeyValueObservingOptionOld block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself forenotice_entranceHandler];
        [XYTrackLivePreparePage eventActionID2961WithClickItemName:@"broadcast_preview"];
    }];
}

- (void)forenotice_entranceHandler {
    NSString *hostId = self.roomInfo.hostInfo.userID ?: @"";
    NSString *link = @"xhsdiscover://rn/chaos/live_notice/list_live_room_pre?background_transparent_v2=true&needNewRootNavigation=true&direction=downToUp&rnAnimated=true&halfShadow=true&statusbar_transparent=true&halfShadowHeight=0.75&halfShadowCorner=12&halfShadowColor=%23f5f5f5&source=live_pre";
    NSURL *url = [[NSURL URLWithString:link] URLByAppendingQueryParameters:@{@"host_id": hostId,
                                                                             @"room_id": self.roomInfo.roomIDStr}];
    [JLRoutes routeURL:url];
}

@end
