//
//  XYLivePushPrepView+HostTask.m
//  XYLiveKit
//
//  Created by houling on 2021/9/27.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import "XYLivePushPrepView.h"
#import "XYLivePushPrepPrivateExt.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePrepHostTaskAlertView.h"
#import "JLRoutes+XYLive.h"

@implementation XYLivePushPrepView (HostTask)

- (void)hostTask_setupKVO {
    __weak typeof(self) wself = self;
    [self.KVOController observe:self.viewModel keyPath:NSStringFromSelector(@selector(notifyViewToShowHostTaskAlertView)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself checkToShowNewHostTaskAlertView];
    }];
    [self.KVOController observe:self.viewModel keyPath:NSStringFromSelector(@selector(notifyViewToShowHostTaskCompleteAuthAlertView)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself checkToShowHostTaskCompleteAuthAlertView];
    }];
}

- (void)checkToShowNewHostTaskAlertView {
    if ([XYLiveGuidanceConfig shouldShowPrepHostTaskAlertView] && [[NSUserDefaults standardUserDefaults] boolForKey:kXYLiveVerfiedShown]) {
        [XYLiveGuidanceConfig completeShowPrepHostTaskAlertView];
        [self sendAlertShowWithStatus:@"1" channelTabName:@"new_style"];
        XYLivePrepHostTaskAlertView * hostTaskAlertView = [XYLivePrepHostTaskAlertView prepHostTaskAlertForNewHost];
        [hostTaskAlertView showInView:self animated:true];
        __weak typeof(self) ws = self;
        hostTaskAlertView.confirmBlock = ^(UIButton * _Nonnull sender) {
            [ws sendAlertClickWithStatus:@"1" channelTabName:@"new_style"];
            ws.viewModel.notifyVCToShowHalfWebviewWithURL = [XYLiveConfigCenter prepHostTaskLink];
        };
        [hostTaskAlertView.confirmButton xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
            return [ws alertClickEventWithStatus:@"1" channelTabName:@"new_style"];
        }];
    }
}

- (void)checkToShowHostTaskCompleteAuthAlertView {
    if ([XYLiveGuidanceConfig shouldShowPrepHostTaskAlertView]) {
        [XYLiveGuidanceConfig completeShowPrepHostTaskAlertView];
        [self sendAlertShowWithStatus:@"0" channelTabName:@"new_style"];
        XYLivePrepHostTaskAlertView * hostTaskAlertView = [XYLivePrepHostTaskAlertView prepHostTaskAlertForRealNameNone];
        [hostTaskAlertView showInView:self animated:true];
        __weak typeof(self) ws = self;
        hostTaskAlertView.confirmBlock = ^(UIButton * _Nonnull sender) {
            [ws sendAlertClickWithStatus:@"0" channelTabName:@"new_style"];
            [JLRoutes routeToIdentificationWebview];
        };
        [hostTaskAlertView.confirmButton xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
            return [ws alertClickEventWithStatus:@"0" channelTabName:@"new_style"];
        }];
    }
}

- (void)sendAlertShowWithStatus:(NSString *)status channelTabName:(NSString *)channelTabName {
[XYAnalyticsOrganizer._.index._status(status)._chltnm(channelTabName).page._pins(PageInstance_LivePreparePage).event._action(NormalizedAction_Impression)._dtype(TargetDisplayType_BroadcastAwardPopup)._pid(11379) send];
}

- (void)sendAlertClickWithStatus:(NSString *)status channelTabName:(NSString *)channelTabName {
//[XYAnalyticsOrganizer._.index._status(status)._chltnm(channelTabName).page._pins(PageInstance_LivePreparePage).event._action(NormalizedAction_Click)._dtype(TargetDisplayType_BroadcastAwardPopup)._pid(11369) send];
    [[self alertClickEventWithStatus:status channelTabName:channelTabName] send];
}

- (XYTrackerEventContext *)alertClickEventWithStatus:(NSString *)status channelTabName:(NSString *)channelTabName {
    return XYAnalyticsOrganizer._.index._status(status)._chltnm(channelTabName).page._pins(PageInstance_LivePreparePage).event._action(NormalizedAction_Click)._dtype(TargetDisplayType_BroadcastAwardPopup)._pid(11369);
}

@end
