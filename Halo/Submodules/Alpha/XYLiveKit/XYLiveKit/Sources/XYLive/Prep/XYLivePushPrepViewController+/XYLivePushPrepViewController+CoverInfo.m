//
//  XYLivePushPrepViewController+CoverInfo.m
//  XYLiveKit
//
//  Created by 周博立 on 2021/2/9.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import <XYLiveKit/XYLiveKit-Swift.h>

@implementation XYLivePushPrepViewController (CoverInfo)

- (void)coverInfo_setupBlock {
    
}

- (void)updateCoverWithLocalImage:(UIImage *)image localPath:(NSString *)path {
    self.coverSelected = YES;
    self.prepVM.coverImage = image;
    self.coverLocalPath = path;
    self.prepVM.showLowQualityTip = NO;
    self.roomInfo.highQualityCoverFlag = 1;
    self.prepVM.coverQualityFlag = 1;
    
    // 产品需求：畅聊下编辑过一次 or 直接继承直播信息之后，才可以由外部修改
    if (self.prepVM.chatParam.coverImage || self.prepVM.chatParam.coverUrl.length > 0) {
        self.prepVM.chatParam.coverImage = image;
        self.prepVM.chatParam.coverLocalPath = path;
    }
}

@end
