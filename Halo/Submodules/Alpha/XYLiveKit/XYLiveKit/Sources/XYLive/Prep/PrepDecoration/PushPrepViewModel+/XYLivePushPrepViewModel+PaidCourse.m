//
//  XYLivePushPrepViewModel+PaidCourse.m
//  XYLiveKit
//
//  Created by houling on 2021/7/14.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewModel.h"
#import <XYLiveKit/XYLiveKit-Swift.h>

@implementation XYLivePushPrepViewModel (PaidCourse)

- (void)fetchPaidCourseListCompletion:(void (^ _Nullable)(NSArray<XYLivePaidCourseStartLessonModel *> * list, NSError *error))completion {
    [XYLivePaidCourseNetwork fetchNearestLessonInfoWithComplete:^(NSArray<XYLivePaidCourseStartLessonModel *> * _Nullable list, NSError * _Nullable error) {
        completion(list, error);
    }];
}

@end
