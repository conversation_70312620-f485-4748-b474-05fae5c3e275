//
//  XYLivePushPrepViewModel+Auth.m
//  XYLiveKit
//
//  Created by sida on 2021/6/1.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewModel.h"
@import XYSessionManager;

@implementation XYLivePushPrepViewModel (Auth)

- (void)fetchRoomAuthCompletion:(void (^ _Nullable)(XYLiveRoomAuth *roomAuth, NSError *error))completion {
    if (XYLiveManagerSharedInstance.roomAuth) {
        XYExecuteOnMainQueue(^{
            if (completion) {
                completion(XYLiveManagerSharedInstance.roomAuth, nil);
            }
        });
    } else {
        [XYLiveManagerSharedInstance fetchRoomAuthWithUserID:CURRENT_USER.userId completion:^(XYLiveRoomAuth * _Nonnull roomAuth, NSError * _Nonnull error) {
            if (completion) {
                completion(roomAuth, error);
            }
        }];
    }
}

@end
