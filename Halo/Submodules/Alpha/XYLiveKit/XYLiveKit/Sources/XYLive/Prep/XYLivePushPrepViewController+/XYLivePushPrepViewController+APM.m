//
//  XYLivePushPrepViewController+APM.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/1/18.
//  Copyright © 2023 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepPrivateExt+Core.h"

@implementation XYLivePushPrepViewController (APM)

#pragma mark - update apm basic params

- (void)updateApmV2BasicParams {
    NSString *pusherName = @"";
    if (self.rtcSession.vendorType == RTCVendorTypeTRTC) {
        pusherName = @"trtc";
    } else {
        pusherName = @"unKnow";
    }
    NSString *pusherVer = [self.netDetector getApplyRTCVersionWithVendorType:self.rtcSession.vendorType];
    NSString *pushURL = XYLiveManagerSharedInstance.roomInfo.streamURLInfo.livePushURL ?: @"";
    NSString *linkID = @"";
    int32_t ptype = linkID.length ? (int32_t)xyLiveSharedLinkmicManager.linkmicType: 0;
    int32_t role = 0;

    [xyLiveSharedAPMManager updatePusherName:pusherName pusherVer:pusherVer pushUrl:pushURL pushType:ptype localRole:role linkId:linkID];
}

- (void)updateApmRtmpParam {
    NSString *pushURL = XYLiveManagerSharedInstance.roomInfo.streamURLInfo.livePushURL ?: @"";
    NSString *linkID = @"";
    int32_t ptype = linkID.length ? (int32_t)xyLiveSharedLinkmicManager.linkmicType: 0;
    int32_t role = 0;
    [xyLiveSharedAPMManager updatePusherName:XYLiveRtmpPusher.sdkPusherName pusherVer:XYLiveRtmpPusher.sdkPusherVersion pushUrl:pushURL pushType:ptype localRole:role linkId:linkID];
}

- (void)updateApmKasaParam {
    NSString *pushURL = XYLiveManagerSharedInstance.roomInfo.streamURLInfo.livePushURL ?: @"";
    NSString *linkID = @"";
    int32_t ptype = linkID.length ? (int32_t)xyLiveSharedLinkmicManager.linkmicType: 0;
    int32_t role = 0;
    [xyLiveSharedAPMManager updatePusherName:XYLiveKasaPusher.sdkPusherName pusherVer:XYLiveKasaPusher.sdkPusherVersion pushUrl:pushURL pushType:ptype localRole:role linkId:linkID];
}

- (void)startApmInfoReport:(XYLivePushPrepBizNaviType)type {
    id<XYLiveCommonMonitorMemServiceProtocol> monitorMemService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveCommonMonitorMemServiceProtocol)];
    if (monitorMemService != nil) {
        DeviceStatistics* deviceStatistics = [monitorMemService generateImmediately];
        if (deviceStatistics != nil) {
            if ([XYLivePushSentryInfo enableUpdate]) {
                NSDictionary *info = @{@"livepush_mem_start": [NSString stringWithFormat:@"%lld", deviceStatistics.memUsage]};
                [XYLivePushSentryInfo updateSentryRecordLivePushInfo:XYLiveManagerSharedInstance.roomInfo.roomIDStr
                                                                info:info];
            }
            [XYLiveStartPushApmReport reportWhenClickButton:type mem:deviceStatistics.memUsage appCPU:deviceStatistics.appCPU roomId:XYLiveManagerSharedInstance.roomInfo.roomIDStr];
        }
    }
}

@end

