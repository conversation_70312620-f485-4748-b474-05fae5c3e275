//
//  XYLivePushPrepViewController+PreCheckPipeline.m
//  XYLiveKit
//
//  Created by gongyidemac on 2023/2/23.
//  Copyright © 2023 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import "XYLiveRouter.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLiveAlertView.h"

@import KVOController;
@import XYLiveCore;
@import XYConfigCenter;

@implementation XYLivePushPrepViewController (PreCheckPipeline)

- (void)preCheck_setupKVO {
    __weak typeof(self) wself = self;
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToCheckPreStartCondition)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        
        // 1.开播弹窗（播前规范+境外直播）
        [wself auth_checkStartLiveConditionWithRoomID:wself.roomInfo.roomIDStr completion:^(BOOL enable) {
            if (!enable) {
                return;
            }
            // 2. 直播课 + mediaControl
            [wself preCheck_paidCourse];
//            if ([XYLiveConfigCenter disableShowDialog]) {
//                [wself preCheck_paidCourse];
//            } else {
//                [wself preCheck_cover];
//            }
        }];
    }];
}

- (BOOL)preCheck_shouldAlertCoverQuality {
    return (self.prepVM.coverQualityFlag == 0 &&
            [XYLiveExperimentConfig usePrepCoverHalfStyle] &&
            (self.prepVM.bizNaviType == XYLivePushPrepBizNaviTypeVideo || self.prepVM.bizNaviType == XYLivePushPrepBizNaviTypeRHVoice));
}

- (BOOL)preCheck_shouldAlertCoverChange {
    return (self.prepVM.lowClickRate == 1 && !self.prepVM.showLowClickRate && (self.prepVM.bizNaviType ==  XYLivePushPrepBizNaviTypeVideo));
}

- (void)preCheck_alertCoverQuality {
    __weak typeof(self) wself = self;
    [XYTrackLivePreparePage eventActionID21709ForCoverTipImpressedWithChlID:[XYLiveRoomEnumUtils convertPrepNaviNameWithType:self.prepVM.bizNaviType]];
    
    XYLiveStarLiveCovertipView *coverTipView = [[XYLiveStarLiveCovertipView alloc] init];
    XYLiveCommonAlertView *alertView = [[XYLiveCommonAlertView alloc] init];
    [alertView showinWithContainerView:wself.prepView title:@"封面质量不佳" letfActionTitle:@"继续直播" rightActionTitle:@"更换封面" tipView:coverTipView];
    alertView.rightCallback = ^() {
        [XYTrackLivePreparePage eventActionID21710ForCoverTipClickWithClickItemName: @"change" chlID:[XYLiveRoomEnumUtils convertPrepNaviNameWithType:self.prepVM.bizNaviType]];
        wself.prepVM.notifyViewToSelectCoverView = YES;
    };
    alertView.leftCallback = ^() {
        [XYTrackLivePreparePage eventActionID21710ForCoverTipClickWithClickItemName: @"continue" chlID:[XYLiveRoomEnumUtils convertPrepNaviNameWithType:self.prepVM.bizNaviType]];
        [wself preCheck_paidCourse];
    };
    [alertView.leftButton xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
        return XYAnalyticsOrganizer._
            .index
               ._chltnm(@"continue")
            .live
               ._aid(XYLiveManagerSharedInstance.hostInfo.userID?:@"")
            .page
               ._pins(PageInstance_LivePreparePage)
            .event
               ._action(NormalizedAction_Click)
               ._pid(21710)
               ._igoto(1)
               ._mid(4216);
    }];
    
    [alertView.rightButton xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
        return XYAnalyticsOrganizer._
            .index
               ._chltnm(@"change")
            .live
               ._aid(XYLiveManagerSharedInstance.hostInfo.userID?:@"")
            .page
               ._pins(PageInstance_LivePreparePage)
            .event
               ._action(NormalizedAction_Click)
               ._pid(21710)
               ._igoto(1)
               ._mid(4216);
    }];
}

- (void)preCheck_alertCoverChange {
    __weak typeof(self) wself = self;
    XYLiveStarLiveCoverLowClickRateView *coverTipView = [[XYLiveStarLiveCoverLowClickRateView alloc] init];
    XYLiveCommonAlertView *alertView = [[XYLiveCommonAlertView alloc] init];
    coverTipView.clickRateDesc = self.prepVM.clickRateDesc;
    coverTipView.showHistoryCallback = ^() {
//        BOOL delete = XYConfigCenter().boolForKey(@"ios_live_shop_delete_track", NO);
//        if (delete == NO) {
//            [XYTrackLivePreparePage eventActionID29597];
//        }
//        wself.prepVM.notifyViewToHistoryCoverView = YES;
        [alertView dismiss];
    };
    alertView.rightCallback = ^() {
        wself.prepVM.notifyViewToSelectCoverView = YES;
        
    };
    [alertView.rightButton xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
        return [XYTrackLivePreparePage eventContextID29599];
    }];
    alertView.leftCallback = ^() {
        [wself preCheck_paidCourse];
        
    };
    [alertView.leftButton xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
        return [XYTrackLivePreparePage eventContextID29601];
    }];
    [alertView showinWithContainerView:wself.prepView title:@"封面更换提醒" letfActionTitle:@"继续直播" rightActionTitle:@"更换封面" tipView:coverTipView];
    [XYTrackLivePreparePage eventActionID29605];
    self.prepVM.showLowClickRate = YES;
}

- (void)preCheck_cover {
    if ([self preCheck_shouldAlertCoverQuality]) {
        [self preCheck_alertCoverQuality];
    } else if ([self preCheck_shouldAlertCoverChange]) {
        [self preCheck_alertCoverChange];
    } else {
        [self preCheck_paidCourse];
    }
}

- (void)preCheck_paidCourse {
    if (self.prepVM.shouldCheckToShowStartLessonAlert && self.prepVM.startLessonModel && self.prepVM.bizNaviType == XYLivePushPrepBizNaviTypeVideo) {
        NSString *title = NSLocalizedString(@"你有一节待开的专栏，是否开播?", @"");
        XYLiveAlertView *alert = [XYLiveAlertView alertViewWithTitle:title subTitle:@""];
        alert.leftBtnTitle = NSLocalizedString(@"开直播", @"");
        alert.rightBtnTitle = NSLocalizedString(@"开专栏直播", @"");
        
        __weak typeof(self) wself = self;
        alert.rightBtnBlock = ^(UIButton * _Nonnull _, BOOL __) {
            __auto_type lessonInfo = wself.prepVM.startLessonModel;
            // 这种情况是自动帮用户绑定，没有取消解绑的逻辑
            [wself bindStartLessonInfoWithPaidCourseLessonId:lessonInfo.lessonID withBizPushStyle:lessonInfo.bizPushStyle withPrivateLesson:lessonInfo.maxSellCount == 1];
            [wself preCheck_calling];
        };
        alert.leftBtnBlock = ^(UIButton * _Nonnull _) {
            [wself preCheck_calling];
        };
        [alert showInView:wself.prepView animated:YES];
    } else {
        [self preCheck_calling];
    }
}

- (void)preCheck_calling {
    id<XYMessageCallService> service = [XYRouter(XYMessageCallService) destination];
    if ([service isInCallingWithShowToast:NO source:@"start live"]) {
        [XYAlertCenter live_showTextItemWithText:@"正在语音通话中，无法开播"];
        [XYLogCollector xyLiveLogTag:@"push_prep_start" content:@"start live in calling"];
    } else {
        [self preCheck_mediaControl];
    }
}

- (void)preCheck_mediaControl {
    [XYLogCollector xyLiveLogTag:@"push_prep" content:@"trigger start button press action"];
    if ([XYLiveConfigCenter liveAndRedhouseMutex]) {
        if (XYMediaController.isActive) {
            [XYLogCollector xyLiveLogTag:@"push_prep_start" content:@"XYMediaController isActive"];
            if (XYMediaController.activePlayers.count != 0) {
                @weakify(self)
                [XYLogCollector xyLiveLogTag:@"push_prep_start" content:@"XYMediaController revokeAllPlayersInScene"];
                [XYMediaController revokeAllPlayersInScene:MCSceneLive inMode:MCPlayModeAudio_video invalid:nil completion:^(BOOL takeControl){
                    NSString *content = [NSString stringWithFormat:@"XYMediaController callback takecontrol = %@", @(takeControl)];
                    [XYLogCollector xyLiveLogTag:@"push_prep_start" content:content];
                    
                    @strongify(self)
                    if (takeControl) {
                        if (self.prepVM.bizNaviType == XYLivePushPrepBizNaviTypeRHVoice) {
                            [self preCheck_liveChat];
                        } else {
                            [self preCheck_beforeStart];
                        }
                    }
                }];
                return;
            }
        }
    }
    
    if (self.prepVM.bizNaviType == XYLivePushPrepBizNaviTypeRHVoice) {
        [self preCheck_liveChat];
    } else {
        [self preCheck_beforeStart];
    }
}

- (void)preCheck_liveChat {
    // bind 畅聊group逻辑
    if ([self relateToGroupChat]) {
        [XYLivePushPrepStartCheckNetwork bindLiveChatWithGroupId:self.groupId
                                                          roomId:self.roomInfo.roomIDStr
                                                      completion:^(BOOL success, NSError * _Nullable error) {
        }];
    }
        
    // preOnMic
    [XYLogCollector xyLiveLogTag:@"multilink" content:[NSString stringWithFormat:@"%ld", (long)self.prepVM.chatMemberCount]];
    [MultiLinkmicCommService prepOnMicWithLayoutType:1 maxUserLimit:self.prepVM.chatMemberCount mediaType:1 completion:^(XYLiveLinkmicAllInfoModel * _Nullable info, NSError * _Nullable error) {
        if (error == nil) {
            [self preCheck_beforeStart];
            [XYLogCollector xyLiveLogTag:@"multilink" content:@"pre on mic success"];
        } else {
            [XYAlert live_showTextItemWithError:error];
            [XYLogCollector xyLiveLogTag:@"multilink" content:@"pre on mic failed"];
        }
    }];
}

// 锁定房间资源 & 校验中台开播条件
- (void)preCheck_beforeStart {
    if ([XYLiveConfigCenter shouldPrepThrottleBeforeStart] && xyLiveSharedAPMManager.startApmInfo.beforeStartRequesting) {
        return;
    }
    // 校验开播协议
    WS
    [self checkProtocol:^{
        SS
        // apm
        xyLiveSharedAPMManager.startApmInfo.startBtnTs = [NSDate date].timeIntervalSince1970;
        xyLiveSharedAPMManager.startApmInfo.bizNaviType = self.prepVM.bizNaviType;
        xyLiveSharedAPMManager.startApmInfo.beforeStartRequesting = YES;
        
        // lock
        [self.livePreparationLoadingItem show];
        [XYLiveManagerSharedInstance sdk_lockRoom:self.roomInfo.roomIDStr];
    }];
}

- (void)preSetCategoryWithPrepRouterContext:(XYLivePushPrepRouterContext *)context {
    if (context.preContentType != XYLiveRoomPushContentTypeScreenCap || !context.categoryId.length || !context.categoryName.length) {
        return;
    }
    XYLiveCategorySubmodel *category = [XYLiveCategorySubmodel new];
    category.categoryId = context.categoryId;
    category.categoryName = context.categoryName;
    category.contentType = context.preContentType;
    category.orientation = context.categoryOrientation;
    [self.prepVM preSetGameCategory:category];
}

@end
