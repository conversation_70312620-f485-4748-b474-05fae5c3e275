//
//  XYLivePushPrepViewController+BeautyFilter.m
//  XYLiveKit
//
//  Created by 周博立 on 2020/9/8.
//  Copyright © 2020 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLivePusher/FaceBeautyType+Convert.h>

@import XYConfigCenter;
@import XYAIModelModule;
@import XYEvolver;
@import XYLivePusher;
@import XYAlphaUtils;
@import XYPostBeautyKit;
@import XYLiveFoundation;
@import XYPostPublicModule;
@import ReactiveObjC;
@import XYAlphaUtils;

@implementation XYLivePushPrepViewController (BeautyFilter)

- (void)effect_handleExistingRoom:(PreRoomInfo *)preInfo {
    [self setupTexiaoWithRoom:preInfo];
}

- (void)updateBeautyConfigIfNeeded {
    NSInteger identifier = XYConfigCenter().integerForKey(@"ios_alpha_prep_change_exsit_beautyto_identifier", XYPKBeautyConfigModelTypeMicrodermabrasion);
    if ([XYLiveEffectConfig effectNewBeautyExpe] && identifier > 0) {
        // 上次保存状态只在当前生命周期生效，链路太长，只能野路子了
        [XYLiveEffectViewModel changeExistSelectedBeautyWithIdentifier:identifier];
    }
    
    if (self.needUpdateBeautyConfig) {
        [XYCSBeautyConfigManager updateBeautyConfigModelsWithServerByType:XYPKBeautyConfigTypeLive];//商汤美颜项
        
        if (XYConfigCenter().boolForKey(@"ios_live_prep_load_ai_model", YES)) {
            if ([XYLivePushPrepViewController aiModelDownloaderUpdateEnable] == YES) {
                [XYAIModelBusinessManager.shared loadAIModelFiles];
            } else {
                [XYSmartMediaModelManager.shared loadModelFiles];// 自研ai model
            }
            
            
            [XYCSBeautyConfigManager updateBeautySectionConfigModelsWithServerByType:XYPKBeautyConfigTypeCapaSection isCapa:false preload:false];// 自研美颜资源
        }
    }
}

- (void)setupFilter {
    RACSignal *signal = [XYAlphaSwitch goodsNofilterDefaultOn] ? self.prepVM.goodsAuthSubject : [RACSignal return:@(YES)];
    __weak typeof(self) wself = self;
    [[[signal filter:^BOOL(id  _Nullable value) {
        return value != nil;
    }] take:1] subscribeNext:^(id  _Nullable x) {
        [wself configFilterWithGoodAuth:[x boolValue]];
    }];
}

- (void)configFilterWithGoodAuth:(BOOL)auth {
    BOOL needNoGoodsFilter = [XYLiveFilterConfigManager noGoodsFilterSwitchOn] && [XYLiveEffectStrategyCenter enableGoodsNoFilter] && auth;
    
    [XYLiveFilterConfigManager updateNoGoodsFilterSwitchOn:needNoGoodsFilter];
    [XYLiveManagerSharedInstance.coreManager.media applyNoGoodsFilterSwitchOn:needNoGoodsFilter];
    
    // 请求滤镜信息并配置
    // 滤镜优先级 < 商品无滤镜，故在其后设置
    [XYLiveFilterConfigManager fetchAndUpdateFilterConfigInfoWithCompleteHandler:^{
        XYLiveFilterConfigModel *latestModel = [XYLiveFilterConfigManager fetchLatestUserFilterConfigModel];
        if (!latestModel) {
            return;
        }
        if (latestModel.resourceState != XYLiveFilterConfigResourceStateDownloaded) {
            __weak typeof(self) wself = self;
            [XYLiveFilterConfigManager downloadTargetFilterConfigForModel:latestModel completeHandler:^(NSError *error){
                if (!error) {
                    [XYLiveManagerSharedInstance.coreManager.media applyFilter:[latestModel generateAVFilter]];
                }
            }];
        } else {
            [XYLiveManagerSharedInstance.coreManager.media applyFilter:[latestModel generateAVFilter]];
        }
    }];
}

- (void)setupStyleMakeup {
    if ([XYLiveEffectStrategyCenter enableStyleMakeupPreLoadCheck]) {
        if (![XYLiveEffectStrategyCenter enableStyleMakeup]) {
            return;
        }
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 延迟500ms设置上次风格妆，因为相机在Appeared之后初始化，也等美颜设置好再设置
        XYLiveStyleMakeupRenderConfig *config = [[XYLiveStyleMakeupEffectViewModel fetchLastSelect] buildRenderConfig];
        if (config) {
            [XYLogCollector xyLiveLogTag:@"StyleMakeup" content:[NSString stringWithFormat:@"上次选择风格为 %@，需要配置", config.name ?: @"无名氏"]];
            [XYLiveManagerSharedInstance.coreManager.media applyStyleMakeup:config beautyParamsUpdate:nil];
        } else {
            [XYLogCollector xyLiveLogTag:@"StyleMakeup" content:@"上次选择风格为空，无需配置"];
        }
    });
}

- (void)setupBody {
    if ([XYLiveEffectStrategyCenter enableBodyPreLoadCheck]) {
        if (![XYLiveEffectStrategyCenter enableEffectBody]) {
            return;
        }
    }
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.6 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 延迟600ms设置上次美体，因为相机在Appeared之后初始化，也等美颜设置好再设置
        NSArray<XYLiveBeautyBodyEffectCellViewModel *> *models = [XYLiveBeautyBodyEffectViewModel getBeautyBodyInfo];
        [models enumerateObjectsUsingBlock:^(XYLiveBeautyBodyEffectCellViewModel * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            XYLiveBeautyBodyRenderConfig *config = [obj buildRenderConfig];
            if (config) {
                [XYLogCollector xyLiveLogTag:@"Body" content:[NSString stringWithFormat:@"上次选择美体为 %@，需要配置", config.name ?: @"无名氏"]];
                [XYLiveManagerSharedInstance.coreManager.media applyBody:config];
            }
        }];
        
        XYLiveBeautyBodyEffectCellViewModel *model = [XYLiveBeautyBodyEffectViewModel fetchLastSelect];
        if (!model || model.renderType == XYCRRenderBeautyBodyTypeNONE) {
            // 如果选择了，上次选择了无，那么关闭所有的效果
            XYLiveBeautyBodyRenderConfig *config = [model buildRenderConfig];
            [XYLiveManagerSharedInstance.coreManager.media applyBody:config];
            [XYLogCollector xyLiveLogTag:@"Body" content:@"上次选择美体为空，需要关闭效果"];
        }
    });
}

- (void)setupTexiaoWithRoom:(PreRoomInfo *)preInfo {
    if ([XYLiveEffectStrategyCenter enableTexiaoPreLoadCheck]) {
        if (![XYLiveEffectStrategyCenter enableTexiao]) {
            return;
        }
        
        if (!preInfo.hasRecoveryRoom) {
            // 不是延播放，就不用上次的特效
            [XYLiveTexiaoEffectViewModel updateSelectCacheWithModel:nil];
            return;
        }
    }
    
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        // 延迟500ms设置上次特效，因为相机在Appeared之后初始化，也等美颜设置好再设置
        [XYLiveTexiaoEffectViewModel fetchLastSelectCheckByRemoteDataWithComplete:^(XYLiveTexiaoEffectCellViewModel *lastTexiao) {
            if (lastTexiao) {
                [XYLogCollector xyLiveLogTag:@"Texiao" content:[NSString stringWithFormat:@"上次选择特效为 %@，需要配置", lastTexiao.name ?: @"无名氏"]];
                [XYLiveManagerSharedInstance.coreManager.media applyTexiao:[lastTexiao buildRenderConfig] beautyParamsUpdate:nil failed:nil];
            }
        }];
    });
}

#pragma mark <XYLiveEffectSettingViewControllerDelegate>

- (void)filterConfigDidChanged:(XYLiveFilterConfigModel *)filter {
    [XYLiveManagerSharedInstance.coreManager.media applyFilter:[filter generateAVFilter]];
}

- (void)beautyConfigDidChanged:(XYCSBeautifyConfig *)beautyConfig {
    [XYLiveManagerSharedInstance.coreManager.media refreshBeautyComboWithConfig:beautyConfig];
}

- (void)styleConfigDidChanged:(id<XYCRRenderStyleMakeupProtocol>)styleConfig beautyParamsUpdate:(void(^)(FaceBeautyType, float))update {
    [XYLiveManagerSharedInstance.coreManager.media applyStyleMakeup:styleConfig beautyParamsUpdate:update];
}

- (id<XYCRRenderStyleMakeupProtocol>)currentStyleMakeup {
    return XYLiveManagerSharedInstance.coreManager.media.currentStyleMakeup;
}

- (void)texiaoDidChanged:(nullable id<XYLiveRenderTexiaoProtocol>)texiao beautyParamsUpdate:(void(^ _Nullable)(FaceBeautyType, float))update failed:(void (^ _Nullable)(void))failed {
    [XYLiveManagerSharedInstance.coreManager.media applyTexiao:texiao beautyParamsUpdate:update failed:failed];
}

- (nullable id<XYLiveRenderTexiaoProtocol>)currentTexiao {
    return XYLiveManagerSharedInstance.coreManager.media.currentTexiao;
}

- (void)fetchTrackInfoWithHostId:(NSString * _Nonnull * _Nullable)hostId roomId:(NSString * _Nonnull * _Nullable)roomId liveStatus:(NSString * _Nonnull * _Nullable)liveStatus liveType:(NSString * _Nonnull * _Nullable)liveType {
    if (hostId != NULL) {
        *hostId = XYLiveManagerSharedInstance.hostInfo.userID;
    }
    
    if (roomId != NULL) {
        *roomId = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
    }
    
    if (liveStatus != NULL) {
        *liveStatus = @"before_broadcast";
    }
    
    if (liveType != NULL) {
        *liveType = XYLiveManagerSharedInstance.roomInfo.hasGoods ? @"goods" : @"Interaction";
    }
}

- (id<XYCRRenderBeautyBodyProtocol>)currentBody {
    return XYLiveManagerSharedInstance.coreManager.media.camera.bodyModel;
}

- (void)bodyConfigDidChanged:(id<XYCRRenderBeautyBodyProtocol>)bodyConfig {
    [XYLiveManagerSharedInstance.coreManager.media applyBody:bodyConfig];
}

- (NSString *)hostId {
    return XYLiveManagerSharedInstance.hostInfo.userID;
}

- (NSString *)roomId {
    return XYLiveManagerSharedInstance.roomInfo.roomIDStr;
}

@end
