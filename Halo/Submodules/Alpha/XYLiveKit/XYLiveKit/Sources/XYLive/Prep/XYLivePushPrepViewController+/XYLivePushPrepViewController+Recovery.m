//
//  XYLivePushPrepViewController+Recovery.m
//  XYLiveKit
//
//  Created by 周博立 on 2020/9/8.
//  Copyright © 2020 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import "XYLivePushPrepPrivateExt+Recovery.h"
#import "XYLiveResumePushAlertView.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
@import XYConfigCenter;
@import XYEvolver;
@import XYLiveFoundation;
@import XYLiveCore;
@import XYAnalytics;
@import XYAlertCenter;

@implementation XYLivePushPrepViewController (Recovery)

#pragma mark - Recovery

- (void)showResumePushAlertWithRoomInfo:(XYLivePreRoomInfo *)roomInfo {
    __weak typeof(self) wself = self;
    void(^triggerCancelAction)(void) = ^{
        [XYLogCollector xyLiveLogTag:@"push_prep" content:@"recovery_cancel_pressed"];
        wself.hasRecoveringRoom = NO;
        //不考虑上一场直播间结束直播
        [wself cancelLiveRecovery:roomInfo];
        
        [XYTrackLivePreparePage eventActionID3577WithRoomID:roomInfo.roomIDStr hostUserID:roomInfo.hostInfo.userID];
        
        // 取消后清理lessonID
        wself.paidCourseLessonID = @"";
    };
    
    if (roomInfo.linkmicLinkingInfo.linkmicConnType == XYLiveLinkmicConnTypeBattle) {
        if (roomInfo.pkID > 0) {
            XYLiveManagerSharedInstance.battleType = XYLiveBattleTypePK;
        } else if (roomInfo.needHostRecoveringLinkmic && roomInfo.linkmicLinkingInfo.linkID.length) {
            XYLiveManagerSharedInstance.battleType = XYLiveBattleTypeRegular;
        }
    }
    if (!roomInfo.hostInfo) {
        roomInfo.hostInfo = self.loginParam.userInfo;
    }
    [XYTrackLivePreparePage eventActionID3575WithRoomID:roomInfo.roomIDStr hostUserID:roomInfo.hostInfo.userID];
    
    NSString *curLessonID = self.paidCourseLessonID;
    NSString *latestLessonID = roomInfo.latestLessonID;
    if (curLessonID.length == 0) {// 1 无课开播
        if (latestLessonID.length > 0) {// 1.1 有课续播 -> 保留lessonID，展示弹窗
            self.paidCourseLessonID = latestLessonID;
        } else {// 1.2 无课续播 -> 正常弹窗逻辑
            // do nothing
        }
    } else {// 2 有课开播
        if (latestLessonID.length == 0) {// 2.1 无课续播 -> 取消续播，保留lessonID
            triggerCancelAction();
            self.paidCourseLessonID = latestLessonID;
            return;
        } else {// 2.2 有课续播
            if ([curLessonID isEqualToString:latestLessonID]) {// 2.2.1 id 相同，展示弹窗
                // do nothing
            } else {// 2.2.2 id 不同，取消续播，保留新lessonID
                triggerCancelAction();
                self.paidCourseLessonID = latestLessonID;
                return;
            }
        }
    }
    
    CGFloat height = [XYLiveResumePushAlertView totalHeightWithTitle:roomInfo.roomName];
    self.resumePushFullScreenView.frame = self.view.bounds;
    self.resumePushFullScreenView.resumePushView.frame = CGRectMake(0, 0, XYLiveResumePushAlertViewWidth, height);
    self.resumePushFullScreenView.resumePushView.center = CGPointMake(CGRectGetWidth(self.view.frame) / 2, CGRectGetHeight(self.view.frame) / 2);
    [self.view addSubview:self.resumePushFullScreenView];
    [self.resumePushFullScreenView.resumePushView layoutIfNeeded];
    [self capa_showLiveTabMenu:NO];
    
    self.resumePushFullScreenView.resumePushView.coverURL = roomInfo.lastCoverURL;
    self.resumePushFullScreenView.resumePushView.title = roomInfo.roomName;
    // 自动埋点绑定
    [XYAnalyticsAutoTracker xyAutoTrack_bindingPointId:3576 pageId:@"" eventTrackerBlock:^XYTrackerEventContext * _Nullable{
        return [XYTrackLivePreparePage eventContextID3576WithRoomID:roomInfo.roomIDStr hostUserID:roomInfo.hostInfo.userID];
    }];
    [XYAnalyticsAutoTracker xyAutoTrack_bindingPointId:3577 pageId:@"" eventTrackerBlock:^XYTrackerEventContext * _Nullable{
        return [XYTrackLivePreparePage eventContextID3577WithRoomID:roomInfo.roomIDStr hostUserID:roomInfo.hostInfo.userID];
    }];
    
    [self.resumePushFullScreenView setDidPressCancelBlock:^(UIButton * _Nonnull sender) {
        [wself.resumePushFullScreenView removeFromSuperview];
        triggerCancelAction();
    }];
    [self.resumePushFullScreenView setDidPressConfirmBlock:^(UIButton * _Nonnull sender) {
        if ([XYLiveStartPushApmReport reportEnable]) {
            [wself startApmInfoReport:wself.prepVM.bizNaviType];
        }
        roomInfo.hasClickRecoveryBtn = YES;
        XYLiveManager.sharedManager.apmManager.startApmInfo.hasClickRecoveryBtn = roomInfo.hasClickRecoveryBtn;
        [XYTrackLivePreparePage eventActionID3576WithRoomID:roomInfo.roomIDStr hostUserID:roomInfo.hostInfo.userID];
        [wself.resumePushFullScreenView removeFromSuperview];
        [XYLogCollector xyLiveLogTag:@"push_prep" content:@"recovery_confirm_pressed"];
        if ([XYLiveConfigCenter liveAndRedhouseMutex]) {
            if (XYMediaController.isActive) {
                if (XYMediaController.activePlayers.count != 0) {
                    [XYMediaController revokeAllPlayersInScene:MCSceneLive inMode:MCPlayModeAudio_video invalid:nil completion:^(BOOL takeControl){
                        if (takeControl) {
                            [wself preProcessForRecoveryWithRoomInfo:roomInfo];
                        } else {
                            triggerCancelAction();
                        }
                    }];
                    return;
                }
            }
        }
        
        [wself preProcessForRecoveryWithRoomInfo:roomInfo];
    }];
}

- (void)startLiveRecoveryInNoPushModeWithRoomInfo:(XYLivePreRoomInfo *)roomInfo recoverBizStyle:(XYLiveRoomBizPushStyle)style {
    self.bizPushStyle = style;
    [self preProcessForRecoveryWithRoomInfo:roomInfo];
    
    NSString *logContent = @"";
    switch (self.bizPushStyle) {
        case XYLiveRoomBizPushStyleOBS:
            logContent = @"recovery_in_OBS";
            break;
        case XYLiveRoomBizPushStyleVideoLesson:
            logContent = @"recovery_in_video_lesson";
            break;
        case XYLiveRoomBizPushStyleGuideStation:
            logContent = @"recover_in_guide_station";
            break;;
        default:
            break;
    }
    [XYLogCollector xyLiveLogTag:@"push_prep" content:logContent];
}

- (void)preProcessForRecoveryWithRoomInfo:(XYLivePreRoomInfo *)roomInfo {
    self.hasRecoveringRoom = YES;
    [self capa_showLiveTabMenu:NO];
    self.roomInfo = roomInfo;
    self.recoveryRetryChance = requestRetryCount;
    [self startLiveRecoveryProcessWithRoomInfo:roomInfo];
}

- (void)cancelLiveRecovery:(XYLiveRoomInfo *)roomInfo {
    [XYLiveManagerSharedInstance reset];
    [XYLiveManagerSharedInstance updateRoomInfoForStop:roomInfo];
    [XYLiveManagerSharedInstance hostLeaveWithReason:@"cancel_recovery"];
}

- (void)startLiveRecoveryProcessWithRoomInfo:(XYLivePreRoomInfo *)roomInfo {
    if (XYConfigCenter().boolForKey(@"ios_live_start_recovery_apm_protect", NO)) {
        xyLiveSharedAPMManager.startApmInfo.beforeStartApiRespTs = [NSDate date].timeIntervalSince1970;
        xyLiveSharedAPMManager.startApmInfo.beforeStartApiRespCode = 0;
    }
        
    // V8.65，续播将话题活动信息携带至开播页进行上报
    id<XYLivePushPrepGambitNodeServiceProtocol> gambitService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushPrepGambitNodeServiceProtocol)];
    XYLiveManagerSharedInstance.cacheGambit = [gambitService currentGambit];
    
    [self pipeline_loginParamCheckOnRecoveryWithRoom:roomInfo];
    [self.livePreparationLoadingItem show];
    
    [XYLiveManagerSharedInstance sdk_startHostSetupWithParam:self.loginParam passport:roomInfo.passport];
    [self recovery_handleLoginResponseWithError:nil];
}

- (void)recovery_handleLoginResponseWithError:(NSError *)respError {
    XYExecuteOnMainQueue(^{
        [self.livePreparationLoadingItem hide];
        if (!respError) {
            [self.livePreparationLoadingItem show];
            __weak typeof(self) wself = self;
            [XYLiveManagerSharedInstance startLiveRecoveryProcessWithLoginParam:self.loginParam
                                                                    bizPushStyle:self.bizPushStyle
                                                                     lessonID:self.paidCourseLessonID
                                                                   completion:^(XYLiveRoomInfo * _Nonnull roomInfo, NSError * _Nonnull error) {
                XYExecuteOnMainQueue(^{
                    [wself.livePreparationLoadingItem hide];
                    if (!error) {
                        [wself pipeline_prepDone];
                        [wself pusher_setupChannelSession];
                        
                        roomInfo.isRecovery = 1;
                        roomInfo.isRecoveryRoom = YES;
                        XYLiveManager.sharedManager.apmManager.startApmInfo.isRecoveryLive = roomInfo.isRecoveryRoom;
                        
                        [wself pipeline_pushBeforeCheckOnRoom:roomInfo countdown:NO];
                    } else {
                        [wself handlePushProcessError:error retryNormalPush:NO];
                    }
                });
            }];
        } else {
            [self handlePushProcessError:respError retryNormalPush:NO];
        }
    });
}

@end
