//
//  XYLivePrepPlanAViewController.h
//  XYPostKit
//
//  Created by <PERSON><PERSON><PERSON> on 2019/9/9.
//

#import <UIKit/UIKit.h>
#import <XYLiveKit/XYIMModel.h>
#import <XYLiveKit/XYLiveWebViewController.h>
#import <XYLiveFoundation/XYLiveUserViewController.h>
#import <XYLiveKit/XYLivePushPrepViewModel.h>

@import XYModuleInterface;

NS_ASSUME_NONNULL_BEGIN

@class XYLiveRoomInfo;
@class XYLiveLoginParam;
@class XYLivePusher;
@class RTCPusher;
@class XYLiveActionRouteManager;
@class PreRoomInfo;
@class XYTrackerEventContext;
@class XYLivePushPrepRouterContext;
@protocol XYLivePushPrepContainerDelegate;

@interface XYLivePushPrepViewController : XYLiveUserViewController <XYLivePushPrepRouteServiceProtocol>
    
@property (nonatomic, assign) BOOL needUpdateBeautyConfig;// dafault YES
@property (nonatomic, assign) BOOL needCleanMemory;// dafault NO
@property (nonatomic, assign) BOOL presented;// dafault YES
@property (nonatomic, assign) NSInteger bizStyle;// 推流业务类型
@property (nonatomic, assign) XYLiveRoomBizPushStyle bizPushStyle;// 直播间推流业务类型，default .normal，映射bizStyle
@property (nonatomic, assign, readonly) BOOL isVideoLive;
@property (nonatomic, assign, readonly) BOOL isVoiceLive;
@property (nonatomic, assign, readonly) BOOL isScreenLive;
@property (nonatomic, assign, readonly) BOOL isChatLive;

@property (nonatomic, copy, nullable) NSString *planId;
@property (nonatomic, copy, nullable) NSString *poolId;
@property (nonatomic, copy, nullable) NSString *goodsBubbleTips; // 商品提示气泡文案

@property (nonatomic, assign) NSInteger contentType;

@property (nonatomic, copy, nullable) NSString *groupId;

@property (nonatomic, assign) NSInteger distributeStyle;// default 0

@property (nonatomic, assign) CGFloat tabbarHeight;

@property (nonatomic, copy, nullable) NSString *source;// 预直播来源，V8.24开始用于埋点
//@property (nonatomic, strong) XYLiveScreenManager *screenManager;

@property (nonatomic, copy, nullable) NSString *paidCourseLessonID;// 付费课课时ID
@property (nonatomic, weak, nullable) XYLiveWebViewController *webVC;
@property (nonatomic, strong, nullable) XYLiveActionRouteManager *actionRouteManager;

@property (nonatomic, copy) void (^prepDoneBlock)(void);
@property (nonatomic, copy) void (^showScollMenu)(BOOL show);
@property (nonatomic, copy) void (^resignLiveBlock)(void);

@property (nonatomic, copy, nullable) XYLivePushPrepDismissalHandler dismissalHandler;

- (void)updateWithNavTag:(NSInteger)tag;

@end

@class XYLiveAuthorityInfo;
@interface XYLivePushPrepViewController (Auth)

- (void)checkQualification;
- (void)checkQualificationWithValidCheckPassFinished:(void (^ _Nullable)(BOOL valid))finished;
- (void)fetchLiveAuthorityCompletion:(void (^)(XYLiveAuthorityInfo * _Nullable authorityInfo, NSError * _Nullable error))completion;
- (void)fetchHostGoodsAuth;

- (void)auth_handleCameraAndLiveAuthOnDidAppear;
- (void)auth_checkStartLiveConditionWithRoomID:(NSString *)roomID completion:(void (^_Nullable)(BOOL enable))completion;

@end

@interface XYLivePushPrepViewController (BeautyFilter)

- (void)effect_handleExistingRoom:(PreRoomInfo * _Nullable)preInfo;
- (void)setupFilter;
- (void)updateBeautyConfigIfNeeded;
- (void)setupStyleMakeup;
- (void)setupBody;

@end

@interface XYLivePushPrepViewController (Pusher)

- (void)camera_stop;
- (void)camera_stopPreview;
- (void)camera_previewSetup;
- (void)camera_initSetup;
- (void)pusher_setupChannelSession;
- (void)prep_handleOnViewDidDisappear;
- (void)pusher_handleOnViewDidAppear;
- (void)prep_handleOnViewWillAppear;
- (void)prep_handleOnTouchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event;

@end

@class XYLiveRoomInfo;
@class XYLivePreRoomInfo;

@interface XYLivePushPrepViewController (RoomService)
- (void)roomService_handleLoginResponseWithRoomInfo:(XYLivePreRoomInfo *)roomInfo error:(NSError *)error completion:(void (^)(XYLivePreRoomInfo *roomInfo, NSError *error))completion;
- (void)handlePreRoomInfoConfirmed:(XYLivePreRoomInfo *)roomInfo;
- (void)handlePushProcessError:(NSError *)error retryNormalPush:(BOOL)retry;
- (void)fetchHostLiveCount;
- (BOOL)canApplyRTC;
- (BOOL)canApplyKasa;

- (XYLivePushPrepBizNaviType)bizNaviType;
- (BOOL)isVideoLive;
- (BOOL)isVoiceLive;
- (BOOL)isScreenLive;
- (BOOL)isChatLive;
@end

@interface XYLivePushPrepViewController (Recovery)

- (void)startLiveRecoveryProcessWithRoomInfo:(XYLiveRoomInfo *)roomInfo;
- (void)cancelLiveRecovery:(XYLiveRoomInfo *)roomInfo;
- (void)preProcessForRecoveryWithRoomInfo:(XYLivePreRoomInfo *)roomInfo;
- (void)startLiveRecoveryInNoPushModeWithRoomInfo:(XYLivePreRoomInfo *)roomInfo
                                  recoverBizStyle:(XYLiveRoomBizPushStyle)style;// 无需推流的续播操作
- (void)showResumePushAlertWithRoomInfo:(XYLivePreRoomInfo *)roomInfo;

@end

@interface XYLivePushPrepViewController (Panel)

- (void)panel_setupBlock;
- (void)panel_handldFuncItem:(XYLivePrepFuncItem *)item;

- (void)handlePanelAction:(XYLivePushPanelItem * _Nonnull)item;
- (void)showDefinitionSelectVC;

@end

@interface XYLivePushPrepViewController (Shopping)

- (void)shopping_syncSelectedGoods:(NSInteger)pageSize;
- (void)shopSelectedGoods_setupBlock;

@end

@interface XYLivePushPrepViewController (Forenotice)

- (void)forenotice_setupKVO;

@end

@interface XYLivePushPrepViewController (CoverInfo)

- (void)coverInfo_setupBlock;
- (void)updateCoverWithLocalImage:(UIImage *)image localPath:(NSString *)path;

@end

@interface XYLivePushPrepViewController (FuncPanel)

- (void)funcPanel_setupKVO;
- (void)checkPanelShowScreenBubble;

@end

@interface XYLivePushPrepViewController (Share)

- (void)share_setupBlock;

@end

@interface XYLivePushPrepViewController (PCLive)

- (void)startPCLiveActionForPartnerMode:(BOOL)isPartner;// 电脑开播
- (void)handlePCExistingLiveRoomForRoomInfo:(XYLivePreRoomInfo *)roomInfo hasRoom:(BOOL)hasRoom error:(NSError *)err;
- (BOOL)pcLive_judgePartnerExistingRoom:(XYLivePreRoomInfo *)roomInfo;// 校验是否直播助手开播流程

@end
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Weverything"
@interface XYLivePushPrepViewController (ContainerDelegate) <XYLivePushPrepContainerDelegate>
#pragma clang diagnostic pop

@end

@interface XYLivePushPrepViewController (PreCheckPipeline)

- (void)preCheck_setupKVO;
- (void)preSetCategoryWithPrepRouterContext:(XYLivePushPrepRouterContext *)context;

@end

@interface XYLivePushPrepViewController (PrepPipeline)
/// normal push
- (void)startLiveButtonPressedAction;
- (void)startNormalLivePushProcess;
- (void)pipeline_normalPushOnRoomResp;
- (void)pipeline_pushBeforeCheckOnRoom:(XYLiveRoomInfo *)roomInfo countdown:(BOOL)cd;
- (void)pipeline_prepDone;
- (void)pipeline_shoppingInfoCheckOnLoginParam;
/// recover push
- (void)pipeline_loginParamCheckOnRecoveryWithRoom:(XYLiveRoomInfo *)roomInfo;
/// helper
- (void)resetDecorationIndeed;
- (void)cleanMemoryForLivePushIfNeeded;
- (UIImageView *)screenshotForLivePushLoadingWithView;
- (void)showCountdownViewWithRoomInfo:(XYLiveRoomInfo *)roomInfo completion:(void(^)(void))completion;
- (void)dismissLivePrepPushViewAnimated:(BOOL)flag completion:(void (^_Nullable)(void))completion;
@end

@interface XYLivePushPrepViewController (KvoRegistry)
- (void)setupKVO;
@end

@interface XYLivePushPrepViewController (ViewHierarchy)
- (void)registerViewHierarchies;
@end

@interface XYLivePushPrepViewController (Screen)
- (void)screen_setupBlock;
- (void)screen_setup;
@end

@interface XYLivePushPrepViewController (Event)
- (void)fireRecoverNetPackage: (XYLivePreRoomInfo *)roomInfo
                   completion:(void(^)(XYLivePreRoomInfo *roomInfo))completion;
- (void)fireNonRecoverNetPackage:(XYLivePreRoomInfo *)roomInfo
                      completion:(void(^)(XYLivePreRoomInfo *roomInfo))completion;
@end

@interface XYLivePushPrepViewController (CoreAdapter)
- (void)sdk_setupListener;
- (void)sdk_prepRoomWithExtra:(NSDictionary<NSString*, id> *_Nullable)extra;
- (void)sdk_handleExistingRoom:(PreRoomInfo * _Nullable)roomInfo
                         extra:(NSDictionary<NSString*, id> *_Nullable)extra
                         error:(NSError * _Nullable)error;
- (void)sdk_handleCancelRecoveryWithError:(NSError * _Nullable)error;
@end

@interface XYLivePushPrepViewController (Web)
- (void)showSilentWebVCWithUrl:(NSString *)url;
- (void)showSilentWebVCWithAddParamUrl:(NSString *)url;
- (void)showSilentWebVCOnSelfWithUrl:(NSString *)url; // subview
- (void)showSilentWebVCOnSelfWithAddParamUrl:(NSString *)url;
- (void)destroySilentWebVC;
- (NSString *)processWithBaseParametersForURLStr:(NSString *)URLStr;
@end

@interface XYLivePushPrepViewController (NavigationModule)
- (void)navigationModule_changeNaviPostionIfNeeded;
- (void)navigationModule_createPrepNav;
- (void)navigationModule_visibleCheck:(NSInteger)naviBizType;
- (BOOL)relateToGroupChat;
@end

@interface XYLivePushPrepViewController (ViewInteracter)
- (void)viewInteracter_setupActionBlock;
- (void)changeNeedBroadcast:(BOOL)needBroadcast;
@end

@interface XYLivePushPrepViewController (PaidCourse)
- (void)paidCourse_setupVcActionBlock;
- (void)paidCourse_updateOnSettingLessonId:(NSString *)paidCourseLessonID;
- (void)bindStartLessonInfoWithPaidCourseLessonId:(NSString *)lessonId withBizPushStyle:(XYLiveRoomBizPushStyle)bizPushStyle withPrivateLesson:(BOOL)privateLesson;
@end

@interface XYLivePushPrepViewController (APM)
- (void)updateApmV2BasicParams;
- (void)updateApmRtmpParam;
- (void)updateApmKasaParam;
- (void)startApmInfoReport:(XYLivePushPrepBizNaviType)type;
@end

@interface XYLivePushPrepViewController (Notification)
- (void)setupNotificationObservation;
- (void)removeNotificationObservation;
- (void)pushVCWillEnterForeground;
- (void)handleWebviewBroadcastNotification:(NSNotification *)notification;
@end

@interface XYLivePushPrepViewController (Capa)
- (void)capa_handlingOnViewWillDisappear;
- (void)capa_showLiveTabMenu:(BOOL)show;
@end

@interface XYLivePushPrepViewController (AutoTracker)

- (XYTrackerEventContext *)track_liveStartBtnContext;
- (void)track_LiveStartBtnAction;

@end

@interface XYLivePushPrepViewController (Guide)
- (void)showPrepGuideIfNeed;
@end

@interface XYLivePushPrepViewController (StreamRecording)
- (void)videoPushFrameAction;
- (void)recordVideoFromStart;
- (void)recordFrameImage;
@end

@interface XYLivePushPrepViewController (Lifecycle)
- (void)lifecycle_viewDidLoad;
@end

@interface XYLivePushPrepViewController (Setup)
- (void)setup_resetParameters;
- (void)setup_layoutBaseUI;
- (void)setupUI;
- (void)setup_insertPCPrepView;
- (void)seutp_switchClearModel:(BOOL)clear;
- (XYLiveLoginParam *)setup_basicLoginParam;
@end

@interface XYLivePushPrepViewController(Protocol)

// 校验是否需要弹协议
- (void)checkProtocol:(void(^)(void))completion;

@end

NS_ASSUME_NONNULL_END

