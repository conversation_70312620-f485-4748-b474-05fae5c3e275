//
//  XYLivePushPrepViewController+Guide.m
//  XYLiveKit
//
//  Created by gongyidemac on 2023/6/8.
//  Copyright © 2023 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"

@implementation XYLivePushPrepViewController (Guide)

- (void)showPrepGuideIfNeed {
    __weak typeof(self) wself = self;
    [self.prepVM fetchPrepGuideInfoWithCompletion:^(XYLiveCommonGuideModel * _Nullable guideInfo) {
        // 弹窗
        if ([guideInfo.displayType isEqualToString:@"dialog"]) {
            NSString *content = [NSString stringWithFormat:@"show prep dialog guide, id = %@", guideInfo.displayType];
            [XYLogCollector xyLiveLogTag:@"push_prep" content:content];
            
            __auto_type displayInfo = guideInfo.displayAlertInfo;
            XYLiveCommonAlertView *alertView = [XYLiveCommonAlertView new];
            alertView.rightCallback = ^{
                if (displayInfo.positiveBtnInfo.link.length  > 0) {
                    wself.prepVM.notifyVCToPushWithActionLink = displayInfo.positiveBtnInfo.link;
                }
            };
            alertView.leftCallback = ^{
                if (displayInfo.negativeBtnInfo.link.length  > 0) {
                    wself.prepVM.notifyVCToPushWithActionLink = displayInfo.negativeBtnInfo.link;
                }
            };
            
            XYLivePushPrepStartCheckAlertContentView *alertContentView = [XYLivePushPrepStartCheckAlertContentView new];
            alertContentView.info = displayInfo;
            
            // 自动埋点
            [alertView.leftButton xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
                return [XYLivePushPrepStartCheckTrackHelper trackContextAlertViewButtonClickedWithName:displayInfo.negativeBtnInfo.clickTrackName hostID:CURRENT_USER.userId pointID:displayInfo.negativeBtnInfo.trackPointID];
            }];
            [alertView.rightButton xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
                return [XYLivePushPrepStartCheckTrackHelper trackContextAlertViewButtonClickedWithName:displayInfo.positiveBtnInfo.clickTrackName hostID:CURRENT_USER.userId pointID:displayInfo.positiveBtnInfo.trackPointID];
            }];
            
            // show
            [alertView showinWithContainerView:[UIApplication sharedApplication].keyWindow title:displayInfo.title letfActionTitle:displayInfo.negativeBtnInfo.title rightActionTitle:displayInfo.positiveBtnInfo.title tipView:alertContentView];
            [XYLivePushPrepStartCheckTrackHelper trackGuideAlertViewImpressedWithStatus:displayInfo.impressionName hostID:CURRENT_USER.userId pointID:displayInfo.impressionTrackID];
            
            [wself.prepVM handleGuideShownWithInfo:guideInfo];
            return;
        }
        
        // url
        if (guideInfo.type == 0 && guideInfo.link.length > 0) {
            NSString *content = [NSString stringWithFormat:@"show prep guide，link = %@", guideInfo.link];
            [XYLogCollector xyLiveLogTag:@"push_prep" content:content];
            wself.prepVM.notifyVCToPushWithActionLink = guideInfo.link;
            [wself.prepVM handleGuideShownWithInfo:guideInfo];
        }
    }];
}

@end

