//
//  XYLivePushPrepViewController+ViewInteracter.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/1/17.
//  Copyright © 2023 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepPrivateExt+Core.h"

#import "XYLivePushSelectCoverView.h"

@implementation XYLivePushPrepViewController (ViewInteracter)

- (void)viewInteracter_setupActionBlock {
    __weak typeof(self) wself = self;
    self.prepView.didPressFuncItemBlock = ^(XYLivePrepFuncItem * _Nonnull item) {
        [wself panel_handldFuncItem:item];
    };
    self.prepView.didPressBroadcastButtonBlock = ^(BOOL needBroadcast) {
        [wself changeNeedBroadcast:needBroadcast];
    };

    self.prepView.selectIconBlock = ^() {
        [XYLivePushSelectCoverView showInVC:wself
                                  cropShape:XYPhotosPickCropShapeRectangleRate4To3
                                 coverImage:nil
                                  coverUrl:wself.roomInfo.lastCoverURL
                                   coverTip:@"上次封面"
                               enableCamera:YES
                                resultImage:^(UIImage * _Nonnull image, NSString * _Nonnull imagePath) {
            [wself updateCoverWithLocalImage:image localPath:imagePath];
        } cancelBlock:nil];
    };
    
    self.prepView.didPressStartButtonBlock = ^() {
        // 0.埋点
        [wself track_LiveStartBtnAction];
    };
    
    self.prepView.editStatusChangeBlock = ^(BOOL isEditStatus) {

    };
    
    self.prepView.didUpdateCoverBlock = ^(UIImage * _Nonnull image, NSString * _Nonnull path) {
        [wself updateCoverWithLocalImage:image localPath:path];
    };
}

- (void)changeNeedBroadcast:(BOOL)needBroadcast {
    self.prepVM.keepSecret = !needBroadcast;
    if (needBroadcast) { //正常
        [XYTrackLivePreparePage eventActionID2961WithClickItemName:@"share_to_fans"];
    } else { //试播
        [XYTrackLivePreparePage eventActionID2961WithClickItemName:@"cancel_share_to_fans"];
    }
}

@end

