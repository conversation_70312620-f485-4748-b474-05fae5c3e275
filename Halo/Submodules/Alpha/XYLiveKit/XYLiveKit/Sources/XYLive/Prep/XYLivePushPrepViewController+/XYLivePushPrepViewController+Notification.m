//
//  XYLivePushPrepViewController+Notification.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/1/18.
//  Copyright © 2023 XingIn. All rights reserved.
//  测试

#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import "XYIMModel.h"

#import <XYLiveKit/XYLiveKit-Swift.h>

@import XYLiveCore;
@import XYLiveFoundation;
@import XYAlphaShare;
@import RedI18N;
@import I18N;

@implementation XYLivePushPrepViewController (Notification)

- (void)setupNotificationObservation {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pushVCWillEnterForeground) name:UIApplicationDidBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleWebviewBroadcastNotification:) name:XYHybridWebBroadcastNotificationName object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pushPreVCDidEnterBackground) name:UIApplicationDidEnterBackgroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pushPreVCWillEnterCoverSelectPage) name:XYLiveWillEnterCoverSelectPageNotification object:nil];

}

- (void)removeNotificationObservation {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)pushVCWillEnterForeground {
    if (self.selectMessageShare) {
        self.selectMessageShare = NO;
        [self pipeline_pushBeforeCheckOnRoom:self.roomInfo countdown:YES];
    }
    [XYLogCollector xyLiveLogTag:@"pushPre" content:@"app did become active"];
    
    [self updateUserPoiLocation];
}

- (void)pushPreVCDidEnterBackground {
    [XYLogCollector xyLiveLogTag:@"pushPre" content:@"app did enter background"];
}

- (void)pushPreVCWillEnterCoverSelectPage {
    self.didEnterCoverSelectPage = YES;
}

// 处理来自webview & rn 的单向通知
- (void)handleWebviewBroadcastNotification:(NSNotification *)notification {
    if (!notification) {
        return;
    }
    
    NSDictionary *userInfo = notification.userInfo;
    NSString *key = userInfo[@"key"];
    NSDictionary *data = nil;
    if ([userInfo[@"data"] isKindOfClass:NSDictionary.class]) {
        data = (NSDictionary *)userInfo[@"data"];
    }
    if (key.length <= 0) {
        return;
    }
    
    XYExecuteOnMainQueue(^{
        if ([key isEqualToString:@"live_action_link"]) {// 执行actionLink
            if ([data[@"url"] isKindOfClass:[NSString class]]) {
                NSString *actionLink = (NSString *)data[@"url"];
                [self.actionRouteManager performWith:actionLink];
            }
        } else if ([key isEqualToString:@"cps_apply_success"]) {
            [XYAlertCenter live_showTextItemWithText:@"权限已开通"];
            @weakify(self)
            [XYLiveManagerSharedInstance fetchRoomAuthWithUserID:CURRENT_USER.userId completion:^(XYLiveRoomAuth * _Nonnull roomAuth, NSError * _Nonnull error) {
                @strongify(self)
                // 避免接口返回比webview收起动画早，导致预直播页面底部tab未被隐藏
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    self.prepVM.notifyVCToShowShoppingList = YES;
                });
                
            }];
        } else if ([key isEqualToString:@"chatgroup_list_selected"]) {
            BOOL success = false;
            if (userInfo[@"data"] != nil && userInfo[@"data"][@"success"] != nil) {
                success = [userInfo[@"data"][@"success"] boolValue];
            }
            if (success) {
                [[XYAlertCenter live_createTextItemWithText:L.live.live_share_success] show];
                [XYAlphaShareTracker eventID55644WithChannelTabName:@"fans_group" from:0];
            }
        } else {
            // do nothing
        }
    });
}

@end
