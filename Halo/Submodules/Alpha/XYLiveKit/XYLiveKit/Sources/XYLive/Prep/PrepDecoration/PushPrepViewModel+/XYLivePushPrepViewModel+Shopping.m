//
//  XYLivePushPrepViewModel+Shopping.m
//  XYLiveKit
//
//  Created by jxx on 2022/6/8.
//  Copyright © 2022 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewModel.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
@import XYConfigCenter;

@implementation XYLivePushPrepViewModel (Shopping)

- (void)syncServiceSelectedGoodsModels:(NSInteger)pageSize {
    @weakify(self)
    [XYLiveShoppingNetworkNewDataParser prePoolGoodsListWithHostID:CURRENT_USER.userId categoryId:@"" page:1 size:[XYLiveConfigCenter preGoodsManageMaxCount:pageSize] completion:^(NSInteger count, NSArray<XYLiveShoppingGoodsModel *> * _Nullable goodsModels, NSError * _Nullable error) {
        @strongify(self)
        if (error == nil) {
            [self updateCurrentSelectedGoodsModels:goodsModels];
        }
    }];
}

- (void)updateCurrentSelectedGoodsModels:(NSArray<XYLiveShoppingGoodsModel *> * _Nullable)goodsModels {
    self.notifyViewToUpdateGoodsCount = goodsModels.count;
    self.selectedGoodsModels = goodsModels;
    if (XYConfigCenter().boolForKey(@"ios_live_push_room_has_single_goods_sync", YES)) {
        XYLiveManagerSharedInstance.roomInfo.hasSingleGoods = goodsModels.count > 0;
    }
}

- (NSArray<XYLiveGoodsUploadItem *> *)newGoodsUploadItems {
    NSMutableArray *uploadItems = [NSMutableArray new];
    [self.selectedGoodsModels enumerateObjectsUsingBlock:^(XYLiveShoppingGoodsModel *obj, NSUInteger idx, BOOL * _Nonnull stop) {
        XYLiveGoodsUploadItem *item = [XYLiveGoodsUploadItem uploadItemWithGoodsModel:obj operateType:XYLiveGoodsItemOperateTypeSelected];
        [uploadItems addObject:item];
    }];
    return uploadItems;
}

@end
