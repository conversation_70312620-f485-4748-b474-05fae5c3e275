//
//  XYLivePushPrepViewController+NavigationModule.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/1/17.
//  Copyright © 2023 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>

#import "XYLivePushPrepPrivateExt+Core.h"
#import "XYLiveEnterConfig.h"

@implementation XYLivePushPrepViewController (NavigationModule)

- (void)navigationModule_changeNaviPostionIfNeeded {
    if (self.contentType == -1) {
        return;
    }
    XYLivePushPrepBizNaviType naviType = XYLivePushPrepBizNaviTypeVideo;
    switch (self.contentType) {
        case XYLiveRoomPushContentTypeVideo:
            naviType = XYLivePushPrepBizNaviTypeVideo;
            break;
        case XYLiveRoomPushContentTypeRedHouse:
            naviType = XYLivePushPrepBizNaviTypeRHVoice;
            break;
        case XYLiveRoomPushContentTypeScreenCap:
            naviType = XYLivePushPrepBizNaviTypeGame;
            break;
        default:
            break;
    }
    if (naviType == self.prepVM.bizNaviType) {
        return;
    }
    
    [self navigationModule_forceMoveToNaviBizType:naviType];
    [self navigationModule_hiddenNaviIfNeeded:naviType];
    [self navigationModule_visibleCheck:naviType];
    self.contentType = -1;
}

- (void)navigationModule_createPrepNav {
    [self.containerView bringSubviewToFront:self.panelContainerView];
}

- (void)navigationModule_forceMoveToNaviBizType:(NSInteger)naviBizType {
    
}

- (void)navigationModule_hiddenNaviIfNeeded:(NSInteger)naviBizType {
    if (naviBizType == XYLivePushPrepBizNaviTypeRHVoice) {
        if ([self relateToGroupChat]) {
            self.prepVM.onlyReserveRedhouse = YES;
        }
    }
}

- (void)navigationModule_visibleCheck:(NSInteger)naviBizType {
    if (naviBizType == XYLivePushPrepBizNaviTypeRHVoice) {
        if ([self relateToGroupChat]) {
            self.prepVM.joinLimit = YES;
            self.prepVM.keepSecret = YES;
            self.roomInfo.groupId = self.groupId;
            [XYLiveEnterConfig updateRoomVisibleStatus:YES];
        }
    }
}

// 是否关联了群聊
- (BOOL)relateToGroupChat {
    return self.groupId.length > 0;
}

@end

