//
//  XYLivePushPrepViewController+StreamRecording.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/8/14.
//  Copyright © 2023 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import <CoreServices/CoreServices.h>
@import KVOController;
@import XYLiveFoundation;
@import XYLiveUIKit;

@implementation XYLivePushPrepViewController (StreamRecording)

- (void)videoPushFrameAction {
    [self presentViewController:self.pickerVC animated:YES completion:nil];
}

- (void)recordVideoFromStart {
    [XYLiveCameraAdapter.shared recordVideoToAlbum:YES];
}

- (void)recordFrameImage {
    [XYLiveCameraAdapter.shared recordFrameImageToAlbum];
}

- (void)exportVideoFromUrl:(NSURL *)url {
    NSString *path = [self fetchPathWithName:url.lastPathComponent];
    BOOL isDirectory;
    BOOL exists = [[NSFileManager defaultManager] fileExistsAtPath:path isDirectory:&isDirectory];
    if (exists && !isDirectory) {
        XYExecuteOnMainQueue(^{
            [self.pickerVC dismissViewControllerAnimated:YES completion:nil];
            [[XYLiveCameraAdapter shared] setupVideoPath:path];
        });
        return;
    }
    
    AVURLAsset *videoAsset = [[AVURLAsset alloc] initWithURL:url options:nil];
    AVAssetExportSession *session = [[AVAssetExportSession alloc] initWithAsset:videoAsset presetName:AVAssetExportPresetHighestQuality];
    session.outputURL = [NSURL fileURLWithPath:path];
    session.shouldOptimizeForNetworkUse = YES;
    session.outputFileType = AVFileTypeMPEG4;
    [session exportAsynchronouslyWithCompletionHandler:^{
        if (session.status == AVAssetExportSessionStatusCompleted) {
            XYExecuteOnMainQueue(^{
                [self.pickerVC dismissViewControllerAnimated:YES completion:nil];
                [[XYLiveCameraAdapter shared] setupVideoPath:session.outputURL.path];
            });
        } else {
            
        }
        XYExecuteOnMainQueue(^{
            [self.pickerVC dismissViewControllerAnimated:YES completion:nil];
        });
    }];
}

- (NSString *)fetchPathWithName:(NSString *)name {
    NSString *directory = [[XYLiveFileManager alphaRootCachesDirectory] stringByAppendingPathComponent:@"VideoPushFrame/"];
    NSString *path = [[XYLiveFileManager alphaDirectoryCreateWithDirectory:directory] stringByAppendingPathComponent:name];
    return path;
}

#pragma mark <UIImagePickerControllerDelegate>

- (void)imagePickerControllerDidCancel:(UIImagePickerController *)picker {
    [self.pickerVC dismissViewControllerAnimated:YES completion:nil];
}

- (void)imagePickerController:(UIImagePickerController *)picker didFinishPickingMediaWithInfo:(NSDictionary<UIImagePickerControllerInfoKey, id> *)info {
    NSString *mediaType = info[UIImagePickerControllerMediaType];
    NSString *videoString = (NSString *)kUTTypeVideo;
    NSString *movieString = (NSString *)kUTTypeMovie;

    if ([mediaType isEqualToString:videoString] || [mediaType isEqualToString:movieString]) {
        NSURL *videoRef = info[UIImagePickerControllerReferenceURL];
        PHFetchResult *refResult = [PHAsset fetchAssetsWithALAssetURLs:@[videoRef] options:nil];
        PHVideoRequestOptions *videoRequestOptions = [[PHVideoRequestOptions alloc] init];
        videoRequestOptions.version = PHVideoRequestOptionsVersionOriginal;
        [[PHImageManager defaultManager] requestAVAssetForVideo:[refResult firstObject] options:videoRequestOptions resultHandler:^(AVAsset *asset, AVAudioMix *audioMix, NSDictionary *info) {
            if ([asset isKindOfClass:[AVURLAsset class]]) {
                NSURL *originURL = [(AVURLAsset *)asset URL];
                [self exportVideoFromUrl:originURL];
            }
        }];
    }
}

@end
