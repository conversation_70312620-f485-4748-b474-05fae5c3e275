//
//  PushPrepUtilites.swift
//  XYLiveKit
//
//  Created by gongyidemac on 2022/1/19.
//  Copyright © 2022 XingIn. All rights reserved.
//

import UIKit
import XYMacroConfig
import XYLiveFoundation

@objcMembers
@objc(XYLivePushPrepPermissioinAuth)
class PushPrepPermissioinAuth: NSObject {
    public static var supportRedHouse: Bool {
        XYLiveManager.shared().roomAuth?.permissionAuth?.redHouseAuth ?? false
    }
}

@objcMembers
@objc(XYLivePushPrepParamCache)
public class PushPrepParamCache: NSObject {
    public static var pushLandscape: Bool {
        get {
            UserDefaults.didUseLandscape
        }
        set {
            UserDefaults.didUseLandscape = newValue
        }
    }
    
    public static var showVoiceLiveShoppingToast: Bool {
        get {
            UserDefaults.didShowVoiceLiveShoppingToast
        }
        set {
            UserDefaults.didShowVoiceLiveShoppingToast = newValue
        }
    }
    
    public static var showScreenLiveShoppingToast: Bool {
        get {
            UserDefaults.didShowScreenLiveShoppingToast
        }
        set {
            UserDefaults.didShowScreenLiveShoppingToast = newValue
        }
    }
    
    public static var didTriggerStartLiveSuccess: Bool {
        get {
            UserDefaults.didTriggerStartLiveSuccess
        }
        set {
            UserDefaults.didTriggerStartLiveSuccess = newValue
        }
    }
}

private
extension UserDefaults {
    private static let kXYLivePushScreenLandscapeSettingKey = "kXYLivePushScreenLandscapeSettingKey"
    static var didUseLandscape: Bool {
        get {
            standard.bool(forKey: kXYLivePushScreenLandscapeSettingKey)
        }
        set {
            standard.set(newValue, forKey: kXYLivePushScreenLandscapeSettingKey)
        }
    }
}

extension UILabel {
    func configCoverQuality(_ quality: Int) {
        isHidden = false
        switch quality {
        case 1:
            backgroundColor = UIColor.xy.orange400
            text = "低质".xyLiveLocalized
        default:
            isHidden = true
        }
    }
}

private
extension UserDefaults {
    /// 语音直播-展示带货提示的key
    private static let kXYLiveDidShowVoiceLiveShoppingSettingKey = "kXYLiveDidShowVoiceLiveShoppingSettingKey"
    static var didShowVoiceLiveShoppingToast: Bool {
        get {
            standard.bool(forKey: kXYLiveDidShowVoiceLiveShoppingSettingKey)
        }
        set {
            standard.set(newValue, forKey: kXYLiveDidShowVoiceLiveShoppingSettingKey)
        }
    }
}

private
extension UserDefaults {
    /// 手游直播-展示带货提示的key
    private static let kXYLiveDidShowScreenLiveShoppingSettingKey = "kXYLiveDidShowScreenLiveShoppingSettingKey"
    static var didShowScreenLiveShoppingToast: Bool {
        get {
            standard.bool(forKey: kXYLiveDidShowScreenLiveShoppingSettingKey)
        }
        set {
            standard.set(newValue, forKey: kXYLiveDidShowScreenLiveShoppingSettingKey)
        }
    }
}

private
extension UserDefaults {
    private static let kXYLiveDidTriggerStartLiveSuccessKey = "kXYLiveDidTriggerStartLiveSuccessKey"
    static var didTriggerStartLiveSuccess: Bool {
        get {
            standard.bool(forKey: kXYLiveDidTriggerStartLiveSuccessKey)
        }
        set {
            standard.set(newValue, forKey: kXYLiveDidTriggerStartLiveSuccessKey)
        }
    }
}

@objc
@objcMembers 
public class PushPrepUtilites: NSObject {
    @objc
    public static func showDebugOrAdhocLogToast(withType type: XYLivePushType) {
        guard XYMacroDefine.debugORAdhoc() == true else {
            return
        }
        var desc = "unknown"
        switch type {
        case .trtc:
            desc = "trtc"
        case .rtmp:
            desc = "rtmp"
        case .kasa:
            desc = "kasa"
        case .undefine:
            desc = "undefined"
        @unknown default:
            desc = "undefined"
        }
        XYAlert.live_showTextItem(withText: desc)
    }
}
