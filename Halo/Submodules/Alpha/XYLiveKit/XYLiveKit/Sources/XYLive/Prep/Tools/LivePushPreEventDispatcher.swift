//
//  LivePushPreEventDispatcher.swift
//  XYLiveKit
//
//  Created by 刘晨阳 on 2022/8/19.
//  Copyright © 2022 XingIn. All rights reserved.
//

import UIKit

@objc(XYLivePushPrePackageType)
public enum PushPrePackageType: UInt {
    case recover
    case nonRecover
}

@objc(XYLivePushPreChannelEventType)
public enum PushPreChannelEventType: UInt {
    case roomAuth       // 权限检查
    case baseInfo       // 基础信息
    case defaultInfo    // 上场信息
    case hostInfo       // 主播信息
    case switchInfo     // 开关信息
    case chatInfo       // 聊天信息
    case courseInfo     // 课程信息
    case feature        // 功能信息
    case statistic      // 统计信息
    case linkMic        // 连线信息
    case linkMicStop    // 关闭连线
    case endPoint
    case fansGroupInfo  // 粉丝团信息
    case linkMicConfigInfo // 连线配置信息
    case adsLeads // 线索广告信息
}
