//
//  LivePushPreEventManager.swift
//  XYLiveKit
//
//  Created by 刘晨阳 on 2022/8/20.
//  Copyright © 2022 XingIn. All rights reserved.
//

import UIKit

@objc(XYLiveEventCondition)
@objcMembers
public class LiveEventCondition: NSObject {

    var fireBlock: ((_ conditionName: NSNumber) -> Void)?
    
    var unfireBlock: ((_ conditionName: NSNumber) -> Void)?
    
    public var conditionName: NSNumber = NSNumber(value: -1)
    
    public var isFired: Bool = false
    
    public func fire() {
        isFired = true
        XYLogCollector.xyLiveLogTag("preEvent:", content: "fire \(conditionName)")
        fireBlock?(conditionName)
    }
    
    public func unfire() {
        isFired = false
        XYLogCollector.xyLiveLogTag("preEvent:", content: "unfire \(conditionName)")
        unfireBlock?(conditionName)
    }
    
    convenience init(name: NSNumber) {
        self.init()
        self.conditionName = name
    }
}

@objc(XYLiveEventListenerProtocol)
public protocol LiveEventListenerProtocol: NSObjectProtocol {
    func tick(info: Any?, condition: LiveEventCondition?)
    func eventType() -> NSNumber
}

@objcMembers 
public class LiveEventListenerElement: NSObject {
    var hasTick: Bool = false
    var listener: LiveEventListenerProtocol?
}

@objc(XYLiveEventDisptacher)
@objcMembers
public class LiveEventDisptacher: NSObject {
    
    public var info: Any?
    
    private var conditions: [LiveEventCondition] = []
    
    private var eventsConditionsMap: [NSNumber: [LiveEventCondition]] = [:]
    private var eventsListnersMap: [NSNumber: LiveEventListenerElement] = [:]

    private func makeCondition(conditionName: NSNumber) -> LiveEventCondition {
        var condition = conditions.first { $0.conditionName == conditionName }
        if condition == nil {
            let con = LiveEventCondition(name: conditionName)
            con.fireBlock = { [weak self] _ in
                self?.checkEventFired()
            }
            con.unfireBlock = { [weak self] name in
                self?.revertEventStatus(conditionName: name)
            }
            conditions.append(con)
            condition = con
        }
        
        return condition ?? LiveEventCondition(name: conditionName)
    }
    
    private func checkEventFired() {
        eventsConditionsMap.forEach { [weak self] key, value in
            let notFireCondition = value.first { $0.isFired == false }
            
            // 条件全部满足
            if notFireCondition == nil {
                let condition = self?.conditions.first(where: { $0.conditionName == key })
                let element = self?.eventsListnersMap[key]
                if let element = element, !element.hasTick {
                    XYLogCollector.xyLiveLogTag("preEvent:", content: " tick \(key)")
                    element.hasTick = true
                    element.listener?.tick(info: self?.info, condition: condition)
                }
            }
        }
    }
    
    private func revertEventStatus(conditionName: NSNumber) {
        eventsConditionsMap.forEach { [weak self] key, value in
            value.forEach { [weak self] con in
                if con.conditionName == conditionName {
                    let element = self?.eventsListnersMap[key]
                    XYLogCollector.xyLiveLogTag("preEvent:", content: "revert tick \(key)")
                    element?.hasTick = false
                }
            }
        }
    }
}

// MARK: Public
extension LiveEventDisptacher {
    public func registerEventListener(_ listener: LiveEventListenerProtocol, requireParentTypes: [NSNumber]?) {
        
        if !Thread.isMainThread {
            return
        }
        
        let eventType = listener.eventType()
        XYLogCollector.xyLiveLogTag("preEvent:", content: "register \(eventType)")
        
        var eventConditions = eventsConditionsMap[eventType]
        if eventConditions.isNilOrEmpty {
            eventConditions = []
        }
        
        if requireParentTypes?.isEmpty == true {
            // 不做操作
        } else {
            requireParentTypes?.forEach { number in
                eventConditions?.append(makeCondition(conditionName: number))
            }
        }
        eventsConditionsMap[eventType] = eventConditions
        
        let element = LiveEventListenerElement()
        element.listener = listener
        eventsListnersMap[eventType] = element
    }
    
    public func getEventListener(eventType: NSNumber) -> LiveEventListenerProtocol? {
        return eventsListnersMap[eventType]?.listener
    }
    
    public func tryTick() {
        XYLogCollector.xyLiveLogTag("preEvent:", content: "try tick")
        checkEventFired()
    }
    
    public func reset() {
        conditions.forEach { $0.isFired = false }
        eventsListnersMap.forEach { _, value in
            value.hasTick = false
        }
    }
}

@objc(XYLiveEventPackage)
@objcMembers
public class LiveEventPackage: NSObject {
    private var info: Any?
    
    public lazy var dispatcher: LiveEventDisptacher = {
        let patcher = LiveEventDisptacher()
        patcher.info = info
        return patcher
    }()
    
    public convenience init(info: Any) {
        self.init()
        self.info = info
    }
    
    public func start() {
        self.dispatcher.tryTick()
    }
    
    public func reset() {
        self.dispatcher.reset()
    }
}

@objc(XYLivePushLiveEventManager)
@objcMembers
public class PushLiveEventManager: NSObject {
    private var packages: [NSNumber: LiveEventPackage] = [:]
    
    public func registerPackage(package: LiveEventPackage, withKey: NSNumber) {
        packages[withKey] = package
    }
    
    public func package(byKey: NSNumber) -> LiveEventPackage? {
        return packages[byKey]
    }
}

