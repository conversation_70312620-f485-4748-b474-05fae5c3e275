//
//  XYLivePushPrepViewController+AutoTracker.m
//  XYLiveKit
//
//  Created by gongyidemac on 2023/3/2.
//  Copyright © 2023 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepPrivateExt+Core.h"
#import "XYLiveEnterConfig.h"

static NSString *kLiveBizTypeDefault = @"视频";
static NSString *kLiveBizTypeAudio = @"语音";
static NSString *kLiveBizTypeRadio = @"电台";
static NSString *kLiveBizTypeChat = @"畅聊";
static NSString *kLiveBizTypeGame = @"手游";
static NSString *kLiveBizTypePC = @"电脑";

@implementation XYLivePushPrepViewController (AutoTracker)

- (XYTrackerEventContext *)track_liveStartBtnContext {
    NSInteger typeIdx = 1;
    NSString *typeName = kLiveBizTypeDefault;
    NSString *minorName = @"";
    NSString *bgID = @"";
    NSString *tabId = self.prepVM.keepSecret ? @"shared_only" : @"all";
    NSString *liveOrChat = @"live";
    switch (self.prepVM.bizNaviType) {
        case XYLivePushPrepBizNaviTypeVideo:
            break;
        case XYLivePushPrepBizNaviTypeRHVoice: {
            typeIdx = 2;
            typeName = kLiveBizTypeChat;
            minorName = kLiveBizTypeChat;
            tabId = self.prepVM.joinLimit ? @"group_chat_only" : tabId;
            liveOrChat = @"chat";
        }
            break;
        case XYLivePushPrepBizNaviTypeGame: {
            typeIdx = 3;
            typeName = kLiveBizTypeGame;
        }
            break;
        case XYLivePushPrepBizNaviTypePC: {
            typeIdx = 4;
            typeName = kLiveBizTypePC;
        }
            break;
        default:
            break;
    }
    
    return [XYTrackLivePreparePage eventContenxtID2962StartLive:[XYLiveFilterConfigManager fetchLatestUserFilterConfigModel].cnName
                                                            Type:typeIdx
                                                         tabName:typeName
                                                    backgroundId:bgID
                                                       minorName:minorName
                                                           tabId:tabId
                                                      liveOrChat:liveOrChat
                                                chatMemeberCount:@(self.prepVM.chatMemberCount).stringValue
                                                          roomID:self.roomInfo.roomIDStr
                                                          source:self.source];
}

- (void)track_LiveStartBtnAction {
    [[self track_liveStartBtnContext] send];
}

@end
