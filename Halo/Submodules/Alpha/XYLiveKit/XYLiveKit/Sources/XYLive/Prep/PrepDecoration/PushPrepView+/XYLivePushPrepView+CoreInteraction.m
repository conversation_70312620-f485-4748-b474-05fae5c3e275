//
//  XYLivePushPrepView+CoreInteraction.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/1/22.
//  Copyright © 2023 XingIn. All rights reserved.
//

#import "XYLivePushPrepView.h"
#import "XYLivePushPrepPrivateExt.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYTrackLivePreparePage.h"

#define kXYLiveFirstAlertRemind @"kXYLiveFirstAlertRemind"

@implementation XYLivePushPrepView (CoreInteraction)

- (void)configBroadcastStatus:(XYLivePushPrepBroadcastConfigStyle)status {
    switch (status) {
        case XYLivePushPrepBroadcastConfigStyleNormal: {
            self.needBroadcast = YES;
        }
            break;
        case XYLivePushPrepBroadcastConfigStyleAllOnly: {
            self.needBroadcast = YES;
        }
            break;
        case XYLivePushPrepBroadcastConfigStyleShareOnly: {
            self.needBroadcast = NO;
        }
            break;
               
        default:
            break;
    }
    
    [self setNeedsLayout];
    if (self.didPressBroadcastButtonBlock) {
        self.didPressBroadcastButtonBlock(self.needBroadcast);
    }
}

@end

