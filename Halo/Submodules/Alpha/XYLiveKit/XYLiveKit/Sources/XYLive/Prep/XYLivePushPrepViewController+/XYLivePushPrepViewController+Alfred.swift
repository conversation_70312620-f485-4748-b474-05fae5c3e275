//
//  XYLivePushPrepViewController+Alfred.swift
//  XYLiveKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/5/5.
//  Copyright © 2023 XingIn. All rights reserved.
//

import Foundation
import XYStorageCore_Linker

public
extension XYLivePushPrepViewController {
    @objc
    func alfred_showPanel() {
        AlfredInterfaceCenter.center().addItem(with: "视频推流") { [weak self] in
            self?.alfred_videoPushFrameAction()
        }
        let duration = XYSCKVMigrateTool.defaultUserDefaultTool_v2.int(kXYLiveOdysseyCaptureVideoDuration)
        let title = "从0开始录制\(duration)s"
        AlfredInterfaceCenter.center().addItem(with: title) { [weak self] in
            self?.alfred_recordVideoFromStart()
        }
        AlfredInterfaceCenter.center().addItem(with: "截帧") { [weak self] in
            self?.alfred_recordFrameImage()
        }
        if XYMacroDefine.debugORAdhoc() {
            AlfredInterfaceCenter.center().addItem(with: "switch kasa config信息") {
                let eapidic = KasaCoreConfigResource.eapiConfig
                let msg = "eapi: \(String(describing: eapidic))"
                let alert = UIAlertController(title: "kasa eapi配置", message: msg, preferredStyle: .alert)
                
                let cancel = UIAlertAction(title: "取消", style: .cancel, handler: nil)
                let confirm = UIAlertAction(title: "确认", style: .default) { action in
                    
                }
                alert.addAction(cancel)
                alert.addAction(confirm)
                
                let alertItem = XYAlert.createViewControllerItem(withCustomViewController: alert)
                alertItem.show()
            }
        }
        AlfredInterfaceCenter.center().showPanel()
    }
    
    func alfred_videoPushFrameAction() {
        self.videoPushFrameAction()
    }
    
    func alfred_recordVideoFromStart() {
        self.recordVideoFromStart()
    }
    
    func alfred_recordFrameImage() {
        self.recordFrameImage()
    }
}
