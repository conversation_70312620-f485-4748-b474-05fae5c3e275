//
//  XYLivePushPrepViewModel+FuncPanel.m
//  XYLiveKit
//
//  Created by 周博立 on 2021/2/9.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveKit-Swift.h>

#import "XYLivePushPrepViewModel.h"
#import "XYLiveEnterConfig.h"
@import XYLivePusher;

@implementation XYLivePushPrepViewModel (FuncPanel)

#pragma mark - Pulics

- (NSArray<XYLivePrepFuncItem *> *)funcItems {
    NSMutableArray *items = [NSMutableArray array];
    for (XYLivePrepFuncItem * item in self.originFuncItems) {
        if ([self showItemForVideoLive:item]) {
            [items addObject:item];
        }
    }
    return items;
}

- (NSArray<XYLivePrepFuncItem *> *)pcFuncItems {
    NSMutableArray *items = [NSMutableArray array];
    for (XYLivePrepFuncItem * item in self.originFuncItems) {
        if ([self showItemForPCLive:item]) {
            [items addObject:item];
        }
    }
    return items;
}

- (NSArray<XYLivePrepFuncItem *> *)gameFuncItems {
    NSMutableArray *items = [NSMutableArray array];
    for (XYLivePrepFuncItem * item in self.originFuncItems) {
        if ([self showItemForGameLive:item]) {
            [items addObject:item];
        }
    }
    return items;
}

- (NSArray<XYLivePrepFuncItem *> *)redHouseFuncItems {
    NSMutableArray *items = [NSMutableArray array];
    for (XYLivePrepFuncItem * item in self.originFuncItems) {
        if ([self showItemForRedHouseLive:item]) {
            [items addObject:item];
        }
    }
    return items;
}

- (NSArray<XYLivePrepFuncItem *> *)moreItems {
    switch (self.bizNaviType) {
        case XYLivePushPrepBizNaviTypeVideo:
            return [self videoMoreItems];
        case XYLivePushPrepBizNaviTypePC:
            return [self pcMoreItems];
        case XYLivePushPrepBizNaviTypeGame:
            return [self gameMoreItems];
        case XYLivePushPrepBizNaviTypeRHVoice:
            return [self redHouseMoreItems];
            break;
    }
    return @[];
}

- (NSArray<XYLivePrepFuncItem *> *)moreDetailForItem:(XYLivePrepFuncItem *)item {
    [item handleFuncType];
    switch (self.bizNaviType) {
        case XYLivePushPrepBizNaviTypeVideo:
            return [item.subFuncs xy_filter:^BOOL(XYLivePrepFuncItem * _Nonnull obj, NSUInteger index) {
                [obj handleFuncType];
                return [self showItemForVideoLive:obj];
            }];
        case XYLivePushPrepBizNaviTypePC:
            return [item.subFuncs xy_filter:^BOOL(XYLivePrepFuncItem * _Nonnull obj, NSUInteger index) {
                [obj handleFuncType];
                return [self showItemForPCLive:obj];
            }];
        case XYLivePushPrepBizNaviTypeGame:
            return [item.subFuncs xy_filter:^BOOL(XYLivePrepFuncItem * _Nonnull obj, NSUInteger index) {
                [obj handleFuncType];
                return [self showItemForGameLive:obj];
            }];
        case XYLivePushPrepBizNaviTypeRHVoice:
            return [item.subFuncs xy_filter:^BOOL(XYLivePrepFuncItem * _Nonnull obj, NSUInteger index) {
                [obj handleFuncType];
                return [self showItemForRedHouseLive:obj];
            }];
            break;
    }
    return @[];
}

#pragma mark - Tools

- (NSArray<XYLivePrepFuncItem *> *)originMoreItems {
    for (XYLivePrepFuncItem *item in self.originFuncItems) {
        [item handleFuncType];
        if (item.type == XYLivePrepFuncItemTypeMore) {
            return item.subFuncs;
        }
    }
    return @[];
}

- (NSArray<XYLivePrepFuncItem *> *)videoMoreItems {
    return [[self originMoreItems] xy_filter:^BOOL(XYLivePrepFuncItem * _Nonnull obj, NSUInteger index) {
        [obj handleFuncType];
        return [self showItemForVideoLive:obj];
    }];
}

- (NSArray<XYLivePrepFuncItem *> *)voiceMoreItems {
    return [[self originMoreItems] xy_filter:^BOOL(XYLivePrepFuncItem * _Nonnull obj, NSUInteger index) {
        [obj handleFuncType];
        return [self showMoreItemForVoiceLive:obj];
    }];
}

- (NSArray<XYLivePrepFuncItem *> *)pcMoreItems {
    return [[self originMoreItems] xy_filter:^BOOL(XYLivePrepFuncItem * _Nonnull obj, NSUInteger index) {
        [obj handleFuncType];
        return [self showMoreItemForPCLive:obj];
    }];
}

- (NSArray<XYLivePrepFuncItem *> *)gameMoreItems {
    return [[self originMoreItems] xy_filter:^BOOL(XYLivePrepFuncItem * _Nonnull obj, NSUInteger index) {
        [obj handleFuncType];
        return [self showMoreItemForGameLive:obj];
    }];
}

- (NSArray<XYLivePrepFuncItem *> *)redHouseMoreItems {
    return [[self originMoreItems] xy_filter:^BOOL(XYLivePrepFuncItem * _Nonnull obj, NSUInteger index) {
        [obj handleFuncType];
        return [self showMoreItemForRedHouseLive:obj];
    }];
}

#pragma mark - Privates

- (BOOL)showItemForVideoLive:(XYLivePrepFuncItem *)item {
    switch (item.type) {
        case XYLivePrepFuncItemTypeFlip:
        case XYLivePrepFuncItemTypeEffect:
        case XYLivePrepFuncItemTypeMore:
            return YES;
        case XYLivePrepFuncItemTypeShop:
            return [self showShopEntrance] && [XYLiveManagerSharedInstance.shoppingAuth.goodsAuth supportVideoShopping];
        case XYLivePrepFuncItemTypeCourse:
            return self.notifyViewToShowPaidCourseEntrance;
            // 更多面板内容：
        case XYLivePrepFuncItemTypeRoomVisible:
        case XYLivePrepFuncItemTypeShieldWords:
        case XYLivePrepFuncItemTypePayFunc:
        case XYLivePrepFuncItemTypeOfficialActivity:
        case XYLivePrepFuncItemTypeFansClub:
            return YES;
        case XYLivePrepFuncItemTypeScreen:
            return [XYLiveConfigCenter greenScreenEntrance] && [XYLiveEnterConfig liveBackgropEntrance];
            
            // 二级页面：
                // 付费功能设置
        case XYLivePrepFuncItemTypeAllowSendGiftTitle:
        case XYLivePrepFuncItemTypeAllowSendGift:
                // 允许加入粉丝团
        case XYLivePrepFuncItemTypeFansClubTitleV2:
        case XYLivePrepFuncItemTypeAllowJoinFansClubV2:
            return YES;
                // 购物粉丝团
        case XYLivePrepFuncItemTypeFansClubTypeTitle:
        case XYLivePrepFuncItemTypeShoppingClubSetting:
        case XYLivePrepFuncItemTypeCommonClubSetting:
            return [XYLiveEnterConfig hasGoodsAuth];
        case XYLivePrepFuncItemTypeFansClubManage:
            return YES;
            // 粉丝团模式切换，有电商权限才可以切换
        case XYLivePrepFuncItemTypeFansClubMode:
            return [XYLiveEnterConfig hasGoodsAuth];
        case XYLivePrepFuncItemTypeLocationVisible:
            return true;
        default:
            return NO;
    }
}

- (BOOL)showItemForGameLive:(XYLivePrepFuncItem *)item {
    switch (item.type) {
        case XYLivePrepFuncItemTypeLandscape:
        case XYLivePrepFuncItemTypeMore:
        case XYLivePrepFuncItemTypeGameSelect:
            return YES;
            // 付费功能设置 二级页面：
        case XYLivePrepFuncItemTypeAllowSendGiftTitle:
        case XYLivePrepFuncItemTypeAllowSendGift:
            return YES;
        case XYLivePrepFuncItemTypeFansClubTitleV2:
        case XYLivePrepFuncItemTypeAllowJoinFansClubV2:
            return YES;
        case XYLivePrepFuncItemTypeFansClubTypeTitle:
        case XYLivePrepFuncItemTypeShoppingClubSetting:
        case XYLivePrepFuncItemTypeCommonClubSetting:
            return [XYLiveEnterConfig hasGoodsAuth];
        case XYLivePrepFuncItemTypeFansClubManage:
            return YES;
        case XYLivePrepFuncItemTypeFansClubMode:
            return [XYLiveEnterConfig hasGoodsAuth];
        default:
            return NO;
    }
}

- (BOOL)showItemForRedHouseLive:(XYLivePrepFuncItem *)item {
    switch (item.type) {
        case XYLivePrepFuncItemTypeShare:
            return YES;
        case XYLivePrepFuncItemTypeMore:
            return YES;
            
            // 付费功能设置 二级页面：
        case XYLivePrepFuncItemTypeAllowSendGiftTitle:
        case XYLivePrepFuncItemTypeAllowSendGift:
            return YES;
        case XYLivePrepFuncItemTypeFansClubTitleV2:
        case XYLivePrepFuncItemTypeAllowJoinFansClubV2:
            return YES;
        case XYLivePrepFuncItemTypeFansClubTypeTitle:
        case XYLivePrepFuncItemTypeShoppingClubSetting:
        case XYLivePrepFuncItemTypeCommonClubSetting:
            return [XYLiveEnterConfig hasGoodsAuth];
        case XYLivePrepFuncItemTypeFansClubManage:
            return YES;
        case XYLivePrepFuncItemTypeFansClubMode:
            return [XYLiveEnterConfig hasGoodsAuth];
        default:
            return NO;
    }
}

- (BOOL)showItemForPCLive:(XYLivePrepFuncItem *)item {
    switch (item.type) {
        case XYLivePrepFuncItemTypeMore:
            return YES;
        case XYLivePrepFuncItemTypeShop:
            return [self showShopEntrance];
        case XYLivePrepFuncItemTypeCourse:
            return self.notifyViewToShowPaidCourseEntrance;
            
        case XYLivePrepFuncItemTypeAllowSendGift:
        case XYLivePrepFuncItemTypeAllowSendGiftTitle:
            return YES;
        case XYLivePrepFuncItemTypeFansClubTitleV2:
        case XYLivePrepFuncItemTypeAllowJoinFansClubV2:
            return YES;
        case XYLivePrepFuncItemTypeFansClubTypeTitle:
        case XYLivePrepFuncItemTypeShoppingClubSetting:
        case XYLivePrepFuncItemTypeCommonClubSetting:
            return [XYLiveEnterConfig hasGoodsAuth];
        case XYLivePrepFuncItemTypeFansClubManage:
            return YES;
        case XYLivePrepFuncItemTypeFansClubMode:
            return [XYLiveEnterConfig hasGoodsAuth];
        default:
            return NO;
    }
}

- (BOOL)showMoreItemForVoiceLive:(XYLivePrepFuncItem *)item {
    switch (item.type) {
        case XYLivePrepFuncItemTypeScreen:
        case XYLivePrepFuncItemTypeGamePip:
            return NO;
        default:
            return YES;
    }
}

- (BOOL)showMoreItemForGameLive:(XYLivePrepFuncItem *)item {
    switch (item.type) {
        case XYLivePrepFuncItemTypeScreen:
            return NO;
        case XYLivePrepFuncItemTypeGamePip:
            return XYLiveManagerSharedInstance.gamePipChatManager.gamePipFunctionConfigSwitch;
        case XYLivePrepFuncItemTypeLocationVisible:
            return true;
        default:
            return YES;
    }
}

- (BOOL)showMoreItemForRedHouseLive:(XYLivePrepFuncItem *)item {
    switch (item.type) {
        case XYLivePrepFuncItemTypeScreen:
        case XYLivePrepFuncItemTypeGamePip:
        case XYLivePrepFuncItemTypeRoomVisible:
        case XYLivePrepFuncItemTypeOfficialActivity:
            return NO;
        case XYLivePrepFuncItemTypeLocationVisible:
            return true;
        default:
            return YES;
    }
}

- (BOOL)showMoreItemForPCLive:(XYLivePrepFuncItem *)item {
    switch (item.type) {
        case XYLivePrepFuncItemTypeRoomVisible:
        case XYLivePrepFuncItemTypeShieldWords:
        case XYLivePrepFuncItemTypePayFunc:
        case XYLivePrepFuncItemTypeFansClub:
        case XYLivePrepFuncItemTypeOfficialActivity:
            return YES;
        case XYLivePrepFuncItemTypeLocationVisible:
            return YES;
        default:
            return NO;
    }
}

- (XYLivePrepFuncItem *)checkToShowShopEntrance {
    if ([self showShopEntrance]) {
        for (XYLivePrepFuncItem *item in self.originFuncItems) {
            if (item.type == XYLivePrepFuncItemTypeShop) {
                return item;
            }
        }
    }
    return nil;
}

- (XYLivePrepFuncItem *)checkToShowPaidCourseEntrance {
    for (XYLivePrepFuncItem *item in self.originFuncItems) {
        if (item.type == XYLivePrepFuncItemTypeCourse) {
            return item;
        }
    }
    return nil;
}

- (BOOL)showShopEntrance {
    return (self.hasShopOrSelectionAuthority || XYLiveManagerSharedInstance.shoppingAuth.goodsAuth.canApplyCPSAuth) && [XYLiveEnterConfig liveGoodsEnter];
}

@end
