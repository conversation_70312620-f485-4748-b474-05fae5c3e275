//
//  XYLivePrepPlanAViewController.m
//  XYPostKit
//
//  Created by <PERSON><PERSON><PERSON> on 2019/9/9.
//

@import XYUIKitCore;
@import KVOController;
@import XYSharingKit;
@import XYAlertCenter;
@import XYLivePlayManager;
@import XYPostBeautyKit;
@import XYModuleInterface;
@import SDWebImage;
@import XYMacroConfig;
@import XYConfigCenter;
@import XYLiveFoundation;
@import XYLivePusher;
@import XYLiveUIKit;
@import XYPrivacy;
@import XYDevice;
@import ReactiveObjC;
@import XYDeveloperKit;

#import "XYLivePushPrepViewController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepPrivateExt+Auth.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import "XYLivePushPrepPrivateExt+Recovery.h"
#import "XYLiveEnterConfig.h"
#import "XYLivePushSelectCoverView.h"
#import "XYLiveServiceFactory.h"
#import "XYLivePushPrepNodesLoader.h"
#import "XYLivePushPrepHierarchyManager.h"

#import "XYTrackLivePreparePage.h"
#import "XYLiveWebViewController.h"
#import "XYTrackLiveBroadcastPage.h"
#import "XYLiveRouter.h"
#import <CoreServices/CoreServices.h>
#import <XYLiveKit/XYLiveKit-Swift.h>

///push prep
XYROUTER_REGISTER_EASY(XYLivePushPrepRouteServiceProtocol, XYLivePushPrepViewController)


@interface XYLivePushPrepViewController ()
<
UIImagePickerControllerDelegate,
UINavigationControllerDelegate,
XYSharingTaskDelegate,
XYLiveEffectSettingViewControllerDelegate,
UIDocumentInteractionControllerDelegate
>

@end

@implementation XYLivePushPrepViewController

- (void)dealloc {
    XYLogDebug(@"[lothar]%s, %d", __PRETTY_FUNCTION__, __LINE__);
    [XYLiveDebugToast showWithToast:@"push prep vc dealloc"];
    [XYLivePushPreviewLogger.shared clearWithToken:@"prepvc"];
    [self removeNotificationObservation];
    [XYLiveAlfredInterfaceCenter destroy];
    [XYLiveGuidanceMutexManager destroy];
    if (XYConfigCenter().boolForKey(@"ios_livepush_previewh_fix_leak", NO)) {
        XYLiveManagerSharedInstance.prepViewHierarchyManager = nil;
    }
    if (!self.shouldKeepIMManagerWhenVCDealloc) {
        BOOL nonStop = XYConfigCenter().boolForKey(@"ios_live_manager_dealloc_non_stop", NO);
        if (nonStop) {
            [XYLiveManagerSharedInstance onDealloc];
        } else {
            [XYLiveManagerSharedInstance destroyWithNotifyServer:YES onPrepRoom:YES];
        }
    }
    if (XYConfigCenter().boolForKey(@"ios_live_fix_goodauth_dispose_leak", YES)) {
        [self.applyGoodsAuthDisposable dispose];
    }
    
    if ([XYAlphaSwitch vpnDetectValid]) {
        [[VPNNetService sharedInstance] unregisterListener:self];
    }
}

- (instancetype)init {
    self = [super init];
    if (self) {
        self.needUpdateBeautyConfig = YES;
        _needCleanMemory = NO;
        _presented = YES;
        _contentType = -1;
        BOOL check = XYConfigCenter().boolForKey(@"ios_live_prep_check_audience", NO);
        if (check) {
            _shouldKeepIMManagerWhenVCDealloc = XYLiveManagerSharedInstance.hostInfo != nil && ![XYLiveManagerSharedInstance.userInfo.userID isEqualToString:XYLiveManagerSharedInstance.hostInfo.userID];
        }
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self lifecycle_viewDidLoad];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.shown = YES;
    [self.navigationController setNavigationBarHidden:YES];
    [self prep_handleOnViewWillAppear];
    if (self.isWillAppearHandleCamera) {
        [self auth_handleCameraAndLiveAuthOnDidAppear];
    }
    
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    if (!self.isWillAppearHandleCamera) {
        [self auth_handleCameraAndLiveAuthOnDidAppear];
    }
    [[XYLiveAlfredInterfaceCenter center] showEntrance];
    [XYLiveFastVcCache addInFindCacheWithVc:self];

    if ([XYMacroDefine debugORAdhoc]) {
        [XYOdysseyManager.shared showShortcut];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    // fix for Capa，离开页面关闭相机
    [self capa_handlingOnViewWillDisappear];
    [[XYLiveAlfredInterfaceCenter center] dismissEntrance];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    self.shown = NO;
    [self.netDetector stopProbing];
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    [self prep_handleOnViewDidDisappear];
    [self destroySilentWebVC];
}

- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    [self prep_handleOnTouchesEnded:touches withEvent:event];
}

- (BOOL)isWillAppearHandleCamera {
    return false;
    // v9改版后恢复带底部栏，不需要全屏渲染相关功能
//    return XYLiveConfigCenter.enablePushPrepFrameSkipOp && self.isFullScreen;
}

#pragma mark - override

- (NSArray *)createServiceItems {
    return [XYLiveServiceFactory servicesBySecen:LiveSceneTypePrePush];
}

- (XYLiveBaseNodeLoader *)createNodesLoader {
    return [[XYLivePushPrepNodesLoader alloc] init];
}

- (XYLiveHierarchyManager *)createHierarchyManager {
    return [[XYLivePushPrepHierarchyManager alloc] init];
}

#pragma mark - XYSharingTaskDelegate

- (void)socialTask:(XYSocialTask *)task didSent:(BOOL)sent request:(XYSocialRequest *)request {
    [self.livePreparationLoadingItem hide];
    if (!sent) {
        [[XYAlertCenter live_createTextItemWithText:@"分享失败"] show];
        [self pipeline_pushBeforeCheckOnRoom:self.roomInfo countdown:YES];
    } else {
        self.selectMessageShare = YES;
    }
}

#pragma mark - <UIDocumentInteractionControllerDelegate>

- (void)documentInteractionControllerDidDismissOpenInMenu:(UIDocumentInteractionController *)controller {
    [XYLiveLog.sharedManager clearAllLogFiles];
}

#pragma mark - <Setter>

- (void)setDistributeStyle:(NSInteger)distributeStyle {
    [self.prepView configBroadcastStatus:(XYLivePushPrepBroadcastConfigStyle)distributeStyle];
}

- (void)setBizStyle:(NSInteger)bizStyle {
    _bizStyle = bizStyle;
    _bizPushStyle = (XYLiveRoomBizPushStyle)bizStyle;
}

- (void)setBizPushStyle:(XYLiveRoomBizPushStyle)bizPushStyle {
    _bizPushStyle = bizPushStyle;
    _bizStyle = bizPushStyle;
}

#pragma mark - <Lazy>

- (XYLiveLoginParam *)loginParam {
    if (!_loginParam) {
        _loginParam = [XYLiveLoginParam new];
    }
    return _loginParam;
}

- (XYLiveDevicePrivacyTipView *)privacyTipView {
    if (!_privacyTipView) {
        CGFloat offset = self.isFullScreen ? 60 : 10;
        _privacyTipView = [[XYLiveDevicePrivacyTipView alloc] initWithButtonTopOffset:offset];
        __weak typeof(self) wself = self;
        _privacyTipView.closePrepViewBlock = ^(){
            wself.prepVM.notifyClickPushPreViewCloseBtn = YES;
        };
    }
    return _privacyTipView;
}

- (XYLiveResumePushAlertFullScreenView *)resumePushFullScreenView {
    if (!_resumePushFullScreenView) {
        _resumePushFullScreenView = [XYLiveResumePushAlertFullScreenView new];
    }
    return _resumePushFullScreenView;
}

- (RTCNetworkProbe *)netDetector {
    if (!_netDetector) {
        _netDetector = [[RTCNetworkProbe alloc] init];
        _netDetector.probeTestBadNetwork = ^(){
            [XYAlert live_showTextItemWithText:@"当前网络较差，建议检查网络状况或更换其他网络"];
        };
    }
    return _netDetector;
}

- (UIView *)containerView {
    if (!_containerView) {
        _containerView = [UIView new];
        _containerView.backgroundColor = [UIColor clearColor];
    }
    return _containerView;
}

- (XYLivePrepCameraView *)cameraView {
    if (!_cameraView) {
        BOOL fullScreenRender = XYLiveConfigCenter.preSupportNewTransition;
        if (self.isWillAppearHandleCamera) {
            fullScreenRender = NO;
        }
        _cameraView = [[XYLivePrepCameraView alloc] initWithFullScreenRender:fullScreenRender];
        _cameraView.backgroundColor = [UIColor clearColor];
        _cameraView.showBottomGradient = self.isFullScreen;
    }
    return _cameraView;
}

- (XYLivePushPrepView *)prepView {
    if (!_prepView) {
        _prepView = [[XYLivePushPrepView alloc] initWithFullScreen:[XYLiveExperimentConfig usePushPrepNewStyle]];
        [_prepView bindWithViewModel:self.prepVM];
        __weak typeof(self) wself = self;
        _prepView.onLayoutBlock = ^(UIView * _Nonnull view) {
            [wself.hierarchyManager triggerLayoutSubviews:view];
        };
        [self viewInteracter_setupActionBlock];
    }
    return _prepView;
}

- (UIImagePickerController *)pickerVC {
    if (!_pickerVC) {
        _pickerVC = [[UIImagePickerController alloc] init];
        _pickerVC.delegate = self;
        _pickerVC.modalPresentationStyle = UIModalPresentationOverFullScreen;
        _pickerVC.mediaTypes = @[(NSString *)kUTTypeMovie, (NSString *)kUTTypeVideo];
    }
    return _pickerVC;
}

- (UIView *)panelContainerView {
    if (!_panelContainerView) {
        _panelContainerView = [XYLiveAutoHiddenView new];
        _panelContainerView.clipsToBounds = YES;
        _panelContainerView.hidden = YES;
    }
    return _panelContainerView;
}


- (XYLivePushPrepViewModel *)prepVM {
    if (!_prepVM) {
        _prepVM = [[XYLivePushPrepViewModel alloc] init];
        @weakify(self);
        _prepVM.getService = ^id _Nonnull(Protocol * _Nonnull protocol) {
            @strongify(self);
            return [self.serviceManager getServiceWithProtocol:protocol];
        };
        _prepVM.getRegion = ^id<XYLiveHierarchyRegionProtocol> _Nullable(NSString * _Nonnull key) {
            @strongify(self);
            return [self.hierarchyManager regionWithKey:key];
        };
    }
    return _prepVM;
}

- (XYLivePushPrepGoodsSelectedViewController *)goodsSelectedVC {
    if (!_goodsSelectedVC) {
        _goodsSelectedVC = [XYLivePushPrepGoodsSelectedViewController new];
        [self shopSelectedGoods_setupBlock];
    }
    return _goodsSelectedVC;
}

- (XYLiveSettingPanelViewController *)moreSettingVC {
    if (!_moreSettingVC) {
        _moreSettingVC = [XYLiveSettingPanelViewController new];
        _moreSettingVC.autoDismiss = YES;
        _moreSettingVC.liveStatus = @"preLive";
        [self panel_setupBlock];
    }
    return _moreSettingVC;
}

- (XYLiveShieldWordViewController *)shieldWordVC {
    if (!_shieldWordVC) {
        _shieldWordVC = [[XYLiveShieldWordViewController alloc] init];
        __weak typeof(self) wself = self;
        _shieldWordVC.dismissBlock = ^ {
            [wself capa_showLiveTabMenu:YES];
        };
    }
    return _shieldWordVC;
}

- (XYLivePaidCourseStartLessonViewController *)paidCourseListVC {
    if (!_paidCourseListVC) {
        _paidCourseListVC = [[XYLivePaidCourseStartLessonViewController alloc]init];
        [self paidCourse_setupVcActionBlock];
    }
    return _paidCourseListVC;
}

- (XYLiveScreenSettingViewController *)screenSettingVC {
    if (!_screenSettingVC) {
        _screenSettingVC = [XYLiveScreenSettingViewController new];
        [self screen_setupBlock];
    }
    return _screenSettingVC;
}

- (XYLiveActionRouteManager *)actionRouteManager {
    if (!_actionRouteManager) {
        _actionRouteManager = [[XYLiveActionRouteManager alloc] initWithScheme:@"xhslive"];
    }
    return _actionRouteManager;
}

- (XYLivePushLiveEventManager *)eventManager {
    if (!_eventManager) {
        _eventManager = [[XYLivePushLiveEventManager alloc] init];
    }
    return _eventManager;
}

- (XYAlertItem *)livePreparationLoadingItem {
    if (!_livePreparationLoadingItem) {
        _livePreparationLoadingItem = [XYLiveConfigCenter shouldPrepLoadingBlockUI] ? [XYAlertCenter createLoadingItemWithLoadingMode:XYAlertLoadingModeModel] : [XYAlertCenter createLoadingItemInView:self.view];
    }
    return _livePreparationLoadingItem;
}

- (BOOL)isOBSMode {
    return (self.bizPushStyle == XYLiveRoomBizPushStyleOBS);
}

- (void)setPaidCourseLessonID:(NSString *)paidCourseLessonID {
    _paidCourseLessonID = [paidCourseLessonID copy];
    [self paidCourse_updateOnSettingLessonId:paidCourseLessonID];
}

- (void)updateWithNavTag:(NSInteger)tag {
    [self.privacyTipView updateWithNavTag:tag];
}

- (XYLiveLiveCategorySelectViewController *)categorySelectVC {
    if (!_categorySelectVC) {
        _categorySelectVC = [XYLiveLiveCategorySelectViewController new];
        __weak typeof(self) wself = self;
        _categorySelectVC.selectCategoryBlock = ^(XYLiveCategorySubmodel * _Nonnull category) {
            wself.prepVM.category = category;
        };
    }
    return _categorySelectVC;
}

- (BOOL)isFullScreen {
    return [XYLiveExperimentConfig usePushPrepNewStyle];
}

#pragma mark - VC life

- (UIStatusBarStyle)preferredStatusBarStyle {
    return UIStatusBarStyleLightContent;
}

@end
