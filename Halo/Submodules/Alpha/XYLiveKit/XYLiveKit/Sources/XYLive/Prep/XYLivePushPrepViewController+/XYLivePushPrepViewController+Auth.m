//
//  XYLivePushPrepViewController+Auth.m
//  XYLiveKit
//
//  Created by 周博立 on 2020/9/8.
//  Copyright © 2020 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Auth.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import "XYLiveLinkmicAlertView.h"
#import "XYLiveManager.h"

@import XYLiveUIKit;
@import XYLiveFoundation;
@import XYConfigCenter;
@import XYPrivacy;
@import ReactiveObjC;
@import XYAlphaNetwork;

@implementation XYLivePushPrepViewController (Auth)

#pragma mark - Host Qualification

- (void)auth_cameraPermissionCheckValid:(void (^)(BOOL valid))finished {
    BOOL hasCameraPermission = XYPRPrivacyManager.shared.camera.authorizeStatus == XYPRPrivacyStatusAuthorized;
    BOOL hasMicrophonePermission = XYPRPrivacyManager.shared.microphone.authorizeStatus == XYPRPrivacyStatusAuthorized;
    if (hasCameraPermission && hasMicrophonePermission) {
        [self camera_initSetup];
        [self pusher_handleOnViewDidAppear];
        [self checkQualification];
    } else {
        __weak typeof(self) wself = self;
        BOOL open = XYConfigCenter().boolForKey(@"ios_live_device_privacy_request_front", NO);
        if (open) {
            [self.privacyTipView requestDevicePrivacy:^(BOOL status) {
                if (status) {
                    [wself checkQualificationWithValidCheckPassFinished:^(BOOL valid) {
                        if (finished) {
                            finished(valid);
                        }
                    }];
                } else if (finished) {
                    finished(status);
                }
            }];
        } else {
            [self checkQualificationWithValidCheckPassFinished:^(BOOL valid) {
                if (valid) {
                    [wself.privacyTipView requestDevicePrivacy:^(BOOL status) {
                        if (finished) {
                            finished(status);
                        }
                    }];
                }
            }];
        }
    }
}

- (void)auth_handleCameraAndLiveAuthOnDidAppear {
    self.didEnterCoverSelectPage = NO;
    BOOL config = XYConfigCenter().boolForKey(@"ios_live_privacy_order_change", NO);
    if (config) {
        __weak typeof(self) wself = self;
        [self auth_cameraPermissionCheckValid:^(BOOL valid) {
            if (valid) {
                [wself camera_initSetup];
                [wself pusher_handleOnViewDidAppear];
            }
        }];
    } else {
        [self pusher_handleOnViewDidAppear];
        [self checkQualification];
        [self.privacyTipView requestDevicePrivacy:nil];
    }
}

- (void)fetchHostGoodsAuth {
    __weak typeof(self.prepVM) wPrepVM = self.prepVM;
    @weakify(self)
    [XYLiveManagerSharedInstance fetchCurrentShoppingAuthWithUserID:CURRENT_USER.userId completion:^(XYLiveShoppingAuthInfo * _Nullable auth, NSError * _Nullable error) {
        @strongify(self)
        [wPrepVM updateShopOrSelectionAuth:(auth.goodsAuth.cpsAuth || auth.goodsAuth.sellerAuth)];
        wPrepVM.notifyVCToCheckShopOrSelectionAuthorityEntrance = YES;
        if (wPrepVM.hasShopOrSelectionAuthority) {
            [self shopping_syncSelectedGoods: auth.goodsAuth.goodsOnsaleMaxCount];
        }
    }];
    [self.prepVM fetchRoomAuthCompletion:^(XYLiveRoomAuth * _Nonnull roomAuth, NSError * _Nonnull error) {
        @strongify(self)
        wPrepVM.notifyViewToShowPaidCourseEntrance = roomAuth.extraAuth.hasCourseAuth;
    }];
}

- (void)auth_handleAlertWithBenefit {
    [XYTrackLivePreparePage eventActionID11365ForAuthCardImpressed];
    NSMutableParagraphStyle *paragraphStyle = [NSMutableParagraphStyle new];
    paragraphStyle.alignment = NSTextAlignmentCenter;
    paragraphStyle.maximumLineHeight = 20;
    paragraphStyle.minimumLineHeight = 20;
    NSAttributedString *atStr = [[NSAttributedString alloc] initWithString:@"完成实名认证，开直播获得【薯钻/提现】和海量【官方流量曝光卡】" attributes:@{NSFontAttributeName:Theme.fontLeSmall,NSForegroundColorAttributeName:[Theme.color.grayLevel1 colorWithAlphaComponent:0.5],NSParagraphStyleAttributeName: paragraphStyle}];
    UILabel *tipLabel = [[UILabel alloc] init];
    tipLabel.attributedText = atStr;
    tipLabel.textAlignment = NSTextAlignmentCenter;
    tipLabel.numberOfLines = 0;
    XYLiveCommonAlertView *alertView = [[XYLiveCommonAlertView alloc] init];
    [alertView showinWithContainerView:self.view title:@"每天开播领奖励" letfActionTitle:@"取消" rightActionTitle:@"去认证" tipView:tipLabel];
    alertView.rightCallback = ^() {
        [JLRoutes routeToIdentificationWebview];
    };
}

- (void)checkQualification {
    [self checkQualificationWithValidCheckPassFinished:nil];
}

- (void)checkQualificationWithValidCheckPassFinished:(void (^)(BOOL valid))finished {
    if (self.roomInfo) {
        if (finished) {
            finished(YES);
        }
        return;
    }
    [self.alertView dismissAnimated:NO];
    if (WorldSnakeReachability.share.status == WorldSnakeNotReachable) {
        [XYAlertCenter live_showTextItemWithText:@"请检查网络后重试"];
        if (finished) {
            finished(NO);
        }
        return;
    }

    if (XYConfigCenter().justOnceBoolForKey(@"ios_alpha_optimize_pre_api_order", true)) {
        BOOL fixCrash = XYConfigCenter().boolForKey(@"ios_alpha_optimize_pre_api_order_fix_crash", true);
        self.applyGoodsAuthSubject = [RACSubject subject];
        @weakify(self)
        [XYLiveManagerSharedInstance fetchCurrentShoppingAuthWithUserID:CURRENT_USER.userId completion:^(XYLiveShoppingAuthInfo * _Nullable auth, NSError * _Nullable error) {
            @strongify(self)
            [XYLogCollector xyLiveDebugLogTag:@"applyGoodsAuthSubject" content:[NSString stringWithFormat:@"sendNext %@", auth.goodsAuth.cpsAuth != true && auth.goodsAuth.canApplyCPSAuth ? @"YES" : @"NO"]];
            [self.applyGoodsAuthSubject sendNext:[NSNumber numberWithBool:auth.goodsAuth.cpsAuth != true && auth.goodsAuth.canApplyCPSAuth]];
            if (fixCrash) { self.shouldApplyGoodsAuth = auth.goodsAuth.cpsAuth != true && auth.goodsAuth.canApplyCPSAuth; }
            [self.prepVM.goodsAuthSubject sendNext:[NSNumber numberWithBool:auth.goodsAuth.cpsAuth || auth.goodsAuth.sellerAuth]];
        }];
        if (!fixCrash) { RAC(self, shouldApplyGoodsAuth) = self.applyGoodsAuthSubject; }
        /// Pre
        [self sdk_prepRoomWithExtra:@{@"scence" : @"normal"}];
    } else {
        @weakify(self)
        [XYLiveManagerSharedInstance fetchCurrentShoppingAuthWithUserID:CURRENT_USER.userId completion:^(XYLiveShoppingAuthInfo * _Nullable auth, NSError * _Nullable error) {
            @strongify(self)
            if (auth.goodsAuth.cpsAuth != true && auth.goodsAuth.canApplyCPSAuth) {
                self.shouldApplyGoodsAuth = YES;
            } else {
                self.shouldApplyGoodsAuth = NO;
            }
            [self.prepVM.goodsAuthSubject sendNext:[NSNumber numberWithBool:auth.goodsAuth.cpsAuth || auth.goodsAuth.sellerAuth]];
            /// Pre
            [self sdk_prepRoomWithExtra:@{@"scence" : @"normal"}];
        }];
    }
    if (finished) {
        finished(YES); // 先默认实名完成
    }
}

- (void)fetchLiveAuthorityCompletion:(void (^)(XYLiveAuthorityInfo *authorityInfo, NSError *error))completion {
    [[XYIMNetworkHandler new] reqAppServLiveAuthoritySucc:^(id value, NSError *error) {
        NSString *content = [NSString stringWithFormat:@"resp /api/sns/v1/live/real_name_verify_check: %@, error: %@", value, error];
        [XYLogCollector xyLiveLogTag:@"push_prep" content:content];
        if ([value isKindOfClass:NSDictionary.class]) {
            XYLiveAuthorityInfo *authorityInfo = [XYLiveAuthorityInfo xy_modelWithDictionary:value];
            if (completion) {
                completion(authorityInfo, nil);
            }
        } else {
            NSError *err = [NSError errorWithDomainType:XYLiveIMErrorDomainTypeClient
                                                errCode:XYLiveHttpCustomErrorCodeInvalidDataFormat
                                                 errMsg:XYLiveClientErrorMsg];
            if (completion) {
                completion(nil, err);
            }
        }
    } fail:^(id value, NSError *error) {
        NSString *content = [NSString stringWithFormat:@"resp /api/sns/v1/live/real_name_verify_check: %@, error: %@", nil, error];
        [XYLogCollector xyLiveLogTag:@"push_prep" content:content];
        if (completion) {
            completion(nil, error);
        }
    }];
}

- (void)auth_handleCommAlertOnStartCheckWithDisplayInfo:(XYLivePushPrepStartCheckDisplayInfo *)displayInfo completion:(void (^_Nullable)(BOOL enable))completion {
    XYLiveCommonAlertView *alertView = [XYLiveCommonAlertView new];
    __weak typeof(self) wself = self;
    alertView.rightCallback = ^{
        [XYLivePushPrepStartCheckTrackHelper trackAlertViewConfirmClickedWithHostID:CURRENT_USER.userId pointID:displayInfo.positiveBtnInfo.trackPointID];
        wself.prepVM.notifyVCToPushWithActionLink = displayInfo.positiveBtnInfo.link;
    };
    alertView.leftCallback = ^{
        // 取消时，回调告知是否可以继续开播
        [XYLivePushPrepStartCheckTrackHelper trackAlertViewCancelClickedWithHostID:CURRENT_USER.userId pointID:displayInfo.negativeBtnInfo.trackPointID];
        completion ? completion(displayInfo.negativeBtnInfo.enableResumeStart) : nil;
    };
    
    XYLivePushPrepStartCheckAlertContentView *alertContentView = [XYLivePushPrepStartCheckAlertContentView new];
    
    __weak typeof(alertView) wAlert = alertView;
    alertContentView.onContentImageViewClicked = ^ {
        [XYLivePushPrepStartCheckTrackHelper trackAlertViewContentClickedWithHostID:CURRENT_USER.userId pointID:displayInfo.image.clickedTrackID];
        if (displayInfo.image.jumpLink.length > 0) {
            [wAlert dismiss];
            wself.prepVM.notifyVCToPushWithActionLink = displayInfo.image.jumpLink;
        }
    };
    alertContentView.info = displayInfo;
    
    // 自动埋点绑定
    [alertView.leftButton xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
        return [XYLivePushPrepStartCheckTrackHelper trackContextAlertViewCancelClickedWithHostID:CURRENT_USER.userId pointID:displayInfo.positiveBtnInfo.trackPointID];
    }];
    [alertView.rightButton xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
        return [XYLivePushPrepStartCheckTrackHelper trackContextAlertViewConfirmClickedWithHostID:CURRENT_USER.userId pointID:displayInfo.positiveBtnInfo.trackPointID];
    }];
    [alertContentView.contentImageView xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
        return [XYLivePushPrepStartCheckTrackHelper trackContextAlertViewContentClickedWithHostID:CURRENT_USER.userId pointID:displayInfo.positiveBtnInfo.trackPointID];
    }];
    
    // show
    [alertView showinWithContainerView:[UIApplication sharedApplication].keyWindow title:displayInfo.title letfActionTitle:displayInfo.negativeBtnInfo.title rightActionTitle:displayInfo.positiveBtnInfo.title tipView:alertContentView];
    [XYLivePushPrepStartCheckTrackHelper trackAlertViewImpressedWithHostID:CURRENT_USER.userId pointID:displayInfo.impressionTrackID];
}

- (void)auth_handleCommAlertForTipsOnStartCheckWithDisplayInfo:(XYLivePushPrepStartCheckDisplayInfo *)displayInfo {
    XYLiveCommonAlertView *alertView = [XYLiveCommonAlertView fetchTopBottomAlertView];
    UILabel *label = [UILabel new];
    label.text = displayInfo.subTitle;
    label.font = Theme.fontLeSmall;
    label.numberOfLines = 0;
    label.textAlignment = NSTextAlignmentCenter;
    label.textColor = [XYLiveTokenColor tertiaryLabel];
    __weak typeof(self) wself = self;
    alertView.rightCallback = ^{
        if (displayInfo.positiveBtnInfo.link.length) {
            wself.prepVM.notifyVCToPushWithActionLink = displayInfo.positiveBtnInfo.link;
        }
    };
    alertView.leftCallback = ^{
        if (displayInfo.tipInfo.link.length) {
            wself.prepVM.notifyVCToPushWithActionLink = displayInfo.tipInfo.link;
        }
    };
    [alertView showinWithContainerView:[UIApplication sharedApplication].keyWindow title:displayInfo.title topActionTitle:displayInfo.positiveBtnInfo.title bottomActionTitle:displayInfo.tipInfo.title tipView: label];
}

- (void)auth_checkStartLiveConditionWithRoomID:(NSString *)roomID completion:(void (^_Nullable)(BOOL enable))completion {
    [self.livePreparationLoadingItem show];
    
    __weak typeof(self) wself = self;
    [XYLivePushPrepStartCheckNetwork fetchCheckInfoWithRoomID:roomID contentType:self.prepVM.bizNaviType distribute:!self.prepVM.keepSecret joinLimit:self.prepVM.joinLimit completion:^(XYLivePushPrepStartCheckInfo * _Nullable info, NSError * _Nullable error) {
        [wself.livePreparationLoadingItem hide];
        
        // 违规检测
        if (error.code == XYLiveBizErrorCodeViolation) {
            NSString *msg = [error.userInfo valueForKeyPath:@"XYAPIErrorFailingJsonErrorKey.msg"];
            [XYRouter(RuleDialogService).destination showViolationDialog:msg routePrepareBlock:^{}];
            [XYLogCollector xyLiveLogTag:@"violationAlert" content:@"checkStartLive"];
            return;
        }
        
        NSString *isForceBlock = @"-1";
        NSString *isErrorToPass = @"-1";
        NSString *bizCode = @"";
        if (info) {
            // 弹出提示
            if (!info.enableStart) {
                __auto_type displayInfo = info.displayInfo;
                if ([displayInfo.type isEqualToString:@"toast"]) {
                    [XYAlertCenter live_showTextItemWithText:info.displayInfo.title];
                } else if ([displayInfo.type isEqualToString:@"dialog"]) {
                    [wself auth_handleCommAlertOnStartCheckWithDisplayInfo:displayInfo completion:completion];
                } else if ([displayInfo.type isEqualToString:@"action_link"]) {
                    wself.prepVM.notifyVCToPushWithActionLink = displayInfo.actionLinkUrl;
                } else if ([displayInfo.type isEqualToString:@"tip_dialog"]) {
                    [wself auth_handleCommAlertForTipsOnStartCheckWithDisplayInfo:displayInfo];
                }
                isForceBlock = info.displayInfo.negativeBtnInfo.enableResumeStart ? @"0" : @"1";
                bizCode = info.displayInfo.bizCode;
                [xyLiveSharedAPMManager reportStartCheckTracker:bizCode isForceBlock:isForceBlock isErrorToPass:isErrorToPass errorDescription:error.localizedDescription];
            } else {
                // 无弹窗时，直接回调结果
                completion ? completion(info.enableStart) : nil;
            }
            return;
        }
        isErrorToPass = @"1";
        [xyLiveSharedAPMManager reportStartCheckTracker:bizCode isForceBlock:isForceBlock isErrorToPass:isErrorToPass errorDescription:error.localizedDescription];
        BOOL pass = XYConfigCenter().boolForKey(@"all_live_enable_push_in_start_check_fail", NO);
        if (!pass) {
            [XYAlertCenter live_showTextItemWithText:@"网络异常，请重试"];
        }
        completion ? completion(pass) : nil;
    }];
    
    if (self.applyGoodsAuthSubject) {
        @weakify(self)
        self.applyGoodsAuthDisposable = [self.applyGoodsAuthSubject subscribeNext:^(NSNumber * _Nullable x) {
            [XYLogCollector xyLiveDebugLogTag:@"goodsAuthSubject" content:[NSString stringWithFormat:@"subscribeNext %@", [x boolValue] ? @"YES" : @"NO"]];
            if ([x boolValue]) {
                @strongify(self)
                [XYLiveManagerSharedInstance.shoppingNetworkParser applyDistributionAuthWithAgreementLink:[XYLiveLinkConfig distributionServiceWebPageLink] serviceAgreementLink:[XYLiveLinkConfig distributionWebPageLink] completion:^(id  _Nullable value, NSError * _Nullable error) {
                    if (!error) {
                        [XYLiveManager sharedManager].shoppingAuth.goodsAuth.cpsAuth = YES;
                    }
                }];
            }
        } error:^(NSError * _Nullable error) {}];
    } else if (self.shouldApplyGoodsAuth) {
        [XYLiveManagerSharedInstance.shoppingNetworkParser applyDistributionAuthWithAgreementLink:[XYLiveLinkConfig distributionServiceWebPageLink] serviceAgreementLink:[XYLiveLinkConfig distributionWebPageLink] completion:^(id  _Nullable value, NSError * _Nullable error) {
            if (!error) {
                [XYLiveManager sharedManager].shoppingAuth.goodsAuth.cpsAuth = YES;
            }
        }];
    }
}

@end
