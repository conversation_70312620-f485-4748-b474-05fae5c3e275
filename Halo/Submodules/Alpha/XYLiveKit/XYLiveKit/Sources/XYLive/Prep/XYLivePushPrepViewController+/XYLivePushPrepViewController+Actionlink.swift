//
//  XYLivePushPrepViewController+Actionlink.swift
//  XYLiveKit
//
//  Created by quhe on 2022/8/8.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation
import XYPHConfig
import XYPostPublicModule

public
extension XYLivePushPrepViewController {
    @objc
    func presetupActionLinkRegister() {
        let res = XYLiveLinkConfig.applyActionLink()
        if !res {
            return
        }
        self.actionRouteManager?.fallbackCallback = {[weak self] urlStr in
            if urlStr.isEmpty {
                return
            }
            if urlStr.hasPrefix("http") {
                self?.prepVM.notifyVCToShowHalfWebviewWithURL = urlStr
                return
            }
            JLRoutes.routeURL(URL(string: urlStr))
        }
        self.registerActionLinkCore()
    }
    
    func registerActionLinkCore() {
        self.actionRouteManager?.register(path: "prelive", action: {[weak self] _, param in
            guard let type = param["open_type"] as? String else {
                return
            }
            
            switch type {
            case "livenotice":
                self?.prepVM.notifyVCToShowForenotice = true
            case "beauty":
                self?.prepVM.notifyVCToPresentBeautyPanel = true
            case "more":
                self?.prepVM.notifyVCToShowMore = true
            case "goods":
                self?.prepVM.notifyVCToShowShoppingList = true
            case "liveclass":
                self?.prepVM.notifyVCToShowPaidCourseList = true
            case "uploadAlbum":
                self?.prepVM.notifyViewToSelectCoverView = true
            default:
                break
            }
        })
        
        self.actionRouteManager?.register(path: "web") { [weak self] _, param in
            guard let self = self, let url = param["url"] as? String else { return }
            if let type = param["type"] as? String, type == "1" {
                self.prepVM.notifyVCToShowSubHalfWebviewWithURL = url
            } else {
                self.prepVM.notifyVCToShowHalfWebviewWithURL = url
            }
        }
        
        self.actionRouteManager?.register(path: "silent_web") { [weak self] _, param in
            guard let self = self, let url = param["url"] as? String else { return }
            if let type = param["type"] as? String, type == "1" {
                self.prepVM.notifyVCToPresentSilentWebURL = url
            } else {
                self.prepVM.notifyVCToShowSilentWebURL = url
            }
        }
        
        self.actionRouteManager?.register(path: "web_full") { _, param in
            guard let url = param["url"] as? String else { return }
            JLRoutes.routeURL(URL(string: url))
        }
        
        self.actionRouteManager?.register(path: "open_market") { _, _ in
            guard let url = URL(string: "itms-apps://itunes.apple.com/app/id\(APP_ID)") else {
                return
            }
            UIApplication.shared.open(url, options: [:], completionHandler: nil)
        }
        
        self.actionRouteManager?.register(path: "share") { [weak self]  _, _ in
            guard let self = self else { return }
            self.shareService()?.loadSharingVChWitIsPrePush(true)
        }
    }
    
    private func shareService() -> XYLiveCommonShareServiceProtocol? {
        self.serviceManager?.getServiceWith(XYLiveCommonShareServiceProtocol.self) as? XYLiveCommonShareServiceProtocol
    }
}

extension XYLivePushPrepViewController {
    /// 是否开启 AI模型预下载实验：代码固化，实验默认值修改成 true
    @objc public static func aiModelDownloaderUpdateEnable() -> Bool {
        return Experiment.shared.justOnceBool(forKey: "ios_capa_ai_model_downloader_update_enable", defalut: true) == true
    }
}
