//
//  XYLivePushPrepViewController+Shopping.m
//  XYLiveKit
//
//  Created by 周博立 on 2021/1/28.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
@import KVOController;
@import XYLiveFoundation;
@import XYLiveUIKit;

@implementation XYLivePushPrepViewController (Shopping)

- (void)shopping_syncSelectedGoods:(NSInteger)pageSize {
    if (self.planId.length > 0) {
        @weakify(self)
        [XYLivePlanNetworkDataParser linkPlanWithHostID:CURRENT_USER.userId planID:self.planId completion:^(BOOL success, NSError * _Nullable error) {
            @strongify(self)
            if (success) {
                [self.prepVM syncServiceSelectedGoodsModels:pageSize];
            }
        }];
    } else {
        [self.prepVM syncServiceSelectedGoodsModels:pageSize];
    }
}

- (void)shopSelectedGoods_setupBlock {
    @weakify(self)
    self.goodsSelectedVC.didUpdateSelectedGoodsItems = ^(NSArray <XYLiveShoppingGoodsModel *> *selectedGoodsItems) {
        @strongify(self)
        [self.prepVM updateCurrentSelectedGoodsModels:selectedGoodsItems];
    };
    self.goodsSelectedVC.showQuickSelectClosure = ^{
        [JLRoutes routeToQuickSelectionGoodsPage];
    };
    self.goodsSelectedVC.showSelectCenterClosure = ^{
        [JLRoutes routeToSelectionGoodsPage];
    };
    self.goodsSelectedVC.dismissBlock = ^{
        @strongify(self)
        // 展示底部栏
        [self capa_showLiveTabMenu:YES];
        // 尝试添加挂起的话题活动
        if (self.prepVM.selectedGoodsModels.count > 0) {
            id<XYLivePushPrepGambitNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushPrepGambitNodeServiceProtocol)];
            [service resumePendingGambitIfNeeded];
        }
        
    };
}

@end
