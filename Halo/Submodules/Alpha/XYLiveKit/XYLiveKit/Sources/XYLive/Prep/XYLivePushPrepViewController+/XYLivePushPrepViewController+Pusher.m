//
//  XYLivePushPrepViewController+Pusher.m
//  XYLiveKit
//
//  Created by 周博立 on 2020/9/8.
//  Copyright © 2020 XingIn. All rights reserved.
//

#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import "XYIMComm.h"
#import <XYLiveKit/XYLiveKit-Swift.h>

@import XYConfigCenter;
@import XYCamera;
@import XYLiveFoundation;

@implementation XYLivePushPrepViewController (Pusher)

- (void)camera_initSetup {
    [XYLiveMediaControl reloadAudioSessionWithReceiver:self sceneType:XYLiveAudioSessionScenePush];
    [XYLiveManagerSharedInstance.coreManager.media setup];
    if (XYConfigCenter().boolForKey(@"ios_live_push_prep_wide_angle_default", YES) && ![XYAlphaSwitch cameraPreviewControlEnable]) {
        [XYLiveManagerSharedInstance setupUltraWideAngle:NO];
    }
}

- (void)pusher_setupChannelSession {
    // fix for capa：异步进入此方法时如页面已切走，则等下次页面展现（viewDidAppear）再获取session
    if ([XYLiveLinkConfig usePrepSessionReleaseStrategy]) {
        if (!self.shown) {
            return;
        }
        if (!self.roomInfo) {
            return;
        }
    }
    
    if ([self canApplyRTC]) {
        self.rtcSession = [RTCChannelSession sessionWithRoomInfo:self.roomInfo userInfo:self.loginParam.userInfo passport:XYLiveManagerSharedInstance.imConfigParam.passport role:RTCMemberRoleHost audienceRtcLinkReference:XYLiveManagerSharedInstance.linkmicManager.audiencePushProtocolType];
        [self.netDetector startProbingWithChannelSession:self.rtcSession];
        RTCVideoEncodeInfo *encodeInfo = nil;
        /// https://experiment.devops.xiaohongshu.com/#/lumos/detail/15659 推流调度实验
        if (self.roomInfo.pushDispatchInfo.defaultResolution && XYAlphaSwitch.livePushDispatch) {
            encodeInfo = [RTCVideoEncodeInfo encodeInfoWithDispatchInfo:self.roomInfo.pushDispatchInfo vendorType:self.rtcSession.vendorType];
        } else {
            encodeInfo = [RTCVideoEncodeInfo encodeInfoWithResolution:self.roomInfo.encodedResolution vendorType:self.rtcSession.vendorType];
        }
        [XYLiveManagerSharedInstance.coreManager.media setupRoomId: self.roomInfo.roomIDStr];
        [XYLiveManagerSharedInstance.coreManager.media setupAnchorId: self.roomInfo.hostInfo.userID];
        [XYLiveManagerSharedInstance.coreManager.media reloadCameraPreset:encodeInfo];
        [self updateApmV2BasicParams];
        [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventRoomCreate recovery:0 encodeInfo:encodeInfo];
    } else if ([self canApplyKasa]) {
        // kasaRtc 开播 不需要设置音频采集 camera设置走 trtc配置
        XYLiveManagerSharedInstance.coreManager.media.cameraType = XYLiveRTCVendorExpTypeTencent;
        [self updateApmKasaParam];
        [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventRoomCreate recovery:0 encodeInfo:[XYLiveKasaPusher presetVideoEncodeInfo]];
        
    } else {
        // rtmp 开播 根据resolution读取配置获得码率 fps等信息
        XYLiveManagerSharedInstance.coreManager.media.cameraType = XYLiveRTCVendorExpTypeRtmp;
        [self updateApmRtmpParam];
        [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventRoomCreate recovery:0 encodeInfo:[XYLiveRtmpPusher presetVideoEncodeInfo]];
    }
}

- (void)camera_previewSetup {
    if (XYConfigCenter().boolForKey(@"ios_live_filter_camera_preview", NO)) {
        if ([self relateToGroupChat]) {
            return;
        }
    }
    
    /// 封面拍摄重置
    // 重置预览UI
    if (self.cameraView.superview != self.view) {
        [self.view insertSubview:self.cameraView belowSubview:self.containerView];
        [self setup_layoutBaseUI];
    }
    
    if (XYLiveConfigCenter.preSupportNewTransition) {
        [self.cameraView layoutRenderViewIfNeed];
    }
    
    // 美颜 & 滤镜 & 预览
    XYLiveFilterConfigModel *filter = [XYLiveFilterConfigManager fetchLatestUserFilterConfigModel];
    XYCSBeautifyConfig *config = [XYLiveBeautyConfigManager loadBeautifyConfigFromUserDefaults];
    XYCSBeautifyConfig *beauty = config;
    if ([XYLiveBeautyConfigManager isBeautyConfigCleared] && ![XYLiveBeautyConfigManager isFirstLaunch]) {
        beauty = [XYCSBeautifyConfig new];
    }
    beauty.levelOrContrastType = [XYLiveBeautyConfigManager fetchQualitySwitchStatus] ?config.levelOrContrastType :0;
    beauty.levelStrength = [XYLiveBeautyConfigManager fetchQualitySwitchStatus] ?config.levelStrength :0;
    beauty.contrastStrength = [XYLiveBeautyConfigManager fetchQualitySwitchStatus] ?config.contrastStrength :0;
    beauty.denoiseStrength = [XYLiveBeautyConfigManager fetchQualitySwitchStatus] ?config.denoiseStrength :0;
    
    BOOL imprv = XYConfigCenter().boolForKey(@"ios_live_camera_imprv", NO);
    if (imprv) {
        [XYLiveManagerSharedInstance.coreManager.media applyFilter:[filter generateAVFilter]];
        [XYLiveManagerSharedInstance.coreManager.media refreshBeautyComboWithConfig:beauty];
    }
    
    XYLivePushResolutionReference res = XYLivePushResolutionReferenceUltra;
    if ([XYLiveDevice deviceLevel] == XYLiveDeviceLevelLow) {
        res = XYLivePushResolutionReferenceHigh;
    }
    
    if ([XYLiveDevice deviceLevel] == XYLiveDeviceLevelMiddle) {
        res = XYLivePushResolutionReferenceSuper;
    }
    
    if (self.roomInfo != nil && self.roomInfo.encodedResolution != XYLivePushResolutionReferenceUndefined) {
        res = self.roomInfo.encodedResolution;
    }
    [XYLiveManagerSharedInstance.coreManager.media setupVideoLiveScene:YES];
    [XYLiveManagerSharedInstance.coreManager.media startPreview:self.cameraView.renderView resolution:res];
    if ([XYAlphaSwitch cameraPreviewControlEnable]) {
        if (self.bizNaviType == XYLivePushPrepBizNaviTypeVideo) {
            id<XYLivePrepVideoNodeServiceProtocol> videoService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePrepVideoNodeServiceProtocol)];
            [videoService resumeZoom];
        }
    }
    
    if (!imprv) {
        [XYLiveManagerSharedInstance.coreManager.media applyFilter:[filter generateAVFilter]];
        [XYLiveManagerSharedInstance.coreManager.media refreshBeautyComboWithConfig:beauty];
    }
}

- (void)camera_stop {
    [XYLiveManagerSharedInstance.coreManager.media stopPreview];
    [XYLiveManagerSharedInstance.coreManager.media releaseCamera];
}

- (void)camera_stopPreview {
    if (self.prepVM.notifyClickPushPreViewCloseBtn) {
        // 关闭预播页面后不处理，否则会重新创建实例
        return;
    }
    
    if (self.didEnterCoverSelectPage) {
        // 进入封面页后可能复用preview，暂不处理
        return;
    }
    
    [XYLiveManagerSharedInstance.coreManager.media stopPreview];
}

- (void)pusher_handleOnViewDidAppear {
    [self logPusherTypeFrom:@"vda"];
    [self camera_previewSetup];
    if ([XYLiveLinkConfig usePrepSessionReleaseStrategy]) {
        [self pusher_setupChannelSession];
    }
}

- (void)prep_handleOnViewDidDisappear {
    [self logPusherTypeFrom:@"vdd"];
}

- (void)prep_handleOnViewWillAppear {
    [self logPusherTypeFrom:@"vwa"];
}

- (void)prep_handleOnTouchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    UITouch *touch = touches.anyObject;
    if ([touch.view isDescendantOfView:self.prepView]) {
        CGPoint point = [touch locationInView:self.cameraView.renderView];
        [self logPusherTypeFrom:@"touchesEnded"];
        if ([XYAlphaSwitch cameraPreviewControlEnable]) {
            if (self.bizNaviType == XYLivePushPrepBizNaviTypeVideo) {
                id<XYLivePrepVideoNodeServiceProtocol> videoService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePrepVideoNodeServiceProtocol)];
                [videoService showFocusViewWithTouchPoint:point touch: touch];
            }
        } else {
            [XYLiveManagerSharedInstance.coreManager.media setCameraFocusWithTouchPoint:point];
        }
    }
}

- (void)logPusherTypeFrom:(NSString *)source {
    if (!self.roomInfo) { return;}
    [XYLogCollector xyLiveLogTag:@"rtc" content:[NSString stringWithFormat:@"%@ rtc_switch: %@", source, @(self.roomInfo.pushType)]];
}

@end

