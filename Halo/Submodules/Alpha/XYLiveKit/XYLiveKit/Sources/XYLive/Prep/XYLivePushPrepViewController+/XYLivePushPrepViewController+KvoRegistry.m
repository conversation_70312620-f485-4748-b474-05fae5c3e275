//
//  XYLivePushPrepViewController+KvoRegistry.m
//  XYLiveKit
//
//  Created by 周博立 on 2021/9/25.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushPrepViewController.h"
#import "XYLivePushPrepPrivateExt+Core.h"
#import "XYLivePushPrepPrivateExt+Recovery.h"
#import "XYTrackLiveBroadcastPage.h"
#import "XYLivePushSelectCoverView.h"
#import "XYLiveEnterConfig.h"

@import XYLiveFoundation;
@import XYConfigCenter;
@import XYEvolver;
@import XYLiveFoundation;
@import XYLiveCore;

@import KVOController;

@import XYPrivacy;

@implementation XYLivePushPrepViewController (KvoRegistry)

- (void)setupKVO {
    __weak typeof(self) wself = self;
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyStartEditTitle)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [XYTrackLivePreparePage eventActionID2961WithClickItemName:@"title"];
    }];
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyClickPushPreViewCloseBtn)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if ([XYAlphaSwitch enablePushMultiLevelV2]) {
            [[KasaLive Instance] stopEchoTest];
            [[KasaLive Instance] terminate];
            [XYLogCollector xyLiveLogTag:@"live_multi_level" content:@"stop echo test by close prep"];
        }
        if (wself.eventActing) {
            return;
        }
        wself.eventActing = YES;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            wself.eventActing = NO;
        });
        XYExecuteOnMainQueue(^{
            [XYTrackLivePreparePage eventActionID2961WithClickItemName:@"cancel"];
            BOOL config = XYConfigCenter().boolForKey(@"ios_live_privacy_order_change", NO);
            if (config) {
                XYPRCameraPrivacyManager *pm = XYPRCameraPrivacyManager.new;
                XYPRPrivacyStatus stats = pm.authorizeStatus;
                if (stats == XYPRPrivacyStatusAuthorized) {
                    [wself camera_stop];
                }
            } else {
                [wself camera_stop];
            }
            BOOL nonStop = XYConfigCenter().boolForKey(@"ios_live_manager_dealloc_non_stop", NO);
            if (nonStop) {
                [XYLiveManagerSharedInstance onDealloc];
            } else {
                [XYLiveManagerSharedInstance destroyWithNotifyServer:YES onPrepRoom:YES];
            }
            
            if (wself.presented && wself.presentingViewController) {
                [wself dismissLivePrepPushViewAnimated:YES completion:nil];
            } else {
                [wself.navigationController popViewControllerAnimated:YES];
            }
        });
    }];
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToFlipCamera)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself eventActionID2961WithClickItemName:@"switch_camera" status:@""];
        [XYLogCollector xyLiveLogTag:@"rtc" content:[NSString stringWithFormat:@"flip_camera rtc_switch: %@", @(wself.roomInfo.pushType)]];
        [XYLiveManagerSharedInstance.coreManager.media switchCamera];
    }];
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToShowMore)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself capa_showLiveTabMenu:NO];
        [wself.moreSettingVC showHostPrepMorePanelIn:wself moreItems:[wself.prepVM moreItems] moreTitle:nil];
        [XYTrackLivePreparePage eventActionID2961WithClickItemName:@"more"];
    }];
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToPresentBeautyPanel)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [XYTrackLivePreparePage eventActionID2961WithClickItemName:@"beauty"];
        XYLiveEffectType panelStyle = XYLiveEffectTypeBeauty;
        if ([XYLiveEnterConfig liveFilterEnter]) {
            panelStyle = panelStyle | XYLiveEffectTypeFilter;
        }
        if ([XYLiveEffectStrategyCenter enablePictureQuality]) {
            panelStyle = panelStyle | XYLiveEffectTypeQuality;
        }
        if ([XYLiveEnterConfig liveMakeupEnter] && [XYLiveEffectStrategyCenter enableAutoLinkStyleMakeup]) {
            panelStyle = panelStyle | XYLiveEffectTypeStyleMakeup;
        }
        if ([XYLiveEnterConfig liveTexiaoEnter]) {
            panelStyle = panelStyle | XYLiveEffectTypeTexiao;
        }
        
        XYLiveEffectViewController *vc = [[XYLiveEffectViewController alloc] initWithStyle:panelStyle target:XYLiveEffectTypeBeauty fromPrep:true];
        vc.delegate = (id<XYLiveEffectSettingViewControllerDelegate>)wself;
        vc.takeClickAutoPoint = ^id _Nonnull {
            return [XYTrackLivePreparePage auto_eventContextID38638];
        };
        vc.takeExposurePoint = ^{
            [XYTrackLivePreparePage eventExposureID38637];
        };
        CGFloat height = 238 + (panelStyle & XYLiveEffectTypeStyleMakeup ? 50 : 0);
        [wself live_presentWithSubVc:vc height:height + [UIApplication safeAreaBottom] clearBackground:YES backgroundTapCallback:^{
            [XYLiveEffectViewController logEffectConfigForScene:@"scene_push_prep"];
        } dismiss:^{
            wself.prepView.alpha = 1;
            [wself capa_showLiveTabMenu:YES];
        } completion:nil];
        wself.prepView.alpha = 0;
        [wself capa_showLiveTabMenu:NO];
    }];
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToPresentEffectPanel)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        XYLiveEffectType panelStyle = XYLiveEffectTypeBeauty;
        if ([XYLiveEnterConfig liveFilterEnter]) {
            panelStyle = panelStyle | XYLiveEffectTypeFilter;
        }
        if ([XYLiveEffectStrategyCenter enablePictureQuality]) {
            panelStyle = panelStyle | XYLiveEffectTypeQuality;
        }
        if ([XYLiveEnterConfig liveMakeupEnter]) {
            panelStyle = panelStyle | XYLiveEffectTypeStyleMakeup;
        }
        if ([XYLiveEnterConfig liveBodyEnter]) {
            panelStyle = panelStyle | XYLiveEffectTypeBeautyBody;
        }
        if ([XYLiveEnterConfig liveTexiaoEnter]) {
            panelStyle = panelStyle | XYLiveEffectTypeTexiao;
        }
        
        XYLiveEffectViewController *vc = [[XYLiveEffectViewController alloc] initWithStyle:panelStyle target:XYLiveEffectTypeBeauty fromPrep:true];

        vc.delegate = (id<XYLiveEffectSettingViewControllerDelegate>)wself;
        vc.takeClickAutoPoint = ^id _Nonnull {
            return [XYTrackLivePreparePage auto_eventContextID38638];
        };
        vc.takeExposurePoint = ^{
            [XYTrackLivePreparePage eventExposureID38637];
        };
        CGFloat height = 238 + (panelStyle & XYLiveEffectTypeStyleMakeup ? 50 : 0);
        [wself live_presentWithSubVc:vc height:height + [UIApplication safeAreaBottom] clearBackground:YES backgroundTapCallback:nil dismiss:^{
            wself.prepView.alpha = 1;
            [wself capa_showLiveTabMenu:YES];
        } completion:nil];
        wself.prepView.alpha = 0;
        [wself capa_showLiveTabMenu:NO];
        [wself eventActionID2961WithClickItemName:@"filter" status:@""];
    }];
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToBeginLive)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        
        // flag
        XYLivePushPrepParamCache.didTriggerStartLiveSuccess = YES;
        
        // toast && log
        [wself checkGoodsToast];
        
        if (wself.eventActing) {
            [XYLogCollector xyLiveLogTag:@"push_prep_start" content:@"eventActing return"];
            return;
        }
        
        // action
        if (wself.bizPushStyle == XYLiveRoomBizPushStyleOBS) {
            wself.bizPushStyle = XYLiveRoomBizPushStyleNormal;
        }
        
        if (!wself.isVoiceLive && !wself.isScreenLive && !wself.isChatLive) {
            wself.prepView.hidden = YES;
        }
        [wself startLiveButtonPressedAction];
    }];
    
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyAlbumWillClosed)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        /// 封面拍摄重置
        // 重置预览UI
        if (wself.cameraView.superview != wself.view) {
            [wself.view insertSubview:wself.cameraView belowSubview:wself.containerView];
            [wself setup_layoutBaseUI];
        }
    }];
    
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToSelectCover)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (wself.eventActing) {
            return;
        }
        wself.eventActing = YES;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            wself.eventActing = NO;
        });
        
        [XYTrackLivePreparePage eventActionID2961WithClickItemName:@"cover"];
        // 半层样式
        NSDictionary *dict = XYConfigCenter().dictionaryForKey(@"all_prelive_good_cover_url", nil);
        NSString *webUrl = nil;
        if ([dict valueForKey:@"platform_i"]) {
            webUrl = dict[@"platform_i"];
        }
        if ((wself.roomInfo.highQualityCoverFlag == 0 || (!wself.prepVM.coverImage && !wself.prepVM.lastCoverURL)) && webUrl) {
            wself.prepVM.notifyVCToShowHalfWebviewWithURL = webUrl;
        } else {
            [XYLivePushSelectCoverView showInVC:wself 
                                      cropShape:XYPhotosPickCropShapeRectangleRate4To3
                                   coverImage:nil
                                      coverUrl:wself.roomInfo.lastCoverURL
                                       coverTip:@"当前封面"
                                   enableCamera:YES resultImage:^(UIImage * _Nonnull image, NSString * _Nonnull imagePath) {
                [wself updateCoverWithLocalImage:image localPath:imagePath];
            } cancelBlock:nil];
        }
    }];
    
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToSelectCategory)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself capa_showLiveTabMenu:NO];
        [wself.categorySelectVC showIn:wself
                              naviType:wself.prepVM.bizNaviType
                                   obs:(wself.prepVM.bizNaviType == XYLivePushPrepBizNaviTypePC)
                     seletctedCategory:wself.prepVM.category
                               dismiss:^{
            [wself capa_showLiveTabMenu:YES];
        }];
    }];
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToSelectVisible)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself showSelectVisibleVC];
    }];
    
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToSelectLocation)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself showSelectLocationVC];
    }];
    
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToShowShoppingList)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [XYLiveManagerSharedInstance fetchCurrentShoppingAuthWithUserID:CURRENT_USER.userId completion:^(XYLiveShoppingAuthInfo * _Nullable auth, NSError * _Nullable error) {
            if (auth.goodsAuth.sellerAuth || auth.goodsAuth.cpsAuth) {
                [wself capa_showLiveTabMenu:NO];
                [wself.goodsSelectedVC showWithVc:wself shoppingAuth:auth selectdGoodsItem:wself.prepVM.selectedGoodsModels];
            } else if (auth.goodsAuth.canApplyCPSAuth) {
                [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"kGoodsCPSRedDotShownKey"];
                wself.prepVM.notifyViewToReloadFuncPanel = YES;
                NSString *URLStr = [XYLiveLinkConfig preLiveCPSApplyLink];
                wself.prepVM.notifyVCToShowHalfWebviewWithURL = URLStr;
            }
        }];
    }];
    
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToSelecteShare)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        NSString *shareItemName = nil;
        switch (wself.prepVM.selectedShareType) {
            case XYLiveShareTypeNone:
                shareItemName = @"share_cancel";
                break;
            case XYLiveShareTypeWeChat:
                shareItemName = @"share_to_wechat_user_link_wx_mp";
                break;
            case XYLiveShareTypeFriendMoment:
                shareItemName = @"share_to_wechat_timeline";
                break;
            case XYLiveShareTypeWeibo:
                shareItemName = @"share_to_weibo";
                break;
            case XYLiveShareTypeQQ:
                shareItemName = @"share_to_qq_user";
                break;
            case XYLiveShareTypeQZone:
                shareItemName = @"share_to_qzone";
                break;
            default:
                break;
        }
        [XYTrackLivePreparePage eventActionID2961WithClickItemName:shareItemName];
    }];
    
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToShowPaidCourseList)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        wself.paidCourseListVC.paidCourseLessonID = wself.paidCourseLessonID;
        [wself.paidCourseListVC showInViewController:wself];
    }];
    
    
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToShowHalfWebviewWithURL)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (wself.prepVM.notifyVCToShowHalfWebviewWithURL.length > 0) {
            [wself capa_showLiveTabMenu:NO];
            [XYLiveWebViewController showDefaultWebViewControllerWithURL:[wself processWithBaseParametersForURLStr:wself.prepVM.notifyVCToShowHalfWebviewWithURL] 
                                                        inViewController:wself
                                                         backgroundAlpha:0
                                                                animated:YES
                                                              completion:nil
                                                               dismissed:^{
                [wself capa_showLiveTabMenu:YES];
            }];
        }
    }];
    
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToShowSubHalfWebviewWithURL)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (wself.prepVM.notifyVCToShowSubHalfWebviewWithURL.length > 0) {
            [XYLiveWebViewController showDefaultWebViewControllerWithURL:[wself processWithBaseParametersForURLStr:wself.prepVM.notifyVCToShowSubHalfWebviewWithURL]
             inViewController:wself
             backgroundAlpha:0
             animated:YES
             completion:nil
             dismissed:nil];
        }
    }];
    
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToShowHalfWebviewOnTopWithURL)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (wself.prepVM.notifyVCToShowHalfWebviewOnTopWithURL.length > 0) {
            [XYLiveWebViewController showDefaultWebViewControllerWithURL:[wself processWithBaseParametersForURLStr:wself.prepVM.notifyVCToShowHalfWebviewOnTopWithURL]
                                                        inViewController:wself.xy_topViewController
                                                         backgroundAlpha:0
                                                                animated:YES
                                                              completion:nil
                                                               dismissed:nil];
        }
    }];
    
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToPushWebviewWithURL)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (wself.prepVM.notifyVCToPushWebviewWithURL.length > 0) {
            [JLRoutes routeURL:[NSURL URLWithString:wself.prepVM.notifyVCToPushWebviewWithURL]];
        }
    }];
    
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToPresentSilentWebURL)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        NSString *URLStr = wself.prepVM.notifyVCToPresentSilentWebURL;
        if (URLStr.length > 0) {
            [wself showSilentWebVCWithAddParamUrl:URLStr];
        }
    }];
    
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToShowSilentWebURL)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        NSString *URLStr = wself.prepVM.notifyVCToShowSilentWebURL;
        if (URLStr.length > 0) {
            [wself showSilentWebVCOnSelfWithAddParamUrl:URLStr];
        }
    }];
    
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyVCToPushWithActionLink)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (wself.prepVM.notifyVCToPushWithActionLink.length > 0) {
            [wself.actionRouteManager performWith:wself.prepVM.notifyVCToPushWithActionLink];
        }
    }];
    [self.KVOController observe:self.prepVM keyPath:NSStringFromSelector(@selector(notifyViewToSelectCoverView)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [XYLivePushSelectCoverView showInVC:wself 
                                  cropShape:XYPhotosPickCropShapeRectangleRate4To3
                               coverImage:nil
                                  coverUrl:wself.roomInfo.lastCoverURL
                                   coverTip:@"上次封面"
                               enableCamera:YES
                                resultImage:^(UIImage * _Nonnull image, NSString * _Nonnull imagePath) {
            [wself updateCoverWithLocalImage:image localPath:imagePath];
        } cancelBlock:nil];
    }];
    
    [self funcPanel_setupKVO];
    [self forenotice_setupKVO];
    [self preCheck_setupKVO];
}



- (void)checkGoodsToast {
    if (self.prepVM.selectedGoodsModels.count > 0) {
        // 存在商品
        switch (self.prepVM.bizNaviType) {
            case XYLivePushPrepBizNaviTypeGame:
                if (XYLivePushPrepParamCache.showScreenLiveShoppingToast) {
                    return;
                }
                if (![XYLiveManagerSharedInstance.shoppingAuth.goodsAuth supportScreenCapShopping]) {
                    [XYAlertCenter live_showTextItemWithText:@"手游直播暂不支持展示商品"];
                    XYLivePushPrepParamCache.showScreenLiveShoppingToast = YES;
                }
                break;
            default:
                break;
        }
    }
}

- (void)eventActionID2961WithClickItemName:(NSString *)tabName status:(NSString *)status {
    XYLivePushPrepViewModel *viewModel = self.prepVM;
    NSString *liveOrChat = @"live";
    if (viewModel.bizNaviType == XYLivePushPrepBizNaviTypeRHVoice) {
        liveOrChat = @"chat";
    }
    [XYTrackLivePreparePage eventActionID2961WithChannelTabName:tabName tabID:viewModel.channelTabID liveOrChat:liveOrChat channelTabIndex:0 status:status];
}

@end

