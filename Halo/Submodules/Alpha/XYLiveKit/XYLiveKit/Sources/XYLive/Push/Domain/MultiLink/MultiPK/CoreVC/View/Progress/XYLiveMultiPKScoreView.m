//
//  XYLiveMultiPKScoreView.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/13.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiPKScoreView.h"
#import <Masonry/Masonry.h>
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>

@interface XYLiveMultiPKScoreView()

@property (nonatomic, strong) XYLiveGradientView2 *gradientView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *scoreLabel;


@end

@implementation XYLiveMultiPKScoreView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    // 创建渐变视图
    [self setupGradientView];
    // 创建标题
    [self setupTitleLabel];
    // 创建分数
    [self setupScoreLabel];
}

#pragma mark - 创建子视图

- (void)setupGradientView {
    XYLiveGradientView2 *gradientView = [[XYLiveGradientView2 alloc] init];
    gradientView.gradientLayer.locations = @[@0, @1];
    gradientView.gradientLayer.startPoint = CGPointMake(0, 0.5);
    gradientView.gradientLayer.endPoint = CGPointMake(1.0, 0.5);
    [self addSubview:gradientView];
    self.gradientView = gradientView;
}

- (void)setupTitleLabel {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.textColor = [XYLiveTokenColor white];
    titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:11];
    [self addSubview:titleLabel];
    self.titleLabel = titleLabel;
}

- (void)setupScoreLabel {
    UILabel *scoreLabel = [[UILabel alloc] init];
    scoreLabel.textColor = [XYLiveTokenColor white];
    scoreLabel.font = [UIFont fontWithName:@"AvenirNext-BoldItalic" size:14];
    scoreLabel.text = @"0";
    [self addSubview:scoreLabel];
    self.scoreLabel = scoreLabel;
}

@end

@implementation XYLiveMultiPKLeftScoreView

- (void)setupSubviews {
    [super setupSubviews];
    
    // 布局
    [self.gradientView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.left.equalTo(self).offset(10);
    }];
    
    [self.scoreLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel.mas_right).offset(4);
        make.centerY.equalTo(self.titleLabel);
    }];
}

@end


@implementation XYLiveMultiPKRightScoreView

- (void)setupSubviews {
    [super setupSubviews];
    
    // 布局
    [self.gradientView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.right.equalTo(self).offset(-10);
    }];
    
    [self.scoreLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.titleLabel.mas_left).offset(-4);
        make.centerY.equalTo(self.titleLabel);
    }];
}

@end
