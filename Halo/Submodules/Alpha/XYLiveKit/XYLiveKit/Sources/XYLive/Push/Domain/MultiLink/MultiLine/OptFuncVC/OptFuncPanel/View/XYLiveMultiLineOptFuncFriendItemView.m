//
//  XYLiveMultiLineOptFuncFriendItemView.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/22.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineOptFuncFriendItemView.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYWebImage/UIImageView+XYWebImage.h>
#import <XYLiveFoundation/UIImage+XYLiveKit.h>
#import <XYUIKitCore/XYUIKitCore.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <Masonry/Masonry.h>

// 重用标识符
static NSString *const kXYLiveMultiLineFuncFriendItemSubCellIde = @"XYLiveMultiLineFuncFriendItemSubCell";

@implementation XYLiveMultiLineFuncFriendItemSubView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

// 数据绑定
- (void)bindListItem:(XYLiveLinkHostInviteeInfo *)listItem {
    // 更新头像
    [self.avatarImgView xy_setImageWithURL:[NSURL URLWithString:listItem.avatarUrl ?: @""]];
    // 更新昵称
    self.nickNameLbl.text = listItem.nickName;
    // 更新邀请中状态
    self.borderView.hidden = !listItem.isInvited;
    self.cancelBtn.hidden = !listItem.isInvited;
    self.invitedMaskView.hidden = !listItem.isInvited;
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 创建边框
    [self setupBorderView];
    // 创建头像
    [self setupAvatarImgView];
    // 创建邀请中
    [self setupInvitedMaskView];
    // 创建昵称
    [self setupNickNameLbl];
    // 创建取消按钮
    [self setupCancelBtn];
    
    // 布局
    [self.borderView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.top.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(54, 54));
    }];
    
    [self.avatarImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.borderView);
        make.size.mas_equalTo(CGSizeMake(48, 48));
    }];
    
    [self.invitedMaskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    
    [self.nickNameLbl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.top.equalTo(self.avatarImgView.mas_bottom).offset(7);
    }];
    
    [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.top.equalTo(self.borderView);
        make.size.mas_equalTo(CGSizeMake(20, 20));
    }];
}

- (void)setupBorderView {
    UIView *borderView = [[UIView alloc] init];
    borderView.layer.cornerRadius = 27;
    borderView.layer.borderWidth = 1;
    borderView.layer.borderColor = [XYLiveTokenColor primary].CGColor;
    borderView.hidden = YES;
    [self addSubview:borderView];
    self.borderView = borderView;
}

- (void)setupAvatarImgView {
    UIImageView *avatarImgView = [[UIImageView alloc] init];
    avatarImgView.layer.cornerRadius = 24;
    avatarImgView.layer.masksToBounds = YES;
    avatarImgView.clipsToBounds = YES;
    avatarImgView.userInteractionEnabled = YES;
    avatarImgView.layer.borderColor = [XYLiveTokenColor separator].CGColor;
    avatarImgView.layer.borderWidth = 0.5;
    avatarImgView.contentMode = UIViewContentModeScaleAspectFill;
    [self addSubview:avatarImgView];
    self.avatarImgView = avatarImgView;
}

- (void)setupInvitedMaskView {
    UILabel *invitedMaskView = [[UILabel alloc] init];
    invitedMaskView.text = @"邀请中";
    invitedMaskView.font = [UIFont fontWithName:@"PingFangSC-Regular" size:10];
    invitedMaskView.textColor = [[XYLiveTokenColor white] colorWithAlphaComponent:0.7];
    invitedMaskView.backgroundColor = [[XYLiveTokenColor black] colorWithAlphaComponent:0.5];
    invitedMaskView.hidden = YES;
    invitedMaskView.textAlignment = NSTextAlignmentCenter;
    [self.avatarImgView addSubview:invitedMaskView];
    self.invitedMaskView = invitedMaskView;
}

- (void)setupNickNameLbl {
    UILabel *nickNameLbl = [[UILabel alloc] init];
    nickNameLbl.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
    nickNameLbl.textColor = [XYLiveTokenColor paragraph];
    nickNameLbl.textAlignment = NSTextAlignmentCenter;
    [self addSubview:nickNameLbl];
    self.nickNameLbl = nickNameLbl;
}

- (void)setupCancelBtn {
    UIButton *cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [cancelBtn setImage:[UIImage xy_liveKitBundleImage:@"xyLiveInfoCardDeleteIcon"] forState:UIControlStateNormal];
    cancelBtn.hidden = YES;
    cancelBtn.xy_responseRectInset = UIEdgeInsetsMake(-5, -5, -5, -5);
    [cancelBtn addTarget:self action:@selector(didTapCancel:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:cancelBtn];
    self.cancelBtn = cancelBtn;
}

#pragma mark - Event

- (void)didTapCancel:(UIButton *)sender {
    self.didTapCancelHandler ? self.didTapCancelHandler() : nil;
}

@end

@interface XYLiveMultiLineFuncFriendItemSubCell : UICollectionViewCell

@property (nonatomic, strong) XYLiveMultiLineFuncFriendItemSubView *itemView;
@property (nonatomic, strong) XYLiveLinkHostInviteeInfo *listItem;
@property (nonatomic, strong) NSIndexPath *indexPath;

// 取消事件
@property (nonatomic, copy) void (^didTapCancelHandler)(XYLiveLinkHostInviteeInfo *listItem, NSIndexPath *indexPath);

// 绑定数据源
- (void)bindListItems:(NSArray<XYLiveLinkHostInviteeInfo *> *)listItems indexPath:(NSIndexPath *)indexPath;

@end

@implementation XYLiveMultiLineFuncFriendItemSubCell: UICollectionViewCell

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

// 绑定数据源
- (void)bindListItems:(NSArray<XYLiveLinkHostInviteeInfo *> *)listItems indexPath:(NSIndexPath *)indexPath {
    XYLiveLinkHostInviteeInfo *listItem = listItems[indexPath.item];
    self.listItem = listItem;
    self.indexPath = indexPath;
    [self.itemView bindListItem:listItem];
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 创建子视图
    XYLiveMultiLineFuncFriendItemSubView *itemView = [[XYLiveMultiLineFuncFriendItemSubView alloc] init];
    WS
    itemView.didTapCancelHandler = ^{
        SS
        self.didTapCancelHandler ? self.didTapCancelHandler(self.listItem, self.indexPath) : nil;
    };
    [self.contentView addSubview:itemView];
    self.itemView = itemView;
    
    // 布局
    [self.itemView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
}

@end


@interface XYLiveMultiLineOptFuncFriendItemView ()<UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout>

@property (nonatomic, strong) UICollectionView *collectionView;
@property (nonatomic, copy)   NSArray<XYLiveLinkHostInviteeInfo *> *dataSource;

@end

@implementation XYLiveMultiLineOptFuncFriendItemView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

// 数据绑定
- (void)bindListItems:(NSArray<XYLiveLinkHostInviteeInfo *> *)listItems {
    self.dataSource = listItems;
    [self.collectionView reloadData];
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 创建列表视图
    [self setupCollectionView];
    
    // 布局
    [self.collectionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
}

- (void)setupCollectionView {
    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
    layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    layout.itemSize = CGSizeMake(64, 76);
    layout.minimumInteritemSpacing = 6;
    
    UICollectionView *collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
    collectionView.backgroundColor = [UIColor clearColor];
    collectionView.delegate = self;
    collectionView.dataSource = self;
    collectionView.showsHorizontalScrollIndicator = NO;
    // 注册cell
    [collectionView registerClass:XYLiveMultiLineFuncFriendItemSubCell.class forCellWithReuseIdentifier:kXYLiveMultiLineFuncFriendItemSubCellIde];
    [self addSubview:collectionView];
    self.collectionView = collectionView;
}

#pragma mark - UICollectionViewDelegate, UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.dataSource.count;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    XYLiveMultiLineFuncFriendItemSubCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kXYLiveMultiLineFuncFriendItemSubCellIde forIndexPath:indexPath];
    WS
    // 事件绑定
    cell.didTapCancelHandler = ^(XYLiveLinkHostInviteeInfo *listItem, NSIndexPath *indexPath) {
        SS
        self.didTapCancelHandler ? self.didTapCancelHandler(listItem, indexPath) : nil;
    };
    // 数据绑定
    [cell bindListItems:self.dataSource indexPath:indexPath];
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    XYLiveLinkHostInviteeInfo *listItem = self.dataSource[indexPath.item];
    self.didTapHandler ? self.didTapHandler(listItem, indexPath) : nil;
}

@end
