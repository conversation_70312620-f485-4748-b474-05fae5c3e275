//
//  XYLivePushRoomViewController+VPN.swift
//  XYLiveKit
//
//  Created by zhangqing on 2025/04/29.
//  Copyright © 2025 XingIn. All rights reserved.
//

import Foundation
import XYLiveCore

extension XYLivePushRoomViewController: VPNConnectChangedListener {
    public func onVPNConnected() {
        if checkVPNToastValid() {
            DispatchQueue.main.asyncAfter(deadline: .now() + Double(XYAlphaSwitch.vpnDetectDelayShowInternval())) {
                XYAlert.live_showTextItem(withText: XYAlphaSwitch.vpnDetectResultText())
            }
        }
    }
    
    @objc
    public func checkVPNToastValid() -> Bool {
        if !XYAlphaSwitch.vpnDetectValid() {
            return false
        }
        if self.decorateVM.vpnToastCount < XYAlphaSwitch.pushVPNCheckCount() {
            self.decorateVM.vpnToastCount += 1
            return true
        }
        return false
    }
}
