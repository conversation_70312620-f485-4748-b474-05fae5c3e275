//
//  XYLivePushMultiChatRoomController.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/30.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatRoomController.h"
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLiveKit/XYLivePushUserCardService.h>
#import "XYLivePushMultiChatRoomInviteVC.h"
#import "XYLivePushMultiChatRoomCoreModel.h"
#import "XYLivePushMultiChatRoomBottomBarItemVC.h"

@interface XYLivePushMultiChatRoomController()

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   id<XYLivePushMultiChatRoomDependProtocol> depend;
@property (nonatomic, strong) XYLivePushMultiChatRoomCoreModel *coreModel;
@property (nonatomic, weak)   XYLivePushMultiChatRoomInviteVC *inviteVC;

@end

@implementation XYLivePushMultiChatRoomController

- (void)dealloc {
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatRoomModule content:@"XYLivePushMultiChatRoomController dealloc"];
}

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containVC
                             depend:(id<XYLivePushMultiChatRoomDependProtocol>)depend {
    if (self = [super init]) {
        _containerVC = containVC;
        _depend = depend;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatRoomModule content:@"XYLivePushMultiChatRoomController didLoad"];
    // 初始化核心Model
    [self setupCoreModel];
    // 初始化底部栏按钮vc
    [self setupBottomBarItemVC];
    // 初始化邀请VC
    [self setupInviteVC];
}

- (void)showInvitePanelWithSource:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString * _Nullable)extraInfo {
    [self.inviteVC showInvitePanelWithSource:source extraInfo:extraInfo];
}

- (void)inviteWithUserId:(NSString *)userId source:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString *)extraInfo {
    [self.inviteVC inviteWithUserId:userId source:source extraInfo:extraInfo];
}

#pragma mark - Private

- (void)setupCoreModel {
    XYLivePushMultiChatRoomCoreModel *coreModel = [[XYLivePushMultiChatRoomCoreModel alloc] initWithLiveInfoService:self.depend.liveInfoService socketService:self.depend.imDistributeService multiLinkService:self.depend.multiLinkService];
    self.coreModel = coreModel;
}

- (void)setupBottomBarItemVC {
    XYLivePushMultiChatRoomBottomBarItemVC *bottomBarItemVC = [[XYLivePushMultiChatRoomBottomBarItemVC alloc] initWithContainerVC:self.containerVC coreModel:self.coreModel bottomBarService:self.depend.bottomBarService];
    [self addViewController:bottomBarItemVC];
}

- (void)setupInviteVC {
    XYLivePushMultiChatRoomInviteVC *inviteVC = [[XYLivePushMultiChatRoomInviteVC alloc] initWithContainerVC:self.containerVC coreModel:self.coreModel liveInfoService:self.depend.liveInfoService multiLinkService:self.depend.multiLinkService groupLiveService:self.depend.groupLiveService];
    WS
    inviteVC.showUserCardHandler = ^(XYLiveUserInfo * _Nonnull userInfo) {
        SS
        [self.depend.userCardService handleFetchingUserInfoAndPresentingWithUser:userInfo];
    };
    [self addViewController:inviteVC];
    self.inviteVC = inviteVC;
}

@end
