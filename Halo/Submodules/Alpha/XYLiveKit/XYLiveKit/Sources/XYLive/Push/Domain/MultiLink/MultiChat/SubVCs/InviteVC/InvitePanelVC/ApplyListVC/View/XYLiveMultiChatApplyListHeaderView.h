//
//  XYLiveMultiChatApplyListHeaderView.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <XYLiveKit/XYLiveMultiChatApplyListModel.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiChatApplyListHeaderView : UIView

// 点击排序事件回调
@property (nonatomic, copy) void(^didTapListItemHandler)(XYLiveMultiChatApplyListSortItem *sortItem);

// 数据绑定
- (void)bindListItems:(NSArray<XYLiveMultiChatApplyListSortItem *> *)listItems;

// 更新
- (void)update;

@end

NS_ASSUME_NONNULL_END
