//
//  XYLivePushMultiLinkAdapterController.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/12.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushMultiLinkAdapterController.h"
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLivePushDecorateView+Private.h>
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYAlphaUtils/XYAlphaUtils-Swift.h>

@interface XYLivePushMultiLinkAdapterController ()<XYLiveMultiLinkListener>

@property (nonatomic, weak) UIViewController *containerVC;
@property (nonatomic, weak) id<XYLivePushMultiLinkAdapterDependProtocol> depend;

@end

@implementation XYLivePushMultiLinkAdapterController

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                             depend:(id<XYLivePushMultiLinkAdapterDependProtocol>)depend {
    if (self = [super init]) {
        _containerVC = containerVC;
        _depend = depend;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    // 注册监听
    [self.depend.multiLinkService registerListener:self];
}

#pragma mark - XYLiveMultiLinkListener

- (void)onRtcBizStart:(XYLiveMultiLinkBizType)bizType bizExtraInfo:(NSString *)bizExtraInfo {
    // 合法性校验
    if (bizType == XYLiveMultiLinkBizTypeUnknown) { return; }
    // 更新聚焦视图状态
    [self updateCameraControlViewStatusWithIsHidden:YES];
}

- (void)onRtcUpdateRoomInfo:(XYLiveMultiLinkRoomInfo *)roomInfo {
    // 获取对应的布局方式
    XYLiveMultiLinkLayoutType layoutType = roomInfo.layoutType;
    // 更新商卡状态
    [self updateRightBottomCardStatusWithIsHidden:layoutType == XYLiveMultiLinkLayoutTypeGrid];
}

- (void)onRtcBizEnd:(XYLiveMultiLinkBizType)bizType bizExtraInfo:(NSString *)bizExtraInfo {
}

- (void)onRtcRoomClosed {
    // 触发老连线房间销毁
    [self.depend.linkService destoryLink];
    // 恢复商卡状态
    [self updateRightBottomCardStatusWithIsHidden:YES];
    // 恢复聚焦视图状态
    [self updateCameraControlViewStatusWithIsHidden:NO];
}

#pragma mark - Private

// 更新焦距状态视图
- (void)updateCameraControlViewStatusWithIsHidden:(BOOL)isHidden {
    self.depend.adapterService.decorateView.cameraControlView.hidden = isHidden;
}

// 更新右下角卡片状态
- (void)updateRightBottomCardStatusWithIsHidden:(BOOL)isHidden {
    if (isHidden) {
        [self.depend.adapterService.decorateView showRightBottomCardAtDefaultPosition];
    } else {
        [self.depend.adapterService.decorateView showRightBottomCardAboveSubhost];
    }
}

@end
