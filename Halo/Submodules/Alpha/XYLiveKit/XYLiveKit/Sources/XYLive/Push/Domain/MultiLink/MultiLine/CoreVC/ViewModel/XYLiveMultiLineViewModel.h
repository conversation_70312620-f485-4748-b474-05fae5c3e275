//
//  XYLiveMultiLineViewModel.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/31.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveIMDistributerServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineCoreServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineConsts.h>


NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiLineViewModel : NSObject

// 布局方式
@property (nonatomic, assign, readonly) XYLiveMultiLinkLayoutType layoutType;
// 渲染区域
@property (nonatomic, assign, readonly) CGRect renderAreaFrame;
// 状态
@property (nonatomic, assign, readonly) XYLiveMultiLineState state;
// 是否连线中
@property (nonatomic, assign, readonly) BOOL isLining;

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                          socketService:(id<XYLiveIMDistributerServiceProtocol>)socketService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

// 结束连线
- (void)stopLink;

// 更新静音状态
- (void)updateMuteStateWithIsMute:(BOOL)isMute bizExtraInfo:(NSString *_Nullable)bizExtraInfo;

// 注册监听
- (void)registerListener:(id<XYLiveMultiLineCoreListener>)listener;

// 取消注册监听
- (void)unregisterListener:(id<XYLiveMultiLineCoreListener>)listener;

@end

NS_ASSUME_NONNULL_END
