//
//  BindAllSelectBottomBar.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/5/9.
//  Copyright © 2025 XingIn. All rights reserved.
//

@objcMembers
open class BindAllSelectBottomBar: UIView, BindCardBottomBarProtocol {
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    convenience init(completion:(() -> Void)?, allSelect:((Bool) -> Void)?) {
        self.init()
        self.completionBtnCallback = completion
        self.allSelectBtnCallback = allSelect
    }
    
    open func setupUI() {
        self.addSubview(completeBtn)
        self.addSubview(countLabel)
        self.addSubview(allSelectBackView)
        self.addSubview(allSelectBtn)
        self.addSubview(allSelectLabel)
        
        self.completeBtn.snp.makeConstraints { make in
            make.height.equalTo(40.0)
            make.width.equalTo(199.0)
            make.top.equalToSuperview().offset(8.0)
            make.right.equalToSuperview().offset(-16.0)
        }
        
        self.countLabel.snp.makeConstraints { make in
            make.height.equalTo(20.0)
            make.centerY.equalTo(self.completeBtn)
            make.right.equalTo(self.completeBtn.snp.left).offset(-12.0)
            make.left.equalToSuperview().offset(86.0)
        }
        
        self.allSelectBtn.snp.makeConstraints { make in
            make.height.equalTo(24.0)
            make.width.equalTo(24.0)
            make.centerY.equalTo(self.completeBtn)
            make.left.equalToSuperview().offset(16.0)
        }
        
        self.allSelectBackView.snp.makeConstraints { make in
            make.height.equalTo(24.0)
            make.width.equalTo(24.0)
            make.centerY.equalTo(self.completeBtn)
            make.left.equalToSuperview().offset(16.0)
        }
        
        self.allSelectLabel.snp.makeConstraints { make in
            make.centerY.equalTo(self.allSelectBtn)
            make.width.equalTo(35.0)
            make.height.equalTo(20.0)
            make.left.equalTo(self.allSelectBtn.snp.right).offset(2.0)
        }
    }
    
    var completionBtnCallback: (() -> Void)?
    var allSelectBtnCallback: ((Bool) -> Void)?
    var selectCardCountHandler: (() -> Int)?
    var limitCardCountHandler: (() -> Int)?
    private var isAllowSelectNone = false
    
    lazy var countLabel = {
        let label = UILabel()
        label.font = Theme.fontLarge
        label.textAlignment = .right
        label.text = "0/0"
        label.textColor = .ReDs.description.light()
        return label
    }()
    
    lazy var completeBtn = {
        let button = UIButton(type: .custom)
        button.layer.cornerRadius = 20.0
        button.backgroundColor = .ReDs.primary.light()
        button.setTitle(AdsLeadGenerationTools.getI18NLanguage(from: "完成"), for: .normal)
        button.setTitleColor(.ReDs.background.light(), for: .normal)
        button.titleLabel?.font = Theme.fontLarge
        button.addTarget(self, action: #selector(completionBtnDidPressed), for: .touchUpInside)
        button.backgroundColor = .ReDs.primary.withAlphaComponent(0.4)
        return button
    }()
    
    lazy var allSelectLabel = {
        let label = UILabel()
        label.font = Theme.fontLarge
        label.text = AdsLeadGenerationTools.getI18NLanguage(from: "全选")
        label.textColor = .ReDs.title.light()
        return label
    }()
    
    lazy var allSelectBtn = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage.xy_liveKitBundleImage("xyLiveShopItemUnCheckIcon"), for: .normal)
        button.setImage(UIImage.xy_liveKitBundleImage("xyLiveShopItemCheckIcon"), for: .selected)
        button.setImage(UIImage.xy_liveKitBundleImage("xyLiveShopItemDisableCheckIcon"), for: .disabled)
        button.setImage(UIImage.xy_liveKitBundleImage("xyLiveShopItemDisableCheckIcon"), for: [.disabled, .selected])
        button.addTarget(self, action: #selector(didPressedAllSelectBtn(sender:)), for: .touchUpInside)
        return button
    }()
    
    lazy var allSelectBackView = {
        let backView = UIView()
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(didTapBackView))
        backView.addGestureRecognizer(tapGesture)
        return backView
    }()
    
    func didTapBackView() {
        guard self.allSelectBtn.isEnabled == false else {
            return
        }
        let limitedCount = self.getLimitCardCount()
        AdsLeadGenerationTools.showToast(message: "最多可绑定\(limitedCount)张卡片")
    }
    
    func didPressedAllSelectBtn(sender: UIButton) {
        sender.isSelected = !sender.isSelected
        allSelectBtnCallback?(sender.isSelected)
    }
    
    func completionBtnDidPressed() {
        guard !self.isAllowSelectNone else {
            // 直播前
            completionBtnCallback?()
            return
        }
        
        // 直播中
        if self.getSelectCardCount() > 0 { // 选择卡片数 大于 0
            completionBtnCallback?()
        } else {
            AdsLeadGenerationTools.showToast(message: "直播间线索工具至少需要挂载1张卡片")
        }
    }
    
    func getSelectCardCount() -> Int {
        let count = self.selectCardCountHandler?() ?? 0
        return count
    }
    
    func getLimitCardCount() -> Int {
        let count = self.limitCardCountHandler?() ?? 10
        return count
    }
    
    func update(shouldShowAllSelectBtn: Bool, shouldEnableCompleteBtn: Bool, shouldShowBottomBar:Bool) {
        self.showAllSubViews(isShow: shouldShowBottomBar)
        let isEnable = shouldShowAllSelectBtn && shouldEnableCompleteBtn
        self.allSelectBtn.isEnabled = isEnable
        self.allSelectBtn.isUserInteractionEnabled = isEnable
        self.completeBtn.isEnabled = shouldEnableCompleteBtn
        if shouldEnableCompleteBtn {
            self.completeBtn.backgroundColor = .ReDs.primary.withAlphaComponent(1.0)
        } else {
            self.completeBtn.backgroundColor = .ReDs.primary.withAlphaComponent(0.4)
        }
        // 更新按钮状态
        self.updateCompleteBtnStatus()
    }
    
    func updateCompleteBtnStatus() {
        self.updateCompleteBtnFrame()
        self.updateCompleteBtnColor()
    }
    
    func updateCompleteBtnColor() {
        // 按钮不可点击，那就是透明红色
        if self.completeBtn.isEnabled == false {
            self.completeBtn.backgroundColor = .ReDs.primary.withAlphaComponent(0.4)
            return
        }
        
        // 直播前绑卡
        if isAllowSelectNone {
            self.completeBtn.backgroundColor = .ReDs.primary.withAlphaComponent(1.0)
        } else { // 直播中绑卡
            self.completeBtn.backgroundColor = self.getSelectCardCount() > 0 ? .ReDs.primary.withAlphaComponent(1.0) : .ReDs.primary.withAlphaComponent(0.4)
        }
    }
    
    func updateCompleteBtnFrame() {
        if self.completeBtn.isEnabled == false {
            self.completeBtn.snp.remakeConstraints { make in
                make.height.equalTo(40.0)
                make.top.equalToSuperview().offset(8.0)
                make.left.equalToSuperview().offset(16.0)
                make.right.equalToSuperview().offset(-16.0)
            }
            
            self.countLabel.snp.remakeConstraints { make in
                make.width.equalTo(0.0)
                make.height.equalTo(0.0)
            }
            
            self.allSelectBtn.snp.remakeConstraints { make in
                make.width.equalTo(0.0)
                make.height.equalTo(0.0)
            }
            
            self.allSelectBackView.snp.remakeConstraints { make in
                make.width.equalTo(0.0)
                make.height.equalTo(0.0)
            }
            
            self.allSelectLabel.snp.remakeConstraints { make in
                make.width.equalTo(0.0)
                make.height.equalTo(0.0)
            }
        } else {
            self.completeBtn.snp.remakeConstraints { make in
                make.height.equalTo(40.0)
                make.width.equalTo(199.0)
                make.top.equalToSuperview().offset(8.0)
                make.right.equalToSuperview().offset(-16.0)
            }
            
            self.countLabel.snp.remakeConstraints { make in
                make.height.equalTo(20.0)
                make.centerY.equalTo(self.completeBtn)
                make.right.equalTo(self.completeBtn.snp.left).offset(-12.0)
                make.left.equalToSuperview().offset(86.0)
            }
            
            self.allSelectBtn.snp.remakeConstraints { make in
                make.height.equalTo(24.0)
                make.width.equalTo(24.0)
                make.centerY.equalTo(self.completeBtn)
                make.left.equalToSuperview().offset(16.0)
            }
            
            self.allSelectBackView.snp.remakeConstraints { make in
                make.height.equalTo(24.0)
                make.width.equalTo(24.0)
                make.centerY.equalTo(self.completeBtn)
                make.left.equalToSuperview().offset(16.0)
            }
            
            self.allSelectLabel.snp.remakeConstraints { make in
                make.centerY.equalTo(self.allSelectBtn)
                make.width.equalTo(35.0)
                make.height.equalTo(20.0)
                make.left.equalTo(self.allSelectBtn.snp.right).offset(2.0)
            }
        }
    }
    
    func showAllSubViews(isShow: Bool) {
        completeBtn.isHidden = !isShow
        countLabel.isHidden = !isShow
        allSelectBtn.isHidden = !isShow
        allSelectBackView.isHidden = !isShow
        allSelectLabel.isHidden = !isShow
    }
    
    private func createCountLabelAttributedText(selectedCount: Int, totalCount: Int) -> NSAttributedString {
        let text = "\(selectedCount)/\(totalCount)"
        let attributedString = NSMutableAttributedString(string: text)
        // 设置"/"前面的数字为红色
        let selectedCountRange = NSRange(location: 0, length: String(selectedCount).count)
        attributedString.addAttribute(.foregroundColor, value: UIColor.ReDs.primary.light(), range: selectedCountRange)
        // 设置"/"和后面的数字为描述色
        let remainingRange = NSRange(location: selectedCountRange.length, length: text.count - selectedCountRange.length)
        attributedString.addAttribute(.foregroundColor, value: UIColor.ReDs.description.light(), range: remainingRange)
        
        return attributedString
    }
    
    func update(selectAllCount: Int, selectedCount: Int) {
        
        if selectedCount == 0 {
            self.allSelectBtn.isSelected = false
        }
        
        self.countLabel.attributedText = createCountLabelAttributedText(selectedCount: selectedCount, totalCount: selectAllCount)
        self.countLabel.snp.makeConstraints { make in
            make.height.equalTo(20.0)
            make.centerY.equalTo(self.completeBtn)
            make.right.equalTo(self.completeBtn.snp.left).offset(-12.0)
            make.left.equalToSuperview().offset(86.0)
        }
    }
    
    func setCanSelectNone(isAllow: Bool) {
        self.isAllowSelectNone = isAllow
    }
    
    func setSelectCardCountHandler(handler: (() -> Int)?) {
        self.selectCardCountHandler = handler
    }
    
    // 最大可选卡片数
    func setLimitCardCountHandler(handler: (() -> Int)?) {
        self.limitCardCountHandler = handler
    }
}
