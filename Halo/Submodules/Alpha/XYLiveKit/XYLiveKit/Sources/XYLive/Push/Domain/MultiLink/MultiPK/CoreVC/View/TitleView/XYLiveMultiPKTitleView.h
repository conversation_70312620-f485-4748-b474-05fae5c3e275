//
//  XYLiveMultiPKTitleView.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/14.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <XYLiveServiceProtocol/XYLiveMultiPKConsts.h>
#import <XYLiveKit/XYLiveMultiPKModel.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiPKTitleView : UIView

// 点击关闭
@property (nonatomic, copy) void(^didTapCloseHandler)(void);

// 初始化
- (instancetype)initWithBizType:(XYLiveMultiLinkBizType)bizType
                liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService;

// 更新状态
- (void)updateState:(XYLiveMultiPKState)state pkInfo:(XYLiveMultiPKModel *)pkInfo;

// 更新倒计时
- (void)updateTrickCountDown:(NSInteger)interval pkInfo:(XYLiveMultiPKModel *)pkInfo;

@end

NS_ASSUME_NONNULL_END
