//
//  XYLiveLinkHostController.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/27.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkConsts.h>
@protocol XYLiveLinkHostDependProtocol;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveLinkHostController : XYViewController

- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                             depend:(id<XYLiveLinkHostDependProtocol>)depend;

// 更新布局
- (void)updateLayoutType:(XYLiveMultiLinkLayoutType)layoutType completion:(void (^)(NSError * _Nonnull))completion;

// 展示设置面板
- (void)showLinkHostSettingPanel;

@end

NS_ASSUME_NONNULL_END
