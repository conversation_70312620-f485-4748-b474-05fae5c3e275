//
//  XYLiveLinkHostBubbleVM.h
//  XYLiveKit
//
//  Created by 大远 on 2025/6/5.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "XYLiveLinkHostBubbleModel.h"

NS_ASSUME_NONNULL_BEGIN

// PK状态
typedef NS_ENUM(NSUInteger, XYLiveLinkHostBubbleType) {
    XYLiveLinkHostBubbleTypePK,          // PK
    XYLiveLinkHostBubbleTypeSwitchBiz,   // 玩法切换
};

@interface XYLiveLinkHostBubbleVM : NSObject

// 气泡配置
- (XYLiveLinkHostBubbleModel *)bubbleModelWithType:(XYLiveLinkHostBubbleType)type;

// 是否可用
- (BOOL)enableBubbleWithType:(XYLiveLinkHostBubbleType)type;

// 气泡不可用
- (void)disableBubbleWithType:(XYLiveLinkHostBubbleType)type;
                                                  
@end

NS_ASSUME_NONNULL_END
