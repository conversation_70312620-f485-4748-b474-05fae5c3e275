//
//  XYLivePushRoomViewController+Notification.m
//  XYLiveKit
//
//  Created by 周博立 on 2021/4/10.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import "XYLivePushRoomViewController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLiveGiftModel+XYLiveGiftAnimation.h"
#import "XYLiveExposureCardPannelItemViewModel.h"
#import "XYLivePushMarketingNode.h"
#import "XYLivePushShoppingBagNode.h"
#import "XYLivePushWebNode.h"
#import "XYLiveNotificationService.h"

#import "XYLiveCommonShareNode.h"
@import XYLiveFoundation;
@import XYHybridModule;
@import XYConfigCenter;
@import RedI18N;
@import I18N;

@implementation XYLivePushRoomViewController (Notification)

- (void)notification_unregistry {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)notification_registry {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pushVCDidEnterBackground) name:UIApplicationDidEnterBackgroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pushVCWillEnterForeground) name:UIApplicationWillEnterForegroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pushVCWillResignActive) name:UIApplicationWillResignActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pushVCDidBecomeActive) name:UIApplicationDidBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(audioSessionInterrupted:) name:AVAudioSessionInterruptionNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pushVCAppWillTerminate) name:UIApplicationWillTerminateNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(pushVCDenoiseInfo:) name:XYLivePushDenoiseInfoNotifyKey object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(presentAnotherWebViewVCNotification:) name:XYLiveBridgeOpenAnotherWebViewNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleWebviewBroadcastNotification:) name:XYHybridWebBroadcastNotificationName object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(handleWebviewOpenSharePanelNotification:) name:XYLiveBridgeOpenShareNotification object:nil];
    
    BOOL dismiss = XYConfigCenter().justOnceBoolForKey(@"ios_live_host_kickout_dismiss", NO);
    __weak typeof(self) wself = self;
    if (dismiss) {
        XYSessionManager.on(XYSessionListenerTypeLogOut).disposeBy(self).process(^(){
            [XYLogCollector xyLiveLogTag:@"push" content:@"dismiss by logout"];
            [wself dismissWithoutStopServer];
        });
    }
}

- (void)pushVCDenoiseInfo: (NSNotification *)notif {
    NSDictionary *dict = notif.userInfo;
    if (dict) {
        [XYLiveManagerSharedInstance postModuleMsgType:XyLiveModuleRouteMsgTypeChatboardDebugInfo custom:dict];
    }
}

- (void)pushVCAppWillTerminate {
    [xyLiveSharedAPMManager updateRTCPusherTotalPushTime:(CFAbsoluteTimeGetCurrent() - self.viewDidLoadTime)];
    [xyLiveSharedAPMManager reportRTCPusherLagSummary];
    [XYLogCollector xyLiveLogTag:@"push" content:@"app was killed by host"];
}

- (void)pushVCDidRecvThermalStateChange {
    if (@available(iOS 11.0, *)) {
        NSProcessInfoThermalState state = NSProcessInfo.processInfo.thermalState;
        NSString *content = [NSString stringWithFormat:@"thermal_change: %@", @(state)];
        [XYLogCollector xyLiveLogTag:@"host_performance" content:content];
    }
}

- (void)audioSessionInterrupted:(NSNotification *)notif {
    NSInteger state = [notif.userInfo[AVAudioSessionInterruptionTypeKey] integerValue];
    switch (state) {
        case AVAudioSessionInterruptionTypeBegan:
            //reserved
            break;
        case AVAudioSessionInterruptionTypeEnded:
            if ([self isVisible]) {
                [XYLiveMediaControl reloadAudioSessionWithReceiver:self sceneType:XYLiveAudioSessionScenePush];
                [self.decorateVM onWillEnterForeground];
            }
            break;
        default:
            break;
    }
}

- (void)pushVCDidEnterBackground {
    [self.decorateVM onDidEnterBackground];
    [XYLogCollector xyLiveLogTag:@"push" content:@"app did enter background"];
}

- (void)pushVCWillEnterForeground {
    [self.decorateVM onWillEnterForeground];
    [XYLogCollector xyLiveLogTag:@"push" content:@"app will enter foreground"];
}

- (void)pushVCWillResignActive {
    if ([self isVisible]) {
        [self.decorateVM onWillResignActive];
    }
    if ([XYLiveLinkConfig allowRTCObserve]
        && !self.pushBreakOff
        && !self.shouldScreenPushing
        && !XYLiveManagerSharedInstance.isHostPausePush) {
        [self pausePush];
    }
}

- (void)pushVCDidBecomeActive {
    self.decorateVM.currentFloatingLikeIconCount = 0;
    if ([self isVisible]) {
        [XYLiveMediaControl reloadAudioSessionWithReceiver:self sceneType:XYLiveAudioSessionScenePush];
        [self.decorateVM onDidBecomeActive];
    }
    if ([XYLiveLinkConfig allowRTCObserve]
        && !self.pushBreakOff
        && !self.shouldScreenPushing
        && !XYLiveManagerSharedInstance.isHostPausePush) {
        [self resumePush];
    }
}

- (void)presentAnotherWebViewVCNotification:(NSNotification *)notification {
    if ([self isVisible]) {
        NSString *openURLStr = notification.userInfo[@"linkurl"];
        if (openURLStr.length <= 0) {
            return;
        }
        
        id<XYLivePushWebServiceProtocol> webService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushWebServiceProtocol)];
        [webService dismissWebVC:YES];
        [webService presentDefaultWebVC:openURLStr];
    }
}

// 处理来自webview的单向通知
- (void)handleWebviewBroadcastNotification:(NSNotification *)notification {
    if (!notification) {
        return;
    }
    
    NSDictionary *userInfo = notification.userInfo;
    NSString *key = userInfo[@"key"];
    NSDictionary *data = nil;
    if ([userInfo[@"data"] isKindOfClass:NSDictionary.class]) {
        data = (NSDictionary *)userInfo[@"data"];
    }
    if (key.length <= 0) {
        return;
    }
    
    XYExecuteOnMainQueue(^{
        if ([self isVisible]) {
            id<XYLiveNotificationServiceProtocol> notificationService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveNotificationServiceProtocol)];
            [notificationService onReceiveH5BroadcastNotification:notification];
            
            if ([key isEqualToString:@"live_action_link"]) {
                if ([data[@"url"] isKindOfClass:[NSString class]]) {
                    NSString *actionLink = (NSString *)data[@"url"];
                    self.decorateVM.notifyVCToPerformActionLink = actionLink;
                }
            } else if ([key isEqualToString:@"live_coupon_send"]) {
                id<XYLivePushMarketingServiceProtocol> service = [self.decorateVM getServiceByProtocol:@protocol(XYLivePushMarketingServiceProtocol)];
                [service showPlatformCouponViewController];
                if ([XYLiveConfigCenter couponSendCloseWeb]) {
                    id<XYLivePushWebServiceProtocol> webService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushWebServiceProtocol)];
                    [webService dismissWebVC:NO];
                }
            } else if ([key isEqualToString:@"live_goods_manage"]) {
                id<XYLivePushShoppingBagServiceProtocol> service = [self.decorateVM getServiceByProtocol:@protocol(XYLivePushShoppingBagServiceProtocol)];
                [service showGoodsManage];
            } else if ([key isEqualToString:@"live_coupon_config"]) {
                NSString *couponType = data[@"couponType"];
                [self handleCouponConfigRequest:couponType];
            } else if ([key isEqualToString:@"link_flow_card"]) {
                XYExecuteOnMainQueue(^{
                    XYLivePushTrafficCardViewController *vc = [XYLivePushTrafficCardViewController new];
                    [vc showWithVc:self];
                });
            } else if ([key isEqualToString:@"chatgroup_list_selected"]) {
                BOOL success = false;
                if (userInfo[@"data"] != nil && userInfo[@"data"][@"success"] != nil) {
                    success = [userInfo[@"data"][@"success"] boolValue];
                }
                if (success) {
                    [[XYAlertCenter live_createTextItemWithText:L.live.live_share_success] show];
                    // 打点
                    [XYAlphaShareTracker eventID38175];
                }
            } else if ([key isEqualToString:@"live_toast"] && XYConfigCenter().justOnceBoolForKey(@"ios_live_push_web_live_toast", YES)) {
                NSString *toastText = data[@"text"];
                if ([toastText isKindOfClass:NSString.class] && toastText.length > 0) {
                    [[XYAlertCenter live_createTextItemWithText:toastText] show];
                }
            } else {
                // do nothing
            }
        }
    });
}

- (void)handleWebviewOpenSharePanelNotification:(NSNotification *)notification {
    XYExecuteOnMainQueue(^{
        if ([self isVisible]) {
            XYLiveShareSourceInfo *shareInfo = [XYLiveShareSourceInfo new];
            if ([notification.userInfo[@"share_source"] isKindOfClass:NSString.class]) {
                shareInfo.source = notification.userInfo[@"share_source"] ?: @"";
            }
            if ([notification.userInfo[@"share_source_id"] isKindOfClass:NSString.class]) {
                shareInfo.sourceID = notification.userInfo[@"share_source_id"] ?: @"";
            }
            
            id<XYLiveCommonShareServiceProtocol> shareService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveCommonShareServiceProtocol)];
            [shareService loadSharingVCWithSource:shareInfo];
        }
    });
}

- (void)handleCouponConfigRequest:(NSString *)couponType {
    if (!couponType || ![couponType isKindOfClass:[NSString class]]) {
        return;
    }
    NSString *resourceString = @"";
    BOOL hasShoppingEntrance = XYLiveManagerSharedInstance.hasShoppingEntrance;
    XYLiveAtmosphereCollectionModel *model = [XYLiveAtmosphereManager.sharedInstance collectionModel:hasShoppingEntrance];
    
    if ([couponType isEqualToString:@"COUPON_MARKETING"]) {
        resourceString = model.couponMarketingModel.funcResource;
    } else if ([couponType isEqualToString:@"COUPON_FANS"]) {
        resourceString = model.couponFansModel.funcResource;
    }
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"type"] = @"coupon_style";
    params[couponType] = resourceString;
    [[NSNotificationCenter defaultCenter] postNotificationName:XYHybridWebViewBroadCastNotice object:params];
}

@end
