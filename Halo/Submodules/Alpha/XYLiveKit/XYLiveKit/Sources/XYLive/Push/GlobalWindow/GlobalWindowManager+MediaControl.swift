//
//  GlobalWindowManager+MediaControl.swift
//  XYLiveKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/5/18.
//  Copyright © 2023 XingIn. All rights reserved.
//

import Foundation
import XYMediaControl
import XYLiveFoundation

extension GlobalWindowManager: MCPlayerFull {
    public func resumeDelay() -> TimeInterval {
        1.0
    }
    
    public func frozeAudioSession() -> <PERSON><PERSON> {
        anchorPictureAndAudiencePictureExperiment()
    }
    
    public func interrupted(at scene: XYMediaControl.MCScene, at mode: XYMediaControl.MCPlayMode, executor: XYMediaControl.MCExecutorWarper) {
        collectLog(content: "mediaControl-interrupted: \(scene) \(mode)")
        if scene == .live {
            
        } else if mode == .audio || mode == .audio_video {
            if !anchorPictureAndAudiencePictureExperiment() {
                self.setMute(true)
            }
        }
        executor.submit(true)
    }
    
    public func invalid(at scene: XYMediaControl.MCScene) {
        collectLog(content: "mediaControl-invalid: \(scene)")
    }
    
    public func autoResume(from player: AnyObject?, isPlayer invalid: Bool) -> Bool {
        true
    }
    
    public func pause(media scene: XYMediaControl.MCScene) {
        collectLog(content: "mediaControl-pause: \(scene)")
        self.setMute(true)
    }
    
    public func resumeMedia() {
        collectLog(content: "mediaControl-resumeMedia")
        self.setMute(false)
    }
    
    public func isPlayingMedia() -> Bool {
        self.curStatus == .active
    }
    
    public func playMode() -> XYMediaControl.MCPlayMode {
        return .audio_video
    }
    
    public func mediaScene() -> XYMediaControl.MCScene {
        return .live
    }
}
