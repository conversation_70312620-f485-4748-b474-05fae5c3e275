//
//  XYLivePushMultiChatInviteSettingPanelVC.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/25.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatInviteSettingPanelVC.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveKit/XYLiveMultiLinkListLayoutItem.h>
#import <XYLiveKit/XYLiveMultiLinkListLayoutItemCell.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLiveKit/XYLiveMultiLinkAlertVC.h>

@import XYConfigCenter;

@interface XYLivePushMultiChatInviteSettingPanelVC ()

@property (nonatomic, weak) id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak) XYLivePushMultiChatCoreModel *coreModel;

@end

@implementation XYLivePushMultiChatInviteSettingPanelVC

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                          coreModel:(XYLivePushMultiChatCoreModel *)coreModel {
    if (self = [super initWithContainerVC:containerVC]) {
        _liveInfoService = liveInfoService;
        _coreModel = coreModel;
    }
    return self;
}

#pragma mark - override

- (NSString *)XYLiveNav_title {
    return  self.coreModel.isGameLive ? @"手游连线设置" : @"观众连线设置";
}

- (CGFloat)heightWhenPortrait {
    return kXYLiveMultiChatInvitePanelHeight;
}

- (void)refreshWithType:(XYLiveRefreshDataType)type completion:(void (^)(NSError * _Nonnull))completion {
    WS
    [self.coreModel loadInitialConfig:^(XYLivePushMultiChatConfig * _Nullable config, NSError * _Nullable error) {
        SS
        if (error == nil) {
            // 配置数据源
            [self configureListItems];
        }
        completion ? completion(error) : nil;
    }];
}

/// 配置数据源
- (void)configureListItems {
    NSMutableArray *listItems = [NSMutableArray array];
    // 布局分组
    XYLiveMultiLinkListGroupItem *layoutGroupItem = [self buildLayoutGroupItem];
    // 能力分组
    XYLiveMultiLinkListGroupItem *switchGroupItem = [self buildSwitchGroupItem];
    // 限制分组
    XYLiveMultiLinkListGroupItem *limitGroupItem = [self buildLimitGroupItem];
    
    // 畅聊直播间,不展示布局
    if (!self.coreModel.isGameLive) {
        // 添加布局模块
        [listItems addObject:layoutGroupItem];
    }
    // 添加能力模块
    [listItems addObject:switchGroupItem];
    
    if (self.coreModel.isOpened) {
        // 添加限制模块
        [listItems addObject:limitGroupItem];
    }
    self.dataSource = listItems.copy;
    // 刷新布局
    [self.tableView reloadData];
}

// 创建布局分组
- (XYLiveMultiLinkListGroupItem *)buildLayoutGroupItem {
    XYLiveMultiLinkListGroupItem *group = [[XYLiveMultiLinkListGroupItem alloc] init];
    group.title = @"布局";
    XYLiveMultiLinkListLayoutItem *item = [[XYLiveMultiLinkListLayoutItem alloc] init];
    item.layoutType = self.coreModel.config.layoutType;
    WS
    item.didTapHanler = ^(XYLiveMultiLinkLayoutType layoutType, NSIndexPath * _Nonnull indexPath) {
        SS
        [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"--didTapLayout--{type:%@, indexPath:%@}",@(layoutType), @(indexPath.row)]];
        [self updateLayoutType:layoutType indexPath:indexPath];
    };
    item.itemH = 60;
    group.items = @[item];
    return group;
}

// 创建能力分组
- (XYLiveMultiLinkListGroupItem *)buildSwitchGroupItem {
    XYLiveMultiLinkListGroupItem *group = [[XYLiveMultiLinkListGroupItem alloc] init];
    group.title = @"能力";
    XYLiveMultiLinkListSwitchItem *item = [[XYLiveMultiLinkListSwitchItem alloc] init];
    item.title = @"允许观众发起连线申请";
    item.isOn = self.coreModel.isOpened;
    WS
    item.didTapSwitchHandler = ^(BOOL isOn, NSIndexPath *indexPath, UISwitch *sender) {
        SS
        [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"--didTapSwitch--{isOn:%d, indexPath:%@}",isOn, @(indexPath.row)]];
        // 如果是关闭开关，需要弹二次确认弹窗
        if (!isOn) {
            XYLiveMultiLinkAlertVC *alertVC = [[XYLiveMultiLinkAlertVC alloc] initWithTitle:@"关闭连线" msg:@"确认关闭观众连线吗？关闭后观众将无法看到连线申请入口"];
            alertVC.didTapConfirmHandler = ^{
                SS
                [self updateSwitchStatus:isOn indexPath:indexPath];
            };
            alertVC.didTapCancelHandler = ^{
                SS
                [self.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
            };
            [alertVC showWithAnimated:YES onVC:self.containerVC complete:nil];
        } else {
            [self updateSwitchStatus:isOn indexPath:indexPath];
        }
    };
    
    XYLiveMultiLinkListSwitchItem *giftItem = [[XYLiveMultiLinkListSwitchItem alloc] init];
    giftItem.title = @"允许对麦上观众送礼";
    giftItem.isOn = self.coreModel.config.enableGiftSwitch;
    giftItem.didTapSwitchHandler = ^(BOOL isOn, NSIndexPath *indexPath, UISwitch *sender) {
        SS
        BOOL isGroupLiveRoom = self.liveInfoService.isHost ? self.coreModel.isGroupLive : self.liveInfoService.roomInfo.businessBaseInfo.roomConfig.isGroupLive;
        if (isGroupLiveRoom && XYConfigCenter().justOnceBoolForKey(@"ios_is_load_group_live", YES)) {
            [sender setOn:self.coreModel.config.enableGiftSwitch animated:NO];
            [XYAlertCenter live_showTextItemWithText:@"团播模式下不支持给上麦观众送礼"];
        } else {
            [self updateGiftSwitch:isOn indexPath:indexPath];
            [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"【麦上送礼】--didTapSwitch --{isOn:%d, indexPath:%@}",isOn, @(indexPath.row)]];
        }
    };
    if ([XYLiveGiftDomain enableMicSendGift]) {
        group.items = @[item, giftItem];
    } else {
        group.items = @[item];
    }
    return group;
}

// 创建限制分组
- (XYLiveMultiLinkListGroupItem *)buildLimitGroupItem {
    XYLiveMultiLinkListGroupItem *group = [[XYLiveMultiLinkListGroupItem alloc] init];
    group.title = @"谁可以申请连线";
    
    XYLiveMultiLinkListCheckBoxItem *item0 = [[XYLiveMultiLinkListCheckBoxItem alloc] init];
    item0.title = @"所有直播间观众";
    item0.checked = self.coreModel.config.limitType == XYLiveMultiChatLimitTypeAll;
    WS
    item0.didTapHanler = ^(BOOL checked, NSIndexPath * _Nonnull indexPath) {
        SS
        [self updateLimitType:XYLiveMultiChatLimitTypeAll applyCoins:self.coreModel.config.applyCoins indexPath:indexPath];
    };
    
    XYLiveMultiLinkListCheckBoxItem *item1 = [[XYLiveMultiLinkListCheckBoxItem alloc] init];
    item1.title = @"仅我的粉丝";
    item1.checked = self.coreModel.config.limitType == XYLiveMultiChatLimitTypeFollow;
    item1.didTapHanler = ^(BOOL checked, NSIndexPath * _Nonnull indexPath) {
        SS
        [self updateLimitType:XYLiveMultiChatLimitTypeFollow applyCoins:self.coreModel.config.applyCoins indexPath:indexPath];
    };
    
    XYLiveMultiLinkListCheckBoxItem *item2 = [[XYLiveMultiLinkListCheckBoxItem alloc] init];
    item2.title = @"仅粉丝团成员";
    item2.checked = self.coreModel.config.limitType == XYLiveMultiChatLimitTypeFans;
    item2.didTapHanler = ^(BOOL checked, NSIndexPath * _Nonnull indexPath) {
        SS
        [self updateLimitType:XYLiveMultiChatLimitTypeFans applyCoins:self.coreModel.config.applyCoins indexPath:indexPath];
    };
    
    XYLiveMultiLinkListCheckBoxItem *item3 = [[XYLiveMultiLinkListCheckBoxItem alloc] init];
    item3.hitTest = YES;
    if (self.coreModel.config.limitType == XYLiveMultiChatLimitTypeGift) {
        item3.title = [NSString stringWithFormat:@"送礼金额达到%@薯币", self.coreModel.config.applyCoins];
        item3.checked = YES;
    } else {
        item3.title = @"送礼达到设置的薯币金额";
        item3.checked = NO;
    }
    item3.didTapHanler = ^(BOOL checked, NSIndexPath * _Nonnull indexPath) {
        SS
        [self showRedCoinInputBarWithIndexPath:indexPath];
    };
    
    group.items = @[item0, item1, item2, item3];
    return group;
}

#pragma mark - Private

// 切换布局
- (void)updateLayoutType:(XYLiveMultiLinkLayoutType)layoutType indexPath:(NSIndexPath *)indexPath {
    XYLiveMultiLinkListGroupItem *group = self.dataSource[indexPath.section];
    XYLiveMultiLinkListItem *listItem = group.items[indexPath.row];
    XYLiveMultiLinkListLayoutItem *layoutItem = XYSAFE_CAST(listItem, XYLiveMultiLinkListLayoutItem);
    // 显示loading
    [self showLoading];
    WS
    [self.coreModel updateLayoutType:layoutType completion:^(NSError * _Nonnull error) {
        SS
        // 隐藏loading
        [self hideLoading];
        if (error == nil) {
            [XYAlertCenter live_showTextItemWithText:@"布局切换成功"];
            layoutItem.layoutType = layoutType;
        } else {
            // Toast提示
            [XYAlertCenter live_showTextItemWithError:error];
            // 刷新表格
            [self.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
        }
    }];
}

// 更新开关状态
- (void)updateSwitchStatus:(BOOL)isOpened indexPath:(NSIndexPath *)indexPath {
    XYLiveMultiLinkListGroupItem *group = self.dataSource[indexPath.section];
    XYLiveMultiLinkListItem *listItem = group.items[indexPath.row];
    XYLiveMultiLinkListSwitchItem *switchItem = XYSAFE_CAST(listItem, XYLiveMultiLinkListSwitchItem);
    // 显示loading
    [self showLoading];
    WS
    [self.coreModel updateSwitchStatus:isOpened completion:^(NSError * _Nonnull error) {
        SS
        // 隐藏loading
        [self hideLoading];
        if (error == nil) {
            switchItem.isOn = isOpened;
            // 开启成功
            if (isOpened) {
                // 重新分组，展示或隐藏条件限制分组
                [self configureListItems];
            } else {
                // Toast提示
                [XYAlert live_showTextItemWithText:kXYLiveMultiChatInviteSwitchCloseToast];
            }
        } else {
            // Toast提示
            [XYAlertCenter live_showTextItemWithError:error];
            //重置状态
            switchItem.isOn = self.coreModel.isOpened;
            [self.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
        }
    }];
}

// 更新限制类型
- (void)updateLimitType:(XYLiveMultiChatLimitType)limitType applyCoins:(NSString * _Nullable)applyCoins indexPath:(NSIndexPath *)indexPath {
    XYLiveMultiLinkListGroupItem *group = self.dataSource[indexPath.section];
    XYLiveMultiLinkListItem *listItem = group.items[indexPath.row];
    // 显示loading
    [self showLoading];
    WS
    [self.coreModel updateLimitType:limitType extraInfo:applyCoins completion:^(NSError * _Nonnull error) {
        SS
        // 隐藏loading
        [self hideLoading];
        if (error == nil) {
            // 重置之前选中的item
            [self clearItemStatusWithItems:group.items];
            // 更新选中状态
            XYLiveMultiLinkListCheckBoxItem *checkBoxItem = XYSAFE_CAST(listItem, XYLiveMultiLinkListCheckBoxItem);
            checkBoxItem.checked = true;
            if (limitType == XYLiveMultiChatLimitTypeGift) {
                checkBoxItem.title = [NSString stringWithFormat:@"送礼金额达到%@薯币", applyCoins];
            }
            [self.tableView reloadSections:[NSIndexSet indexSetWithIndex:indexPath.section] withRowAnimation:UITableViewRowAnimationNone];
        } else {
            // Toast提示
            [XYAlertCenter live_showTextItemWithError:error];
        }
    }];
}

// 切换送礼开关
- (void)updateGiftSwitch:(BOOL)enableGiftSwitch indexPath:(NSIndexPath *)indexPath {
    XYLiveMultiLinkListGroupItem *group = self.dataSource[indexPath.section];
    XYLiveMultiLinkListItem *listItem = group.items[indexPath.row];
    XYLiveMultiLinkListSwitchItem *switchItem = XYSAFE_CAST(listItem, XYLiveMultiLinkListSwitchItem);
    // 显示loading
    [self showLoading];
    WS
    [self.coreModel updateEnableGiftSwitch:enableGiftSwitch completion:^(NSError * _Nonnull error) {
        SS
        // 隐藏loading
        [self hideLoading];
        if (error == nil) {
            switchItem.isOn = enableGiftSwitch;
        } else {
            // Toast提示
            [XYAlertCenter live_showTextItemWithError:error];
            //重置状态
            switchItem.isOn = self.coreModel.config.enableGiftSwitch;
            // 刷新表格
            [self.tableView reloadRowsAtIndexPaths:@[indexPath] withRowAnimation:UITableViewRowAnimationNone];
        }
    }];
}


// 展示薯币输入框
- (void)showRedCoinInputBarWithIndexPath:(NSIndexPath *)indexPath {
    XYLiveMultiChatNumberInputBar *inputBar = [[XYLiveMultiChatNumberInputBar alloc] init];
    WS
    inputBar.confirmActionCallback = ^(NSString * _Nonnull redCoins) {
        SS
        [self updateLimitType:XYLiveMultiChatLimitTypeGift applyCoins:redCoins ?: self.coreModel.config.applyCoins indexPath:indexPath];
    };
    [inputBar show];
}

// 重置选中状态
- (void)clearItemStatusWithItems:(NSArray<XYLiveMultiLinkListItem *> *)listItems {
    XYLiveMultiLinkListCheckBoxItem *checkBoxItem = [listItems xy_match:^BOOL(XYLiveMultiLinkListItem * _Nonnull obj) {
        XYLiveMultiLinkListCheckBoxItem *checkboxItem = XYSAFE_CAST(obj, XYLiveMultiLinkListCheckBoxItem);
        if ([checkboxItem.title containsString:@"送礼"]) {
            checkboxItem.title = @"送礼达到设置的薯币金额";
        }
        return checkboxItem && checkboxItem.isChecked;
    }];
    checkBoxItem.checked = NO;
}

@end
