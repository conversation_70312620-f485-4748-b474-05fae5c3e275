//
//  XYLivePushMultiChatRoomController.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/30.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveServiceProtocol/XYLivePushMultiChatRoomServiceProtocol.h>
@protocol XYLivePushMultiChatRoomDependProtocol;

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiChatRoomController : XYViewController<XYLivePushMultiChatRoomServiceProtocol>

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containVC
                             depend:(id<XYLivePushMultiChatRoomDependProtocol>)depend;

@end

NS_ASSUME_NONNULL_END
