//
//  XYLiveMultiPKModel.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/12.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiPKModel.h"
#import <XYSessionManager/XYSessionManager.h>

@implementation XYLiveMultiPKContributorUserInfo

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"score": @"score",
        @"userInfo": @"user_info"
    };
}

@end

@implementation XYLiveMultiPKTeamInfo

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"score": @"score",
        @"teamId": @"team",
        @"roomId": @"room_id",
        @"userInfo": @"user_info",
        @"sessionId": @"session_id",
        @"userId": @"user_id",
        @"contributorList": @"contributors"
    };
}

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"contributorList" : [XYLiveMultiPKContributorUserInfo class],
    };
}

- (NSString *)description {
    return [NSString stringWithFormat:@"<roomId:%@, userId:%@, score:%@, teamId:%@>", self.roomId, self.userId, @(self.score), self.teamId];
}

@end

@implementation XYLiveMultiPKModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"stage": @"stage",
        @"pkId": @"pk_id",
        @"startTs": @"start_time",
        @"endTs": @"end_time",
        @"teamList": @"pk_users",
        @"winnerTeamId": @"winner_team"
    };
}

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"teamList" : [XYLiveMultiPKTeamInfo class],
    };
}

- (NSString *)description {
    return [NSString stringWithFormat:@"<pkId:%@, stage:%@, startTs:%@, endTs:%@, winnerTeamId:%@>", self.pkId, self.stage, @(self.startTs), @(self.endTs), self.winnerTeamId];
}

@end
