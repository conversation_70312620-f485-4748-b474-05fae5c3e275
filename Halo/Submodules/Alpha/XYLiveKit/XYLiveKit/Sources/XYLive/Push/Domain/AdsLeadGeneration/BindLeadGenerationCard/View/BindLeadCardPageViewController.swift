//
//  BindLeadCardPageView.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/4/29.
//  Copyright © 2025 XingIn. All rights reserved.
//

import XYPageViewController
import XYSegmentControl
import I18N
import RedI18N

@objcMembers
class BindLeadCardPageViewController: UIViewController {
    
    private var requestDidCompleteAction: (() -> Void)?
    private var originFormMsgDatasource = [AdsLeadBindCardModel]()
    private var originConsultDatasource = [AdsLeadBindCardModel]()
    var selectedTab = 0
    var isLivingTime = false
    
    convenience init(selectedTab: Int, isLivingTime: Bool, formMsgDatasource: [AdsLeadBindCardModel], consultDatasource : [AdsLeadBindCardModel], requestCompletionBlock: (() -> Void)?) {
        self.init()
        self.selectedTab = selectedTab
        self.isLivingTime = isLivingTime
        self.originFormMsgDatasource = formMsgDatasource
        self.originConsultDatasource = consultDatasource
        self.requestDidCompleteAction = requestCompletionBlock
    }
    private var consultCardPageWillAppearAction: ViewWillAppearAction? {
        didSet {
            self.bindLeadCardConsultVC.viewWillAppearAction = consultCardPageWillAppearAction
        }
    }
    private var formMsgCardPageWillAppearAction: ViewWillAppearAction? {
        didSet {
            self.bindLeadCardFormMsgVC.viewWillAppearAction = formMsgCardPageWillAppearAction
        }
    }

    // 是否失效全选按钮，是否失效完成按钮，是否隐藏底部栏目，卡片列表类型
    var bottomBarUpdateIfNeededAction: ((Bool, Bool, Bool, LeadCardType) -> Void)? {
        didSet {
            self.bindLeadCardFormMsgVC.bottomBarUpdateIfNeededAction = bottomBarUpdateIfNeededAction
            self.bindLeadCardConsultVC.bottomBarUpdateIfNeededAction = bottomBarUpdateIfNeededAction
        }
    }
    var bottomBarChangedHandler: ((Int , Int, LeadCardType) -> Void)?
    
    lazy var bindLeadCardConsultVC: BindLeadCardConsultViewController = {
        let controller = BindLeadCardConsultViewController()
        controller.requestDidCompleteAction = self.requestDidCompleteAction
        controller.dataSourceDidFetchedHandler = { [weak self] selectedCount, limitCount in
            self?.bottomBarDidChanged(cardType: .consult)
        }
        return controller
    }()
    
    lazy var bindLeadCardFormMsgVC: BindLeadCardFormMsgViewController = {
        let controller = BindLeadCardFormMsgViewController()
        controller.requestDidCompleteAction = self.requestDidCompleteAction
        controller.dataSourceDidFetchedHandler = { [weak self] selectedCount, limitCount in
            self?.bottomBarDidChanged(selectedCount: selectedCount, limitCount: limitCount, cardType: .formMsg)
        }
        return controller
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.track79446()

        // 点击了咨询单元格
        self.bindLeadCardConsultVC.cellSelectedAction = { [weak self] cardModel in
            guard let self = self else {
                return
            }
            // 是否应该选择
            let shouldSelect = !cardModel.selected
            cardModel.selected = shouldSelect
            
            // 为 true 时
            if shouldSelect {
                // 咨询卡片只保留当前一张
                bindLeadCardConsultVC.leadCardDataSource.forEach { consultModelBean in
                    // 除了选中的那张卡片，其他都置为未选中状态
                    consultModelBean.selected = consultModelBean.cardId == cardModel.cardId
                }
            }
            // 默认传 0，即可
            self.bottomBarDidChanged(cardType: .consult)
            // 重置表单页面选中状态
            self.checkFormMsgCardWhenCardClicked()
        }
        
        // 点击了表单单元格
        self.bindLeadCardFormMsgVC.cellSelectedAction = { [weak self] cardModel in
            guard let self = self else {
                return
            }
            
            let limitCount = self.bindLeadCardFormMsgVC.limitCount
            let selectedCount = self.bindLeadCardFormMsgVC.leadCardDataSource.filter { $0.selected == true }.count
            
            if !cardModel.selected, selectedCount >= limitCount {
                AdsLeadGenerationTools.showToast(message: "最多可绑定\(limitCount)张卡片")
                return
            }
            
            // 是否应该选择
            let shouldSelect = !cardModel.selected
            cardModel.selected = shouldSelect
            // limitCount 为全部卡片数
            self.bottomBarDidChanged(selectedCount: self.bindLeadCardFormMsgVC.leadCardDataSource.filter { $0.selected == true }.count, limitCount: self.bindLeadCardFormMsgVC.listView.leadCardDataSource.count, cardType: .formMsg)
            // 重置咨询页面选中状态
            self.checkConsultCardWhenCardClicked()
        }
        
        self.consultCardPageWillAppearAction = {}
        self.formMsgCardPageWillAppearAction = { [weak self] in
            guard let self = self else {
                return
            }
            // limitCount 为全部卡片数
            self.bottomBarDidChanged(selectedCount: self.bindLeadCardFormMsgVC.leadCardDataSource.filter { $0.selected == true }.count, limitCount: self.bindLeadCardFormMsgVC.leadCardDataSource.count, cardType: .formMsg)
        }
        self.view.backgroundColor = .ReDs.background.light()
        self.setupUI()
        self.setupData()
    }
    
    // 点击表单卡片的时候，检查咨询页面
    func checkConsultCardWhenCardClicked() {
        // 检查数组中是否有选中项
        let hasSelectedItem = bindLeadCardFormMsgVC.leadCardDataSource.contains { formMsgModelBean in
            return formMsgModelBean.selected == true
        }
        // 根据结果进行后续处理
        if hasSelectedItem, !isLivingTime {
            // TODO: 处理有选中项的情况
            bindLeadCardConsultVC.leadCardDataSource.forEach { counsultModelBean in
                counsultModelBean.selected = false
            }
        }
    }
    
    // 点击咨询卡片的时候，检查表单页面
    private func checkFormMsgCardWhenCardClicked() {
        // 检查数组中是否有选中项
        let hasSelectedItem = bindLeadCardConsultVC.leadCardDataSource.contains { modelBean in
            return modelBean.selected == true
        }
        // 根据结果进行后续处理
        if hasSelectedItem, !isLivingTime {
            // TODO: 处理有选中项的情况
            bindLeadCardFormMsgVC.leadCardDataSource.forEach { counsultModelBean in
                counsultModelBean.selected = false
            }
        }
    }
    
    private func setupData() {
        // 初始化咨询数据
        self.originConsultDatasource.forEach { model in
            model.selected = true
        }
        self.bindLeadCardConsultVC.listView.leadCardDataSource = self.originConsultDatasource
        // 初始化留资数据
        self.originFormMsgDatasource.forEach { model in
            model.selected = true
        }
        self.bindLeadCardFormMsgVC.listView.leadCardDataSource = self.originFormMsgDatasource
    }
    
    private func bottomBarDidChanged(selectedCount: Int = 0, limitCount: Int = 0, cardType: LeadCardType) {
        self.bottomBarChangedHandler?(selectedCount, limitCount, cardType)
    }
    
    lazy var pageViewController: XYPageViewController = {
        let pageVC = XYPageViewController()
        pageVC.delegate = self
        pageVC.dataSource = self
        pageVC.bounces = false
        return pageVC
    }()
    
    lazy var segmentControl: XYSegmentControl = {
        let segmentControl = XYSegmentControl()
        segmentControl.segmentWidthStyle = .fixed
        segmentControl.segmentWidth = 90.0
        segmentControl.selectionIndicatorWidth = 56.0
        segmentControl.selectedTitleColor = .ReDs.title.light()
        segmentControl.selectedTitleFont = Theme.fontLargeBold.xy_dynamic
        segmentControl.titles = [AdsLeadGenerationTools.getI18NLanguage(from: "私信咨询"), AdsLeadGenerationTools.getI18NLanguage(from: "表单留资")]
        segmentControl.titleFont = Theme.fontXMedium
        segmentControl.titleColor = UIColor.ReDs.description.light()
        segmentControl.selectedTitleColor = UIColor.ReDs.title.light()
        segmentControl.selectedTitleFont = Theme.fontXMediumBold
        segmentControl.delegate = self
        segmentControl.backgroundColor = .ReDs.background.light()
        
        return segmentControl
    }()
    
    private lazy var controllerList = {
        let controllers: Array<BindLeadCardListViewController> = [bindLeadCardConsultVC, bindLeadCardFormMsgVC]
        return controllers
    }()
    
    // MARK: - functions
    func setupUI() {
        addChild(pageViewController)
        view.addSubview(pageViewController.view)
        pageViewController.didMove(toParent: self)
        self.view.addSubview(segmentControl)
        
        segmentControl.snp.makeConstraints { make in
            make.height.equalTo(44.0)
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
        }
        
        pageViewController.view.snp.makeConstraints { make in
            make.top.equalTo(segmentControl.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
        
        self.pageViewController.show(at: self.selectedTab, animated: false)
    }
}

extension BindLeadCardPageViewController: XYSegmentControlDelegate {
    
    public func segmentControl(_ segmentControl: XYSegmentControl, paddingAt index: UInt) -> CGFloat {
        return 12.0
    }
    
    public func segmentControl(_ segmentControl: XYSegmentControl, didSelectSegmentAt index: UInt) {
        guard let index = Int(exactly: index) else {
            return
        }
        
        self.pageViewController.show(at: index, animated: true)
        
        let channelTab = index == 1 ? "2" : "1"
        self.track79447(channelTab: channelTab)
    }
    
    public func segmentControl(_ segmentControl: XYSegmentControl, didMoveToSegmentAt index: UInt) {
    }
}

extension BindLeadCardPageViewController: XYPageViewControllerDelegate, XYPageViewControllerDataSource {
    func pageViewController(_ pageViewController: XYPageViewController, viewControllerAt index: Int) -> UIViewController? {
        let currentVC = self.currentPageViewController(at: index)
        return currentVC
    }
    
    func numberOfControllers(in pageViewController: XYPageViewController) -> Int {
        return self.controllerList.count
    }
    
    func currentPageViewController(at index: Int) -> BindLeadCardListViewController? {
        if self.controllerList.indices.contains(index) {
            return self.controllerList[index]
        }
        return nil
    }
    
    func pageViewController(_ pageViewController: XYPageViewController, didTransitionFrom fromViewController: UIViewController, to toViewController: UIViewController) {
        self.segmentControl.moveToSegment(at: UInt(pageViewController.currentPageIndex), animated: true)
    }
    
    func pageViewControllerDidScroll(_ pageViewController: XYPageViewController) {
        let progress = pageViewController.scrollProgress
        segmentControl.update(withScrollProgress: progress)
    }
}

extension BindLeadCardPageViewController {
    // Track
    func baseTrackContext() -> XYTrackerEventContext {
        let noteID = XYLiveAnalyticsDataSource.sharedInstance().sourceNoteID
        let noteTrackID = XYLiveAnalyticsDataSource.sharedInstance().noteTrackID
        let adsTrackID = XYLiveAnalyticsDataSource.sharedInstance().adsTrackID
        let adsTrackUrl = XYLiveAnalyticsDataSource.sharedInstance().adsTrackUrl
        let liveType = XYLiveAnalyticsDataSource.sharedInstance().liveType

        return XYLiveTrackBasePage.sharedInstance().hostAnalyticsOrganizer()
            .ads
            .trackId(adsTrackID)
            .trackURL(adsTrackUrl)
            .note
            .noteId(noteID)
            .trackId(noteTrackID)
            .page
            .pageInstance(.liveViewPage)
            .live
            .liveType(liveType)
            .event
    }
    
    func track79447(channelTab: String) {
        baseTrackContext()
            .index
            .channelTabName(channelTab)
            .page
            .pageInstance(.livePreparePage)
            .event
            .action(.click)
            .pointId(79447 /* 直播准备页/B-选卡页-卡片栏点击/点击 */)
            .isGoto(1)
            .moduleId(47076)
            .send()
    }
    
    func track79446() {
        XYAnalyticsOrganizer._
            .event
            .action(.impression)
            .pointId(79446 /* 直播准备页/B-选卡页-曝光/曝光 */)
            .isGoto(2)
            .moduleId(47075)
            .page
            .pageInstance(.livePreparePage)
            .send()
    }
}

