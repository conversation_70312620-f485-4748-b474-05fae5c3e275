//
//  XYLivePushRoomViewController+BottomSection.m
//  XYLiveKit
//
//  Created by 周博立 on 2020/9/9.
//  Copyright © 2020 XingIn. All rights reserved.
//

@import XYLiveUIKit;
@import KVOController;

#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushRoomViewController.h"
#import "XYTrackLiveBroadcastPage.h"
#import "XYLivePushRoomVC+PKInternal.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLiveEnterConfig.h"
#import "XYLivePushIntroRequestNode.h"
#import "XYLivePushWebNode.h"
#import "XYLivePushEffectNode.h"



@import XYConfigCenter;
@import XYStorageCore_Linker;

@implementation XYLivePushRoomViewController (BottomSection)

- (void)setupKVOForBottomView {
    __weak typeof(self) wself = self;
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCToPresentPKVC)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (!XYLiveManagerSharedInstance.streamDidPush) {
            ///推流尚未完成
            [XYAlertCenter live_showTextItemWithText:xyllm_toast_sys_push_not_ready];
            return;
        }
        
        [wself.pkVC showInViewController:wself resolution:wself.decorateVM.currentRoomInfo.encodedResolution];
    }];

    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCToPresentMorePanelVC)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself showMorePanelWithGuide:nil];
    }];
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCToPresentInteractPanelVC)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself showInteractPanelWithGuide:nil];
    }];
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCToPresentSettingPanelVC)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself showRoomSettingPanelWithGuide:nil];
    }];
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyViewToShowLinkMicButton)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself.decorateView updateBottomLinkMicButtonEntrance:xyLiveSharedLinkmicManager.linkmicSwitch];
    }];
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyViewToShowLinkHostButton)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself.decorateView updateBottomLinkHostButtonEntrance:xyLiveSharedLinkmicManager.battleSwitch];
    }];
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCToPresentGroupChat)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        id<XYLivePushGroupChatServiceProtocol> groupChatService = [wself.serviceManager getServiceWithProtocol:@protocol(XYLivePushGroupChatServiceProtocol)];
        [groupChatService showGroupChatPushView];
    }];
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCToPresentDashboard)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        XYExecuteOnMainQueue(^{
            id<XYLivePushWebServiceProtocol> webService = [wself.serviceManager getServiceWithProtocol:@protocol(XYLivePushWebServiceProtocol)];
            [webService presentDashboardVC];
        });
    }];
    
    NSDictionary *settings = XYConfigCenter().dictionaryForKey(@"ios_alpha_bugfix_settings", nil);
    BOOL enable = [settings[@"alphaLiveAtmosphereAnimateCompletionFixEnable"] boolValue];
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyBottomViewRefreshLottie)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself.decorateView refreshBottomLottie];
        if (enable) {
            [wself.decorateView.bottomView excuteShoppingBtnAnimatePlayCompletionManual];
        }
    }];
}

- (void)clearMoreIconRedDot {
    if ([XYLiveEnterConfig liveFilterEnter]) {
        [XYLiveFilterNewFeatureTipManager setRedDotShowed];
    }
}

- (void)showMorePanelWithGuide:(NSString *)guide {
    BOOL frontCamera = [XYLiveManagerSharedInstance.coreManager.media isCameraFront];
    BOOL supportPause = !self.shouldStopPush;
    if (![XYLiveLinkConfig enableKasaPause]) {
        supportPause = !self.shouldStopPush && ![self canApplyKasa];
    }
    id<XYLivePushIntroRequestServiceProtocol> introService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushIntroRequestServiceProtocol)];
    [self.morePanelVC showHostMorePanelIn:self
                                     guide:guide
                               frontCamera:frontCamera
                                   backApp:self.shouldStopPush
                           goodsIntroReqOn:![introService isIntroRequestSwitchOff]
                  hostSettingRedDotCleared:self.decorateVM.isHostLiveSettingRedDotCleared
                       beautyRedDotCleared:self.decorateVM.isHostEffectRedDotCleared panelItemsRef:self.decorateVM.panelItemsRef
                                     pause:supportPause
                                    isGame:self.shouldScreenPushing];
    [self clearMoreIconRedDot];
    [XYLogCollector xyLiveLogTag:@"MorePannel" content:[NSString stringWithFormat:@"show"]];
}

- (void)showInteractPanelWithGuide:(NSString *)guide {
    [self.morePanelVC showHostInteractPanelIn:self
                                         guide:guide
                                         music:([self canApplyRTC] || [self canApplyKasa])
                                       linkmic:[self.decorateView.bottomView needMoveLinkmicToMore]
                                     redPacket:YES
                                     letterBox:[self.decorateView.bottomView needMoveLetterBoxListToMore]];
    [self clearMoreIconRedDot];
}

- (void)showRoomSettingPanelWithGuide:(NSString *)guide {
    id<XYLivePushIntroRequestServiceProtocol> introService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushIntroRequestServiceProtocol)];
    id<XYLiveMultiLinkPushServiceProtocol> linkService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveMultiLinkPushServiceProtocol)];
    id<XYLivePushEffectNodeServiceProtocol> effectService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushEffectNodeServiceProtocol)];
    
    [self.settingPanelVC showHostRoomSettingPanelIn:self
                                              guide:guide
                                    goodsIntroReqOn:![introService isIntroRequestSwitchOff]
                                        frontCamera:[XYLiveManagerSharedInstance.coreManager.media isCameraFront]
                              voiceAnnounceDuration:self.decorateVM.announceVM.enable ? self.decorateVM.announceVM.duration : 0
                                          isLinking:linkService.isLinking
                                      noGoodsFilter:[effectService enableNoGoodsFilter] && !self.shouldStopPush && !XYLiveManagerSharedInstance.isScreenLive
                                    noGoodsFilterOn:[effectService noGoodsFilterSwitchOn]];
}

@end
