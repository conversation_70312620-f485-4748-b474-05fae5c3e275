//
//  XYLivePushRoomViewController+KvoRegistry.m
//  XYLiveKit
//
//  Created by 周博立 on 2021/4/10.
//  Copyright © 2021 XingIn. All rights reserved.
//

@import KVOController;
@import XYConfigCenter;
@import XYLiveUIKit;
@import XYNoteCardComponent;

#import "XYLivePushRoomViewController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLivePushRoomVC+UserCardInternal.h"
#import "XYTrackLiveBroadcastPage.h"
#import "XYLivePlayRoomEventService.h"
#import "XYLivePushUserCardService.h"
#import "XYLiveCommonShareNode.h"
#import "XYLivePushGiftNode.h"
#import "XYLiveEnterConfig.h"

@implementation XYLivePushRoomViewController (KvoRegistry)

- (void)kvoRegistry_setup {
    [self setupKVOForDecorateVM];
    
    [self setupKVOForBottomView];
    [self link_setupKvo];
    [self battle_setupKVO];
    
    [self actionlink_setupKVO];
    [self rtcPusher_setupKvo];
    [self im_setupKVO];
    [self setupFuncUpdateKVO];
}

- (void)setupKVOForDecorateVM {
    __weak typeof(self) wself = self;
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCToPresentRedpacketGiftListVC)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        id<XYLivePushGiftNodeServiceProtocol> service = [wself.serviceManager getServiceWithProtocol:@protocol(XYLivePushGiftNodeServiceProtocol)];
        [service showGiftListWithRedPacketStyle];
    }];
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCToDismiss)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        XYExecuteOnMainQueue(^{
            if ([wself canApplyRTC]) {
                [wself.rtcPusher stopPush];
                [wself.rtcCore stopPush];
            } else if ([wself canApplyKasa]){
                [wself link_reset];
                [wself.kasaPusher stopPush];
            } else {
                [wself link_reset];
                [wself.pusher stopPush];
            }
            [XYLiveManagerSharedInstance.coreManager.media stopPreview];
            if ([XYLivePushSentryInfo enable]) {
                [XYLivePushSentryInfo cleanSentryRecordLivePushInfo];
            }
            if (wself.presentingViewController) {
                [wself dismissViewControllerAnimated:YES completion:nil];
            } else {
                [wself.navigationController popViewControllerAnimated:YES];
            }
        });
    }];
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCToDismissWithNoAnimation)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        XYExecuteOnMainQueue(^{
            if ([wself canApplyRTC]) {
                [wself.rtcPusher stopPush];
                [wself.rtcCore stopPush];
            } else if ([wself canApplyKasa]){
                [wself link_reset];
                [wself.kasaPusher stopPush];
            } else {
                [wself link_reset];
                [wself.pusher stopPush];
            }
            [XYLiveManagerSharedInstance.coreManager.media stopPreview];
            if ([XYLivePushSentryInfo enable]) {
                [XYLivePushSentryInfo cleanSentryRecordLivePushInfo];
            }
            NSMutableArray *vcs = [wself.navigationController.viewControllers mutableCopy];
            if ([vcs containsObject:wself]) {
                [vcs removeObject:wself];
            }
            [wself.navigationController setViewControllers:vcs.copy animated:NO];
        });
    }];
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCToDismissNotStopRTC)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        XYExecuteOnMainQueue(^{
            if (wself.presentingViewController) {
                [wself dismissViewControllerAnimated:YES completion:nil];
            } else {
                [wself.navigationController popViewControllerAnimated:YES];
            }
        });
    }];
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCLiveTerminated)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (!XYLiveManagerSharedInstance.isLiveFinished) {
            NSInteger maxCount = wself.decorateVM.maxAudienceCountWhenTerminated;
            maxCount = maxCount == 0 ? -1 : maxCount;
            NSString *cameraLens = [XYLiveManagerSharedInstance.coreManager.media isCameraFront] ?@"front_camera" :@"rear_camera";
            NSString *switchJson = [[XYLiveCameraAdapter shared] currentEffectSwitchUploadInfo];
            NSString *filterJson = [[XYLiveCameraAdapter shared] currentFilterUploadInfo];
            NSString *beautyJson = [[XYLiveCameraAdapter shared] currentBeautyUploadInfo];
            NSString *styleMakeupJson = [[XYLiveCameraAdapter shared] currentStyleMakeupUploadInfo];
            NSString *bodyJson = [[XYLiveCameraAdapter shared] currentBodyUploadInfo];
            NSString *texiaoJson = [XYLiveManagerSharedInstance.coreManager.media currentTexiaoUploadInfo];
            NSString *liveType = XYLiveManagerSharedInstance.roomInfo.hasGoods ? @"goods" : @"Interaction";
            float faceInfo = [[XYLiveCameraAdapter shared] currentFaceInfoUploadInfo];
            [XYTrackLiveBroadcastPage eventActionID2965WithRoomID:@(wself.roomInfo.roomID).stringValue hostId:wself.roomInfo.hostInfo.userID liveType:liveType maxViewer:maxCount cameraLens:cameraLens switchParams:switchJson filterParams:filterJson beautyParams:beautyJson styleMakeupParams:styleMakeupJson bodyParams:bodyJson texiaoParams:texiaoJson faceInfo:faceInfo];
            
            if (XYConfigCenter().boolForKey(@"ios_alpha_camera_whitebalance_status_report", YES)) {
                if (XYLiveManagerSharedInstance.coreManager.media.supportLockWhiteBalance) {
                    NSString *roomId = @(wself.roomInfo.roomID).stringValue;
                    BOOL locked = XYLiveManagerSharedInstance.coreManager.media.whiteBalanceLocked;
                    BOOL isFront = [XYLiveManagerSharedInstance.coreManager.media isCameraFront];
                    [XYAPMLiteTracker track:^(XYAPMLiteContext * _Nonnull context) {
                        context.eventName(@"ios_alpha_camera_whitebalance_status")
                            .sampleRate(1)
                            .appendEventData(@"roomId", roomId ?: @"")
                            .appendEventData(@"status", locked ? @"1" : @"0")
                            .appendEventData(@"isfront", isFront ? @"1" : @"0");
                    }];
                }
            }
            
            if (XYConfigCenter().boolForKey(@"ios_alpha_camera_effect_detail_report", YES)) {
                if (XYLiveCameraConfig.autoPushOrginBuffer) {
                    NSString *roomId = @(wself.roomInfo.roomID).stringValue;
                    NSString *effectsJson = XYLiveManagerSharedInstance.coreManager.media.effectDetail;
                    CGFloat percent = [XYLiveManagerSharedInstance.coreManager.media pushOriginBufferPercent];
                    [XYAPMLiteTracker track:^(XYAPMLiteContext * _Nonnull context) {
                        context.eventName(@"ios_alpha_camera_effect_detail")
                            .sampleRate(1)
                            .appendEventData(@"roomId", roomId ?: @"")
                            .appendEventData(@"effects_json", effectsJson ?: @"")
                            .appendEventData(@"origin_percent", [NSString stringWithFormat:@"%.4f", percent]);
                    }];
                }
            }
        }
        XYLiveManagerSharedInstance.isLiveRoomJoined = NO;
        XYLiveManagerSharedInstance.isLiveFinished = YES;
    }];
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(userInSelectedChatMsg)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (!wself.decorateVM.userInSelectedChatMsg.userID.length) {
            return;
        }
        XYLiveUserInfo *user = wself.decorateVM.userInSelectedChatMsg;
        id<XYLivePushUserCardServiceProtocol> service = [wself.serviceManager getServiceWithProtocol:@protocol(XYLivePushUserCardServiceProtocol)];
        [service handleFetchingUserInfoAndPresentingWithUser:user clearBackground:YES];
    }];
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(userInTappedOtherHost)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        XYLiveUserInfo *user = wself.decorateVM.userInTappedOtherHost;
        id<XYLivePushUserCardServiceProtocol> service = [wself.serviceManager getServiceWithProtocol:@protocol(XYLivePushUserCardServiceProtocol)];
        [service handleFetchingUserInfoAndPresentingWithUser:user otherRole:XYLiveOtherRoleHostLiving sourceType:XYLiveInvokeSourceTypeBattle];
    }];
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(userInTappedGiftBubbleView)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        XYLiveUserInfo *user = wself.decorateVM.userInTappedGiftBubbleView;
        id<XYLivePushUserCardServiceProtocol> service = [wself.serviceManager getServiceWithProtocol:@protocol(XYLivePushUserCardServiceProtocol)];
        [service handleFetchingUserInfoAndPresentingWithUser:user];
    }];
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyViewToUpdateStopUI)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself dismissPresentingCardView];
        [xyLiveSharedAPMManager updateRTCPusherTotalPushTime:(CFAbsoluteTimeGetCurrent() - wself.viewDidLoadTime)];
        [xyLiveSharedAPMManager reportRTCPusherLagSummary];
    }];
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCToBreakOffPusher)) options:NSKeyValueObservingOptionNew | NSKeyValueObservingOptionOld block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        wself.pushBreakOff = [[change objectForKey:NSKeyValueChangeNewKey] boolValue];
        [XYLogCollector xyLiveLogTag:@"LivePush" content:[NSString stringWithFormat:@"notify to stop publish: %d", wself.pushBreakOff]];
    }];
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCToStopPusher)) options:NSKeyValueObservingOptionNew | NSKeyValueObservingOptionOld block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        // pushType 打日志用
        NSString *pushType = @"nil";
        if ([wself canApplyRTC]) {
            pushType = @"RTC";
            if ([change objectForKey:NSKeyValueChangeOldKey] != [change objectForKey:NSKeyValueChangeNewKey]) {
                [wself updateApmV2BasicParams];
                [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventPushStop recovery:wself.roomInfo.isRecovery encodeInfo:wself.rtcPusher.referenceVideoEncoderParam];
            }
//            [wself.rtcPusher stopPush];
//            [wself.rtcCore stopPush];
            [XYLiveManagerSharedInstance.bgmManager endPlay];
            if (wself.shouldScreenPushing) {
                [XYLogCollector xyLiveLogTag:@"screen_capture" content:@"call stop capture"];
                [wself.rtcPusher stopScreenCapture];
                [wself.rtcCore stopScreenCapture];
            }
            [wself.rtcPusher stopPush];
            [wself.rtcCore stopPush];
        } else {
            
            if ([change objectForKey:NSKeyValueChangeOldKey] != [change objectForKey:NSKeyValueChangeNewKey]) {
                if ([wself canApplyKasa]) {
                    pushType = @"KASA";
                    [wself updateApmKasaParam];
                    [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventPushStop recovery:wself.roomInfo.isRecovery encodeInfo:wself.kasaPusher.videoEncodeInfo];
                    [wself.kasaPusher stopPush];
                } else {
                    pushType = @"RTMP";
                    [wself updateApmRtmpParam];
                    [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventPushStop recovery:wself.roomInfo.isRecovery encodeInfo:wself.pusher.videoEncodeInfo];
                    [wself.pusher stopPush];
                }
            }
            [XYLiveManagerSharedInstance.bgmManager endPlay];
//            [wself.pusher stopPreview];
            [wself link_reset];
        }
        [XYLogCollector xyLiveLogTag:@"LivePush" content:[NSString stringWithFormat:@"Stop Publish pushType: %@", pushType]];
        
        [XYLiveManagerSharedInstance.coreManager.media stopPreview];
        
        XYLivePlayRoomEventService *eventService = [wself.serviceManager getServiceWithProtocol:@protocol(XYLivePlayRoomEventServiceProtocol)];
        [eventService.asXYLivePlayRoomEventServiceProtocol eventPushFinished];
    }];
}

- (void)im_setupKVO {
    __weak typeof(self) wself = self;
    [self.KVOController observe:self.decorateVM
                        keyPath:NSStringFromSelector(@selector(notifiyIMAccountForcedOffline))
                        options:NSKeyValueObservingOptionNew
                          block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        XYExecuteOnMainQueue(^{
            // cny直播间兜底处理，不进行弹出
            if (XYLiveManagerSharedInstance.isNewYearRoom && !XYConfigCenter().boolForKey(@"ios_live_host_not_in_room_kicked", YES)) {
                return;
            }
            
            void(^backToAPPWithoutNotifyStop)(void) = ^{
                XYLiveAlertView *alert = [XYLiveAlertView alertViewWithTitle:@"提示" subTitle:@"已在其他设备开播或观看，请重新开播"];
                alert.isSingleButtonStyle = YES;
                wself.decorateVM.notifyVCToStopPusher = YES;
                alert.singleBtnBlock = ^(UIButton * _Nonnull sender) {
                    [wself mutiLandDismissWithoutStopServer];
                };
                
                [alert showInWindow:[UIApplication sharedApplication].keyWindow animated:YES];
            };
            
            if (XYConfigCenter().boolForKey(@"ios_live_enable_host_red_kick", YES)) {
                backToAPPWithoutNotifyStop();
                return;
            }
            
            [XYAlertCenter live_showTextItemWithText:@"连接断开"];
        });
    }];
}

- (void)setupFuncUpdateKVO {
    __weak typeof(self) wself = self;
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyAudienceSendGiftEnable)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        XYExecuteOnMainQueue(^{
            BOOL isOn = [XYLiveEnterConfig giftEnterStatus] == XYLiveEnterConfigStatusOn;
            [wself.settingPanelVC updateSwitchStateWithType: XYLivePushPanelItemTypeGiftControl isOn: isOn];
        });
    }];
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(kvoMessageFaceV)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself updateFaceVInfo:wself.decorateVM.kvoMessageFaceV];
    }];
}

@end
