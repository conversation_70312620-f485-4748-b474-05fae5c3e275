//
//  XYLivePushMultiChatConfig.m
//  XYLiveKit
//
//  Created by 大远 on 2025/2/17.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatConfig.h"

@implementation XYLivePushMultiChatConfig

- (instancetype)init{
    if (self = [super init]) {
        _isOpened = YES;
        _layoutType = XYLiveMultiLinkLayoutTypeFloat;
        _limitType = XYLiveMultiChatLimitTypeAll;
        _applyCoins = @"";
        _enableGiftSwitch = NO;
    }
    return self;
}

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"layoutType": @"layout_type",
        @"isOpened": @"linkmic_switch",
        @"limitType": @"viewer_apply_restrict_type",
        @"applyCoins": @"viewer_apply_coins",
        @"enableGiftSwitch": @"viewer_enable_send_gift"
    };
}

@end
