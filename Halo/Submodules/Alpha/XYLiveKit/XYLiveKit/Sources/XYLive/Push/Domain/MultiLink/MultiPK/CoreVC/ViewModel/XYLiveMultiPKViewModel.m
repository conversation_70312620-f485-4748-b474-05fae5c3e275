//
//  XYLiveMultiPKViewModel.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/31.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiPKViewModel.h"
#import <XYAlphaUtils/NSDictionary+XYLive.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYLiveKit/XYLiveMultiLinkTimer.h>
#import <XYLiveKit/XYLiveMultiPKModel.h>
#import <XYAlphaNetwork/XYAlphaNetwork-Swift.h>
#import <XYSessionManager/XYSessionManager.h>

@interface XYLiveMultiPKViewModel()<XYLiveMultiLinkTimerDelegate>

@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveIMDistributerServiceProtocol> socketService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, assign) XYLiveMultiPKState state;
@property (nonatomic, strong) XYLiveMultiLinkRoomInfo *roomInfo;
@property (nonatomic, strong) XYLiveMultiPKModel *pkModel;
@property (nonatomic, strong) XYLiveMultiPKTeamInfo *leftTeamInfo;
@property (nonatomic, strong) XYLiveMultiPKTeamInfo *rightTeamInfo;
@property (nonatomic, assign) XYLiveMultiLinkBizType bizType;
@property (nonatomic, assign) XYLiveMultiPKResult result;
@property (nonatomic, strong) XYLiveMultiLinkTimer *timer;

@end

@implementation XYLiveMultiPKViewModel

- (void)dealloc {
    [self stopTimer];
}

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                          socketService:(id<XYLiveIMDistributerServiceProtocol>)socketService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _liveInfoService = liveInfoService;
        _socketService = socketService;
        _multiLinkService = multiLinkService;
        // 注册监听
        [self registerObservers];
    }
    return self;
}

// 是否处于PK中
- (BOOL)isPKing {
    return self.roomInfo != nil;
}

- (CGRect)renderAreaFrame {
    return self.roomInfo ? self.roomInfo.renderAreaFrame : CGRectZero;
}

// 是否是主播
- (BOOL)isHost {
    return self.liveInfoService.isHost;
}

// PK数据
- (XYLiveMultiPKModel *)pkInfo {
    return self.pkModel;
}

// 结束连接
- (void)stopLink {
    // 发起请求
    [self.multiLinkService leaveRoomWithBizExtraInfo:nil completion:^(NSError * _Nonnull error) {
        // 如果失败，则Toast提示
        error ? [XYAlert live_showTextItemWithError:error] : nil;
    }];
}

// 再来一局
- (void)startPKAgain {
    // 支持发送邀请
    [self.multiLinkService inviteWithBizType:self.bizType targetUserId:self.rightTeamInfo.userId targetRoomId:self.rightTeamInfo.roomId mediaType:XYLiveMultiLinkMediaTypeVideo bizExtraInfo:nil];
}

// 结束PK
- (void)stopPK {
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/pk/v1/normal_quit" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMPOST;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"room_id"] = self.liveInfoService.roomId;
    params[@"app_id"] = @(1);
    [api addingQueryItems:params.copy];
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        [XYLogCollector xyLiveLogTag:kXYLiveMultiPKModuleName content:[NSString stringWithFormat:@"stop pk response,{error:%@}", response.error]];
        // Toast提示
        if (response.error) {
            [XYAlert live_showTextItemWithError:response.error];
        }
    }];
}

// 静音
- (void)muteSpeakerWithUserId:(NSString *)userId isMute:(BOOL)isMute completion:(void(^_Nullable)(NSError *error))completion {
    [self.multiLinkService updateMuteStateWithTargetUserId:userId isMute:isMute bizExtraInfo:nil completion:^(NSError * _Nonnull error) {
        [XYLogCollector xyLiveLogTag:kXYLiveMultiPKModuleName content:[NSString stringWithFormat:@"mute speaker response,{error:%@}", error]];
        if (error) {
            [XYAlert live_showTextItemWithError:error];
        } else {
            if (isMute) {
                [XYAlert live_showTextItemWithText:@"已将对方静音"];
            }
        }
        completion ? completion(error) : nil;
    }];
}

#pragma mark - Private

- (void)registerObservers {
    // 监听多人互动框架
    [self.multiLinkService registerListener:self];
    // 注册监听IM
    WS
    [self.socketService im_registerWithType:@"pk_change" key:kXYLiveMultiPKModuleName completionHandler:^(NSString * _Nonnull key, XYLiveCodableModel * _Nullable rawModel, NSDictionary<NSString *,id> * _Nonnull rawData) {
        // 合法性校验
        NSDictionary *pkInfoRawData = [rawData xyLive_objectValueForKey:@"pk_info"];
        NSInteger currentTimeMills = [[rawData xyLive_numberValueForKey:@"current_time"] integerValue];
        if (!pkInfoRawData) { return; }
        // 数据解析
        XYLiveMultiPKModel *pkModel = [XYLiveMultiPKModel xy_modelWithDictionary:pkInfoRawData];
        pkModel.currentTs = currentTimeMills / 1000;
        XYExecuteOnMainQueue(^{
            // 更新数据
            [weak_self updatePKModel:pkModel source:@"PK_CHANGE"];
        });
    }];
}

// 更新PK数据
- (void)updatePKModel:(XYLiveMultiPKModel *)pkModel source:(NSString *)source {
    // 合法性校验
    if (pkModel.currentTs <= self.pkModel.currentTs) { return; }
    // 校验pkId是否合法
    if (![pkModel.pkId isEqualToString:self.multiLinkService.roomInfo.chatId]) { return; }
    // 刷新缓存
    self.pkModel = pkModel;
    // 更新战队信息
    [self updateTeamInfoWithModel:pkModel source:source];
    // 更新状态
    [self updatePKStage:pkModel.stage source:source];
    // 更新PK结果
    [self updatePKResult:pkModel.winnerTeamId source:source];
}

// 过滤战队信息
- (void)updateTeamInfoWithModel:(XYLiveMultiPKModel *)model source:(NSString *)source {
    XYLiveMultiPKTeamInfo *leftTeamInfo = nil;
    XYLiveMultiPKTeamInfo *rightTeamInfo = nil;
    for (XYLiveMultiPKTeamInfo *teamInfo in model.teamList) {
        if ([teamInfo.userId isEqualToString:self.liveInfoService.anchorId]) {
            leftTeamInfo = teamInfo;
        } else {
            rightTeamInfo = teamInfo;
        }
    }
    self.leftTeamInfo = leftTeamInfo;
    self.rightTeamInfo = rightTeamInfo;
    // 事件分发
    if ([self.delegate respondsToSelector:@selector(onPKUpdateTeamInfo:rightTeamInfo:)]) {
        [self.delegate onPKUpdateTeamInfo:leftTeamInfo rightTeamInfo:rightTeamInfo];
    }
}

// 更新PK状态
- (void)updatePKStage:(NSString *)stage source:(NSString *)source {
    // 状态映射
    XYLiveMultiPKState state = [self convertToPKStateWithStage:stage];
    // 合法性校验
    if (self.state == state) { return; }
    NSInteger interval = (self.pkModel.endTs - self.pkModel.currentTs);
    [XYLogCollector xyLiveLogTag:kXYLiveMultiPKModuleName content:[NSString stringWithFormat:@"updatePKState: %@,  to:%@, source: %@, countDown:%@", @(self.state), @(state), source, @(interval)]];
    // 更新缓存
    self.state = state;
    // 开启定时器
    [self startTimerWithInterval:interval];
    // 事件分发
    if ([self.delegate respondsToSelector:@selector(onPKUpdateState:pkInfo:)]) {
        [self.delegate onPKUpdateState:state pkInfo:self.pkModel];
    }
}

// 更新PK结果
- (void)updatePKResult:(NSString *)winnerTeamId source:(NSString *)source {
    // 合法性校验
    if (self.state != XYLiveMultiPkStateCommunication) { return; }
    // 结果映射
    XYLiveMultiPKResult result = [self convertPKResultWithWinnerTeamId:winnerTeamId];
    // 合法性校验
    if (self.result == result) { return; }
    [XYLogCollector xyLiveLogTag:kXYLiveMultiPKModuleName content:[NSString stringWithFormat:@"updatePKResult: %@, source: %@", @(result), source]];
    self.result = result;
    // 事件分发
    if ([self.delegate respondsToSelector:@selector(onPKUpdateResult:pkInfo:)]) {
        [self.delegate onPKUpdateResult:result pkInfo:self.pkModel];
    }
}

// 处理PK开始
- (void)handlePKStartWithBizType:(XYLiveMultiLinkBizType)bizType pkInfo:(XYLiveMultiPKModel *)pkInfo {
    // 更新缓存
    self.bizType = bizType;
    [XYLogCollector xyLiveLogTag:kXYLiveMultiPKModuleName content:[NSString stringWithFormat:@"onPKStart:%@", @(bizType)]];
    // 事件分发
    if ([self.delegate respondsToSelector:@selector(onPKStart:pkInfo:)]) {
        [self.delegate onPKStart:bizType pkInfo:pkInfo];
    }
}

// 处理PK结束
- (void)handlePKEndWithBizType:(XYLiveMultiLinkBizType)bizType {
    [XYLogCollector xyLiveLogTag:kXYLiveMultiPKModuleName content:[NSString stringWithFormat:@"onPKEnd:%@", @(bizType)]];
    // 事件分发
    if ([self.delegate respondsToSelector:@selector(onPKEnd:)]) {
        [self.delegate onPKEnd:bizType];
    }
    // 清理资源
    [self clear];
}

// 清理资源
- (void)clear {
    self.pkModel = nil;
    self.state = XYLiveMultiPKStateIdle;
    self.bizType = XYLiveMultiLinkBizTypeUnknown;
    self.leftTeamInfo = nil;
    self.rightTeamInfo = nil;
    self.roomInfo = nil;
    self.result = XYLiveMultiPKResultUnknown;
    // 关闭定时器
    [self stopTimer];
}

// 状态映射
- (XYLiveMultiPKState)convertToPKStateWithStage:(NSString *)stage {
    if ([stage isEqualToString:@"battle"]) {
        return XYLiveMultiPKStateGaming;
    } else if ([stage isEqualToString:@"communication"]) {
        return XYLiveMultiPkStateCommunication;
    }
    return XYLiveMultiPKStateIdle;
}

// 结果映射
- (XYLiveMultiPKResult)convertPKResultWithWinnerTeamId:(NSString *)winnerTeamId {
    for (XYLiveMultiPKTeamInfo *teamInfo in self.pkModel.teamList) {
        if ([teamInfo.teamId isEqualToString:winnerTeamId]) {
            if ([teamInfo.userId isEqualToString:self.liveInfoService.anchorId]) {
                return XYLiveMultiPKResultWin;
            }
            return XYLiveMultiPKResultLose;
        }
    }
    return XYLiveMultiPKResultDraw;
}

#pragma mark - XYLiveMultiLinkListener

- (void)onRtcBizStart:(XYLiveMultiLinkBizType)bizType bizExtraInfo:(NSString *)bizExtraInfo {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypePKHeat &&  bizType != XYLiveMultiLinkBizTypePKGift) { return; }
    // 更新缓存
    self.roomInfo = self.multiLinkService.roomInfo;
    // 数据转模型
    XYLiveMultiPKModel *pkModel = [XYLiveMultiPKModel xy_modelWithJSON:bizExtraInfo];
    pkModel.currentTs = self.multiLinkService.roomInfo.currentTimeMills / 1000.0;
    // 事件转发
    [self handlePKStartWithBizType:bizType pkInfo:pkModel];
    // 更新数据
    [self updatePKModel:pkModel source:@"BIZ_START"];
}

- (void)onRtcUpdateRoomInfo:(XYLiveMultiLinkRoomInfo *)roomInfo {
    // 合法性校验
    if (roomInfo.bizType != XYLiveMultiLinkBizTypePKHeat &&  roomInfo.bizType != XYLiveMultiLinkBizTypePKGift) { return; }
    // 更新缓存
    self.roomInfo = roomInfo;
    // 数据转模型
    XYLiveMultiPKModel *pkModel = [XYLiveMultiPKModel xy_modelWithJSON:roomInfo.bizExtraInfo];
    pkModel.currentTs = roomInfo.currentTimeMills / 1000.0;
    // 更新数据
    [self updatePKModel:pkModel source:@"ROOM_IFNO"];
    // 事件转发
    if ([self.delegate respondsToSelector:@selector(onPKUpdateRoomInfo:)]) {
        [self.delegate onPKUpdateRoomInfo:roomInfo];
    }
}

- (void)onRtcBizEnd:(XYLiveMultiLinkBizType)bizType bizExtraInfo:(NSString *)bizExtraInfo {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypePKHeat &&  bizType != XYLiveMultiLinkBizTypePKGift) { return; }
    // 更新状态
    [self updatePKStage:@"unknown" source:@"BIZ_END"];
    // 事件转发
    [self handlePKEndWithBizType:bizType];
}

#pragma mark - Timer

- (void)startTimerWithInterval:(NSInteger)interval {
    // 保证仅有一个定时器
    if (self.timer) {
        [self stopTimer];
    }
    XYLiveMultiLinkTimer *timer = [[XYLiveMultiLinkTimer alloc] initWithInterval:interval];
    timer.delegate = self;
    [timer start];
    self.timer = timer;
}

- (void)stopTimer {
    [self.timer stop];
    self.timer = nil;
}

- (void)onTimerTrick:(NSInteger)remainInterval totalInterval:(NSInteger)totalInterval {
    // 事件转发
    if ([self.delegate respondsToSelector:@selector(onPKTrickCountDown:pkInfo:)]) {
        [self.delegate onPKTrickCountDown:remainInterval pkInfo:self.pkModel];
    }
}

- (void)onTimerFinish {
    [self stopTimer];
    [XYLogCollector xyLiveLogTag:kXYLiveMultiPKModuleName content:@"count down finish"];
    // 为了解决IM丢失导致无法结算，需要单独调用同步接口
    if (self.liveInfoService.isHost) {
        [self syncPKSettleStatus];
    }
}

// 同步结算状态
- (void)syncPKSettleStatus {
    // 发起业务请求
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/v1/line/pk/settle" type:XYLiveDomainTypeLiveGo];
    api.method = WSHTTPMPOST;
    api.urlEncoding = WorldSnakeJsonEncoding.jsonEncoding;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"room_id"] = self.liveInfoService.roomId;
    params[@"app_id"] = @(1);
    [api addingQueryItems:params.copy];
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        [XYLogCollector xyLiveLogTag:kXYLiveMultiPKModuleName content:[NSString stringWithFormat:@"upload pk settle response,{error:%@}", response.error]];
        if (response.error) {
            [XYAlert live_showTextItemWithError:response.error];
        }
    }];
}
    
@end
