//
//  XYLivePushMultiChatConfig.h
//  XYLiveKit
//
//  Created by 大远 on 2025/2/17.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveKit/XYLivePushMultiChatConsts.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiChatConfig : NSObject

// 布局方式
@property (nonatomic, assign) XYLiveMultiLinkLayoutType layoutType;
// 是否开启
@property (nonatomic, assign) BOOL isOpened;
// 限制类型
@property (nonatomic, assign) XYLiveMultiChatLimitType limitType;
// 薯币数量
@property (nonatomic, copy)   NSString *applyCoins;
// 是否开启送礼开关
@property (nonatomic, assign) BOOL enableGiftSwitch;

@end

NS_ASSUME_NONNULL_END
