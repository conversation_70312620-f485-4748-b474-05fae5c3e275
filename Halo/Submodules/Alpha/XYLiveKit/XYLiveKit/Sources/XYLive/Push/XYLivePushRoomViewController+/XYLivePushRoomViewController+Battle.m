//
//  XYLivePushRoomViewController+Battle.m
//  XYLiveKit
//
//  Created by 洛萨 on 2020/7/23.
//  Copyright © 2020 XingIn. All rights reserved.
//

@import XYTracker;
@import KVOController;
@import XYLiveUIKit;
@import XYLiveFoundation;
@import XYDevice;

@import Masonry;
@import XYConfigCenter;

#import "XYLivePushRoomViewController.h"
#import "XYTrackLiveBroadcastPage.h"
#import "XYLivePushDecorateViewModel.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLivePushRoomVC+PKInternal.h"
#import "XYLiveLinkmicAlertView.h"
#import "XYLivePushUserCardService.h"

#import <XYLiveKit/XYLiveKit-Swift.h>

static NSString * _Nonnull kLinkmicAlert = @"LinkmicAlert";

@implementation XYLivePushRoomViewController (Battle)

#pragma mark <Battle IM Handling>

- (void)battle_setupKVO {
    __weak typeof(self) wself = self;
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(userInHostCandidateListView)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        XYLiveOtherRole otherRole = (wself.decorateVM.linkmicCardContainerViewModel.candidateViewModel.tabIndex == 0 || XYLiveManagerSharedInstance.isLiveChat) ? XYLiveOtherRoleNone :XYLiveOtherRoleHostLiving;
        XYLiveUserInfo *user = wself.decorateVM.userInHostCandidateListView;
        id<XYLivePushUserCardServiceProtocol> service = [wself.serviceManager getServiceWithProtocol:@protocol(XYLivePushUserCardServiceProtocol)];
        [service handleFetchingUserInfoAndPresentingWithUser:user otherRole:otherRole sourceType:XYLiveInvokeSourceTypeDefault];
    }];
}

@end
