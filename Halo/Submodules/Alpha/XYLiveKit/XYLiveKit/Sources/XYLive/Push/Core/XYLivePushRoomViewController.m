//
//  XYLivePushPlanAViewController.m
//  XYPostKit
//
//  Created by <PERSON><PERSON><PERSON> on 2019/9/9.
//

@import XYUIKitCore;
@import KVOController;
@import XYAlertCenter;
@import XYLivePlayManager;
@import XYDevice;
@import XYFoundation;
@import XYPostBeautyKit;
@import XYLogCollector;
@import XYRouter;
@import XYLiveFoundation;
@import XYLiveUIKit;
@import XYNoteCardComponent;
@import XYConfigCenter;
@import XYLivePusher;
@import XYLivePlayManager;
@import XYDeveloperKit;
@import XYMacroConfig;

#import "UINavigationController+XYLiveNav.h"
#import "XYLivePushRoomViewController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>

/// node kit
#import "XYLiveServiceFactory.h"
#import "XYLivePushNodesLoader.h"
#import "XYLivePushHierarchyManager.h"
#import "XYLivePlayRoomEventService.h"
#import "XYLivePushAdapterService.h"
#import "XYLiveRtcCoreService.h"
#import "XYLiveInfoService.h"

///private class extensions
#import "XYLivePushRoomVC+BottomInternal.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLivePushRoomVC+UserCardInternal.h"
#import "XYLivePushRoomVC+PKInternal.h"

///management
#import "XYTrackLiveBroadcastPage.h"
#import "XYLiveEnterConfig.h"
#import <XYLiveKit/XYLiveWebResourceManager.h>
#import "XYLiveWalletManager.h"
#import "XYLivePKManager.h"
#import "XYLiveManager.h"

///view
#import "XYLivePushDecorateView.h"
#import <XYLiveKit/XYLiveHostCardView.h>
#import "XYLivePlayingRenderContainerView.h"

///model
#import "XYIMModel.h"
#import <XYLiveKit/XYLivePushDecorateViewModel.h>
#import "XYLiveGiftModel+XYLiveGiftAnimation.h"
#import "XYLiveCommonInfoCardNode.h"
#import "XYLivePushWebNode.h"
#import "UIViewController+XYLive.h"
#import "XYLivePushUserCardService.h"

@import XYLiveFoundation;

@import XYLiveUIKit;
@import XYLiveCore;

NSInteger pushAudioCheckInterval = 5;

@interface XYLivePushRoomViewController ()
<
XYLiveKasaPusherDelegate,
XYLivePusherDelegate,
XYLivePlayerEventDelegate,
RTCPusherDelegate,
XYLiveRtcCoreListenerProtocol
>

@property (nonatomic, strong) XYLiveDomainBuilder *domainBuilder;

@end

@implementation XYLivePushRoomViewController

- (instancetype)initWithRoomInfo:(XYLiveRoomInfo * _Nullable)roomInfo
                      loginParam:(XYLiveLoginParam * _Nullable)loginParam
                          pusher:(XYLiveRtmpPusher * _Nullable)pusher
                       bizPushStyle:(XYLiveRoomBizPushStyle)bizPushStyle
                      screenshot:(UIImageView * _Nullable)screenshotView {
    if (self = [super init]) {
        XYLiveManagerSharedInstance.enterRoomTimeMs = NSDate.date.timeIntervalSince1970 * 1000;
        self.needInitLivePush = YES;
        self.roomInfo = roomInfo;
        self.loginParam = loginParam;
        self.pusher = pusher;
        self.bizPushStyle = bizPushStyle;
        self.screenshotView = screenshotView;
    }
  
    [self configHostLeavingImage:roomInfo.isVoiceLive];
    return self;
}

- (instancetype)initWithRoomInfo:(XYLiveRoomInfo * _Nullable)roomInfo
                      loginParam:(XYLiveLoginParam * _Nullable)loginParam
                          kasaPusher:(XYLiveKasaPusher * _Nullable)kasaPusher
                       bizPushStyle:(XYLiveRoomBizPushStyle)bizPushStyle
                      screenshot:(UIImageView * _Nullable)screenshotView {
    if (self = [super init]) {
        XYLiveManagerSharedInstance.enterRoomTimeMs = NSDate.date.timeIntervalSince1970 * 1000;
        self.needInitLivePush = YES;
        self.roomInfo = roomInfo;
        self.loginParam = loginParam;
        self.kasaPusher = kasaPusher;
        self.bizPushStyle = bizPushStyle;
        self.screenshotView = screenshotView;
    }

    [self configHostLeavingImage:roomInfo.isVoiceLive];
    [self.kasaPusher setMicrophoneStatus:!XYLiveManagerSharedInstance.isMicrophoneOff];
    return self;
}

- (instancetype)initWithRoomInfo:(XYLiveRoomInfo * _Nullable)roomInfo
                      loginParam:(XYLiveLoginParam * _Nullable)loginParam
                       session:(RTCChannelSession * _Nullable)rtcSession
                       bizPushStyle:(XYLiveRoomBizPushStyle)bizPushStyle
                      screenshot:(UIImageView * _Nullable)screenshotView {
    if (self = [super init]) {
        XYLiveManagerSharedInstance.enterRoomTimeMs = NSDate.date.timeIntervalSince1970 * 1000;
        [self setupOnInitWithRoomInfo:roomInfo loginParam:loginParam rtcSession:rtcSession];
        if (rtcSession != nil) {
            if (XYLiveManagerSharedInstance.globalWindow.rtcPusher) {
                self.rtcPusher = XYLiveManagerSharedInstance.globalWindow.rtcPusher;
                XYLiveManagerSharedInstance.globalWindow.rtcPusher = nil;
            } else {
                self.rtcPusher = [[RTCPusher alloc] init];
            }
            
            [self.rtcPusher loadRTChannelSession:rtcSession];
            XYLiveBussinessType busType = XYLiveBussinessTypeVideo;
            // 语音直播
            if (roomInfo.isVoiceLive) {
                [XYLiveManagerSharedInstance.coreManager.media stopPreview];
            }
            
            // 录屏直播
            if (self.shouldScreenPushing) {
                [XYLogCollector xyLiveLogTag:@"screen_capture" content:@"destroy camera"];
                [XYLiveManagerSharedInstance.coreManager.media releaseCamera];
                busType = XYLiveBussinessTypeGame;
            }
            // 根据业务线获取不同的dispathInfo
            RTCVideoEncodeInfo *encodeInfo = nil;
            if (self.roomInfo.pushDispatchInfo.defaultResolution && XYAlphaSwitch.livePushDispatch) {
                if (XYConfigCenter().boolForKey(@"ios_live_enable_talk_encode", YES) == YES) {
                    encodeInfo = [RTCVideoEncodeInfo encodeInfoWithDispatchInfo:self.roomInfo.pushDispatchInfo vendorType:rtcSession.vendorType contentType:self.roomInfo.contentType];
                } else {
                    encodeInfo = [RTCVideoEncodeInfo encodeInfoWithDispatchInfo:self.roomInfo.pushDispatchInfo vendorType:rtcSession.vendorType bussType: busType];
                }
            } else {
                encodeInfo = [RTCVideoEncodeInfo encodeInfoWithResolution:self.roomInfo.encodedResolution vendorType:rtcSession.vendorType];
            }
            
            [self configHostLeavingImage:roomInfo.isVoiceLive];
            [self.rtcPusher reloadVideoEncoderParam:encodeInfo];
            [self.rtcPusher setMicrophoneStatus:!XYLiveManagerSharedInstance.isMicrophoneOff];
        }
        self.bizPushStyle = bizPushStyle;
        self.screenshotView = screenshotView;
    }
    return self;
}

- (instancetype)initWithRoomInfo:(XYLiveRoomInfo * _Nullable)roomInfo
                      loginParam:(XYLiveLoginParam * _Nullable)loginParam
                       liveSession:(RTCChannelSession * _Nullable)rtcSession
                       bizPushStyle:(XYLiveRoomBizPushStyle)bizPushStyle
                      screenshot:(UIImageView * _Nullable)screenshotView {
    if (self = [super init]) {
        XYLiveManagerSharedInstance.enterRoomTimeMs = NSDate.date.timeIntervalSince1970 * 1000;
        [self setupOnInitWithRoomInfo:roomInfo loginParam:loginParam rtcSession:rtcSession];
        if (rtcSession != nil) {
            XYLiveBussinessType busType = XYLiveBussinessTypeVideo;
            
            if (roomInfo.isVoiceLive) {
                [XYLiveManagerSharedInstance.coreManager.media stopPreview];
            }
            
            // 录屏直播
            if (self.shouldScreenPushing) {
                [XYLogCollector xyLiveLogTag:@"screen_capture" content:@"destroy camera"];
                [XYLiveManagerSharedInstance.coreManager.media releaseCamera];
                busType = XYLiveBussinessTypeGame;
            }
            
            // 根据业务线获取不同的dispathInfo
            RTCVideoEncodeInfo *encodeInfo = nil;
            if (self.roomInfo.pushDispatchInfo.defaultResolution && XYAlphaSwitch.livePushDispatch) {
                if (XYConfigCenter().boolForKey(@"ios_live_enable_talk_encode", YES) == YES) {
                    encodeInfo = [RTCVideoEncodeInfo encodeInfoWithDispatchInfo:self.roomInfo.pushDispatchInfo vendorType:rtcSession.vendorType contentType:self.roomInfo.contentType];
                } else {
                    encodeInfo = [RTCVideoEncodeInfo encodeInfoWithDispatchInfo:self.roomInfo.pushDispatchInfo vendorType:rtcSession.vendorType bussType: busType];
                }
            } else {
                encodeInfo = [RTCVideoEncodeInfo encodeInfoWithResolution:self.roomInfo.encodedResolution vendorType:rtcSession.vendorType];
            }
            // 畅聊开播前必须保证是可连线状态，这里调用before_client_link
            
            [rtcSession reloadEncodeInfo:encodeInfo];
            [self loadRtcCoreWithRTCSession:rtcSession];
            
        }
        self.bizPushStyle = bizPushStyle;
        self.screenshotView = screenshotView;
    }
    return self;
}

#pragma mark - VC life

- (BOOL)prefersStatusBarHidden {
    return NO;
}

- (UIStatusBarStyle)preferredStatusBarStyle {
    return UIStatusBarStyleLightContent;
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    [XYLogCollector xyLiveLogTag:@"push" content:@"didReceiveMemoryWarning"];
}

- (void)dealloc {
    [self lifecycle_dealloc];
}

static NSString * const kXhsAppGroupId = @"group.com.lothar.live.replay.ext";

- (void)viewDidLoad {
    [super viewDidLoad];
    // 埋点页面注册
    [[XYLiveTrackBasePage sharedInstance] registerAnchorLivePageHost:self];
    [self setTrackCommonParams];
    // 解决iOS18导航条展示白条问额
    if ([XYLiveConfigCenter enableIOS18NavAdapter]) {
        self.navigationController.navigationBarHidden = YES;
    }
    
    [self lifecycle_viewDidLoad];
}

- (void)setTrackCommonParams {
    NSString *type = self.roomInfo.hasGoods ? @"goods" : @"Interaction";
    XYLivePushTrackCommonParams *commonParams = [XYLivePushTrackCommonParams new];
    commonParams.liveType = type;
    commonParams.roomID = self.roomInfo.roomIDStr;
    commonParams.hostID = self.roomInfo.hostInfo.userID;
    [[XYLiveTrackBasePage sharedInstance] updatePushCommonParams:commonParams];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES];
    [self lifecycle_viewWillAppear];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    [UIApplication sharedApplication].idleTimerDisabled = YES;
    [self lifecycle_viewDidAppear];
    
    if ([XYMacroDefine debugORAdhoc]) {
        [XYOdysseyManager.shared showShortcut];
    }
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [self lifecycle_viewWillDisappear];
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    [UIApplication sharedApplication].idleTimerDisabled = NO;
    [self lifecycle_viewDidDisappear];
}

- (BOOL)shouldAutorotate {
    return NO;
}

#pragma mark - override

- (NSArray *)createServiceItems {
    return [XYLiveServiceFactory servicesBySecen:LiveSceneTypePush];
}

- (XYLiveBaseNodeLoader *)createNodesLoader {
    return [[XYLivePushNodesLoader alloc] init];
}

- (XYLiveHierarchyManager *)createHierarchyManager {
    return [[XYLivePushHierarchyManager alloc] init];
}

- (void)setupServiceManager {
    [super setupServiceManager];
    [self rtcPusher_addRtcCoreService];
}

#pragma mark - Private data

- (BOOL)isVisible {
    return (self.isViewLoaded && self.view.window);
}

- (void)dismissPresentingCardView {
    XYExecuteOnMainQueue(^{
        [self notifyAllDismissCardView];
        [self->_hostGiftListVC dismissRecvGiftList];
        
        [self->_shieldWordVC dismiss];
        [self->_liveAlertView dismissAnimated:NO];
        [self->_morePanelVC dismiss];
        [self->_webVC dismissAnimated:NO completion:nil];
        [self->_pkVC dismiss];
        [self.presentedViewController dismissViewControllerAnimated:NO completion:NULL];
    });
}

- (void)setupPusherDelegate {
    if ([self canApplyRTC]) {
        self.rtcPusher.rtcPusherDelegate = self;
        id<XYLiveRtcCoreServiceProtocol> coreService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveRtcCoreServiceProtocol)];
        [coreService registerListener:self];
    } else if ([self canApplyKasa]) {
        self.kasaPusher.delegate = self;
    } else {
        if (self.pusher) {
            self.pusher.delegate = self;
        } else {
            self.pusher.delegate = self;
        }
    }
}

#pragma mark <Getter & Setter>

- (void)setLinkType:(XYLiveIMLinkmicType)linkType {
    _linkType = linkType;
    if (linkType == XYLiveIMLinkmicTypeAudio) {
        self.decorateView.linkMicHeight = XYLiveLinkmicWindowHeight;
    } else {
        self.decorateView.linkMicHeight = XYLiveLinkmicVideoWindowHeight;
    }
}

- (XYLiveBlurBackgroundView *)blurBGView {
    if (!_blurBGView) {
        _blurBGView = [XYLiveBlurBackgroundView new];
        _blurBGView.hidden = YES;
    }
    return _blurBGView;
}

- (XYLiveStopPushBackgroundView *)stopPushBGView {
    if (!_stopPushBGView) {
        _stopPushBGView = [XYLiveStopPushBackgroundView new];
        _stopPushBGView.hidden = YES;
    }
    return _stopPushBGView;
}

- (XYLiveCustomAlertView *)screenCaptureView {
    if (!_screenCaptureView) {
        ScreenCaptureAlertView *view = [[ScreenCaptureAlertView alloc] init];
        __weak typeof(self) wself = self;
        _screenCaptureView = [[XYLiveCustomAlertView alloc] initWithContentView:view leftViewEvent:nil rightViewEvent:^{
            XYExecuteOnMainQueue(^{
                [XYLiveBroadcastExtensionLauncher launch];
                
                xyLiveSharedAPMManager.startApmInfo.triggerScreenCapBizTs = [NSDate date].timeIntervalSince1970;
                xyLiveSharedAPMManager.startApmInfo.triggerScreenCapBizFlag = YES;
                // 点击自定义录屏pickerView 开启录屏能力检测
                [wself startScreenPusherCheck];
            });
        }];
    }
    return _screenCaptureView;
}

- (void)startScreenPusherCheck {
    if ([self canApplyRTC]) {
        [self.rtcCore startScreenPusherCheck];
    }
}
- (UIView *)cameraRenderingContainerView {
    if (!_cameraRenderingContainerView) {
        _cameraRenderingContainerView = [UIView new];
        _cameraRenderingContainerView.backgroundColor = Theme.color.black;
    }
    return _cameraRenderingContainerView;
}

- (XYLivePushDecorateViewModel *)decorateVM {
    if (!_decorateVM) {
        _decorateVM = [XYLivePushDecorateViewModel new];
        @weakify(self);
        BOOL enable = [XYLiveConfigCenter enableLiveDomainServiceConnect];
        _decorateVM.getService = ^id _Nonnull(Protocol * _Nonnull protocol, XYServiceType serviceType) {
            @strongify(self);
            if (enable) {
                return [self.smImpl getServiceFromAll:protocol];
            } else {
                return [self.serviceManager getServiceWithProtocol:protocol type:serviceType];
            }
        };
        
        _decorateVM.getRegion = ^id<XYLiveHierarchyRegionProtocol> _Nullable(NSString * _Nonnull key) {
            @strongify(self);
            return [self.hierarchyManager regionWithKey:key];
        };
    }
    return _decorateVM;
}

- (XYLivePushDecorateView *)decorateView {
    if (!_decorateView) {
        _decorateView = [XYLivePushDecorateView new];
        [self setup_BindViewAndVM];
    }
    return _decorateView;
}

- (UIView *)renderContainerView {
    if (!_renderContainerView) {
        _renderContainerView = [UIView new];
    }
    return _renderContainerView;
}

- (XYLivePlayingRenderContainerView *)playerRenderingView {
    if (!_playerRenderingView) {
        _playerRenderingView = [XYLiveRenderContainerViewFactory createRenderView];
        _playerRenderingView.hidden = YES;
    }
    return _playerRenderingView;
}

- (XYLivePKViewController *)pkVC {
    if (!_pkVC) {
        _pkVC = [XYLivePKViewController new];
        [self setupPKVCBlock];
    }
    return _pkVC;
}

- (XYLiveHostGiftListViewController *)hostGiftListVC {
    if (!_hostGiftListVC) {
        _hostGiftListVC = [XYLiveHostGiftListViewController new];
        __weak typeof(self) wself = self;
        _hostGiftListVC.didSelectAudienceBlock = ^(XYLiveRecvGiftInfo * _Nonnull recvGift, NSIndexPath * _Nonnull indexPath) {
            id<XYLivePushUserCardServiceProtocol> service = [wself.serviceManager getServiceWithProtocol:@protocol(XYLivePushUserCardServiceProtocol)];
            [service handleFetchingUserInfoAndPresentingWithUser:recvGift.user];
        };
    }
    return _hostGiftListVC;
}

- (XYLiveMorePanelViewController *)morePanelVC {
    if (!_morePanelVC) {
        _morePanelVC = [XYLiveMorePanelViewController new];
        _morePanelVC.disableShowTopic = self.roomInfo.obsType == XYLiveRoomBizPushOBSTypePC;
        _morePanelVC.broadcastMode = [XYLiveRoomEnumUtils convertBroadcastModeNameWithContentType:self.roomInfo.contentType obsType:self.roomInfo.obsType];
        [self setupMorePanelBlock];
    }
    return _morePanelVC;
}

- (XYLiveSettingPanelViewController *)settingPanelVC {
    if (!_settingPanelVC) {
        _settingPanelVC = [XYLiveSettingPanelViewController new];
        _settingPanelVC.liveStatus = @"livePlay";
        [self setupSettingPanelBlock];
        // 预强制使用深色模式
        if (@available(iOS 13.0, *)) {
            _settingPanelVC.overrideUserInterfaceStyle = UIUserInterfaceStyleDark;
        }
    }
    return _settingPanelVC;
}

- (XYLiveShieldWordViewController *)shieldWordVC {
    if (!_shieldWordVC) {
        _shieldWordVC = [XYLiveShieldWordViewController new];
    }
    return _shieldWordVC;
}

- (XYLiveVoiceAnnoncementSettingViewController *)voiceAnnounceSettingVC {
    if (!_voiceAnnounceSettingVC) {
        _voiceAnnounceSettingVC = [[XYLiveVoiceAnnoncementSettingViewController alloc] init];
    }
    return _voiceAnnounceSettingVC;
}

- (RTCCore * _Nullable)rtcCore {
    id<XYLiveRtcCoreServiceProtocol> coreService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveRtcCoreServiceProtocol)];
    return coreService.rtcCore;
}

- (RTCPresetImageUtil *)presetImageUtil {
    if (!_presetImageUtil) {
        _presetImageUtil = [RTCPresetImageUtil new];
        __weak typeof(self) wself = self;
        _presetImageUtil.inactiveImageCompleted = ^(UIImage * _Nullable image, NSError * _Nullable error) {
            [wself configInactiveImage:image];
        };
    }
    return _presetImageUtil;
}

- (void)setPushBreakOff:(BOOL)pushBreakOff {
    _pushBreakOff = pushBreakOff;
    if (pushBreakOff) {
        [self pausePush];
        if ([self canApplyRTC]) {
            [self.rtcPusher muteLocalAudio:YES];
            [self.rtcCore muteLocalAudio:YES];
        }
    } else {
        [self resumePush];
        if ([self canApplyRTC]) {
            [self.rtcPusher muteLocalAudio:NO];
            [self.rtcCore muteLocalAudio:NO];
        }
    }
}


- (void)createDomainBuilderIfNeed {
    if (!_domainBuilder) {
        XYLiveDomainBuilder *obj = [[XYLiveDomainBuilder alloc] initWithHostVC:self container:self.decorateView serviceManager:self.smImpl isHost:YES];
        self.domainBuilder = obj;
    }
}

@end
