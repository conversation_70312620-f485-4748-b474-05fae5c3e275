//
//  XYLivePushMultiChatRoomCoreModel.m
//  XYLiveKit
//
//  Created by 大远 on 2025/2/14.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatRoomCoreModel.h"
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYAlertCenter/XYAlertCenter.h>
#import <XYAlphaUtils/XYLiveIMMacro.h>
#import <XYLiveCore/XYLiveCore-Swift.h>
#import <XYLiveKit/XYLivePushMultiLinkBizTracker.h>

@interface XYLivePushMultiChatRoomCoreModel()<XYLiveMultiLinkListener>

@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveIMDistributerServiceProtocol> socketService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, assign) NSInteger applyNum;
@property (nonatomic, copy)   NSString *sessionId;
@property (nonatomic, strong) NSHashTable<id<XYLivePushMultiChatRoomCoreModelListener>> *listeners;

@end

@implementation XYLivePushMultiChatRoomCoreModel

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                          socketService:(id<XYLiveIMDistributerServiceProtocol>)socketService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _liveInfoService = liveInfoService;
        _socketService = socketService;
        _multiLinkService = multiLinkService;
        // 注册监听
        [self registerAllObserver];
    }
    return self;
}

/// 邀请
- (void)inviteWithUserId:(NSString *)userId source:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString * _Nullable)extraInfo {
    [self.multiLinkService inviteWithBizType:XYLiveMultiLinkBizTypeChatRoom targetUserId:userId targetRoomId:self.liveInfoService.roomId mediaType:XYLiveMultiLinkMediaTypeAudio bizExtraInfo:extraInfo];
}

/// 注册监听
- (void)registerListener:(id<XYLivePushMultiChatRoomCoreModelListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call or listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    // 事件补发
    if ([listener respondsToSelector:@selector(onUpdateApplyNum:)]) {
        [listener onUpdateApplyNum:self.applyNum];
    }
    [self.listeners addObject:listener];
}

/// 取消注册监听
- (void)unregisterListener:(id<XYLivePushMultiChatRoomCoreModelListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call or listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners removeObject:listener];
}

- (NSString *)roomId {
    return self.liveInfoService.roomId;
}

#pragma mark - Private

- (void)registerAllObserver {
    // 监听多人互动服务
    [self.multiLinkService registerListener:self];
    WS
    // 监听IM
    [self.socketService im_registerWithType:XYIM_CUSTOM_LINKMIC_APPLY_COUNT_CHANGE key:kXYLivePushMultiChatRoomModule completionHandler:^(NSString * _Nonnull key, XYLiveCodableModel * _Nullable rawModel, NSDictionary<NSString *,id> * _Nonnull rawData) {
        SS
        // 数据转模型
        IMLinkmicApplyCountChange *model = [IMLinkmicApplyCountChange xy_modelWithDictionary:rawData];
        // 本地缓存
        XYExecuteOnMainQueue(^{
            self.applyNum = model.applyCount;
            // 事件转发
            [self.listeners.allObjects xy_each:^(id<XYLivePushMultiChatRoomCoreModelListener>  _Nonnull obj) {
                if([obj respondsToSelector:@selector(onUpdateApplyNum:)]) {
                    [obj onUpdateApplyNum:model.applyCount];
                }
            }];
        });
    }];
}

#pragma mark - XYLiveMultiLinkListener

- (void)onInviteResponseWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId error:(NSError *)error {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeChatRoom) { return; }
    if (error == nil) {
        // 如果来源于P页触发，则进行埋点上报
        if ([self.sessionId isEqualToString:sessionId]) {
            // 上报埋点
            [XYLivePushMultiLinkBizTracker eventActionId34903WithUserId:targetUserId];
            // 清除会话标识
            self.sessionId = nil;
        }
        // Toast提示
        [XYAlert live_showTextItemWithText:@"已发送邀请"];
        // 埋点上报
        [XYLivePushMultiLinkBizTracker eventActionId33415WithBizType:XYLiveMultiLinkBizTypeChatRoom thirdTabName:@"房内观众" userId:targetUserId hasGoods:NO];
    } else {
        // Toast提示
        [XYAlert live_showTextItemWithError:error];
    }
}

- (void)onInviteFailWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId failReason:(XYLiveMultiLinkInviteFailReason)failReason {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeChatRoom) { return; }
    // 如果被拒则Toast提示
    if (failReason == XYLiveMultiLinkInviteFailReasonReject) {
        [XYAlertCenter live_showTextItemWithText:@"对方拒绝了你的连线邀请"];
    }
}

#pragma mark - Lazy

- (NSHashTable<id<XYLivePushMultiChatRoomCoreModelListener>> *)listeners {
    if (_listeners == nil) {
        _listeners = [NSHashTable weakObjectsHashTable];
    }
    return _listeners;
}


@end
