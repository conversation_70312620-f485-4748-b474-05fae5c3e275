//
//  ConditionLinkMicNumberInputBar.swift
//  XYLiveKit
//
//  Created by quhe on 2023/2/22.
//  Copyright © 2023 XingIn. All rights reserved.
//

import UIKit
import XYUITheme
import XYHybridModule
import IQKeyboardManager

@objc(XYLiveMultiChatNumberInputBar)
@objcMembers
public class MultiChatNumberInputBar: UIControl {
    public var confirmActionCallback: ((_ content: String) -> Void)?
    public var shouldChangeTextCallback: ((_ curText: String, _ range: NSRange) -> Bool)?
    public var shownCallback: (() -> Void)?
    // for simple style
    public var shouldReturnCallback:((_ content: String) -> Bool)?
    
    private lazy var editView = makeEditView()
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        p_setupUI()
        p_setupNotification()
        addTarget(self, action: #selector(p_backgroundClickedHandler), for: .touchUpInside)
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

// MARK: - Privates
private
extension MultiChatNumberInputBar {
    func p_setupUI() {
        backgroundColor = UIColor.black.withAlphaComponent(0.4)
    }
    
    func p_setupNotification() {
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillShow), name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillHide), name: UIResponder.keyboardWillHideNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(webviewKeyboardShouldCloseHandler(notification:)), name: NSNotification.Name(rawValue: XYHybridWebBroadcastNotificationName), object: nil)
    }
    
    func makeEditView() -> MultiChatNumberInputView {
        let view = MultiChatNumberInputView()
        
        view.confirmActionCallback = { [weak self] content in
            self?.confirmActionCallback?(content)
        }
        
        view.frame = CGRect(x: 0,
                            y: DeviceUtility.screenHeight,
                            width: DeviceUtility.screenWidth,
                            height: view.inputHeight)
        
        view.shouldChangeTextCallback = { [weak self] text, range -> Bool in
            guard let shouldChange = self?.shouldChangeTextCallback else {
                return true
            }
            return shouldChange(text, range)
        }
        
        view.closeActionCallback = { [weak self] in
            self?.dismiss()
        }
        return view
    }
}

// MARK: - Handlers
extension MultiChatNumberInputBar {
    func keyboardWillShow(_ notification: Notification) {
        guard window != nil else {
            return
        }
        let endFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect ?? CGRect.zero
        let keyboardHeight = DeviceUtility.screenHeight - endFrame.minY
        let curve = notification.userInfo?[UIResponder.keyboardAnimationCurveUserInfoKey] as? Int ?? 0
        let options = curve << 16
        let duration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? TimeInterval ?? 0
            UIView.animate(withDuration: duration, delay: 0, options: UIView.AnimationOptions(rawValue: UInt(options)), animations: {
                self.editView.xy_bottom = DeviceUtility.screenHeight - keyboardHeight
            })
        }
    
    func keyboardWillHide(_ notification: Notification) {
        guard window != nil else {
            return
        }
        let curve = notification.userInfo?[UIResponder.keyboardAnimationCurveUserInfoKey] as? Int ?? 0
        let options = curve << 16
        let duration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? TimeInterval ?? 0
        UIView.animate(withDuration: duration,
                       delay: 0,
                       options: UIView.AnimationOptions(rawValue: UInt(options)),
                       animations: {
            self.editView.xy_top = DeviceUtility.screenHeight
        },
                       completion: { _ in
            self.editView.removeFromSuperview()
            self.removeFromSuperview()
        })
    }
    
    func webviewKeyboardShouldCloseHandler(notification: Notification) {
        if let key = notification.userInfo?["key"] as? String, key == "live_keyboard_input" {
            guard let data = notification.userInfo?["data"] as? [String: Any] else { return }
            guard let model: CommonInputConfigModel = try? CommonInputConfigModel.xy_model(dictionary: data) else { return }
            model.adaptWebviewRule()
            if !model.shouldOpen {
                self.dismiss()
            }
        }
    }
    
    @objc
    func p_backgroundClickedHandler() {
        dismiss()
    }
}

extension MultiChatNumberInputBar {
    public func show() {
        show(inView: nil)
    }
    
    public func show(inView: UIView?) {
        var view = inView
        if view == nil {
            view = UIApplication.shared.keyWindow
        }
        guard let superView = view else {
            return
        }
        
        frame = superView.bounds
        superView.addSubview(self)
        
        addSubview(editView)
        
        // 某些页面打开了IQKeyboard导致UI异常
        IQKeyboardManager.shared().isEnabled = false
        IQKeyboardManager.shared().isEnableAutoToolbar = false
        IQKeyboardManager.shared().shouldResignOnTouchOutside = false
        
        editView.triggerKeyboard()
        shownCallback?()
    }
    
    public func dismiss() {
        editView.resignKeyboard()
    }
}

