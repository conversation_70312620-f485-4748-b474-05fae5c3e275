//
//  LiveAdsLeadBroadcastBulletCardProcessor.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/5/27.
//  Copyright © 2025 XingIn. All rights reserved.
//

import XYCanvas

@objcMembers
@objc(XYLiveAdsLeadBroadcastBulletCardCountUpProcessor)
class LiveAdsLeadBroadcastBulletCardCountUpProcessor: NSObject {
    
    weak var viewNode: XYCViewNode?
    private var timestamp: Int64?
    
    func start(node: XYCViewNode?, explainSeconds: Int64?) {
        self.viewNode = node
        self.timestamp = explainSeconds
        self.startTimerIfNeeded()
    }

    private var weakTimer: XYWeakTimer?
    
    private func startTimerIfNeeded() {
        self.stopTimer()
        weakTimer = XYWeakTimer.scheduledTimer(withTimeInterval: 1.0,
                                               target: self,
                                               selector: #selector(timerTick),
                                               userInfo: nil,
                                               repeats: true)
        weakTimer?.add(to: RunLoop.main, forMode: .common)
    }
    
    private func stopTimer() {
        weakTimer?.invalidate()
        weakTimer = nil
    }
    
    @objc private func timerTick() {
        let currentTimestamp = timestamp ?? 0
        timestamp = currentTimestamp + 1
        updateLabel()
    }
    
    private func updateLabel() {
        guard let timestamp = self.timestamp else { return }
        
        let totalSeconds = timestamp // 移除 Int 转换，保持 Int64 类型
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60
        
        let timeString: String
        if hours > 0 {
            timeString = String(format: "%02d:%02d:%02d", hours, minutes, seconds)
        } else {
            timeString = String(format: "%02d:%02d", minutes, seconds)
        }
        
        let explainTimeComponent = self.viewNode?.kittComponent?.findByTagName("host_explain_timer") as? DslTextComponent
        explainTimeComponent?.attributedString = NSMutableAttributedString(string: timeString)
        explainTimeComponent?.textContent = timeString
        explainTimeComponent?.labelView?.text = timeString
        explainTimeComponent?.labelView?.sizeToFit()
        explainTimeComponent?.requestLayout()
    }
    
    deinit {
        stopTimer()
    }
}
