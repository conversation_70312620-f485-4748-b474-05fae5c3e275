//
//  Untitled.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/5/21.
//  Copyright © 2025 XingIn. All rights reserved.
//

protocol BindCardBottomBarProtocol: NSObjectProtocol {
    // 是否隐藏全选按钮
    func update(shouldShowAllSelectBtn: Bool, shouldEnableCompleteBtn: Bool, shouldShowBottomBar:Bool)
    func update(selectAllCount: Int, selectedCount: Int)
    func setCanSelectNone(isAllow: Bool)
    func setSelectCardCountHandler(handler: (() -> Int)?)
    func setLimitCardCountHandler(handler: (() -> Int)?)
    func updateCompleteBtnStatus()
}
