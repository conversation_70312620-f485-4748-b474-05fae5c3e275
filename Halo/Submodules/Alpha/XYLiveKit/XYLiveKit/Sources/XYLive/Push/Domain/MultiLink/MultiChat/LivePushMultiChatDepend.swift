//
//  LivePushMultiChatDepend.swift
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

import UIKit
import XYLiveServiceProtocol
import XYLiveCore

@objc(XYLivePushMultiChatDependProtocol)
public protocol LivePushMultiChatDependProtocol: NSObjectProtocol {
    // 直播间信息服务
    func liveInfoService() -> XYLiveInfoServiceProtocol?
    // 长链接服务
    func imDistributeService() -> XYLiveIMDistributerServiceProtocol?
    // 多人互动服务
    func multiLinkService() -> XYLiveMultiLinkServiceProtocol?
    // 直播间适配层
    func pushAdapterService() -> XYLivePushAdapterServiceProtocol?
    // 个人资料卡服务
    func userCardService() -> XYLivePushUserCardServiceProtocol?
    // 底部栏服务
    func bottomBarService() -> LivePushBottomBarNodeServiceProtocol?
    // 老连麦服务
    func linkService() -> LiveMultiLinkPushServiceProtocol?
    // 团播
    func groupLiveService() -> LiveGroupServiceProtocol?
}

@objc(XYLivePushMultiChatDepend)
@objcMembers
class LivePushMultiChatDepend: NSObject, LivePushMultiChatDependProtocol {
    
    private weak var provider: ServiceProvider?
    
    init(_ provider: ServiceProvider) {
        self.provider = provider
    }
    
    public func liveInfoService() -> XYLiveInfoServiceProtocol? {
        return provider?.getService(XYLiveInfoServiceProtocol.self)
    }
    
    public func imDistributeService() -> XYLiveIMDistributerServiceProtocol? {
        return provider?.getService(XYLiveIMDistributerServiceProtocol.self)
    }
    
    public func multiLinkService() -> XYLiveMultiLinkServiceProtocol? {
        return provider?.getService(XYLiveMultiLinkServiceProtocol.self)
    }
    
    public func pushAdapterService() -> XYLivePushAdapterServiceProtocol? {
        return provider?.getService(XYLivePushAdapterServiceProtocol.self)
    }
    
    public func userCardService() -> XYLivePushUserCardServiceProtocol? {
        return provider?.getService(XYLivePushUserCardServiceProtocol.self)
    }
    
    public func bottomBarService() -> LivePushBottomBarNodeServiceProtocol? {
        return provider?.getService(LivePushBottomBarNodeServiceProtocol.self)
    }
    
    public func linkService() -> LiveMultiLinkPushServiceProtocol? {
        return provider?.getService(LiveMultiLinkPushServiceProtocol.self)
    }
    
    public func groupLiveService() -> LiveGroupServiceProtocol? {
        return provider?.getService(LiveGroupServiceProtocol.self)
    }
}
