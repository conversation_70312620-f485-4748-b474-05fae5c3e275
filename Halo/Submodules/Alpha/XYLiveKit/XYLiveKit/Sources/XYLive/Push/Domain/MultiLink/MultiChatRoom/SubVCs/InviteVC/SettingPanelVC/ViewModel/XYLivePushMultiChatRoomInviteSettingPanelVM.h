//
//  XYLivePushMultiChatRoomInviteSettingPanelVM.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/28.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiChatRoomInviteSettingPanelVM : NSObject

// 是否开启
@property (nonatomic, assign, getter=isOpened) BOOL opened;

// 是否开启送礼开关
@property (nonatomic, assign) BOOL enableGiftSwitch;

// 请求开关状态接口
- (void)requestSwitchStatusWithRoomId:(NSString *)roomId
                           completion:(void(^)(BOOL isOn, NSError *error))completion;

// 更新开关状态
- (void)updateStatusWithRoomId:(NSString *)roomId
                      isOpened:(BOOL)isOpened
                    completion:(void(^)(id result, NSError *error))completion;

// 更新送礼开关状态
- (void)updateGiftSwitchWithRoomId:(NSString *)roomId
                  enableGiftSwitch:(BOOL)enableGiftSwitch
                        completion:(void(^)(id result, NSError *error))completion;

@end

NS_ASSUME_NONNULL_END
