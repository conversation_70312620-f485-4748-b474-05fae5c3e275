//
//  XYLivePushMultiChatRoomInviteSettingPanelVC.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/27.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveMultiLinkSettingBasePanelVC.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveKit/XYLivePushMultiChatRoomCoreModel.h>
@protocol XYLiveGroupServiceProtocol;

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiChatRoomInviteSettingPanelVC : XYLiveMultiLinkSettingBasePanelVC

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)contianerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   groupLiveService:(id<XYLiveGroupServiceProtocol>)groupLiveService
                          coreModel:(XYLivePushMultiChatRoomCoreModel *)coreModel;

@end

NS_ASSUME_NONNULL_END
