//
//  XYLiveLinkHostInviteListHeaderView.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/19.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveLinkHostInviteListRandomItemView : UIView

@property (nonatomic, strong) UIImageView *bgImgView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIImageView *iconImgView;
// 点击事件回调
@property (nonatomic, copy) void(^didTapHandler)(void);

// 创建子视图
- (void)setupSubviews;

@end

@interface XYLiveLinkHostInviteListLineRandomItemView : XYLiveLinkHostInviteListRandomItemView

@end

@interface XYLiveLinkHostInviteListPKRandomItemView : XYLiveLinkHostInviteListRandomItemView

@end

@interface XYLiveLinkHostInviteListHeaderView : UIView

// 点击事件回调
@property (nonatomic, copy) void(^didTapMatchHandler)(BOOL isPK);

@end

NS_ASSUME_NONNULL_END
