//
//  XYLivePushRoomViewController+kasaPusherDelegate.m
//  XYLiveKit
//
//  Created by zhangzheng on 2022/10/11.
//  Copyright © 2022 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveKit/XYLiveKit-Swift.h>
@import XYLiveUIKit;
#import "XYLivePushRoomViewController.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLiveManager.h"
#import "XYTrackLiveBroadcastPage.h"

@import XYLiveFoundation;
@import XYLivePusher;
@import XYLiveCore;

@implementation XYLivePushRoomViewController (KasaPusherDelegate)

- (void)liveKasaPusher:(nonnull XYLiveKasaPusher *)pusher didRecvNetStats:(nonnull NSDictionary *)param {
    [self reportKasaLoop:param];
    
    BOOL check = XYLiveLinkConfig.enableRegularAudioCheck;
    if (check) {
        if (self.pushAudioCheckCountdown <= 0) {
            self.pushAudioCheckCountdown = pushAudioCheckInterval;
            [XYLiveMediaControl reloadAudioSessionWithReceiver:self sceneType:XYLiveAudioSessionScenePush];
        } else {
            self.pushAudioCheckCountdown --;
        }
    }
}

- (void)liveKasaPusherCutStreamHotSwitch:(nonnull XYLiveKasaPusher *)pusher {
    [XYLogCollector xyLiveLogTag:@"push_room" content:@"kasa recv cutStreamHotSwitch callback"];
    //主线程执行
    dispatch_async(dispatch_get_main_queue(), ^{
        // 会涉及View操作 切换到主线程执行
        [self hotSwitchToRtcPusher];
        [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventPushRtmpHotSwitchRtc recovery:self.roomInfo.isRecovery encodeInfo:self.kasaPusher.videoEncodeInfo];
    });
}

- (void)liveKasaPusherDidBeginPush:(nonnull XYLiveKasaPusher *)pusher {
    [self sdk_startLive];// 推流成功后触发start
    [self sdk_submitPushInfo:NO];
    
    [XYLiveManagerSharedInstance updatePushStats:YES];
    [XYLogCollector xyLiveLogTag:@"kasaPusher" content:@"begin"];
    [XYLiveManagerSharedInstance startPushStreamCompletion:^(BOOL success, NSError * _Nullable error) {}];
    [XYTrackLiveBroadcastPage startLiveEvent:@"start_push" isOBS:NO success:YES roomID:self.roomInfo.roomID pushUrl:self.roomInfo.streamURLInfo.livePushURL errorMessage:@""];
    
    [self updateApmRtmpParam];
    [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventPushStart recovery:self.roomInfo.isRecovery encodeInfo:self.kasaPusher.videoEncodeInfo];
    [XYLiveManagerSharedInstance reportCurrentPushType:XYLivePushTypeKasa completion:^(BOOL success, NSError * _Nullable error) {
        NSString *content = [NSString stringWithFormat:@"submit ptype resp: %@, err: %@", @(success), error];
        [XYLogCollector xyLiveLogTag:@"hotswitch" content:content];
        NSLog(@"[kasaPusher]%@", content);
        [self.decorateVM.adapter appendDebugInfo:content];
    }];
    [self.decorateVM.adapter appendDebugInfo:[NSString stringWithFormat:@"submit push type: %@", @(XYLivePushTypeKasa)]];
    
    [XYLiveManagerSharedInstance.consumeService memoryGenerate:XYLiveConsumeMemOptionPushMem];
    [XYLiveManagerSharedInstance.consumeService setContentType:XYLiveManagerSharedInstance.roomInfo.contentType];
    [XYLiveManagerSharedInstance.consumeService reportStartPush];
}

- (void)liveKasaPusherErrorWithErrorCode:(nonnull XYLiveKasaPusher *)pusher errorCode:(NSInteger)errorCode errorMsg:(nonnull NSString *)errorMsg {
    if (self.kasaPusher != nil) {
        [self updateApmKasaParam];
        [xyLiveSharedAPMManager reportLivePushError:(int32_t)errorCode msg:errorMsg extInfo:@""];
        //for New push_event
        [xyLiveSharedAPMManager reportLivePushEventError:XYLivePushEventError recovery:self.roomInfo.isRecovery encodeInfo:self.kasaPusher.videoEncodeInfo errorCode:(int32_t)errorCode errorMsg:errorMsg];
        xyLiveSharedAPMManager.startApmInfo.pushRespTs = [NSDate date].timeIntervalSince1970;
        xyLiveSharedAPMManager.startApmInfo.pushRespCode = errorCode;
        xyLiveSharedAPMManager.startApmInfo.pushRespMsg = errorMsg ?: @"";
        [xyLiveSharedAPMManager reportStartPipelineTracker];
    }
}

- (void)liveKasaPusherErrorWithNetDisconnect:(nonnull XYLiveKasaPusher *)pusher {
    [XYLogCollector xyLiveLogTag:@"kasaPusher" content:@"net_disconn"];
    if (XYLiveManagerSharedInstance.isLiveRoomJoined) {
        XYLiveRoomInfo *roomInfo = XYLiveManagerSharedInstance.roomInfo;
        NSString *rtmp = roomInfo.streamURLInfo.livePushURL;
        if (self.pushReconnChance > 0 && rtmp.length) {
            self.pushReconnChance --;
            if ([self canApplyRTC]) {
                //TODO: 洛萨
            } else {
                [self.kasaPusher stopPush];
                NSString *roomID = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
                NSString *userID = XYLiveManagerSharedInstance.userInfo.userID;
                NSDictionary *extra = @{
                    kLogHostID: userID ?: @"",
                    kLogRoomID: roomID ?: @"",
                };
                [self.kasaPusher startPush:rtmp roomId:roomID extra:extra];
            }
        } else {
            [XYLogCollector xyLiveLogTag:@"push_room" content:@"failure_to_reopen_toast"];
            __weak typeof(self) weakSelf = self;
            XYAlertAction *OK = [XYAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:^(UIAlertAction *_Nonnull action) {
                [weakSelf.decorateVM notifyManagerToLeaveRoomWithReason:@"kasa_error_confirm"];
            }];
            [[XYAlertCenter createAlertItemWithTitle:@"服务器连接失败了，可尝试重新开播" message:nil actions:@[OK] textFieldConfigs:nil isSystem:YES] show];
        }
    }
}

- (void)liveKasaPusherNetworkQuality:(nonnull XYLiveKasaPusher *)pusher withData:(RTCNetworkQualityInfo * _Nonnull)qualityInfo {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.decorateView.viewModel.networkQualityScore = qualityInfo.aggregateQualityScore;
//        NSLog(@"rtmp model: qualityInfo : rtt = %@ uploss = %@ socore = %@", @(qualityInfo.rtt), @(qualityInfo.upLoss),@(qualityInfo.aggregateQualityScore));
    });
}

- (void)liveKasaPusherPushCodecChangedEvent:(nonnull XYLiveKasaPusher *)pusher currentCodec:(int)codec {
    if (self.kasaPusher != nil) {
        // 自研推流收到codec变化 submitPushType增量更新codec
        [self sdk_submitPushInfo:YES];
        [self updateApmKasaParam];
        [xyLiveSharedAPMManager reportLivePushCodecChange:codec recovery:self.roomInfo.isRecovery encodeInfo:self.kasaPusher.videoEncodeInfo];
        NSString *content = [NSString stringWithFormat: @"kasa current Codec: %d", codec];
        [self.decorateVM.adapter appendDebugInfo: content];
    }
}

- (void)liveKasaPusherPushEvent:(nonnull XYLiveKasaPusher *)pusher eventId:(XYLivePusherEvent)eventId {
    if (self.kasaPusher != nil) {
        [self updateApmKasaParam];
        XYLivePushEvent event;
        switch(eventId) {
            case XYLivePusherEventSentFirstVideoFrame:
                [self sendSubmitPushType:NO];
                event = XYLivePushEventPushFirstVideoFrame;
                break;
            case XYLivePusherEventSentFirstAudioFrame:
                event = XYLivePushEventPushFirstAudioFrame;
                break;
            case XYLivePusherEventUserVideoAvailable:
                event = XYLivePushEventPushVideo;
                break;
            case XYLivePusherEventUserAudioAvailable:
                event = XYLivePushEventPushAudio;
                break;
            case XYLivePusherEventConnectLost:
                event = XYLivePushEventConnectionLost;
                break;
            case XYLivePusherEventConnectReconnect:
                event = XYLivePushEventConnectionReconnect;
                break;
            case XYLivePusherEventConnectRecovery:
                event = XYLivePushEventConnectionRecovery;
                break;
            case XYLivePusherEventStartPubCDNStream:
                event = XYLivePushEventStartPushCdnStream;
                break;
            case XYLivePusherEventStopPubCDNStream:
                event = XYLivePushEventStopPushCdnStream;
                break;
            case XYLivePusherEventStartPubSRSStream:
                event = XYLivePushEventStartPushSRSStream;
                break;
            case XYLivePusherEventStopPubSRSStream:
                event = XYLivePushEventStopPushSRSStream;
                break;
            case XYLivePusherEventSetMixConfig:
                event = XYLivePushEventSetMixTranscodingConfig;
                break;
            case XYLivePusherHotSwitch:
                event = XYLivePushEventPushRtmpHotSwitchRtc;
                break;
            case XYLivePusherCameraReady:
                event = XYLivePushEventCameraReady;
                break;
            case XYLivePusherMicReady:
                event = XYLivePushEventMicReady;
                break;
            case XYLivePusherVTBEncodedFirstVideo:
                /// 自研推流 在收到VTB 首帧回调 上报success 事件
                event = XYLivePushEventPushSuccess;
                break;
            default:
                event = XYLivePushEventUnknown;
                break;
        }
        [xyLiveSharedAPMManager reportLivePushEvent:event recovery:self.roomInfo.isRecovery encodeInfo:self.kasaPusher.videoEncodeInfo];
    }
}

- (void)liveKasaPusherWarnWithWarnCode:(nonnull XYLiveKasaPusher *)pusher warnCode:(NSInteger)warnCode warnMsg:(nonnull NSString *)warnMsg {
    if (self.kasaPusher != nil) {
        [self updateApmKasaParam];
        [xyLiveSharedAPMManager reportLivePushWarn:(int32_t)warnCode msg:warnMsg extInfo:@""];
        [xyLiveSharedAPMManager reportLivePushEventWarn:XYLivePushEventWarn recovery:self.roomInfo.isRecovery encodeInfo:self.kasaPusher.videoEncodeInfo warnCode:(int32_t)warnCode warnMsg:warnMsg];
    }
}

- (void)liveKasaPusherWarningWithNetBusy:(nonnull XYLiveKasaPusher *)pusher {
    [XYLogCollector xyLiveLogTag:@"kasaPusher" content:@"net_busy"];
}

- (void)liveKasaPusherWarningWithReconnect:(nonnull XYLiveKasaPusher *)pusher {
    [XYLogCollector xyLiveLogTag:@"kasaPusher" content:@"reconn"];
}

- (void)onUserVoiceVolumeWithKasaLocal:(int)volume {
    
}

- (void)liveKasaPusherLog:(nonnull XYLiveKasaPusher *)pusher withMsg:(nonnull NSString *)msg {
    [self.decorateVM.adapter appendDebugInfo: msg];
}

- (void)liveKasaGopChanged:(nonnull XYLiveKasaPusher *)pusher withGop:(int)gop {
    if (self.kasaPusher != nil) {
        [self updateApmKasaParam];
        [xyLiveSharedAPMManager reportLivePushGopEvent:gop recovery:self.roomInfo.isRecovery encodeInfo:self.kasaPusher.videoEncodeInfo];
    }
}


#pragma mark - private func
- (void)reportKasaLoop:(NSDictionary *)param {
    [self updateApmKasaParam];
    
    RTCApmStatistics *stat = [[RTCApmStatistics alloc] init];
    stat.localStats.resWidth = [param[@"videoWidth"] intValue];
    stat.localStats.resHeight = [param[@"videoHeight"] intValue];
    stat.localStats.fps = [param[@"videoFps"] intValue];
    stat.localStats.ara = [param[@"audioBitrate"] intValue];
    stat.localStats.asr = [param[@"audioSamplerate"] intValue];
    stat.localStats.vra = [param[@"videoBitrate"] intValue];
    stat.upLoss = [param[@"uploss"] intValue];
    stat.sentBytes = [param[@"sentBytes"] longValue];
    stat.rtt = [param[@"rtt"] intValue];
    
    double cameraRate = [param[@"cameraRate"] doubleValue];
    double previewRate = [param[@"previewRate"] doubleValue];
    int ori = [param[@"orientation"] intValue];

    [xyLiveSharedAPMManager reportLivePushLoop:stat cameraRate:cameraRate previewRate:previewRate cameraOrientation: ori];
}
@end
