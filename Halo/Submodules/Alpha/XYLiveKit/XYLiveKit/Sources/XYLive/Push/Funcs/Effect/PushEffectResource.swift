//
//  PushEffectResourceConfig.swift
//  XYLiveKit
//
//  Created by gongyidemac on 2024/6/14.
//  Copyright © 2024 XingIn. All rights reserved.
//

import UIKit

@objcMembers
public class PushEffectResourceConfig: NSObject {
    var noGoodsFilterTipTopBgImage: String = "https://picasso-static.xiaohongshu.com/fe-platform/1e3b386cb9a769dd93842a55fb9861883a72a0c2.webp"
}

@objc(XYLivePushEffectResource)
@objcMembers
public class PushEffectResource: NSObject {
    static var config: PushEffectResourceConfig {
        let dict: [String: Any] = ConfigCenter.shared.dictionary(forKey: "ios_live_push_effect_resource_config")
        if let c: PushEffectResourceConfig = try? PushEffectResourceConfig.xy_model(dictionary: dict) {
            return c
        }
        return PushEffectResourceConfig()
    }
    
    public static var noGoodsFilterTipTopBgImageURL: String { config.noGoodsFilterTipTopBgImage }
}
    

