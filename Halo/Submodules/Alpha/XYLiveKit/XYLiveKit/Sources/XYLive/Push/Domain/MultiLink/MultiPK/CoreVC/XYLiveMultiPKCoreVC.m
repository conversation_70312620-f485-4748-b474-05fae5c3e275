//
//  XYLiveMultiPKCoreVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/13.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiPKCoreVC.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLiveMultiPKListViewCell.h>
#import <XYLiveKit/XYLiveMultiPKAnimateView.h>
#import <XYLiveKit/XYLiveMultiPKTitleView.h>
#import <XYLiveKit/XYLiveMultiPKProgressView.h>
#import <XYLiveKit/XYLiveMultiPKContributorView.h>
#import <XYLiveKit/XYLiveMultiPKViewModel.h>
#import <XYLiveKit/XYLiveAlertViewController.h>
#import <XYLiveKit/XYLiveActionSheetViewController.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYLiveKit/XYLiveLinkHostInviteeInfo.h>
#import <XYSessionManager/XYSessionManager.h>
#import <Masonry/Masonry.h>

@interface XYLiveMultiPKCoreVC()<XYLiveMultiLinkBizStageListener, XYLiveMultiPKDelegate>

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveIMDistributerServiceProtocol> socketService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, weak)   XYLiveMultiPKAnimateView *animateView;
@property (nonatomic, weak)   XYLiveMultiPKProgressView *progressView;
@property (nonatomic, weak)   XYLiveMultiPKTitleView *titleView;
@property (nonatomic, weak)   XYLiveMultiPKContributorView *contributorListView;
@property (nonatomic, weak)   UIButton *pkAgainView;
@property (nonatomic, weak)   XYLiveAlertViewController *alertVC;
@property (nonatomic, weak)   XYLiveActionSheetViewController *sheetVC;
@property (nonatomic, strong) NSHashTable<id<XYLiveMultiPKCoreListener>> *listeners;
@property (nonatomic, strong) XYLiveMultiPKViewModel *viewModel;
@property (nonatomic, strong) XYLiveEventPassView *view;

@end

@implementation XYLiveMultiPKCoreVC

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                      socketService:(id<XYLiveIMDistributerServiceProtocol>)socketService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _liveInfoService = liveInfoService;
        _socketService = socketService;
        _multiLinkService = multiLinkService;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    // 初始化ViewModel
    [self setupViewModel];
    // 注册监听
    [self.multiLinkService registerUIListener:self forBizType:XYLiveMultiLinkBizTypePKGift];
    [self.multiLinkService registerUIListener:self forBizType:XYLiveMultiLinkBizTypePKHeat];
}

// 结束连线
- (void)stopLink {
    WS
    XYLiveAlertViewController *alertVC = [[XYLiveAlertViewController alloc] initWithTitle:@"确认结束连线" message:@"选择确定后，你将断开当前连线" cancelButtonTitle:@"取消" confirmButtonTitle:@"确认" handler:^(NSInteger index) {
        SS
        if (index == 1) {
            [self.viewModel stopLink];
        }
    }];
    [alertVC showWithAnimated:YES onVC:self.containerVC complete:nil];
}

// 重开一局
- (void)startPKAgain {
    WS
    XYLiveAlertViewController *alertVC = [[XYLiveAlertViewController alloc] initWithTitle:@"确认重开一局" message:@"选择确定后，将重开一局PK" cancelButtonTitle:@"取消" confirmButtonTitle:@"确认" handler:^(NSInteger index) {
        SS
        if (index == 1) {
            [self.viewModel startPKAgain];
        }
    }];
    [alertVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.alertVC = alertVC;
}

// 结束PK
- (void)stopPK {
    WS
    XYLiveAlertViewController *alertVC = [[XYLiveAlertViewController alloc] initWithTitle:@"确认结束PK" message:@"选择确定后，你将结束本次PK，但仍保持连线" cancelButtonTitle:@"取消" confirmButtonTitle:@"确认" handler:^(NSInteger index) {
        SS
        if (index == 1) {
            [self.viewModel stopPK];
        }
    }];
    [alertVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.alertVC = alertVC;
}

#pragma mark - XYLiveMultiLineCoreServiceProtocol

- (BOOL)isPKing {
    return self.viewModel.isPKing;
}

- (XYLiveMultiLinkBizType)bizType {
    return self.viewModel.bizType;
}

- (CGRect)renderAreaFrame {
    return self.viewModel.renderAreaFrame;
}

// 注册监听
- (void)registerListener:(id<XYLiveMultiPKCoreListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners addObject:listener];
}

// 取消注册监听
- (void)unregisterListener:(id<XYLiveMultiPKCoreListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners addObject:listener];
}

#pragma mark - XYLiveMultiLinkBizStageListener

- (UIView *)overlayListContainerView {
    return self.view;
}

- (XYLiveMultiLinkListViewCell *)overlayListView:(XYLiveMultiLinkListView *)listView cellForReuseId:(NSString *)reuseId windowInfo:(XYLiveMultiLinkWindowInfo *)windowInfo mediaInfo:(XYLiveMultiLinkMediaInfo *)mediaInfo {
    XYLiveMultiPKListViewCell *cell = [listView dequeueReusableCellWithIdentifier:reuseId];
    if (cell == nil) {
        cell = [[XYLiveMultiPKListViewCell alloc] initWithLiveInfoService:self.liveInfoService];
        cell.likeTapGesture = self.likeTapGesture;
        WS
        cell.didTapMuteHandler = ^(NSString * _Nonnull userId, BOOL isMute) {
            SS
            [self didTapMuteWithUserId:userId isMute:isMute];
        };
    }
    return cell;
}

- (void)overlayListView:(XYLiveMultiLinkListView *)listView didSelectedAtWindowInfo:(XYLiveMultiLinkWindowInfo *)windowInfo mediaInfo:(XYLiveMultiLinkMediaInfo *)mediaInfo {
    // 如果点击空麦位，不做处理
    if (windowInfo.isEmptyMic) { return; }
    // 展示个人资料卡
    XYLiveUserInfo *userInfo = windowInfo.userInfo.userInfo;
    userInfo.roomRole = XYLiveRoomRoleHost;
    userInfo.roomID = [windowInfo.userInfo.roomId integerValue];
    [self showUserCardWithUserInfo:windowInfo.userInfo.userInfo];
}

#pragma mark - XYLiveMultiPKDelegate

- (void)onPKStart:(XYLiveMultiLinkBizType)bizType pkInfo:(XYLiveMultiPKModel *)pkInfo{
    // 初始化子视图
    [self setupSubviews];
    // 事件分发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLiveMultiPKCoreListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onPKStart)]) {
            [obj onPKStart];
        }
    }];
}

- (void)onPKUpdateState:(XYLiveMultiPKState)state pkInfo:(XYLiveMultiPKModel *)pkInfo {
    // 更新倒计时状态
    [self.titleView updateState:state pkInfo:pkInfo];
    // 更新再来一局状态
    self.pkAgainView.hidden = (state != XYLiveMultiPkStateCommunication || !self.liveInfoService.isHost);
    // 事件分发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLiveMultiPKCoreListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onPKUpdateState:)]) {
            [obj onPKUpdateState:state];
        }
    }];
}

- (void)onPKUpdateRoomInfo:(XYLiveMultiLinkRoomInfo *)roomInfo {
    // 事件转发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLiveMultiPKCoreListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onPKUpdateRoomInfo:)]) {
            [obj onPKUpdateRoomInfo:roomInfo];
        }
    }];
}

- (void)onPKUpdateTeamInfo:(XYLiveMultiPKTeamInfo *)leftTeamInfo rightTeamInfo:(XYLiveMultiPKTeamInfo *)rightTeamInfo {
    // 更新贡献榜信息
    [self.contributorListView updateLeftTeamInfo:leftTeamInfo rightTeamInfo:rightTeamInfo];
    // 更新血条
    [self.progressView updateLeftTeamInfo:leftTeamInfo rightTeamInfo:rightTeamInfo];
}

- (void)onPKUpdateResult:(XYLiveMultiPKResult)result pkInfo:(XYLiveMultiPKModel *)pkInfo {
    // 更新贡献榜
    [self.contributorListView updateViewStatusWithResult:result];
    // 更新胜负动画
    [self.animateView updateViewStatusWithResult:result];
}

- (void)onPKTrickCountDown:(NSInteger)interval pkInfo:(XYLiveMultiPKModel *)pkInfo {
    // 更新倒计时
    [self.titleView updateTrickCountDown:interval pkInfo:pkInfo];
}

- (void)onPKEnd:(XYLiveMultiLinkBizType)bizType {
    // 销毁子视图
    [self.view removeFromSuperview];
    self.view = nil;
    // 关闭面板
    [self hideAllPanel];
    // 事件分发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLiveMultiPKCoreListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onPKEnd)]) {
            [obj onPKEnd];
        }
    }];
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 创建胜负动画视图
    [self setupAnimtionView];
    // 创建血条视图
    [self setupProgressView];
    // 创建贡献榜单视图
    [self setupContributorListView];
    // 创建计时器视图
    [self setupTitleView];
    // 创建再来一局视图
    [self setupPKAgainView];
    
    // 布局
    [self.animateView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    
    [self.progressView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.right.equalTo(self.view);
        make.height.mas_equalTo(20);
    }];
    
    [self.contributorListView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.progressView.mas_bottom).offset(4);
        make.left.equalTo(self).offset(10);
        make.right.equalTo(self).offset(-10);
    }];
    
    [self.titleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.progressView.mas_bottom);
        make.height.mas_equalTo(24);
        make.centerX.equalTo(self.view);
    }];
    
    [self.pkAgainView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.bottom.mas_equalTo(-16);
        make.size.mas_equalTo(CGSizeMake(120, 38));
    }];
    
    // 强制刷新布局
    [self.view.superview layoutIfNeeded];
}

// 创建动画视图
- (void)setupAnimtionView {
    XYLiveMultiPKAnimateView *animateView = [[XYLiveMultiPKAnimateView alloc] init];
    [self.view addSubview:animateView];
    self.animateView = animateView;
}

// 创建血条视图
- (void)setupProgressView {
    XYLiveMultiPKProgressView *progressView = [[XYLiveMultiPKProgressView alloc] init];
    // 更新样式
    [progressView updateUIWithBizType:self.viewModel.bizType];
    // 更新进度
    [self.view addSubview:progressView];
    self.progressView = progressView;
}

// 创建贡献榜视图
- (void)setupContributorListView {
    XYLiveMultiPKContributorView *contributorListView = [[XYLiveMultiPKContributorView alloc] init];
    WS
    contributorListView.didTapUserHandler = ^(XYLiveUserInfo * _Nonnull userInfo) {
        SS
        if (userInfo == nil) { return; }
        userInfo.roomRole = XYLiveRoomRoleAudience;
        [self showUserCardWithUserInfo:userInfo];
    };
    [self.view addSubview:contributorListView];
    self.contributorListView = contributorListView;
}

// 创建倒计时视图
- (void)setupTitleView {
    XYLiveMultiPKTitleView *titleView = [[XYLiveMultiPKTitleView alloc] initWithBizType:self.viewModel.bizType liveInfoService:self.liveInfoService];
    WS
    titleView.didTapCloseHandler = ^{
        SS
        [self showOptActionSheetPanel];
    };
    [self.view addSubview:titleView];
    self.titleView = titleView;
}

// 创建再来一局视图
- (void)setupPKAgainView {
    UIButton *pkAgainView = [UIButton buttonWithType:UIButtonTypeCustom];
    [pkAgainView setTitle:@"再来一局" forState:UIControlStateNormal];
    pkAgainView.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:15];
    pkAgainView.backgroundColor = [XYLiveTokenColor primary];
    [pkAgainView setTitleColor:[XYLiveTokenColor white] forState:UIControlStateNormal];
    pkAgainView.layer.cornerRadius = 19;
    pkAgainView.layer.masksToBounds = YES;
    pkAgainView.hidden = YES;
    [pkAgainView addTarget:self action:@selector(didTapPKAgain:) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:pkAgainView];
    self.pkAgainView = pkAgainView;
}

#pragma mark - Private

// 初始化viewModel
- (void)setupViewModel {
    XYLiveMultiPKViewModel *viewModel = [[XYLiveMultiPKViewModel alloc] initWithLiveInfoService:self.liveInfoService socketService:self.socketService multiLinkService:self.multiLinkService];
    viewModel.delegate = self;
    self.viewModel = viewModel;
}

// 展示操作面板
- (void)showOptActionSheetPanel {
    WS
    // 结束连线
    XYLivePopupItem *stopItem = XYLivePopupItemMake(@"结束连线", XYLivePopupItemTypeHighlight, ^(NSInteger index) {
        SS
        [self stopLink];
    });
    // 结束PK
    XYLivePopupItem *stopPKItem = XYLivePopupItemMake(@"结束PK", XYLivePopupItemTypeNormal, ^(NSInteger index) {
        SS
        [self stopPK];
    });
    // 重新匹配
    XYLivePopupItem *rematchItem = XYLivePopupItemMake(@"重新匹配", XYLivePopupItemTypeNormal, ^(NSInteger index) {
        SS
        self.didTapReMatchHandler ? self.didTapReMatchHandler() : nil;
    });
    // 重开一局
    XYLivePopupItem *reOpenItem = XYLivePopupItemMake(@"重开一局", XYLivePopupItemTypeNormal, ^(NSInteger index) {
        SS
        [self startPKAgain];
    });
    XYLiveActionSheetViewController *sheetVC = [[XYLiveActionSheetViewController alloc] initWithTitle:nil items:@[stopItem, stopPKItem, rematchItem, reOpenItem]];
    [sheetVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.sheetVC = sheetVC;
}

// 展示个人资料卡
- (void)showUserCardWithUserInfo:(XYLiveUserInfo *)userInfo {
    self.didTapUserHandler ? self.didTapUserHandler(userInfo) : nil;
}

// 关闭面板
- (void)hideAllPanel {
    [self.alertVC dismissWithAnimated:YES complete:nil];
    [self.sheetVC dismissWithAnimated:YES complete:nil];
}

#pragma mark - Event

- (void)didTapPKAgain:(UIButton *)sender {
    [self startPKAgain];
}

- (void)didTapMuteWithUserId:(NSString *)userId isMute:(BOOL)isMute {
    [self.viewModel muteSpeakerWithUserId:userId isMute:isMute completion:nil];
}

#pragma mark - Lazy

- (XYLiveEventPassView *)view {
    if (_view == nil) {
        _view = [[XYLiveEventPassView alloc] init];
    }
    return _view;
}

- (NSHashTable<id<XYLiveMultiPKCoreListener>> *)listeners {
    if (_listeners == nil) {
        _listeners = [NSHashTable weakObjectsHashTable];
    }
    return _listeners;
}

@end
