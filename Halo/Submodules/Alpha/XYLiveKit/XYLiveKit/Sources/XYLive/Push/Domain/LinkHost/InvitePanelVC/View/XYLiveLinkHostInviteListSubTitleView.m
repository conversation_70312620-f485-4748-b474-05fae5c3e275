//
//  XYLiveLinkHostInviteListSubTitleView.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/20.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostInviteListSubTitleView.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <Masonry/Masonry.h>

@interface XYLiveLinkHostInviteListSubTitleView()

@property (nonatomic, copy)   NSArray<UILabel *> *itemViews;
@property (nonatomic, strong) UIStackView *stackView;

@end

@implementation XYLiveLinkHostInviteListSubTitleView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

/// 绑定数据数据
- (void)bindListItems:(NSArray<NSString *> *)listItems {
    // 合法性校验
    // 创建子视图
    [self setupItemViewsWithListItems:listItems];
    // 更新数据
    [self updateItemViewsWithListItems:listItems];
}

#pragma mark - 创建子视图

- (void)setupItemViewsWithListItems:(NSArray<NSString *> *)listItems {
    // 合法性校验
    if (self.itemViews.count == listItems.count) { return; }
    // 清除之前视图
    [self.stackView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    // 动态创建视图
    NSInteger itemNum = listItems.count;
    NSMutableArray *itemViewsM = [NSMutableArray array];
    [listItems enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        // 创建标题
        UILabel *itemView = [[UILabel alloc] init];
        itemView.textColor = XYLiveTokenColor.desc;
        itemView.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
        [self.stackView addArrangedSubview:itemView];
        if (idx != itemNum - 1) {
            // 创建分割线
            UIView *divideView = [[UIView alloc] init];
            divideView.backgroundColor = XYLiveTokenColor.separator;
            [self.stackView addArrangedSubview:divideView];
            
            [divideView mas_makeConstraints:^(MASConstraintMaker *make) {
                make.size.mas_equalTo(CGSizeMake(0.5, 8));
            }];
        }
        [itemViewsM addObject:itemView];
    }];
    self.itemViews = itemViewsM.copy;
}

- (void)updateItemViewsWithListItems:(NSArray<NSString *> *)listItems {
    // 合法性校验
    if (self.itemViews.count != listItems.count) { return; }
    // 更新标题
    [listItems enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        UILabel *itemView = self.itemViews[idx];
        itemView.text = obj;
    }];
}

- (void)setupSubviews {
    // 创建伸缩容器
    UIStackView *stackView = [[UIStackView alloc] init];
    stackView.axis = UILayoutConstraintAxisHorizontal;
    stackView.spacing = 6;
    stackView.alignment = UIStackViewAlignmentCenter;
    [self addSubview:stackView];
    self.stackView = stackView;
    
    // 布局
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
}

@end
