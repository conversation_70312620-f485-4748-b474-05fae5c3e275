//
//  XYLivePushRoomViewController+Panel.m
//  XYLiveKit
//
//  Created by 周博立 on 2020/9/9.
//  Copyright © 2020 XingIn. All rights reserved.
//

@import XYLiveFoundation;
@import XYLivePusher;
@import XYLiveUIKit;
@import XYStorageCore_Linker;
@import RedI18N;
@import I18N;

#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYMacroConfig/XYMacroDefine.h>
#import "XYLivePushRoomViewController.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLivePushRoomVC+UserCardInternal.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLivePushDecorateViewModel.h"
#import "XYLivePushIntroRequestNode.h"
#import "XYLiveCommonGoodsStockNode.h"
#import "XYLiveCommonInfoCardNode.h"
#import "XYLivePushForeNoticeNode.h"
#import "XYLiveCommonShareNode.h"
#import "XYTrackLiveBroadcastPage.h"
#import "XYLivePushLotteryNode.h"
#import "XYLivePushEffectNode.h"
#import "XYLivePushGiftNode.h"
#import "XYLivePushChromakeyScreenNode.h"
#import "XYLivePushTopSectionNode.h"
#import "XYLivePushLetterNode.h"
#import "XYLivePushChangeCoverNode.h"
#import "XYIMNetworkHandler.h"
#import "XYLivePushShoppingBagNode.h"
#import "XYLiveEnterConfig.h"
#import "XYLivePushWebNode.h"
#import "XYLivePushVariousTaskNode.h"


#import <XYLiveKit/XYLiveMultiLinkSwitchConfig.h>

@import XYConfigCenter;


static NSString *const kXYLiveOBSPushBackToAPPAlertShownKey = @"XYLiveOBSPushBackToAPPAlertShownKey";

@implementation XYLivePushRoomViewController (Panel)


- (void)setupMorePanelBlock {
    self.morePanelVC.didSelectPanelItem = [self selectPanelItemBlock];
}

- (void)setupSettingPanelBlock {
    self.settingPanelVC.didSelectPanelItem = [self selectPanelItemBlock];
    
    __weak typeof(self) wself = self;
    self.settingPanelVC.didSelectItemDetail = ^(XYLivePushPanelItem * _Nonnull item) {
        switch (item.type) {
            case XYLivePushPanelItemTypeUltraWideAngle:
                [wself panel_showWideAngleAlert];
                break;
            case XYLivePushPanelItemTypeNoGoodsFilter:
                [wself panel_showNoGoodsFilterAlert];
                break;
            case XYLivePushPanelItemTypeWhiteBalanceLock:
                [wself panel_showWhiteBalanceLockAlert];
                break;
            default:
                break;
        }
    };
}

- (void (^)(XYLivePushPanelItem *item))selectPanelItemBlock {
    @weakify(self);
    return ^(XYLivePushPanelItem * _Nonnull item) {
        @strongify(self);
        XYLivePushPanelItemType type = item.type;
        [XYLogCollector xyLiveLogTag:@"MorePannel" content:[NSString stringWithFormat:@"selectPanelItemBlock:%ld", type]];
        switch (type) {
            case XYLivePushPanelItemTypePK: {
                self.decorateVM.notifyVCToPresentPKVC = YES;
            }
                break;
            case XYLivePushPanelItemTypeFastLoadGoods: {
                id<XYLiveCommonGoodsStockServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveCommonGoodsStockServiceProtocol)];
                [service showFastLoadGoods];
            }
                break;
            case XYLivePushPanelItemTypeLinkmic: {
                if (!XYLiveManagerSharedInstance.streamDidPush) {
                    ///推流尚未完成
                    [XYAlertCenter live_showTextItemWithText:xyllm_toast_sys_push_not_ready];
                    return;
                }
                // 畅聊直播间
                if ([XYLiveManager sharedManager].isLiveChat) {
                    id<XYLivePushMultiChatRoomServiceProtocol> multiChatRoomService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushMultiChatRoomServiceProtocol) type:XYServiceTypeDomain];
                    [multiChatRoomService showInvitePanelWithSource:XYLiveMultiLinkInviteSourceMore extraInfo:nil];
                } else {
                    id<XYLivePushMultiChatServiceProtocol> multiChatRoomService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushMultiChatServiceProtocol) type:XYServiceTypeDomain];
                    [multiChatRoomService showInvitePanelWithSource:XYLiveMultiLinkInviteSourceMore extraInfo:nil];
                }
            }
                break;
            case XYLivePushPanelItemTypeSendGift: {
                id<XYLivePushGiftNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushGiftNodeServiceProtocol)];
                [service moveupGiftBubbleViewAnimation:YES];
                [service showGiftList];
            }
                break;
            case XYLivePushPanelItemTypeAdminConfig: {
                id<XYLivePushTopSectionNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushTopSectionNodeServiceProtocol)];
                [service showAudienceList:XYLiveAudienceListStyleAdmin];
            }
                break;
            case XYLivePushPanelItemTypeCustomBlock: {
                [self.shieldWordVC showInViewController:self];
            }
                break;
            case XYLivePushPanelItemTypeChangeCover: { // 点击封面标题设置
                id<XYLivePushChangeCoverNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushChangeCoverNodeServiceProtocol)];
                [service showChangeCoverTip];
            }
                break;
            case XYLivePushPanelItemTypeGroupChat: {
                self.decorateVM.notifyVCToPresentGroupChat = YES;
            }
                break;
            case XYLivePushPanelItemTypeScreen: {
                id<XYLivePushChromakeyScreenNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushChromakeyScreenNodeServiceProtocol)];
                [service showChromakeySettingVC];
            }
                break;
            case XYLivePushPanelItemTypeGoodsCollection:
                break;
            case XYLivePushPanelItemTypeEncryptUserInfo: {
                item.showRedDot = NO;
                [self.settingPanelVC reloadData];
                BOOL isHiden = XYLiveManagerSharedInstance.roomInfo.nickNameEncrypted;
                NSString *title = L.live.live_viewer_profile_limit; // @"禁止观众相互查看资料";
                NSString *subTitle = [XYLiveExperimentConfig enableCommentAt] ? L.live.live_notice_profile_limit : @"禁止后，除主播和超级助手外，观众将无法互相查看他人昵称与资料";
                if (isHiden) {
                    title = L.live.live_viewer_profile; //@"允许观众相互查看资料";
                    subTitle = [XYLiveExperimentConfig enableCommentAt] ? L.live.live_notice_profile : @"允许后，观众将可以互相查看他人昵称与资料";
                }
                XYLiveAlertView *alert = [XYLiveAlertView alertViewWithTitle:title subTitle:subTitle];
                alert.rightBtnBlock = ^(UIButton * _Nonnull sender, BOOL timeout) {
                    [self.settingPanelVC reverseSettingWithItem:item];
                    [self.decorateVM hideUserInfo:!isHiden complete:^(BOOL success) {
                        if (success) {
                            XYLiveManagerSharedInstance.roomInfo.nickNameEncrypted = !isHiden;
                        }
                    }];
                };
                [alert showInView:self.view animated:YES];
            }
                break;
            case XYLivePushPanelItemTypeShare: {
                @weakify(self)
                [self updateShareInfo:^{
                    @strongify(self)
                    id<XYLiveCommonShareServiceProtocol> shareService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveCommonShareServiceProtocol)];
                    [shareService loadSharingVC];
                }];
            }
                break;
            case XYLivePushPanelItemTypeGiftAnimationToggle: {
                [self.settingPanelVC reverseSettingWithItem:item];
                XYLiveManager.sharedManager.roomInfo.giftAnimationToggle = !XYLiveManager.sharedManager.roomInfo.giftAnimationToggle;
                if (XYLiveManager.sharedManager.roomInfo.giftAnimationToggle) {
                    [XYAlertCenter live_showTextItemWithText:@"已屏蔽其他用户的礼物特效"];
                    BOOL enableEffectV2 = XYExperiments().justOnceBoolForKey(@"ios_enable_effect_domain_v2");
                    if (enableEffectV2) {
                        id<XYLiveEffectServiceProtocolV2> effectService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveEffectServiceProtocolV2) type:XYServiceTypeDomain];
                        [effectService clearGuestTask];
                    } else {
                        id<XYLiveEffectServiceProtocol> effectService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveEffectServiceProtocol) type:XYServiceTypeDomain];
                        [effectService clearGuestTask];
                    }
                } else {
                    [XYAlertCenter live_showTextItemWithText:@"已开启其他用户的礼物特效"];
                }
            }
                break;
            case XYLivePushPanelItemTypeBeauty: { // 点击美颜
                [self didClickBeauty];
            }
                break;
            case XYLivePushPanelItemTypeFilter: {
                id<XYLivePushEffectNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushEffectNodeServiceProtocol)];
                [service handleEnterEffectSettingViewController:XYLiveEffectTypeFilter];
                
                [XYLiveFilterNewFeatureTipManager setRedDotCleared];
            }
                break;
            case XYLivePushPanelItemTypeRecvGift: {
                [self.hostGiftListVC showInViewController:self];
            }
                break;
            case XYLivePushPanelItemTypeMirror: {
                [self.settingPanelVC reverseSettingWithItem:item];
                NSString *toast = [self doPanelItemTypeMirror: item];
                if (toast.length) {
                    [[XYAlertCenter live_createTextItemWithText:toast] show];
                }
            }
                break;
            case XYLivePushPanelItemTypeBackAPP: {
                [XYLogCollector xyLiveLogTag:@"obs" content:@"back_app_btn_pressed"];
                // 不发起stop，直接dismiss
                @weakify(self);
                void(^backToAPPWithoutNotifyStopBlock)(void) = ^{
                    @strongify(self);
                    [self dismissWithoutStopServer];
                    [[XYSCKVMigrateTool defaultUserDefaultTool_v2] setBool:YES forKey:kXYLiveOBSPushBackToAPPAlertShownKey];
                    [XYLogCollector xyLiveLogTag:@"obs" content:@"did_back_to_app"];
                };
                if (![[XYSCKVMigrateTool defaultUserDefaultTool_v2] boolForKey:kXYLiveOBSPushBackToAPPAlertShownKey]) {
                    [[XYAlertCenter createAlertItemWithMessage:@"返回App后可正常使用其他功能，点击首页底部加号-直播可返回手机直播间" cancelTitle:@"取消" confirmTitle:@"确定并不再提醒" completionHandler:^(BOOL isOrNot) {
                        if (isOrNot) {
                            backToAPPWithoutNotifyStopBlock();
                        }
                    }] show];
                } else {
                    backToAPPWithoutNotifyStopBlock();
                }
            }
                break;
            case XYLivePushPanelItemTypeDashboard: {
                self.decorateVM.notifyVCToPresentDashboard = YES;
            }
                break;
            case XYLivePushPanelItemTypeIntroReq: {
                [self.settingPanelVC reverseSettingWithItem:item];
                BOOL on = item.status  == XYLivePushPanelItemStatusSelected;
                id<XYLivePushIntroRequestServiceProtocol> introService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushIntroRequestServiceProtocol)];
                [introService updateGoodsIntroSwitch:on];
            }
                break;
            case XYLivePushPanelItemTypeEffect: {
                [self didClickBeauty];
                self.decorateVM.isHostEffectRedDotCleared = YES;
            }
                break;
                
            case XYLivePushPanelItemTypeSetting: { // 直播间设置
                self.decorateVM.notifyVCToPresentSettingPanelVC = YES;
                if (item.showRedDot) {
                    self.decorateVM.isHostLiveSettingRedDotCleared = YES;
                }
            }
                break;
                
            case XYLivePushPanelItemTypeLetterBox: {
                id<XYLivePushLetterNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushLetterNodeServiceProtocol)];
                [service presentQuesionBoxList];
            }
                break;
            case XYLivePushPanelItemTypeForenotice: { // 点击直播预告
                [self setupForeNotice];
            }
                break;
            case XYLivePushPanelItemTypeVote: {
                id<XYLiveHostVoteServiceProtocol> voteService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveHostVoteServiceProtocol) type:XYServiceTypeDomain];
                [voteService showCreateVoteViewController];
            }
                break;
            case XYLivePushPanelItemTypeHostComment: { // 点击评论
                [self.decorateView triggerCommentBarResponse];
            }
                break;
            case XYLivePushPanelItemTypeVoiceAnnouncement: {
                @weakify(self);
                self.voiceAnnounceSettingVC.onConfigChanged = ^(XYLiveVoiceAnnouncementConfigCacheModel * _Nullable config) {
                    @strongify(self);
                    if (config.voiceOn) {
                        item.duration = config.voiceInterval;
                    } else {
                        item.duration = 0;
                    }
                    
                    [self.settingPanelVC reloadData];
                    [self.decorateVM.announceVM resetWithModel:config];
                };
                [self.voiceAnnounceSettingVC configDataWithConfig:self.decorateVM.announceVM.configModel];
                [self.voiceAnnounceSettingVC showWithVc:self];
            }
                break;
            case XYLivePushPanelItemTypeInfoCard: {
                id<XYLiveCommonInfoCardServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveCommonInfoCardServiceProtocol)];
                [service enterInfoCard];
            }
                break;
            case XYLivePushPanelItemTypeMusic: {
                [self note_bgm_showPanel];
            }
                break;
            case XYLivePushPanelItemTypePushPause: {
                id<XYLivePushPauseNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushPauseNodeServiceProtocol)];
                [service handlePushPauseClicked];
                
            }
                break;
            case XYLivePushPanelItemTypeFloatingPlay: {
                // 命中开关 & 连麦状态，则不支持展示小窗
                id<XYLiveMultiLinkPushServiceProtocol> linkService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveMultiLinkPushServiceProtocol)];
                if ([XYLiveMultiLinkSwitchConfig enableRTCInterceptFloatWindow] &&  [linkService isLinking]) {
                    [XYAlert live_showTextItemWithText:@"连线场景下，无法开启小窗"];
                    return;
                }
                id<XYLivePushFloatingWindowNodeServiceProtocol> floatingService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushFloatingWindowNodeServiceProtocol)];
                [floatingService handleSmallWindowClicked];
            }
                break;
            case XYLivePushPanelItemTypePureComment: {
                if ([XYLiveExperimentConfig enableAnchorCommentSetting]) {
                    [self.decorateView showCommentSettingVC];
                    [XYLivePanelFuncDataSource saveCommentSettingReddot];
                } else {
                    [self.decorateView enterPureCommentMode];
                }
            }
                break;
            case XYLivePushPanelItemTypeGamePipChatSet: {
                id<XYLivePushGamePipChatNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushGamePipChatNodeServiceProtocol)];
                [service showGamePipChatSetVC];
            }
                break;
            case XYLivePushPanelItemTypeGamePipChatOpen: {
                id<XYLivePushGamePipChatNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushGamePipChatNodeServiceProtocol)];
                [service openGamePip];
            }
                break;
            case XYLivePushPanelItemTypeMicrophone: {
                [self.settingPanelVC reverseSettingWithItem:item];
                BOOL on = item.status  == XYLivePushPanelItemStatusSelected;
                XYLiveManagerSharedInstance.isMicrophoneOff = !on;
                if ([self canApplyRTC]) {
                    [self.rtcPusher setMicrophoneStatus:on];
                    [self.rtcCore setMicrophoneStatus:on];
                } else if ([self canApplyKasa]) {
                    [self.kasaPusher setMicrophoneStatus:on];
                }
                id<XYLiveMultiLinkPushServiceProtocol> linkService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveMultiLinkPushServiceProtocol)];
                if ([linkService isLinking]) {
                    [linkService changeMicphoneStatus: on];
                } else {
                    NSString *text = on ? @"麦克风已开启" : @"麦克风已关闭";
                    [XYAlertCenter live_showTextItemWithText:text];
                }
            }
                break;
            case XYLivePushPanelItemTypeUltraWideAngle: {
                [self.settingPanelVC reverseSettingWithItem:item];
                BOOL on = (item.status == XYLivePushPanelItemStatusSelected);
                [XYLiveManagerSharedInstance.coreManager.media setupUltraWideAngleWithOpen:on];
                NSString *text = on ?@"近距离对焦已开启" :@"近距离对焦已关闭";
                [XYAlertCenter live_showTextItemWithText:text];
                [self.settingPanelVC setupSwitchStatusWithType:type off:!on];
            }
                break;
            case XYLivePushPanelItemTypeRedpacket: { // 点击红包
                self.decorateVM.notifyVCToPresentRedpacketGiftListVC = YES;
            }
                break;
            case XYLivePushPanelItemTypeDraw: {
                id<XYLivePushLotteryNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushLotteryNodeServiceProtocol)];
                [service notifyToPresentLotteryPanel];
            }
                break;
            case XYLivePushPanelItemTypeCameraRotate: {
                id<XYLivePushChromakeyScreenNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushChromakeyScreenNodeServiceProtocol)];
                [service flipCamera];
            }
                break;
            case XYLivePushPanelItemTypeTopMessage: {
                id<XYLiveCommonTopMessageServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveCommonTopMessageServiceProtocol)];
                [service showTopMessageViewController];
            }
                break;
            case XYLivePushPanelItemTypeTrafficCard: {
                if (XYConfigCenter().justOnceBoolForKey(@"ios_live_anchor_new_traffic_card", YES)) {
                    [self panel_newTrafficCardClicked];
                } else {
                    XYLivePushTrafficCardViewController *vc = [XYLivePushTrafficCardViewController new];
                    [vc showWithVc:self];
                }
                break;
            }
            case XYLivePushPanelItemTypeLowEnergy: {
                [self showLowEnergySwitchDialog];
                break;
            }
            case XYLivePushPanelItemTypeGiftControl: {
                BOOL toState = !item.buttonSwitchOn;
                NSString *type = @"ALLOW_SEND_GIFT";
                @weakify(self);
                [XYLiveFuncConfigNetworkHandler updateFuncInLiveingFor:type btnSwitch: toState completion:^(NSInteger btnSwitch, NSError * _Nullable err) {
                    @strongify(self);
                    if (err == nil) {
                        [self.decorateVM hostSwitchDidUpdate:type btnSwitch:btnSwitch];
                    } else {
                        [XYAlertCenter live_showTextItemWithText:err.localizedDescription];
                    }
                }];
                break;
            }
            case XYLivePushPanelItemTypeNoGoodsFilter: {
                [self.settingPanelVC reverseSettingWithItem:item];
                BOOL on = (item.status == XYLivePushPanelItemStatusSelected);
                NSString *text = on ? @"已开启商品无滤镜" : @"已关闭商品无滤镜";
                [XYAlertCenter live_showTextItemWithText:text];
                id<XYLivePushEffectNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushEffectNodeServiceProtocol)];
                [service applyNoGoodsFilter:on];
                break;
            }
            case XYLivePushPanelItemTypeLiveClass: {
                [self.decorateView didClickLiveClass];
                break;
            }
            case XYLivePushPanelItemTypeChipsPromote: {
                [self panel_chipsPromoteClicked];
                break;
            }
            case XYLivePushPanelItemTypeLiveTitleSetting: { // 点击直播标题设置
                [self didClickChangeTitle];
                break;
            }
            case XYLivePushPanelItemTypeLiveImageSetting: {
                [self didClickChangeCover];
                break;
            }
                
            case XYLivePushPanelItemTypeTaskCenter: {// 主播任务中心
                if ([XYLiveConfigCenter enableLoopBannerOpt]) {
                    id<XYLivePushHostTaskServiceProtocol> taskService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushHostTaskServiceProtocol) type:XYServiceTypeDomain];
                    [taskService openTaskCenter];
                } else {
                    id<XYLivePushVariousTaskProtocol> taskService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushVariousTaskProtocol)];
                    [taskService openTaskCenter];

                }
                break;
            }
            case XYLivePushPanelItemTypeTopic: {
                [self panel_topicClicked];
                break;
            }
            case XYLivePushPanelItemTypeDefinition: {
                // 清晰度
                [self showDefinitionSelectVC];
                break;
            }
            case XYLivePushPanelItemTypeWhiteBalanceLock: {
                [self.settingPanelVC reverseSettingWithItem:item];
                BOOL lock = (item.status == XYLivePushPanelItemStatusSelected);
                [XYLiveManagerSharedInstance.coreManager.media whiteBalanceWithLock:lock];
                NSString *text = lock ? @"白平衡已锁定" :@"自动白平衡";
                [XYAlertCenter live_showTextItemWithText:text];
                [self.settingPanelVC setupSwitchStatusWithType:type off:!lock];
            }
                break;
                
            case XYLivePushPanelItemTypeWishCard: {
                id<XYLiveWishCardProtocol> wishCardService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveWishCardProtocol) type:XYServiceTypeDomain];
                [wishCardService openHostWishListPage];
                [[XYSCKVMigrateTool defaultRecorderTool] setBool:YES forKey:@"kPushInteractionPannelWishCardRedDotTipsShown"];
            }
                break;
            case XYLivePushPanelItemTypeFeelBorder: {
                id<XYLiveFeelBorderService> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveFeelBorderService) type:XYServiceTypeDomain];
                [service showFeelBorderPanel];
            }
                break;
            default:
                break;
        }
    };
}

- (void)showDefinitionSelectVC {
    if (!XYAlphaSwitch.enablePushMultiLevel) { return; }
    id<XYLiveMultiLinkPushServiceProtocol> linkService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveMultiLinkPushServiceProtocol)];
    if ([linkService isLinking]) {
        [XYAlertCenter live_showTextItemWithText:@"连线期间无法调整清晰度"];
        return;
    }
    [self.settingPanelVC dismiss:nil];
    XYLiveSubtitleListViewController *list = [[XYLiveSubtitleListViewController alloc] init];
    __weak typeof(self) wself = self;
    __weak typeof(list) wlist = list;
    BOOL video = XYLiveManagerSharedInstance.isVideoLive;
    BOOL game = XYLiveManagerSharedInstance.isScreenLive;
    
    NSArray *title = [NSArray new];
    NSArray *subTitle = [NSArray new];
    NSInteger selectIndex = 0;
    NSInteger recommendIndex = 0;
    if (video) {
        title = [self.roomInfo.pushDispatchInfo videoLevels];
        subTitle = [self.roomInfo.pushDispatchInfo videoLevelsDescription];
        selectIndex = [self.roomInfo.pushDispatchInfo videoLevelIndex];
        recommendIndex = [self.roomInfo.pushDispatchInfo videoRecommendLevelIndex];
    } else if (game) {
        title = [self.roomInfo.pushDispatchInfo gameLevels];
        subTitle = [self.roomInfo.pushDispatchInfo gameLevelsDescription];
        selectIndex = [self.roomInfo.pushDispatchInfo gameLevelIndex];
        recommendIndex = [self.roomInfo.pushDispatchInfo gameRecommendLevelIndex];
    }
    
    [XYLogCollector xyLiveLogTag:@"push_definition_select_vc" content:[NSString stringWithFormat:@"titles = %@, subtitles = %@, index = %@",title, subTitle, @(selectIndex)]];
    
    list.itemClickedCallback = ^(NSInteger index, NSString *name) {
        // 本地存储对应的编码参数
        [XYLogCollector xyLiveLogTag:@"push_definition_select_vc" content:[NSString stringWithFormat:@"item click name = %@, index = %@",name, @(index)]];
        if (selectIndex == index) { return; }
        [[NSNotificationCenter defaultCenter] postNotificationName: XYDefinitionTipViewController.pushMultiLevelUpgrade
                                                            object:nil
                                                          userInfo:nil];
        if (video) {
            [PushDispatchMultiLevelUtil setVideoLevel:name];
            [wself.roomInfo.pushDispatchInfo restoreSavedLevelForVideo:name];
        } else if (game) {
            [PushDispatchMultiLevelUtil setGameLevel:name];
            [wself.roomInfo.pushDispatchInfo restoreSavedLevelForGame:name];
        }
        [XYLiveManagerSharedInstance.coreManager.media updateGrade:name];
        [XYAlertCenter live_showTextItemWithText:[NSString stringWithFormat:@"切换「%@」成功",name]];
        [wlist dismiss];
    };
    
    [list showInViewController:self
                           tip:@"清晰度越高，用户的观看体验更好"
                          with:title
            subtitleSelections:subTitle
                   selectIndex:selectIndex
                recommendIndex:recommendIndex
                       dismiss:nil];
}


- (void)didClickChangeTitle {
    [self.settingPanelVC dismiss:nil];
    id<XYLivePushChangeCoverNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushChangeCoverNodeServiceProtocol)];
    [service showLivePushTitleChangeVC];
}

- (void)didClickChangeCover {
    [self.settingPanelVC dismiss:nil];
    id<XYLivePushChangeCoverNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushChangeCoverNodeServiceProtocol)];
    [service showLivePushCoverChangeVC];
}

- (void)updateShareInfo:(void(^)(void))completion {
    XYLiveShareSourceInfo *info = [XYLiveShareSourceInfo new];
    info.roomID = self.roomInfo.roomIDStr;
    [XYLiveShareNetworking reqAppServLiveShareCompletionInfoWithSoureInfo:info succ:^(id  _Nullable value, NSError * _Nullable error) {
        if (!error && [value isKindOfClass:[XYLiveShareInfo class]]) {
            XYLiveShareInfo *share = (XYLiveShareInfo *)value;
            [XYLiveShareManager.shared updateShareInfo:share];
            if (completion) {
                completion();
            }
        }
    } fail:^(id  _Nullable value, NSError * _Nullable error) {
        if (completion) {
            completion();
        }
    }];
}

/// 点击美颜
- (void)didClickBeauty {
    id<XYLivePushEffectNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushEffectNodeServiceProtocol)];
    [service handleEnterEffectSettingViewController:XYLiveEffectTypeBeauty];
}

- (NSString *)doPanelItemTypeMirror:(XYLivePushPanelItem *)item {
    NSString *toast = nil;
    switch (item.status) {
        case XYLivePushPanelItemStatusSelected: {
            if ([self canApplyRTC]) {
                [XYLiveManagerSharedInstance.coreManager.media applyMirror:NO];
            } else if ([self canApplyKasa]){
                [self.kasaPusher setRemoteMirror: YES];
            } else {
                [self.pusher setRemoteMirror:YES];
            }
            [[XYSCKVMigrateTool defaultUserDefaultTool_v2] setInt:XYLivePusherMirrorStatusMirror forKey:kXYLivePusherMirror];
            toast = xylive_toast_push_mirror;
        }
            break;
        case XYLivePushPanelItemStatusNormal: {
            if ([self canApplyRTC]) {
                [XYLiveManagerSharedInstance.coreManager.media applyMirror:YES];
            } else if ([self canApplyKasa]) {
                [self.kasaPusher setRemoteMirror: NO];
            } else {
                [self.pusher setRemoteMirror:NO];
            }
            [[XYSCKVMigrateTool defaultUserDefaultTool_v2] setInt:XYLivePusherMirrorStatusOpposite forKey:kXYLivePusherMirror];
            toast = xylive_toast_push_mirror_disabled;
        }
            break;
        default:
            break;
    }
    return toast;
}

- (void)setupForeNotice {
    id<XYLivePushForeNoticeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushForeNoticeServiceProtocol)];
    [service showForenoticePushView];
}

- (void)note_bgm_showPanel {
    id<XYLivePushBGMServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushBGMServiceProtocol)];
    [service bgm_showPanel];
}

- (void)panel_showWideAngleAlert {
    XYLiveCommonAlertView *alertView = [XYLiveCommonAlertView new];
    UILabel *label = [UILabel new];
    label.text = @"当拍摄物体贴近镜头时，镜头会重新对物体进行对焦，使画面更加清晰，利于展示物品更多细节\n镜头对焦过程可能会出现画面抖动，属于正常现象";
    label.font = Theme.fontLeSmall;
    label.numberOfLines = 0;
    label.textAlignment = NSTextAlignmentCenter;
    label.textColor = [[Theme.color.hexString(@"#000000") colorWithAlphaComponent:0.62] colorByAddingDarkColor:[Theme.color.hexString(@"#FFFFFF") colorWithAlphaComponent:0.56]];
    [alertView showinWithContainerView:self.settingPanelVC.view title:@"功能说明" mainActionTitle:@"我知道了" secondActionTitle:nil tipView:label];
}

- (void)panel_showNoGoodsFilterAlert {
    XYLivePushNoGoodsFilterAlertView *alertView = [XYLivePushNoGoodsFilterAlertView new];
    [alertView showIn:self.settingPanelVC.view];
}

- (void)panel_showWhiteBalanceLockAlert {
    XYLiveCommonAlertView *alertView = [XYLiveCommonAlertView new];
    UILabel *label = [UILabel new];
    label.text = @"相机自动识别当前环境色温并锁定，避免拍摄场景/物体颜色忽冷忽暖，自动变化";
    label.font = Theme.fontLeSmall;
    label.numberOfLines = 0;
    label.textAlignment = NSTextAlignmentCenter;
    label.textColor = [[Theme.color.hexString(@"#000000") colorWithAlphaComponent:0.62] colorByAddingDarkColor:[Theme.color.hexString(@"#FFFFFF") colorWithAlphaComponent:0.56]];
    [alertView showinWithContainerView:self.settingPanelVC.view title:@"功能说明" mainActionTitle:@"我知道了" secondActionTitle:nil tipView:label];
}

- (void)panel_newTrafficCardClicked {
    NSString *url = XYConfigCenter().justOnceStringForKey(@"live_anchor_traffic_web_url", @"https://www.xiaohongshu.com/hina/traffic_card?fullscreen=true&halfRatio=100&is_clear_background=1");
    id<XYLivePushWebServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushWebServiceProtocol)];
    [service presentHostTaskVC:url];
}

- (void)panel_chipsPromoteClicked {
    NSString *url = XYConfigCenter().justOnceStringForKey(@"live_clips_promote_web_url", @"https://www.xiaohongshu.com/ads/chips-v2/live/heat?fullscreen=true&halfRatio=100&is_clear_background=1&from=live_room");
    NSString *hostID = XYLiveManagerSharedInstance.roomInfo.hostInfo.userID;
    url = [NSString stringWithFormat:@"%@&hostId=%@", url, hostID];
    id<XYLivePushWebServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushWebServiceProtocol)];
    [service presentHostTaskVC:url];
    [XYLivePanelFuncDataSource saveChipsPromoteReddot];
}

- (void)panel_topicClicked {
    [XYLivePanelFuncDataSource saveTopicPromoteReddot];
    id<XYLivePushPrepGambitNodeServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushPrepGambitNodeServiceProtocol)];
    [service openGambitRNPage];
}

@end
