//
//  XYLiveMultiChatApplyListViewModel.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatApplyListViewModel.h"
#import <XYAlphaNetwork/XYAlphaNetwork-Swift.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveKit/XYLivePushMultiChatConsts.h>
#import <XYFoundation/XYFoundation.h>

@interface XYLiveMultiChatApplyListViewModel()

// 申请列表数据源
@property (nonatomic, copy)   NSArray<XYLiveMultiChatApplyListItem *> *listItems;
// 排序数据源
@property (nonatomic, copy)   NSArray<XYLiveMultiChatApplyListSortItem *> *sortListItems;
// 排序方式
@property (nonatomic, strong) XYLiveMultiChatApplyListSortItem *lastSortItem;
// 排序方式
@property (nonatomic, copy)   NSString *sortType;

@end

@implementation XYLiveMultiChatApplyListViewModel

- (instancetype)init {
    if (self = [super init]) {
        _sortType = [self initialSortType];
    }
    return self;
}

// 请求数据
- (void)requestListDataWithRoomId:(NSString *)roomId
                         sortType:(NSString *)sortType
                       completion:(void(^)(XYLiveMultiChatApplyListModel *listModel, NSError *error))completion {
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/line/v1/host/apply_list" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMGET;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"room_id"] = roomId;
    params[@"app_id"] = @(1);
    params[@"apply_sort_type"] = sortType;
    [api addingQueryItems:params.copy];
    WS
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"start request apply list api, params:{%@}",params]];
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"apply list response, result:{error: %@}",response.error]];
        [response onSuccess:^(id  _Nonnull value) {
            SS
            // 数据转模型
            XYLiveMultiChatApplyListModel *listModel = [XYLiveMultiChatApplyListModel xy_modelWithDictionary:value];
            // 更新本地缓存
            self.listItems = listModel.applyList;
            [self updateListSortItemStatusWithType:sortType];
            completion ? completion(listModel, nil) : nil;
        } onFailure:^(NSError * _Nonnull error) {
            completion ? completion(nil, error) : nil;
        }];
    }];
}

// 删除数据
- (void)deleteItemWithUserId:(NSString *)userId {
    if (!userId.length) { return; }
    self.listItems = [self.listItems xy_filter:^BOOL(XYLiveMultiChatApplyListItem * _Nonnull obj, NSUInteger index) {
        return ![obj.userId isEqualToString:userId];
    }];
}

// 更新交互状态
- (void)updateItemStatusWithUserId:(NSString *)userId disable:(BOOL)disable {
    if (!userId.length) { return; }
    XYLiveMultiChatApplyListItem *listItem = [self.listItems xy_match:^BOOL(XYLiveMultiChatApplyListItem * _Nonnull obj) {
        return [obj.userId isEqualToString:userId];
    }];
    listItem.disable = disable;
}

#pragma mark - Private

- (NSString *)initialSortType {
    XYLiveMultiChatApplyListSortItem *item = [self.sortListItems xy_match:^BOOL(XYLiveMultiChatApplyListSortItem * _Nonnull obj) {
        return obj.isSelected;
    }];
    return item.type;
}

- (void)updateListSortItemStatusWithType:(NSString *)sortType {
    if (!sortType.length) { return; }
    [self.sortListItems enumerateObjectsUsingBlock:^(XYLiveMultiChatApplyListSortItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        obj.selected = [sortType isEqualToString:obj.type];
    }];
    self.sortType = sortType;
}

#pragma mark - Lazy

- (NSArray<XYLiveMultiChatApplyListSortItem *> *)sortListItems {
    if (_sortListItems == nil) {
        XYLiveMultiChatApplyListSortItem *item1 = [[XYLiveMultiChatApplyListSortItem alloc] init];
        item1.desc = @"等待时长";
        item1.type = @"applyTime";
        item1.selected = YES;
        
        XYLiveMultiChatApplyListSortItem *item2 = [[XYLiveMultiChatApplyListSortItem alloc] init];
        item2.desc = @"打赏金额";
        item2.type = @"coins";
        item2.selected = NO;
        _sortListItems = @[item1, item2];
    }
    return _sortListItems;
}

@end
