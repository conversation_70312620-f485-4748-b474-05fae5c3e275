//
//  XYLiveMultiPKAvatarView.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/14.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiPKAvatarView.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYUIKitCore/UIView+XYUIKCPointInside.h>
#import <XYLiveFoundation/XYLiveUserInfo.h>
#import <XYWebImage/XYWebImage.h>
#import <Masonry/Masonry.h>


@interface XYLiveMultiPKAvatarView()

@property (nonatomic, strong) UIButton *avatarBtn;
@property (nonatomic, strong) UILabel *numberLabel;
@property (nonatomic, strong) XYLiveUserInfo *userInfo;

@end

@implementation XYLiveMultiPKAvatarView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

// 设置头像
- (void)setUserInfo:(XYLiveUserInfo * _Nullable)userInfo placeholder:(UIImage *)placeholder {
    if ([userInfo.avatar isEqualToString:self.userInfo.avatar]) { return; }
    // 加载头像
    [self.avatarBtn xy_setImageWithURL:[NSURL URLWithString:userInfo.avatar ?: @""] forState:UIControlStateNormal placeholderImage:placeholder];
    // 更新缓存
    self.userInfo = userInfo;
}

// 设置边框内容和颜色
- (void)setText:(NSString *)text borderColor:(UIColor *)borderColor {
    self.numberLabel.text = text;
    self.avatarBtn.layer.borderColor = borderColor.CGColor;
    self.numberLabel.backgroundColor = borderColor;
    [self.numberLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(text.length > 1 ? 18 : 10, 10));
    }];
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 创建头像
    [self setupAvatarButton];
    // 创建排名
    [self setupNumberLabel];
}

- (void)setupAvatarButton {
    UIButton *avatarBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [avatarBtn addTarget:self action:@selector(didTapHandler:) forControlEvents:UIControlEventTouchUpInside];
    avatarBtn.layer.cornerRadius = 16;
    avatarBtn.layer.masksToBounds = YES;
    avatarBtn.layer.borderWidth = 1.5;
    avatarBtn.xy_responseRectInset = UIEdgeInsetsMake(-5, -5, -5, -5);
    [self addSubview:avatarBtn];
    self.avatarBtn = avatarBtn;
}

- (void)setupNumberLabel {
    UILabel *numberLabel = [[UILabel alloc] init];
    numberLabel.textColor = [XYLiveTokenColor white];
    numberLabel.font = [UIFont fontWithName:@"AvenirNext-BoldItalic" size:6];
    numberLabel.textAlignment = NSTextAlignmentCenter;
    numberLabel.layer.cornerRadius = 5;
    numberLabel.layer.masksToBounds = YES;
    [self addSubview:numberLabel];
    self.numberLabel = numberLabel;
}

#pragma mark - Event

- (void)didTapHandler:(UIButton *)sender {
    self.didTapHandler ? self.didTapHandler(self.userInfo) : nil;
}

@end

@implementation XYLiveMultiPKLeftAvatarView

- (void)setupSubviews {
    [super setupSubviews];
    
    // 布局
    [self.avatarBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    
    [self.numberLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self);
        make.bottom.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(10, 10));
    }];
}

@end

@implementation XYLiveMultiPKRightAvatarView

- (void)setupSubviews {
    [super setupSubviews];
    
    // 布局
    [self.avatarBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    
    [self.numberLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self);
        make.bottom.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(10, 10));
    }];
}

@end
