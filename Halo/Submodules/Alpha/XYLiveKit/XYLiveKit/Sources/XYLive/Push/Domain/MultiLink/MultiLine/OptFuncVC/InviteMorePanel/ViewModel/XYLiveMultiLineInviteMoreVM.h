//
//  XYLiveMultiLineInviteMoreVM.h
//  XYLiveKit
//
//  Created by 大远 on 2025/6/3.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineInviteServiceProtocol.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiLineInviteMoreVM : NSObject

// 数据源
@property (nonatomic, copy, readonly) NSArray *dataSource;

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkSevice;

// 请求列表数据
- (void)requestListDataWithCompletion:(void(^)(NSError *error))completion;

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo;

@end

NS_ASSUME_NONNULL_END
