//
//  XYLivePushRoomViewController+Pusher.m
//  XYLiveKit
//
//  Created by 周博立 on 2020/12/1.
//  Copyright © 2020 XingIn. All rights reserved.
//

@import XYLiveUIKit;
@import XYLivePusher;
@import XYStorage;
@import XYConfigCenter;

#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushRoomViewController.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYTrackLiveBroadcastPage.h"

#import "XYIMComm.h"

@implementation XYLivePushRoomViewController (Pusher)

- (BOOL)shouldStopPush {
    return [XYLiveRoomEnumUtils judgeIfShouldStopPushWithBizStyle:self.bizPushStyle];
}

- (BOOL)shouldScreenPushing {
    return self.roomInfo.isScreenLive;
}

//收到发送首帧视频 发送submitPushType
- (void)sendSubmitPushType:(bool)onlyCodec {
    XYLivePushType pType = [self fetchPushType];
    NSString *pushInfo = nil;
    
    if (![self checkPushUrlRoomID]) {
        return;
    }
    
    pushInfo = [self getSubmitPushInfo:pType onlyCodec: onlyCodec];
    NSString *content1 = [NSString stringWithFormat: @"submitPushTypeV2 pushInfo: %@, ptype: %ld", pushInfo, (long)pType];
    [XYLogCollector xyLiveLogTag:@"hotswitch" content:content1];
    [XYLiveManagerSharedInstance reportCurrentPushTypeV2:pType pushInfo:pushInfo completion:^(BOOL success, NSError * _Nullable error) {
        NSString *content = [NSString stringWithFormat:@"submitV2 ptype resp: %@, err: %@", @(success), error];
        [XYLogCollector xyLiveLogTag:@"hotswitch" content:content];
        [self.decorateVM.adapter appendDebugInfo:content];
    }];
}

- (XYLivePushType)fetchPushType {
    XYLivePushType pType = XYLivePushTypeUndefine;
    if ([self canApplyRTC]) {
        pType = self.rtcPusher.pushType;
        if (self.rtcCore) {
            pType = self.rtcCore.pushType;
        }
    } else if ([self canApplyKasa]){
        pType = XYLivePushTypeKasa;
    } else {
        //rtmp
        pType = XYLivePushTypeRtmp;
    }
    return pType;
}

- (NSString *)fetchSubmitPushInfo:(bool)onlyCodec {
    if (![self checkPushUrlRoomID]) {
        return nil;
    }
    return [self getSubmitPushInfo:[self fetchPushType] onlyCodec: onlyCodec];
}

- (NSString *)getSubmitPushInfo:(XYLivePushType)currentPushType onlyCodec:(bool)onlyCodec {
    NSInteger bitrate = 1000;
    NSInteger fps = 10;
    NSInteger bussinessType = 0;
    NSInteger res = 0;
    NSInteger width = 0;
    NSInteger height = 0;
    XYLivePushCodecType codec = XYLivePushCodecTypeAvc;
    
    if (self.roomInfo.isVoiceLive) {
        bussinessType = 1;
    }
    
    if ([self canApplyRTC]) {
        bitrate = self.rtcPusher.referenceVideoEncoderParam.videoBitrate.rtcReference;
        fps = self.rtcPusher.referenceVideoEncoderParam.videoFPS;
        res = self.rtcPusher.referenceVideoEncoderParam.resolution;
        codec = self.rtcPusher.currentCodecType;
        width = self.rtcPusher.usedEncodeInfo.width;
        height = self.rtcPusher.usedEncodeInfo.height;
        if (self.rtcCore) {
            bitrate = self.rtcCore.encodeInfo.videoBitrate.rtcReference;
            fps = self.rtcCore.encodeInfo.videoFPS;
            res = self.rtcCore.encodeInfo.resolution;
//            codec = self.rtcCore.currentCodecType;
            codec = self.rtcCore.usedEncodeInfo.codec;
            width = self.rtcCore.usedEncodeInfo.width;
            height = self.rtcCore.usedEncodeInfo.height;
        }
    } else if ([self canApplyKasa]) {
        bitrate = self.kasaPusher.videoEncodeInfo.videoBitrate.rtcReference;
        fps = self.kasaPusher.videoEncodeInfo.videoFPS;
        res = self.kasaPusher.videoEncodeInfo.resolution;
        codec = self.kasaPusher.currentCodecType;
    } else {
        bitrate = self.pusher.videoEncodeInfo.videoBitrate.rtcReference;
        fps = self.pusher.videoEncodeInfo.videoFPS;
        res = self.pusher.videoEncodeInfo.resolution;
        codec = self.pusher.currentCodecType;
    }
    
    NSDictionary *dictionary = @{
        @"push_type": @(currentPushType),
        @"push_url": self.roomInfo.streamURLInfo.livePushURL,
        @"resolution": @(res),
        @"business_type": @(bussinessType),
        @"bitrate": @(bitrate),
        @"fps": @(fps)
    };
    
    if (onlyCodec == YES) {
        dictionary = nil;
        if (width > 0 && height > 0) {
            dictionary = @{
                @"push_type": @(currentPushType),
                @"codec": @(codec),
                @"bitrate": @(bitrate),
                @"fps": @(fps),
                @"width": @(width),
                @"height": @(height)
            };
        } else {
            dictionary = @{
                @"push_type": @(currentPushType),
                @"codec": @(codec),
                @"bitrate": @(bitrate),
                @"fps": @(fps)
            };
        }
    }
    
    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dictionary
                                                       options:0
                                                         error:&error];
    NSLog(@"submit jsonData: %@", dictionary);
    if ([jsonData length] > 0 && error == nil){
        return [[NSString alloc] initWithData:jsonData
                                     encoding:NSUTF8StringEncoding];
    } else {
        return nil;
    }
}

- (BOOL)checkPushUrlRoomID {
    // 校验roomID
    NSString *managerRoomId = [NSString stringWithFormat:@"%ld", (long)XYLiveManagerSharedInstance.roomInfo.roomID];
    
    if (self.roomInfo.streamURLInfo.livePushURL == nil || managerRoomId == nil) {
        return false;
    }
    
    if ([self.roomInfo.streamURLInfo.livePushURL containsString: managerRoomId] == NO) {
        NSString *content = [NSString stringWithFormat:@"submitPushTypeV2 pushUrl: %@, currentRoomId: %@ not equal!!!!! return", self.roomInfo.streamURLInfo.livePushURL, managerRoomId];
        [XYLogCollector xyLiveLogTag:@"hotswitch" content:content];
        return false;
    }
    return true;
}

- (void)setupPusher {
    if (self.shouldStopPush) {
        return;
    }
    XYLivePushResolutionReference reference = XYLivePushResolutionReferenceSuper;
    if ([self canApplyRTC]) {
        [self rtcPusher_setupBlock];
        self.decorateVM.currentRoomInfo.resolution = self.rtcPusher.referenceVideoEncoderParam.resolution;
        reference = self.rtcPusher.referenceVideoEncoderParam.resolution;
        if (self.rtcCore != nil) {
            self.decorateVM.currentRoomInfo.resolution = self.rtcCore.rtcChannelSession.encode.resolution;
            reference = self.rtcCore.rtcChannelSession.encode.resolution;
        }
    } else if ([self canApplyKasa]) {
        self.decorateVM.currentRoomInfo.resolution = self.kasaPusher.videoEncodeInfo.resolution;
        reference = self.kasaPusher.videoEncodeInfo.resolution;
    } else {
        self.decorateVM.currentRoomInfo.resolution = self.pusher.videoEncodeInfo.resolution;
        reference = self.pusher.videoEncodeInfo.resolution;
    }
    
    [XYLivePushPreviewLogger.shared setupWithToken:@"pushvc"];
    XYLivePushPreviewLogger.shared.prefix = @"pushvc";
    
    [XYLiveMediaControl reloadAudioSessionWithReceiver:self sceneType:XYLiveAudioSessionScenePush];
    
    [self setupPusherDelegate];
    
    self.cameraRenderingContainerView.frame = self.view.bounds;
    if (self.cameraRenderingContainerView.superview != self.view) {
        [self.view addSubview:self.cameraRenderingContainerView];
    }
    
    BOOL sellAuth = XYLiveManagerSharedInstance.shoppingAuth.goodsAuth.sellerAuth || XYLiveManagerSharedInstance.shoppingAuth.goodsAuth.cpsAuth;
    BOOL needNoGoodsFilter = [XYLiveFilterConfigManager noGoodsFilterSwitchOn]
                            && [XYLiveEffectStrategyCenter enableGoodsNoFilter]
                            && sellAuth;
    [XYLiveFilterConfigManager updateNoGoodsFilterSwitchOn:needNoGoodsFilter];
    XYLiveFilterConfigModel *filter = [XYLiveFilterConfigManager fetchLatestUserFilterConfigModel];
    XYCSBeautifyConfig *config = [XYLiveBeautyConfigManager loadBeautifyConfigFromUserDefaults];
    XYCSBeautifyConfig *beauty = config;
    if ([XYLiveBeautyConfigManager isBeautyConfigCleared]) {
        beauty = [XYCSBeautifyConfig new];
    }
    beauty.levelOrContrastType = [XYLiveBeautyConfigManager fetchQualitySwitchStatus] ?config.levelOrContrastType :0;
    beauty.levelStrength = [XYLiveBeautyConfigManager fetchQualitySwitchStatus] ?config.levelStrength :0;
    beauty.contrastStrength = [XYLiveBeautyConfigManager fetchQualitySwitchStatus] ?config.contrastStrength :0;
    beauty.denoiseStrength = [XYLiveBeautyConfigManager fetchQualitySwitchStatus] ?config.denoiseStrength :0;
    
    if (reference == XYLivePushResolutionReferenceUndefined) {
        reference = XYLivePushResolutionReferenceSuper;
    }
    
    if (self.resumeCameraSetup) {
        self.resumeCameraSetup = NO;
        if ([XYLiveManagerSharedInstance.coreManager.media isSetup] == NO) {
            [XYLiveManagerSharedInstance.coreManager.media setup];
        }
    }

    // 根据roomInfo适配直播类型，取消预览
    if (!self.roomInfo.isVoiceLive && !self.shouldScreenPushing) {
        [XYLiveManagerSharedInstance.coreManager.media setupVideoLiveScene:YES];
        if (self.rtcCore != nil) {
            [XYLiveManagerSharedInstance.coreManager.media reloadCameraDispatchWithInfo: self.rtcCore.encodeInfo];
        } else if (self.rtcPusher != nil) {
            [XYLiveManagerSharedInstance.coreManager.media reloadCameraDispatchWithInfo: self.rtcPusher.referenceVideoEncoderParam];
        }
        [XYLiveManagerSharedInstance.coreManager.media startPreview:self.cameraRenderingContainerView resolution:reference];
        [XYLiveManagerSharedInstance.coreManager.media applyNoGoodsFilterSwitchOn:needNoGoodsFilter];
        [XYLiveManagerSharedInstance.coreManager.media applyFilter:[filter generateAVFilter]];
        [XYLiveManagerSharedInstance.coreManager.media refreshBeautyComboWithConfig:beauty];
        [XYLiveManagerSharedInstance.coreManager.media startCaptureFrame];
        [[XYSCKVMigrateTool defaultUserDefaultTool_v2] setInt:XYLiveManagerSharedInstance.coreManager.media.fetchCameraPosition forKey:XYLiveCameraConfig. kLiveCameraPositionKey];
        if (XYConfigCenter().boolForKey(@"ios_live_recover_ultra_wide_angle", NO)) {
            [XYLiveManagerSharedInstance setupUltraWideAngle:YES];
        }
    } else {
        [XYLiveManagerSharedInstance.coreManager.media setupVideoLiveScene:NO];
        [XYLiveManagerSharedInstance.coreManager.media stopCaptureFrame];
    }
    [XYLiveManagerSharedInstance.coreManager.media setupAnchorId:XYLiveManagerSharedInstance.hostInfo.userID];
    [XYLiveManagerSharedInstance.coreManager.media setupRoomId:XYLiveManagerSharedInstance.roomInfo.roomIDStr];
    
    if (self.roomInfo.isVideoLive &&
        self.screenshotView &&
        [XYLiveExperimentConfig pushPrepSnapshotOpt]) {
        [self.screenshotView removeFromSuperview];
        self.screenshotView = nil;
    }
}

- (void)pausePush {
    if ([self canApplyRTC]) {
        [self.rtcPusher pausePush];
        [self.rtcCore pausePush];
        [self.multiLinkService reportSuspend:YES];
    } else if ([self canApplyKasa]) {
        [self.kasaPusher pausePush];
    } else {
        [self.pusher pausePush];
    }
}

- (void)resumePush {
    if ([self canApplyRTC]) {
        [self.rtcPusher resumePush];
        [self.rtcCore resumePush];
        [self.multiLinkService checkHostStatus];
        [self.multiLinkService reportSuspend:NO];
    } else if ([self canApplyKasa]) {
        [self.kasaPusher resumePush];
    } else {
        [self.pusher resumePush];
    }
}

- (id<XYLiveMultiLinkPushServiceProtocol>)multiLinkService {
    return [self.serviceManager getServiceWithProtocol:@protocol(XYLiveMultiLinkPushServiceProtocol)];
}

- (void)clearPreviewLogger {
    [XYLivePushPreviewLogger.shared clearWithToken:@"pushvc"];
}

- (void)handlePusherMirrorStats {
    if ([self canApplyRTC]) {
        if ([XYLiveManagerSharedInstance.coreManager.media isCameraFront]) {
            XYLivePusherMirrorStatus lastStatsMirror = (XYLivePusherMirrorStatus)[[XYSCKVMigrateTool defaultUserDefaultTool_v2] intForKey:kXYLivePusherMirror];
            BOOL mirror = YES;
            switch (lastStatsMirror) {
                case XYLivePusherMirrorStatusOpposite:
                    mirror = NO;
                    break;
                case XYLivePusherMirrorStatusMirror:
                    mirror = YES;
                    break;
                case XYLivePusherMirrorStatusInitial:
                    mirror = YES;
                    break;
                default:
                    mirror = YES;
                    break;
            }
            [XYLiveManagerSharedInstance.coreManager.media applyMirror:!mirror];
        } else {
            [XYLiveManagerSharedInstance.coreManager.media applyMirror:NO];
        }
    } else {
        if ([XYLiveManagerSharedInstance.coreManager.media isCameraFront]) {
            XYLivePusherMirrorStatus lastStatsMirror = (XYLivePusherMirrorStatus)[[XYSCKVMigrateTool defaultUserDefaultTool_v2] intForKey:kXYLivePusherMirror];
            BOOL mirror = YES;
            switch (lastStatsMirror) {
                case XYLivePusherMirrorStatusOpposite:
                    mirror = NO;
                    break;
                case XYLivePusherMirrorStatusMirror:
                    mirror = YES;
                    break;
                case XYLivePusherMirrorStatusInitial:
                    mirror = YES;
                    break;
                default:
                    mirror = YES;
                    break;
            }
            if ([self canApplyKasa]) {
                [self.kasaPusher setRemoteMirror: mirror];
            } else {
                [self.pusher setRemoteMirror:mirror];
            }
        } else {
            if ([self canApplyKasa]) {
                [self.kasaPusher setRemoteMirror: YES];
            } else {
                [self.pusher setRemoteMirror:YES];
            }
        }
    }
}

- (void)startPush:(XYLiveRoomInfo *)roomInfo loginParam:(XYLiveLoginParam *)loginParam {
    [XYLogCollector xyLiveLogTag:@"push_room" content:@"enter start push"];
    XYExecuteOnMainQueue(^{
        BOOL isCellular = [WorldSnakeReachability share].status == WorldSnakeReachableViaWWAN;
        if (isCellular) {
            [[XYAlertCenter live_createTextItemWithText:@"当前正使用移动网络"] show];
        }
    });
    self.decorateVM.hostLoginParam = loginParam;
    XYLiveManagerSharedInstance.isLiveRoomJoined = YES;
    if ([self canApplyRTC]) {
        [XYLogCollector xyLiveLogTag:@"push_room" content:@"rtc apply push"];
        NSString *roomID = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
        if (XYLiveManagerSharedInstance.isVoiceLive) {
            if (XYLiveManagerSharedInstance.isLiveChat) {
                [XYTrackLiveBroadcastPage eventActionID2963WithRoomID:roomID type:@"畅聊" source:XYLiveManagerSharedInstance.prepSource];
            } else {
                [XYTrackLiveBroadcastPage eventActionID2963WithRoomID:roomID type:@"语音" source:XYLiveManagerSharedInstance.prepSource];
            }
        } else {
            [XYTrackLiveBroadcastPage eventActionID2963WithRoomID:roomID type:@"视频" source:XYLiveManagerSharedInstance.prepSource];
        }
        
        if ([UIApplication sharedApplication].applicationState != UIApplicationStateActive) {
            [XYLiveManagerSharedInstance.coreManager.media startPreview:self.cameraRenderingContainerView resolution:roomInfo.encodedResolution];
        }
        // 根据roomInfo切换语音直播 or 录屏直播
        if (self.shouldScreenPushing) {
            [self.rtcPusher startPushWithLandscape:XYLivePushPrepParamCache.pushLandscape];
            [self.rtcCore startPush];
            [XYLogCollector xyLiveLogTag:@"screen_capture" content:@"start pusher"];
        } else {
            [self.rtcPusher startPush];
            
            if (![self enableFixRejoinPushKasaTerminate]) {
                [self.rtcCore startPush];
            }
            if (self.rtcCore) {
                XYLiveManagerSharedInstance.coreManager.media.customVendor = self.rtcCore.videoTransmiter;
            }
            
            // 主播小窗进入
            if (self.hostFloatingEnter) {
                self.decorateVM.notifyVCToBreakOffPusher = NO;
                if ([self canApplyRTC]) {
                    [self.rtcPusher muteAllRemoteAudio:NO];
                    [self.rtcCore muteAllRemoteAudio:NO];
                    [self.rtcCore muteLocalVideo:![XYLiveManagerSharedInstance.coreManager.media running]];
                }
                [XYLiveManagerSharedInstance.liveImDistributer trigger:@"MANUAL_FLOATING_RESUME_LAYOUT" info:nil raw:@{}];
                // 兼容新框架逻辑
                id<XYLiveMultiLinkServiceProtocol> multiLinkService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveMultiLinkServiceProtocol) type:XYServiceTypeDomain];
                [multiLinkService enterLiveFloatWindow];
                self.hostFloatingEnter = NO;
            }
            
            [self handlePusherMirrorStats];
        }
        [self checkToClearRTCMixTranscodingWithRoomInfo:roomInfo];
        self.decorateVM.currentRoomInfo = roomInfo;
    } else {
        NSString *rtmp = roomInfo.streamURLInfo.livePushURL;
        if (rtmp.length) {
            [XYLogCollector xyLiveLogTag:@"push_room" content:@"rtmp apply push"];
            NSString *roomID = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
            NSString *userID = XYLiveManagerSharedInstance.userInfo.userID;
            if (XYLiveManagerSharedInstance.isVoiceLive) {
                if (XYLiveManagerSharedInstance.isLiveChat) {
                    [XYTrackLiveBroadcastPage eventActionID2963WithRoomID:roomID type:@"畅聊" source:XYLiveManagerSharedInstance.prepSource];
                } else {
                    [XYTrackLiveBroadcastPage eventActionID2963WithRoomID:roomID type:@"语音" source:XYLiveManagerSharedInstance.prepSource];
                }
            } else if (XYLiveManagerSharedInstance.isScreenLive) {
                [XYTrackLiveBroadcastPage eventActionID2963WithRoomID:roomID type:@"手游" source:XYLiveManagerSharedInstance.prepSource];
            } else {
                [XYTrackLiveBroadcastPage eventActionID2963WithRoomID:roomID type:@"视频" source:XYLiveManagerSharedInstance.prepSource];
            }
            
            if ([UIApplication sharedApplication].applicationState != UIApplicationStateActive) {
                [XYLiveManagerSharedInstance.coreManager.media startPreview:self.cameraRenderingContainerView resolution:XYLivePushResolutionReferenceSuper];
            }
            NSDictionary *extra = @{
                kLogHostID: userID ?: @"",
                kLogRoomID: roomID ?: @"",
                kRtmpPushResolution: @(roomInfo.encodedResolution)
            };
            if ([self canApplyKasa]) {
                [self.kasaPusher startPush:rtmp roomId:roomID extra:extra];
            } else {
                [self.pusher startPush:rtmp extra:extra];
            }
            [self handlePusherMirrorStats];
            self.decorateVM.currentRoomInfo = roomInfo;
        }
    }
}

- (BOOL)enableFixRejoinPushKasaTerminate {
    return self.hostFloatingEnter && XYConfigCenter().boolForKey(@"enable_fix_kasa_terminate", true);
}

- (void)checkToClearRTCMixTranscodingWithRoomInfo:(XYLiveRoomInfo *)roomInfo {
    BOOL stop = XYLiveLinkConfig.forceToStopRTCMixTranscoding;
    if (!stop) {
        return;
    }
    if (![self canApplyRTC]) {
        return;
    }
    if (!roomInfo) {
        return;
    }
    BOOL hasLink = roomInfo.needHostRecoveringLinkmic || roomInfo.pkID > 0;
    if (!hasLink) {
        return;
    }
    [self.rtcPusher forceToStopMixTranscoding];
}

#pragma mark - update rtmp apm basic params
- (void)updateApmRtmpParam {
    [self updateBasicParamByPushType: XYLivePushTypeRtmp];
}

#pragma mark - update kasaPusher apm basic params
- (void)updateApmKasaParam {
    [self updateBasicParamByPushType: XYLivePushTypeKasa];
}

- (void)updateBasicParamByPushType: (XYLivePushType)pushType {
    NSString *pushURL = XYLiveManagerSharedInstance.roomInfo.streamURLInfo.livePushURL ?: @"";
    NSString *linkID = @"";
    int32_t ptype = linkID.length ? (int32_t)xyLiveSharedLinkmicManager.linkmicType: 0;
    int32_t role = 0;
    
    NSString *pusherName = @"";
    NSString *pusherVer = @"";
    if (pushType == XYLivePushTypeRtmp && self.pusher != nil) {
        pusherName = self.pusher.pusherName;
        pusherVer = self.pusher.pusherVer;
    } else if (pushType == XYLivePushTypeKasa && self.kasaPusher != nil) {
        pusherName = self.kasaPusher.pusherName;
        pusherVer = self.kasaPusher.pusherVer;
    }
    
    [xyLiveSharedAPMManager updatePusherName: pusherName pusherVer: pusherVer pushUrl:pushURL pushType:ptype localRole:role linkId:linkID];
    
}

@end

