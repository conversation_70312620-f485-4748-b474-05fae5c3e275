//
//  XYLiveLinkHostSearchListSectionView.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/21.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostSearchListSectionView.h"
#import <XYLiveKit/XYLiveLinkHostInviteListSectionView.h>
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <Masonry/Masonry.h>

@interface XYLiveLinkHostSearchListSectionView()

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) XYLiveLinkHostInviteListSectionView *itemView;

@end

@implementation XYLiveLinkHostSearchListSectionView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

- (void)setChecked:(BOOL)checked {
    _checked = checked;
    self.itemView.checked = checked;
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 设置背景色
    self.backgroundColor = [XYLiveTokenColor bg];
    // 创建标题
    [self setupTitleLabel];
    // 创建列表
    [self setupItemView];
    
    // 布局
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16);
        make.top.mas_equalTo(8);
    }];
    
    [self.itemView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self);
        make.top.equalTo(self.titleLabel.mas_bottom);
        make.height.mas_equalTo(47);
    }];
}

- (void)setupTitleLabel {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.textColor = [XYLiveTokenColor desc];
    titleLabel.text = @"搜索结果";
    titleLabel.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
    [self addSubview:titleLabel];
    self.titleLabel = titleLabel;
}

- (void)setupItemView {
    XYLiveLinkHostInviteListSectionView *itemView = [[XYLiveLinkHostInviteListSectionView alloc] init];
    WS
    itemView.didTapHandler = ^(BOOL isChecked) {
        SS
        self.didTapHandler ? self.didTapHandler(isChecked) : nil;
    };
    [self addSubview:itemView];
    self.itemView = itemView;
}

@end
