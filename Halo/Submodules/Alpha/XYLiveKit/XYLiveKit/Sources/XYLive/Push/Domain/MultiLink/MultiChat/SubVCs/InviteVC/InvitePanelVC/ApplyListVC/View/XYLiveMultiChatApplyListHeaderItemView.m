//
//  XYLiveMultiChatApplyListHeaderItemView.m
//  XYLiveKit
//
//  Created by 大远 on 2025/2/25.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiChatApplyListHeaderItemView.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYUITheme/XYThemeManager.h>
#import <XYFoundation/XYFoundation.h>
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <Masonry/Masonry.h>

@interface XYLiveMultiChatApplyListHeaderItemView()

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, copy)   NSString *title;

@end

@implementation XYLiveMultiChatApplyListHeaderItemView

- (instancetype)initWithTitle:(NSString *)title {
    self = [super initWithFrame:CGRectZero];
    if (self) {
        _title = title;
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    self.clipsToBounds = YES;
    // 创建标题
    [self setupTitleLabel];
    
    // 布局
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(0, 10, 0, 10));
    }];
}

- (void)setupTitleLabel {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
    titleLabel.text = self.title;
    [self addSubview:titleLabel];
    self.titleLabel = titleLabel;
}

- (void)setSelected:(BOOL)selected {
    _selected = selected;
    if (selected) {
        self.titleLabel.textColor = XYLiveTokenColor.primary;
        self.backgroundColor = XYLiveTokenColor.primary2;
    } else {
        self.titleLabel.textColor = XYLiveTokenColor.paragraph;
        self.backgroundColor = XYLiveTokenColor.fill1;
    }
}

@end
