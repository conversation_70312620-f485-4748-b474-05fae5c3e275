//
//  XYLiveMultiChatApplyListVC.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatApplyListVC.h"
#import <Masonry/Masonry.h>
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLiveKit/XYLiveMultiChatApplyListCell.h>
#import <XYLiveKit/XYLiveMultiChatApplyListHeaderView.h>
#import <XYLiveKit/XYLiveMultiChatApplyListViewModel.h>
#import <XYLiveKit/XYLiveMutiLinkEmptyView.h>
#import <XYAlphaUtils/XYAlert+XYLive.h>
#import <XYLiveKit/XYLivePushMultiChatConsts.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLivePushMultiLinkBizTracker.h>
#import <XYUIKit/XYEmptyView.h>

// cell重用标识符
static NSString * const kXYLiveMultiChatApplyListCellIde = @"XYLiveMultiChatApplyListCell";

@interface XYLiveMultiChatApplyListVC()<XYLiveMultiLinkListener>

@property (nonatomic, assign) XYLiveMultiLinkBizType bizType;
@property (nonatomic, assign) XYLiveMultiLinkInviteSource source;
@property (nonatomic, copy)   NSString *extraInfo;
@property (nonatomic, strong) XYLiveMultiChatApplyListViewModel *viewModel;
@property (nonatomic, strong) XYLiveMultiChatApplyListHeaderView *headerView;

@end

@implementation XYLiveMultiChatApplyListVC

/// 初始化
- (instancetype)initWithBizType:(XYLiveMultiLinkBizType)bizType
                         source:(XYLiveMultiLinkInviteSource)source
                      extraInfo:(NSString * _Nullable)extraInfo {
    if (self = [super init]) {
        _bizType = bizType;
        _source = source;
        _extraInfo = extraInfo;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // 注册cell
    [self.tableView registerClass:XYLiveMultiChatApplyListCell.class forCellReuseIdentifier:kXYLiveMultiChatApplyListCellIde];
    // 注册监听
    [self.multiLinkService registerListener:self];
}

#pragma mark - override

- (UIView *)emptyDataView {
    XYLiveMutiLinkEmptyView *emptyView = [[XYLiveMutiLinkEmptyView alloc] init];
    emptyView.title = @"暂无申请消息";
    emptyView.retryBtnTitle = @"邀请连线";
    emptyView.didTapRetryHandler = self.didTapInviteLinkHandler;
    return emptyView;
}

- (void)refreshWithType:(XYLiveRefreshDataType)type completion:(void (^)(NSError * _Nonnull))completion {
    // 埋点上报
    if (type == XYLiveRefreshDataTypePull) {
        [XYLivePushMultiLinkBizTracker eventActionId69406WithBizType:self.bizType secondTabName:@"申请消息"];
    }
    [self.viewModel requestListDataWithRoomId:self.liveInfoService.roomId sortType:self.viewModel.sortType  completion:^(XYLiveMultiChatApplyListModel * _Nonnull listModel, NSError * _Nonnull error) {
        completion ? completion(error) : nil;
    }];
}

- (BOOL)hasData {
    return self.viewModel.listItems.count > 0;
}

#pragma mark - UITableViewDelegate / UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.viewModel.listItems.count ? 1 : 0;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.viewModel.listItems.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    XYLiveMultiChatApplyListCell *cell = [tableView dequeueReusableCellWithIdentifier:kXYLiveMultiChatApplyListCellIde forIndexPath:indexPath];
    // 数据绑定
    [cell bindListItems:self.viewModel.listItems indexPath:indexPath];
    WS
    // 点击接通
    cell.didTapAcceptHandler = ^(XYLiveMultiChatApplyListItem * _Nonnull item, NSIndexPath * _Nonnull indexPath) {
        SS
        // 更新状态
        [self.viewModel updateItemStatusWithUserId:item.userId disable:YES];
        // 埋点上报
        [XYLivePushMultiLinkBizTracker eventActionId32878WithBizType:self.bizType userId:item.userId hasGoods:self.hasGoods];
        // 缓存链路监控id
        [XYLiveLinkSessionManager saveSessionId:item.sessionId forKey:item.userId];
        // 发起接受申请
        [self.multiLinkService acceptApplyDirectlyWithBizType:self.bizType targetUserId:item.userId bizExtraInfo:self.extraInfo];
    };
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 68;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    XYLiveMultiChatApplyListItem *listItem = self.viewModel.listItems[indexPath.row];
    // 拉起P页
    self.showUserCardHandler ? self.showUserCardHandler(listItem.userInfo) : nil;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return 44;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (self.headerView == nil) {
        XYLiveMultiChatApplyListHeaderView *headerView = [[XYLiveMultiChatApplyListHeaderView alloc] init];
        WS
        headerView.didTapListItemHandler = ^(XYLiveMultiChatApplyListSortItem * _Nonnull sortItem) {
            SS
            [self refreshListSortItem:sortItem];
        };
        [headerView bindListItems:self.viewModel.sortListItems];
        self.headerView = headerView;
    }
    return self.headerView;
}

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath {
    XYLiveMultiChatApplyListItem *listItem = self.viewModel.listItems[indexPath.row];
    if (!listItem.hasShow) {
        listItem.hasShow = YES;
        // 埋点上报
        [XYLivePushMultiLinkBizTracker eventActionId69410WithUserId:listItem.userId];
    }
}

#pragma mark - XYLiveMultiLinkOnApplyListener

- (void)onAcceptApplyResponseWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId error:(NSError *)error {
    // 合法性校验
    if (bizType != self.bizType) { return; }
    // 接通接口成功
    if (error == nil) {
        // 埋点上报
        [XYLivePushMultiLinkBizTracker eventActionId32879WithBizType:self.bizType userId:targetUserId hasGoods:self.hasGoods];
        // Toast提示
        [XYAlertCenter live_showTextItemWithText:kXYLiveMultiChatInviteAcceptSuccessToast];
        // 删除数据源
        [self.viewModel deleteItemWithUserId:targetUserId];
        // 兜底展示空数据
        if (self.viewModel.listItems.count == 0) {
            [self showEmptyPHView];
        }
        // 更新列表
        [self.tableView reloadData];
    } else {
        // Toast提示
        [XYAlertCenter live_showTextItemWithError:error];
        // 重置状态
        [self.viewModel updateItemStatusWithUserId:targetUserId disable:NO];
        // 刷新列表
        [self.tableView reloadData];
    }
}

#pragma mark - Private

/// 更新排序方式
- (void)refreshListSortItem:(XYLiveMultiChatApplyListSortItem *)item {
    // 加载提示
    [XYAlert live_showLoadingInView:self.view];
    WS
    [self.viewModel requestListDataWithRoomId:self.liveInfoService.roomId sortType:item.type completion:^(XYLiveMultiChatApplyListModel * _Nonnull listModel, NSError * _Nonnull error) {
        SS
        [[XYAlert sharedInstance] hideLoadingAlertItems];
        if (error == nil) {
            [self.headerView update];
            // 刷新列表
            [self.tableView reloadData];
        } else {
            // Toast提示
            [XYAlert live_showTextItemWithError:error];
        }
    }];
}

- (BOOL)hasGoods {
    return self.hasGoodsHandler ? self.hasGoodsHandler() : NO;
}

- (id<XYLiveInfoServiceProtocol>)liveInfoService {
    return self.liveInfoServiceHandler ? self.liveInfoServiceHandler() : nil;
}

- (id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    return self.multiLinkServiceHandler ? self.multiLinkServiceHandler() : nil;
}

#pragma mark - Lazy

- (XYLiveMultiChatApplyListViewModel *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[XYLiveMultiChatApplyListViewModel alloc] init];
    }
    return _viewModel;
}

@end
