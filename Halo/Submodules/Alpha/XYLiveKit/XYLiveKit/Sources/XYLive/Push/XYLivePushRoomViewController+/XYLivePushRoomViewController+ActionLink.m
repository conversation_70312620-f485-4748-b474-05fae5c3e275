//
//  XYLivePushRoomViewController+ActionLink.m
//  XYLiveKit
//
//  Created by gongyidemac on 2021/11/25.
//  Copyright © 2021 XingIn. All rights reserved.
//

@import XYConfigCenter;
@import KVOController;

#import "XYLivePushRoomViewController.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLiveCommonShareNode.h"
#import "XYLivePushWebNode.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushIntroRequestNode.h"
#import "XYLiveCommonGoodsStockNode.h"
#import "XYLiveCommonInfoCardNode.h"
#import "XYLivePushForeNoticeNode.h"
#import "XYLivePushUserCardService.h"
#import "XYLivePushLotteryNode.h"
#import "XYLivePushChangeCoverNode.h"

#import "XYLiveCommonConst.h"
#import <XYLiveKit/XYLiveMultiLinkSwitchConfig.h>

@implementation XYLivePushRoomViewController (ActionLink)

- (void)setupActionLinkRegister {
    BOOL res = [XYLiveLinkConfig applyHostActionLink];
    if (!res) {
        return;
    }
    
    @weakify(self)
    XYLiveManagerSharedInstance.actionRouteManager.fallbackCallback = ^(NSString * _Nonnull urlStr) {
        if (urlStr.length <= 0) {
            return;
        }
        
        @strongify(self)
        if ([urlStr hasPrefix:@"http"]) {
            id<XYLivePushWebServiceProtocol> webService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushWebServiceProtocol)];
            [webService presentHostTaskVC:urlStr];
            return;
        }
        [JLRoutes routeURL:[NSURL URLWithString:urlStr]];
    };
    
    [self registerActionLinkCore];
}

- (void)actionlink_setupKVO {
    @weakify(self)
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCToPerformActionLink)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        @strongify(self)
        XYExecuteOnMainQueue(^{
            if (self.decorateVM.notifyVCToPerformActionLink.length > 0) {
                [XYLiveManagerSharedInstance.actionRouteManager performWith:self.decorateVM.notifyVCToPerformActionLink];
            }
        });
    }];
}

- (void)registerActionLinkCore {
    __weak typeof(self) wself = self;
    
    // 分享面板
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"share" action:^(NSString * _Nonnull _, NSDictionary<NSString *,id> * _Nonnull param) {
        XYLiveShareSourceInfo *shareInfo = [XYLiveShareSourceInfo new];
        if ([param[@"share_source"] isKindOfClass:NSString.class]) {
            shareInfo.source = param[@"share_source"] ?: @"";
        }
        if ([param[@"share_source_id"] isKindOfClass:NSString.class]) {
            shareInfo.sourceID = param[@"share_source_id"] ?: @"";
        }
        id<XYLiveCommonShareServiceProtocol> shareService = [wself.serviceManager getServiceWithProtocol:@protocol(XYLiveCommonShareServiceProtocol)];
        [shareService loadSharingVCWithSource:shareInfo];
    }];
    
    // 用户连线
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"link_detail_panel" action:^(NSString * _Nonnull _, NSDictionary<NSString *,id> * _Nonnull param) {
        if (!XYLiveManagerSharedInstance.streamDidPush) {
            ///推流尚未完成
            [XYAlertCenter live_showTextItemWithText:xyllm_toast_sys_push_not_ready];
            return;
        }
        // 畅聊直播间
        if ([XYLiveManager sharedManager].isLiveChat) {
            id<XYLivePushMultiChatRoomServiceProtocol> multiChatRoomService = [wself.serviceManager getServiceWithProtocol:@protocol(XYLivePushMultiChatRoomServiceProtocol) type:XYServiceTypeDomain];
            [multiChatRoomService showInvitePanelWithSource:XYLiveMultiLinkInviteSourceDeepLink extraInfo:nil];
        } else {
            id<XYLivePushMultiChatServiceProtocol> multiChatService = [wself.serviceManager getServiceWithProtocol:@protocol(XYLivePushMultiChatServiceProtocol) type:XYServiceTypeDomain];
            [multiChatService showInvitePanelWithSource:XYLiveMultiLinkInviteSourceDeepLink extraInfo:nil];
        }
    }];
    
    // 主播PK
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"pk_panel" action:^(NSString * _Nonnull _, NSDictionary<NSString *,id> * _Nonnull param) {
        // 判断是否命中新框架
        if ([XYAlphaSwitch enableNewMultiLinkFramework]) { return; }
        wself.decorateVM.notifyVCToPresentPKVC = YES;
    }];
    
    // 打开麦克风
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"open_mic" action:^(NSString * _Nonnull _, NSDictionary<NSString *,id> * _Nonnull param) {
        XYLiveManagerSharedInstance.isMicrophoneOff = NO;
        if ([wself canApplyRTC]) {
            [wself.rtcPusher setMicrophoneStatus:YES];
            [wself.rtcCore setMicrophoneStatus:YES];
        } else if ([wself canApplyKasa]) {
            [wself.kasaPusher setMicrophoneStatus:YES];
        }
        [XYAlertCenter live_showTextItemWithText:@"麦克风已开启"];
    }];
    
    // 更多面板 & 引导
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"emcee_more" action:^(NSString * _Nonnull _, NSDictionary<NSString *,id> * _Nonnull param) {
        NSString *guide = [param xyLive_stringValueForKey:@"guide"];
        [wself showMorePanelWithGuide:guide];
    }];
    
    // 互动面板 & 引导
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"emcee_interaction" action:^(NSString * _Nonnull _, NSDictionary<NSString *,id> * _Nonnull param) {
        NSString *guide = [param xyLive_stringValueForKey:@"guide"];
        [wself showInteractPanelWithGuide:guide];
    }];
    
    // 直播间设置 & 引导
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"emcee_room_setting" action:^(NSString * _Nonnull _, NSDictionary<NSString *,id> * _Nonnull param) {
        NSString *guide = [param xyLive_stringValueForKey:@"guide"];
        [wself showRoomSettingPanelWithGuide:guide];
    }];
    
    // 连线能力聚合
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"emcee_link" action:^(NSString * _Nonnull _, NSDictionary<NSString *,id> * _Nonnull param) {
        // 1.随机
        NSString *isRandom = param[@"random"];
        if ([isRandom isEqualToString:@"1"]) {
            // 判断是否命中新框架
            if ([XYAlphaSwitch enableNewMultiLinkFramework]) { return; }
            XYAlertLoadingView *loading = [XYAlertLoadingView live_createAlertLoading];
            [loading showInView:wself.decorateView];
            [MultiLinkmicCommService queryRandomLinkmicInfoWithCompletion:^(RandomLinkmicInfo * _Nullable info, NSError * _Nullable error) {
                [loading hide];
                if (info.randomMatching || !info.randomLinkmicEnable) {
                    return;
                }
                [wself.decorateView triggerRandomLinkMicDirectly];
                [wself.decorateView.cardContainerView dismissAnimated:YES];
            }];
            return;
        }
        
        // 2.指定
        NSString *uid = param[@"user_id"];
        NSString *roomid = param[@"room_id"];
        NSString *mediaType = param[@"media_type"];
        BOOL isSameRoom = YES;
        if (roomid.length == 0) {// 未传roomid则默认同房
            roomid = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
        } else {
            isSameRoom = [XYLiveManagerSharedInstance.roomInfo.roomIDStr isEqualToString:roomid];
        }
        // 跨房邀请 & 新框架
        if (!isSameRoom && [XYAlphaSwitch enableNewMultiLinkFramework]) { return; }
        NSString *selfRoomId = XYLiveManager.sharedManager.roomInfo.roomIDStr;
        NSString *userId = XYLiveManager.sharedManager.userInfo.userID;
        XYLiveRoomPushContentType type = XYLiveManager.sharedManager.roomInfo.contentType;
        [XYLiveManagerSharedInstance.coreManager.media.linkMic inviteWithTargetUid:uid targetRoomId:roomid ?:@"" mediaType:[mediaType isEqualToString:@"video"] ? XYLiveLinkmicMeidaTypeVideo : XYLiveLinkmicMeidaTypeAudio sceneType:isSameRoom ? LinkmicInviteTypeAudience :  LinkmicInviteTypeHost contentType:type selfUserId:userId ?:@"" type: RandomLinkmicTypeLinkmic subBizType: RandomLinkmicSubBizTypeNormal extraInfo:nil completion:^(NSString * _Nullable linkId, NSError * _Nullable error) {
            if (error) {
                [XYAlert live_showTextItemWithError:error];
            } else {
                [[XYAlert createTextItemWithTextOnMiddle:@"已发送邀请"] show];
            }
            [wself.decorateView.cardContainerView dismissAnimated:YES];
        }];
    }];

    // 直播间拉起封面选择页面
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"cover_select" action:^(NSString * _Nonnull _, NSDictionary<NSString *,id> * _Nonnull param) {
        id<XYLivePushChangeCoverNodeServiceProtocol> service = [wself.serviceManager getServiceWithProtocol:@protocol(XYLivePushChangeCoverNodeServiceProtocol)];
        [service showChangeCoverPhotoVC];
    }];
    
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"modify_title" action:^(NSString * _Nonnull _, NSDictionary<NSString *,id> * _Nonnull param) {
        XYExecuteOnMainQueue(^{
            NSString *place = @"";
            if ([param[@"place_holder"] isKindOfClass:[NSString class]]) {
                NSData *decodeData = [[NSData alloc] initWithBase64EncodedString:param[@"place_holder"] options:0];
                place = [[NSString alloc] initWithData:decodeData encoding:NSUTF8StringEncoding];
            }
            [wself.decorateView editTitleByPlaceHolder:place];
        });
    }];
    
    // 打开流量卡
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"link_flow_card" action:^(NSString * _Nonnull, NSDictionary<NSString *,id> * _Nonnull) {
        XYExecuteOnMainQueue(^{
            XYLivePushTrafficCardViewController *vc = [XYLivePushTrafficCardViewController new];
            [vc showWithVc:wself];
        });
    }];
    
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"vote" action:^(NSString * _Nonnull str, NSDictionary<NSString *,id> * _Nonnull dic) {
        id<XYLiveHostVoteServiceProtocol> voteService = [wself.serviceManager getServiceWithProtocol:@protocol(XYLiveHostVoteServiceProtocol) type:XYServiceTypeDomain];
        [voteService showCreateVoteViewController];
    }];
    
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"live_open_popularity_rank_panel" action:^(NSString * _Nonnull _, NSDictionary<NSString *,id> * _Nonnull param) {
        XYPopularityRankListViewController *listVC = [[XYPopularityRankListViewController alloc] init];
        [listVC showWithVc:wself];
    }];
}

@end
