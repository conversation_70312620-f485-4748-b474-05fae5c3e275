//
//  XYLivePushMultiChatController.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/30.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLiveKit/XYLivePushMultiChatBottomBarItemVC.h>
#import <XYLiveKit/XYLivePushMultiChatCoreModel.h>
#import <XYLiveKit/XYLivePushMultiChatInviteVC.h>
#import <XYLiveKit/XYLivePushUserCardService.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveKit/XYLivePushMultiChatOperateVC.h>

@interface XYLivePushMultiChatController()

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   id<XYLivePushMultiChatDependProtocol> depend;
@property (nonatomic, strong) XYLivePushMultiChatCoreModel *coreModel;
@property (nonatomic, weak)   XYLivePushMultiChatBottomBarItemVC *bottomBarItemVC;
@property (nonatomic, weak)   XYLivePushMultiChatInviteVC *inviteVC;
@property (nonatomic, weak)   XYLivePushMultiChatOperateVC *operateVC;

@end

@implementation XYLivePushMultiChatController

- (void)dealloc {
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:@"XYLivePushMultiChatController dealloc"];
}

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containVC
                             depend:(id<XYLivePushMultiChatDependProtocol>)depend {
    if (self = [super init]) {
        _containerVC = containVC;
        _depend = depend;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:@"XYLivePushMultiChatController didLoad"];
    // 初始化核心Model
    [self setupCoreModel];
    // 初始化底部栏按钮VC
    [self setupBottomBarItemVC];
    // 初始化邀请VC
    [self setupInviteVC];
    // 初始化操作VC
    [self setupOperateVC];
}

#pragma mark - Public

- (void)showInvitePanelWithSource:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString *)extraInfo {
    [self.inviteVC showInvitePanelWithSource:source extraInfo:extraInfo];
}

- (void)inviteWithUserId:(NSString *)userId source:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString *)extraInfo {
    [self.inviteVC inviteWithUserId:userId source:source extraInfo:extraInfo];
}

- (void)showOperatePanelWithUserInfo:(XYLiveOnmicUserModel *)userInfo {
    [self.operateVC showOperatePanelWithUserInfo:userInfo];
}

#pragma mark - Private

// 初始化核心Model
- (void)setupCoreModel {
    XYLivePushMultiChatCoreModel *coreModel = [[XYLivePushMultiChatCoreModel alloc] initWithLiveInfoService:self.depend.liveInfoService socketService:self.depend.imDistributeService multiLinkService:self.depend.multiLinkService groupLiveService:self.depend.groupLiveService];
    self.coreModel = coreModel;
}

// 初始化底部栏按钮VC
- (void)setupBottomBarItemVC {
    XYLivePushMultiChatBottomBarItemVC *bottomBarItemVC = [[XYLivePushMultiChatBottomBarItemVC alloc] initWithContainerVC:self.containerVC coreModel:self.coreModel adapterService:self.depend.pushAdapterService];
    [self addViewController:bottomBarItemVC];
    self.bottomBarItemVC = bottomBarItemVC;
}

// 初始化邀请VC
- (void)setupInviteVC {
    XYLivePushMultiChatInviteVC *inviteVC = [[XYLivePushMultiChatInviteVC alloc] initWithContainerVC:self.containerVC coreModel:self.coreModel liveInfoService:self.depend.liveInfoService multiLinkService:self.depend.multiLinkService];
    WS
    inviteVC.showUserCardHandler = ^(XYLiveUserInfo * _Nonnull userInfo) {
        SS
        [self.depend.userCardService handleFetchingUserInfoAndPresentingWithUser:userInfo];
    };
    [self addViewController:inviteVC];
    self.inviteVC = inviteVC;
}

// 初始化操作VC
- (void)setupOperateVC {
    XYLivePushMultiChatOperateVC *operateVC = [[XYLivePushMultiChatOperateVC alloc] initWithContainerVC:self.containerVC coreModel:self.coreModel liveInfoService:self.depend.liveInfoService linkService:self.depend.linkService];
    [self addViewController:operateVC];
    self.operateVC = operateVC;
}

@end
