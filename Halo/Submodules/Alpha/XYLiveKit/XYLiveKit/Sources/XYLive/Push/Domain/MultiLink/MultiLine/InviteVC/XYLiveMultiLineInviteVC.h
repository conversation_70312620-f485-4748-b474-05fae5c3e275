//
//  XYLiveMultiLineInviteVC.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/26.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineInviteServiceProtocol.h>
@class XYLiveUserInfo;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiLineInviteVC : XYViewController<XYLiveMultiLineInviteServiceProtocol>

// 点击用户
@property (nonatomic, copy) void(^didTapUserHandler)(XYLiveUserInfo *userInfo);
// 邀请列表
@property (nonatomic, copy, readonly) NSArray<XYLiveLinkHostInviteeInfo *> *inviteeInfoList;

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

// 注册监听
- (void)registerListener:(id<XYLiveMultiLineInviteListener>)listener;

// 取消注册监听
- (void)unregisterListener:(id<XYLiveMultiLineInviteListener>)listener;

@end

NS_ASSUME_NONNULL_END
