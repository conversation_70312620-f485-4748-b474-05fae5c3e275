//
//  PushNoGoodsFilterAlertView.swift
//  XYLiveKit
//
//  Created by gongyidemac on 2024/6/12.
//  Copyright © 2024 XingIn. All rights reserved.
//

import UIKit

@objc(XYLivePushNoGoodsFilterAlertView)
@objcMembers
public class PushNoGoodsFilterAlertView: UIControl {
    private lazy var containerView: UIView = makeContainerView()
    private lazy var topImageView: UIImageView = makeTopImageView()
    private lazy var titleLabel: UILabel = makeTitleLabel()
    private lazy var detailLabel: UILabel = makeDetailLabel()
    private lazy var confirmButton: UIButton = makeConfirmButton()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        p_setupUI()
        p_setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

fileprivate let kXYLivePushNoGoodsFilterAlertViewWidth: CGFloat = 270
fileprivate let kXYLivePushNoGoodsFilterAlertViewHeight: CGFloat = 376

// MARK: - Privates
extension PushNoGoodsFilterAlertView {
    private func p_setupUI() {
        self.backgroundColor = Theme.color.hexString("#000000").withAlphaComponent(0.4)
        addTarget(self, action: #selector(handleBackgroundClicked), for: .touchUpInside)
        
        addSubview(containerView)
        containerView.addSubview(topImageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(detailLabel)
        containerView.addSubview(confirmButton)
    }
    
    private func p_setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.width.equalTo(kXYLivePushNoGoodsFilterAlertViewWidth)
            make.height.equalTo(kXYLivePushNoGoodsFilterAlertViewHeight)
            make.center.equalToSuperview()
        }
        
        topImageView.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalTo(104)
            make.centerX.top.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalTo(24)
            make.top.equalTo(topImageView.snp.bottom).offset(20)
            make.centerX.equalToSuperview()
        }
        
        detailLabel.snp.makeConstraints { make in
            make.width.equalToSuperview().inset(24)
            make.height.equalTo(144)
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
        }
        
        confirmButton.snp.makeConstraints { make in
            make.width.equalTo(220)
            make.height.equalTo(36)
            make.bottom.equalToSuperview().offset(-20)
            make.centerX.equalToSuperview()
        }
    }
}

// MARK: - Privates
extension PushNoGoodsFilterAlertView {
    private func dismiss() {
        self.removeFromSuperview()
    }
}

// MARK: - Handlers
extension PushNoGoodsFilterAlertView {
    @objc
    private func handleConfirmButtonClicked() {
        dismiss()
    }
    
    @objc
    private func handleBackgroundClicked() {
        dismiss()
    }
}

// MARK: - Factory
extension PushNoGoodsFilterAlertView {
    private func makeContainerView() -> UIView {
        let view: UIView = UIView()
        view.layer.cornerRadius = 12
        view.clipsToBounds = true
        view.backgroundColor = UIColor.ReDs.alwaysWhite
        return view
    }
    
    private func makeTopImageView() -> UIImageView {
        let view: UIImageView = UIImageView()
        view.contentMode = .scaleAspectFill
        view.xy_setImage(with: URL(string: PushEffectResource.noGoodsFilterTipTopBgImageURL))
        return view
    }
    
    private func makeTitleLabel() -> UILabel {
        let label: UILabel = UILabel()
        label.font = Theme.font16Bold
        label.textColor = UIColor.ReDs.alwaysDarkTitle
        label.textAlignment = .center
        label.text = "商品无滤镜"
        return label
    }
    
    private func makeDetailLabel() -> UILabel {
        let label: UILabel = UILabel()
        label.font = Theme.font12
        label.textColor = UIColor.ReDs.alwaysDarkParagraph
        label.numberOfLines = 0
        label.text = "1、生效范围：开启后，直播间内美颜&滤镜&风格妆等将仅针对面部等皮肤区域生效，非皮肤区域将不生效；\n2、功能作用：开启后，观众侧将在商卡上看到“商品无滤镜”标签，提升用户的信任感，帮助更好售卖商品；降低观众因滤镜&美颜导致的商品客诉和售后问题，提升信用分。"
        return label
    }

    private func makeConfirmButton() -> UIButton {
        let button: UIButton = UIButton()
        button.backgroundColor = UIColor.ReDs.primary
        button.setTitleColor(UIColor.ReDs.alwaysWhite, for: .normal)
        button.titleLabel?.font = Theme.font14
        button.setTitle("我知道了", for: .normal)
        button.layer.cornerRadius = 18
        button.addTarget(self, action: #selector(handleConfirmButtonClicked), for: .touchUpInside)
        return button
    }
}

// MARK: - Publics
extension PushNoGoodsFilterAlertView {
    public func show(in parentView: UIView?) {
        guard let sView = parentView else {
            return
        }
        
        sView.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
}
