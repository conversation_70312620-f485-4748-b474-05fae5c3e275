//
//  XYLivePushRoomViewController+CoreSDK.swift
//  XYLiveKit
//
//  Created by gongyidemac on 2022/8/30.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation
import XYLiveCore
import XYConfigCenter
import MobileCoreServices
import I18N
import RedI18N

extension XYLivePushRoomViewController {
    @objc
    public func sdk_setupListener() {
        XYLiveManager.shared().coreManager.addHostListener(self)
        XYLiveManager.shared().coreManager.media.addRecordDelegate(self)
    }
    
    @objc
    public func sdk_setCameraCapture() {
        XYLiveManager.shared().coreManager.setupRecordIm()
    }
    
    @objc
    public func sdk_startLive() {
        if ConfigCenter.shared.bool(forKey: "ios_live_start_room_idempotent", defalut: false), XYLiveManager.shared().startRoomSucc {// 防重入
            return
        }
        
        // apm
        XYLiveManager.shared().apmManager.startApmInfo.pushRespTs = Date().timeIntervalSince1970
        XYLiveManager.shared().apmManager.startApmInfo.pushRespCode = 0
        
        if self.shouldStopPush {
            // 无需推流则默认before_start通过
            XYLiveManager.shared().apmManager.startApmInfo.beforeStartApiRespTs = Date().timeIntervalSince1970
            XYLiveManager.shared().apmManager.startApmInfo.beforeStartApiRespCode = 0
        }
        
        // join im
        XYLogCollector.xyLiveLogTag("core_sdk", content: "prepare join IM room, roomid = \(roomInfo?.roomIDStr ?? "null")")
        self.sdk_hostJoinRoom()
    }
    
    func sdk_hostJoinRoom() {
        XYLiveManager.shared().imService_hostJoinImGroup(roomInfo?.roomIDStr ?? "") { [weak self] in
            guard let self = self else {
                return
            }
            
            // apm
            XYLiveManager.shared().apmManager.startApmInfo.joinImRespTs = Date().timeIntervalSince1970
            XYLiveManager.shared().apmManager.startApmInfo.joinImRespCode = 0
            XYLogCollector.xyLiveLogTag("core_sdk", content: "join IM room succ")
            
            // start
            XYLiveManager.shared().sdk_startLive(with: self.fetchPushType(), pushInfo: self.fetchSubmitPushInfo(false))
        } fail: { [weak self] errCode, errDesc in
            let retried: Bool = self?.hasRetryJoin ?? false
            let disableRetry: Bool = ConfigCenter.shared.bool(forKey: "ios_live_disable_join_im_fail_retry", defalut: false)
            
            // retry once
            if retried == false, disableRetry == false {
                self?.hasRetryJoin = true
                self?.sdk_hostJoinRoom()
                XYLogCollector.xyLiveLogTag("core_sdk", content: "join IM room need retry")
                return
            }
            // apm
            XYLiveManager.shared().apmManager.startApmInfo.joinImRespTs = Date().timeIntervalSince1970
            XYLiveManager.shared().apmManager.startApmInfo.joinImRespCode = Int(errCode)
            XYLiveManager.shared().apmManager.startApmInfo.joinImMsg = errDesc
            XYLiveManager.shared().apmManager.reportStartPipelineTracker()
            XYLogCollector.xyLiveLogTag("core_sdk", content: "join IM room fail = \(errDesc ?? "")")
            
            // release room
            XYLiveManager.shared().sdk_unlockRoom(self?.roomInfo?.roomIDStr)
            
            // stop room
            let lReason: String = (self?.roomInfo?.isRecoveryRoom ?? false) ? "join_im_fail_in_recovery" : "join_im_fail"
            self?.handleStartFailAction(reason: lReason)
            XYAlert.live_showTextItem(withText: "服务异常，请重新开播")
        }
    }
    
    @objc
    public func sdk_submitPushInfo(_ onlyCodec: Bool = false) {
        PushService.submit(appID: "1", bizType: .live, roomID: XYLiveManager.shared().roomInfo.roomIDStr, pushInfo: fetchSubmitPushInfo(onlyCodec), completion: nil)
    }
    
    private func startSubmitBizRequest() {
        // 选品
        selectGoods()
        
        // 课程
        bindLessonsIfNeeded()
        
        // 直播间信息
        submitRoomInfo()
        
        // 直播间氛围请求
        self.decorateVM.fetchAtmosphereConfig()
    }
    
    private func startFetchStreamInfoIfNeeded() {
        if self.livePlayer_judgeShouldSupportPlayer() {
            XYLiveManager.shared().sdk_fetchBaseRoomInfo()
        }
    }
    
    private func selectGoods() {
        XYLiveManager.shared().check { successItems, failedItems, error in
            if error == nil {
                if let failedItems = failedItems, !failedItems.isEmpty {
                    let vc = GoodsManageResultViewController()
                    vc.show(vc: self, title: "上架失败", failedGoods: failedItems, successCount: successItems?.count ?? 0, failedCount: failedItems.count)
                }
            }
        }
    }
    
    private func bindLessonsIfNeeded() {
        let lessonID = XYLiveManager.shared().submitInfo?.lessonId ?? ""
        if !lessonID.isEmpty {
            PushCoreNetworkHandler.bindLessonRoom(roomID: XYLiveManager.shared().roomInfo.roomIDStr, lessonID: lessonID) { [weak self] _, error in
                
                guard let self = self else {
                    return
                }
                
                if error != nil {
                    let alert = XYLiveAlertView.alertForBindLessonError()

                    alert.rightBtnBlock = { [weak self] _, _ in
                        guard let self = self else {
                            return
                        }
                        self.decorateView.terminateGiftRendering()
                        self.decorateVM.notifyLinkingHostQuit()
                        self.decorateView.viewModel.notifyManagerToLeaveRoom(withReason: "bind_lesson_fail_cancel")
                        self.decorateVM.announceVM?.stopAnnounce()
                    }
                    alert.show(in: self.view, animated: true)
                }
            }
        }
    }
    
    private func submitRoomInfo() {
        // 续播无需重复submit
        if self.roomInfo?.isRecoveryRoom ?? false {
            refreshHostSetupInfo()
        } else {
            guard let submitInfo = XYLiveManager.shared().submitInfo?.roomInfoParams() else {
                XYLogCollector.xyLiveLogTag("core_sdk", content: "submit is nil")
                return
            }
            // 上报直播间信息
            
            let coverApm = XYLiveManager.shared().pushStartRoomCoverInfo?.apmInfo
            if !ReportCoverAPMInfo.disableTrack() {
                coverApm?.beginSubmit()
            }
            PushCoreNetworkHandler.submitRoomInfo(roomID: XYLiveManager.shared().roomInfo.roomIDStr,
                                                  hostID: XYSessionManager.shared().user?.userId ?? "", 
                                                  contentType: XYLiveManager.shared().startParam?.contentType.rawValue ?? 0,
                                                  obs: XYLiveManager.shared().roomInfo.obsType.rawValue ?? 0,
                                                  extraParams: submitInfo) { [weak self] _, error in
                guard let self = self else {
                    return
                }
                
                if !ReportCoverAPMInfo.disableTrack() {
                    if let cApm = coverApm {// 上传过封面
                        if cApm.uploadRespCode != ReportCoverAPMInfo.kXYLiveJoinReportPushCoverCodeSuccess {// 当前 未上传成功，则直接上报
                            cApm.report()
                        } else {// 上传成功，则上报接口情况
                            cApm.submitRespCode = "\((error as? NSError)?.code ?? 0)"
                        }
                    }
                }
                
                if let error = error {
                    self.submitRetryCount += 1
                    if self.submitRetryCount > 3 {
                        if ConfigCenter.shared.justOnceBool(forKey: "ios_live_submit_room_info_opt", defalut: true) {
                            self.decorateView.showSubmitRoomInfoErrorAlert { [weak self] in
                                self?.submitRoomInfo()
                            }
                        } else {
                            XYAlert.live_showTextItemWithError(error, missingText: "封面设置失败，请重新上传")
                        }
                       
                        XYLogCollector.xyLiveLogTag("core_sdk", content: "submit api error: \(error)")
                        return
                    }
                    self.submitRoomInfo()
                }
                else {
                    if ConfigCenter.shared.justOnceBool(forKey: "ios_live_submit_room_info_opt", defalut: true),
                       self.submitRetryCount > 3 {
                        XYAlert.live_showTextItemWithError(error, missingText:L.script.XYRUploadsuccessfu)
                    }
                }
                self.refreshHostSetupInfo()
                self.checkAndUpdateCover()// 检测封面正确上报
            }
        }
    }
    
    private func checkAndUpdateCover() {
        let retryCount = LiveConfigCenter.coverUploadRetryCount()
        guard XYLiveManager.shared().submitInfo?.submitCoverFail == true, let coverLocalPath = XYLiveManager.shared().pushStartRoomCoverInfo?.localCoverPath, self.uploadCoverRetryCount < retryCount else {
            return
        }
        let submitInfo = XYLiveManager.shared().submitInfo
        let apm = ReportCoverAPMInfo(scence: "fallback")
        XYLiveManager.shared().uploadCoverImageInSilent(withLocalPath: coverLocalPath) { [weak self] coverID, coverFileInfo, err in
            if !ReportCoverAPMInfo.disableTrack() {
                apm.uploadRespCode = "\((err as? NSError)?.code ?? 0)"
                apm.beginSubmit()

            }
            self?.uploadCoverRetryCount += 1
            if !coverID.isEmpty && !coverFileInfo.isEmpty {
                XYLiveManager.shared().submitInfo?.submitCoverFail = false
                PushPrepNetworkHandler.updateRoomCover(coverID, coverFileInfo, roomTitle: submitInfo?.title ?? "") { e in
                    if !ReportCoverAPMInfo.disableTrack() {
                        apm.submitRespCode = "\((e as? NSError)?.code ?? 0)"
                    }
                    
                    guard let error = e else {
                        XYLogCollector.xyLiveLogTag("core_sdk", content: "/api/sns/red/live/app/v1/ecology/cover/update success")
                        return
                    }
                    XYLogCollector.xyLiveLogTag("core_sdk", content: "/api/sns/red/live/app/v1/ecology/cover/update error: \(error)")
                }
            } else {
                self?.checkAndUpdateCover()
                XYLogCollector.xyLiveLogTag("core_sdk", content: "upload cover count: \(self?.uploadCoverRetryCount ?? 0)")
            }
        }
    }
    
    @objc
    public func showLowEnergySwitchDialog() {
        let perfStatusService = self.smImpl.getService(PerfStatusServiceProtocol.self)
        perfStatusService?.showDegradeSwitchAlert()
    }
}

extension XYLivePushRoomViewController: RoomHostDelegate {
    public func onStartResponsed(_ error: Error?, extra: [String: Any]?) {
        // apm
        XYLiveManager.shared().apmManager.startApmInfo.startApiRespTs = Date().timeIntervalSince1970
        XYLiveManager.shared().apmManager.startApmInfo.startApiRespCode = (error as? NSError)?.code ?? 0
        XYLiveManager.shared().apmManager.startApmInfo.startApiMsg = (error as? NSError)?.localizedDescription ?? ""
        XYLiveManager.shared().apmManager.reportStartPipelineTracker()
        
        // error
        if let err = error {
            // 文案提示 & log
            XYLogCollector.xyLiveLogTag("core_sdk", content: "start api error: \(err)")
            XYAlert.live_showTextItemWithError(err, missingText: "服务异常，请重新开播")
            // 释放锁定房间
            XYLiveManager.shared().sdk_unlockRoom(self.roomInfo?.roomIDStr)
            // 退房
            let lReason: String = (self.roomInfo?.isRecoveryRoom ?? false) ? "start_fail_in_recovery" : "start_fail"
            handleStartFailAction(reason: lReason)
            
            return
        }

        // log & flag
        XYLogCollector.xyLiveLogTag("core_sdk", content: "start api success")
        XYLiveManager.shared().startRoomSucc = true
        // 业务数据提交
        startSubmitBizRequest()
        // 电脑推流拉取流信息
        startFetchStreamInfoIfNeeded()
        // 处理分享等业务
        let eventService = serviceManager?.getServiceWith(LiveRoomEventServiceProtocol.self) as? LiveRoomEventServiceProtocol
        eventService?.eventOnHostStartLive?(isRecovery: roomInfo?.isRecoveryRoom ?? false, bizStyle: self.bizPushStyle)
        updateSentryInfo()
        reportOnStartApmInfo()
        checkStartDegrade()
    }
    
    private func updateSentryInfo() {
        if LivePushSentryInfo.enableUpdate() {
            let info = ["livepush_contentType": "\(XYLiveManager.shared().roomInfo.contentType.rawValue)",
                        "livepush_bizStyle": "\(self.bizPushStyle.rawValue)",
                        "livepush_obsType": "\(XYLiveManager.shared().roomInfo.obsType.rawValue)",
                        "liveType": "push",
                        "livepushOpenTime":"\(LivePushSentryInfo.livepushOpenTime)"]
            LivePushSentryInfo.updateSentryRecordLivePushInfo(XYLiveManager.shared().roomInfo.roomIDStr, info: info)
        } else {
            if LivePushSentryInfo.enable() {
                LivePushSentryInfo.sentryRecordLivePushInfo(XYLiveManager.shared().roomInfo.roomIDStr, liveType: "push")
            }
        }
    }
    
    private func reportOnStartApmInfo() {
        if StartPushApmReport.reportEnable() {
            let commonMemMonitor = serviceManager?.getServiceWith(LiveCommonMonitorMemServiceProtocol.self) as? LiveCommonMonitorMemServiceProtocol
            if let startSuccessDeviceInfo = commonMemMonitor?.generateImmediately() {
                let roomId = XYLiveManager.shared().roomInfo.roomIDStr
                StartPushApmReport.reportWhenStartComplete(XYLiveManager.shared().roomInfo.contentType, mem: startSuccessDeviceInfo.memUsage, appCPU: startSuccessDeviceInfo.appCPU, roomId: roomId)
                if LivePushSentryInfo.enableUpdate() {
                    let info = ["livepush_mem_success": "\(startSuccessDeviceInfo.memUsage)"]
                    LivePushSentryInfo.updateSentryRecordLivePushInfo(roomId, info: info)
                }
            }
        }
    }
    
    private func checkStartDegrade() {
        if LivePushDegradeSwitch.degradeOpen() {
            LivePushDegradeManager.shared.roomInfo = roomInfo
            LivePushDegradeManager.shared.beginDetectingTs = XYLiveManager.shared().apmManager.startApmInfo.enterPushTs
            let commonMemMonitor = serviceManager?.getServiceWith(LiveCommonMonitorMemServiceProtocol.self) as? LiveCommonMonitorMemServiceProtocol
            if let monitor = commonMemMonitor?.monitor {
                if LivePushDegradeSwitch.pushStartOpen() {
                    LivePushDegradeManager.shared.start(monitor)
                }
            } else {
                XYLogCollector.xyLiveLogTag("checkStartDegrade", content: "no monitor")
            }
        }
    }
    
    private func handleAfterResponsed() {
        self.decorateVM.noUseShouldLeaveRoomCompletion = true
        decorateView.afterStopSomeAction()
        
        // 干掉IM监听，防止在结束页展示UI浮层or频繁触发群组解散逻辑
        decorateVM.stopListeningParser()
        
        self.live_dismissChildren()
        // 停止主播拉流
        livePlayer_stopHostPlay()
    }
    
    private func handleStartFailAction(reason: String) {
        // 命中优化后不调用stop接口，直接跳转结束页
        if ConfigCenter.shared.justOnceBool(forKey: "ios_live_push_start_fail_handle_strategy_opt", defalut: true) {
            decorateVM.noUseShouldLeaveRoomCompletion = true
            decorateVM.notifyManagerToLeaveRoom(withReason: reason)
            handleAfterResponsed()
        } else {
            decorateVM.notifyManagerToLeaveRoom(withReason: reason)
        }
    }
    
    public func onStopResponsed(_ error: Error?, extra: [String: Any]?) {
        if let err = error {
            XYLogCollector.xyLiveLogTag("core_sdk", content: "stop api error: \(err)")
            XYAlert.live_showTextItemWithError(err, missingText: SwitchConfig.stopFailErrorToast())
            return
        }
        XYLogCollector.xyLiveLogTag("core_sdk", content: "stop api success")
        handleAfterResponsed()
    }
    
    public func onBaseRoomInfoResponsed(_ liveInfo: XYLiveCore.LiveInfo?, error: XYLiveCore.CoreNetworkError?) {
        guard let info = liveInfo else {
            XYLogCollector.xyLiveLogTag("core_sdk", content: "base info api null, error: \(String(describing: error))")
            return
        }
        // 主播拉流
        livePlayer_startHostPlay(with: info.stream)
    }
    
    public func onRoomCreated(_ roomInfo: XYLiveCore.PreRoomInfo?, extra: [String: Any]?, error: Error?) {}
    public func onLockResponsed(_ lockInfo: XYLiveCore.RoomLockInfo?, error: Error?) {}
    public func onUnLockResponsed(_ error: Error?) {}
}

extension XYLivePushRoomViewController: MediaEventDelegate {
    public func onCameraCapturePhoto(image: UIImage?, error: Error?) {}
    public func onCameraCaptureVideoResponse(id: Int, path: String?, isFinish: Bool) {
        guard let path = path else {
            XYLogCollector.xyLiveLogTag("[cameraVideo]", content: "录制视频保存本地错误")
            return
        }
        XYLogCollector.xyLiveLogTag("[cameraVideo]", content: "录制视频保存本地: \(path)")
        enum StaticBox {
            static var originPath = ""
            static var denoisePath = ""
            static var effectPath = ""
        }
        UploadNetWorkHandler.upload(filePath: path,
                                    businessType: .alpha,
                                    fileTpe: .video,
                                    startClosure: {},
                                    progressClosure: { _ in },
                                    completionClosure: { fileId, _, error in
            if let error = error {
                XYLogCollector.xyLiveLogTag("[cameraVideo]", content: "upload failed: \(error.localizedDescription)")
                return
            }
            if id == 0 {
                StaticBox.originPath = fileId
            } else if id == 1 {
                StaticBox.denoisePath = fileId
            } else if id == 2 {
                StaticBox.effectPath = fileId
            }
            if isFinish {
                let denoiseFlag = CameraAdapter.shared().denoiseFlag
                let beautyVerison = "v3"
                let resolution = "\(XYLiveManager.shared().coreManager.media.outputSize)"
                let cpuUsage = "\(ApmContext.cpuUsage)"
                let memTotal = "\(ApmContext.totalMemory)MB"
                let systemFreeMem = "\(ApmContext.systemFreeRam)MB"
                let cameraFps = [XYLiveManager.shared().coreManager.media.cameraFrameCount].xyLive_jsonString()
                let renderFps = [XYLiveManager.shared().coreManager.media.previewFrameCount].xyLive_jsonString()
                let filterParams = CameraAdapter.shared().currentFilterUploadInfo()
                let beautyParams = CameraAdapter.shared().currentBeautyUploadInfo()
                XYTracker.track { context in
                    _ = context.snsLiveUploadFileid
                        .sampleRate(1.0)
                        .fileId(beautyVerison)
                        .denoiseFlag(denoiseFlag)
                        .beforeVideoFileld(StaticBox.originPath)
                        .denoiseVideoFileld(StaticBox.denoisePath)
                        .afterVideoFileld(StaticBox.effectPath)
                        .beforeVideoResolution(resolution)
                        .afterVideoResolution(resolution)
                        .memTotal(memTotal)
                        .memFree(systemFreeMem)
                        .cpuUsage(cpuUsage)
                        .systemCpuUsage(cpuUsage)
                        .cameraFps(cameraFps)
                        .renderkitFps(renderFps)
                        .filterParams(filterParams)
                        .beautifyParams(beautyParams)
                }
            }
            XYLogCollector.xyLiveLogTag("[cameraVideo]", content: "upload succeed: \(fileId)")
            try? FileManager.default.removeItem(atPath: path)
        }, tag: nil)
    }
    
    public func onRequestFrameIndex(completion: @escaping ((FrameExtractionIndexModel?, Error?) -> Void)) {
        let roomId = XYLiveManager.shared().roomInfo.roomIDStr
        ScreenNetwork.fetchFrameIndex(roomId, completion: completion)
    }
    
    public func onCameraInterceptImageResponse(images: [UIImage], timestamp: String) {
        if images.count == 3 || images.count == 2 {
            let roomId = XYLiveManager.shared().roomInfo.roomIDStr
            let originName = "origin_\(roomId)_\(timestamp).png"
            let originDir = XYLiveFileManager.alphaRootCachesDirectory() + CameraConfig.kOriginImagePath
            let originPath = XYLiveFileManager.alphaDirectoryCreate(withDirectory: originDir) ?? ""
            let originUrl = NSURL.fileURL(withPath: originPath, isDirectory: true).appendingPathComponent(originName)
            let originRes = self.saveImageToPath(originUrl, image: images.first)
            
            let effectName = "beauty_\(roomId)_\(timestamp).png"
            let effectDir = XYLiveFileManager.alphaRootCachesDirectory() + CameraConfig.kEffectImagePath
            let effectPath = XYLiveFileManager.alphaDirectoryCreate(withDirectory: effectDir) ?? ""
            let effectUrl = NSURL.fileURL(withPath: effectPath, isDirectory: true).appendingPathComponent(effectName)
            let effectRes = self.saveImageToPath(effectUrl, image: images.last)
            CameraAdapter.shared().interceptImages.removeAll()
            if originRes, effectRes {
                var urls = [originUrl.path, effectUrl.path]
                var denoiseUrlPath = ""
                let flag = ConfigCenter.shared.bool(forKey: "ios_live_image_alone_upload", defalut: false)
                if flag {
                    if CameraAdapter.shared().denoiseFlag, images.count == 3 {
                        let denoiseName = "denoise_\(roomId)_\(timestamp).png"
                        let denoiseDir = XYLiveFileManager.alphaRootCachesDirectory() + CameraConfig.kDenoiseImagePath
                        let denoisePath = XYLiveFileManager.alphaDirectoryCreate(withDirectory: denoiseDir) ?? ""
                        let denoiseUrl = NSURL.fileURL(withPath: denoisePath, isDirectory: true).appendingPathComponent(denoiseName)
                        let denoiseRes = self.saveImageToPath(denoiseUrl, image: images[1])
                        if denoiseRes {
                            urls.append(denoiseUrl.path)
                            denoiseUrlPath = denoiseUrl.path
                            DispatchQueue.main.asyncAfter(deadline: .now() + 10) { [weak self] in
                                self?.uploadResourceService(id: 1, filePath: denoiseUrl.path, timestamp: timestamp)
                            }
                        }
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0) { [weak self] in
                        self?.uploadResourceService(id: 0, filePath: originUrl.path, timestamp: timestamp)
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + 20) { [weak self] in
                        self?.uploadResourceService(id: 2, filePath: effectUrl.path, timestamp: timestamp)
                    }
                    return
                }
                
                let localDate = self.fetchLocalDate()
                UploadNetWorkHandler.upload(filePaths: urls,
                                            businessType: .alpha,
                                            fileTpe: .image,
                                            startClosure: {},
                                            progressClosure: { _ in },
                                            completionClosure: { response, error in
                    if let error = error as NSError? {
                        XYLogCollector.xyLiveLogTag("[cameraFrame]", content: "upload failed: \(error.localizedDescription)")
                        return
                    }
                    if let response = response, let originRes = response[originUrl.path], let effectRes = response[effectUrl.path] {
                        let originFileId = originRes.fileID
                        var denoiseFileId = ""
                        if CameraAdapter.shared().denoiseFlag, images.count == 3, let denoiseRes = response[denoiseUrlPath] {
                            denoiseFileId = denoiseRes.fileID
                        }
                        let effectFileId = effectRes.fileID
                        let denoiseFlag = CameraAdapter.shared().denoiseFlag
                        let cpuUsage = "\(ApmContext.cpuUsage)"
                        let memTotal = "\(ApmContext.totalMemory)MB"
                        let systemFreeMem = "\(ApmContext.systemFreeRam)MB"
                        let roomId = XYLiveManager.shared().roomInfo.roomIDStr
                        XYTracker.track { context in
                            _ = context.snsLiveCameraFrameUpload
                                .sampleRate(1.0)
                                .originImage(originFileId)
                                .denoiseImage(denoiseFileId)
                                .effectImage(effectFileId)
                                .denoiseFlag(denoiseFlag)
                                .cpuUsage(cpuUsage)
                                .systemCpuUsage(cpuUsage)
                                .memTotal(memTotal)
                                .memFree(systemFreeMem)
                                .timestamp(timestamp)
                                .roomId(roomId)
                                .localDate(localDate)
                        }
                        XYLogCollector.xyLiveLogTag("[cameraFrame]", content: "upload succeed: \(originFileId), \(denoiseFileId), \(effectFileId)")
                    }
                    try? FileManager.default.removeItem(atPath: originUrl.path)
                    if ConfigCenter.shared.bool(forKey: "live_ios_fix_removeitem_empty_crash", defalut: true) {
                        if !denoiseUrlPath.isEmpty {
                            try? FileManager.default.removeItem(atPath: denoiseUrlPath)
                        }
                    } else {
                        try? FileManager.default.removeItem(atPath: denoiseUrlPath)
                    }
                    try? FileManager.default.removeItem(atPath: effectUrl.path)
                }, tag: nil)
            }
        }
    }
    
    private func uploadResourceService(id: Int, filePath: String, timestamp: String) {
        UploadNetWorkHandler.upload(filePath: filePath,
                                    businessType: .alpha,
                                    fileTpe: .image,
                                    startClosure: {},
                                    progressClosure: { _ in },
                                    completionClosure: { [weak self] fileId, _, error in
            if let error = error {
                XYLogCollector.xyLiveLogTag("[cameraFrame]", content: "upload failed: \(error.localizedDescription)")
                return
            }
            if let model = CameraAdapter.shared().fetchFrameModel() {
                // 上传后端接口
                let roomId = XYLiveManager.shared().roomInfo.roomIDStr
                let index = model.index
                var type = "camera"
                if id == 1 {
                    type = "denoise"
                } else if id == 2 {
                    type = "beauty"
                }
                ScreenNetwork.reportFrameExtraction(roomId, index: index, fileId: fileId, type: type) { _ in }
            }
            // apm上报
            self?.uploadAPM(id: id, fileId: fileId, timestamp: timestamp)
            try? FileManager.default.removeItem(atPath: filePath)
        }, tag: nil)
    }
    
    private func uploadAPM(id: Int, fileId: String, timestamp: String) {
        enum StaticBox {
            static var originFileId = ""
            static var denoiseFileId = ""
            static var effectFileId = ""
        }
        if id == 0 {
            StaticBox.originFileId = fileId
        } else if id == 1 {
            StaticBox.denoiseFileId = fileId
        } else if id == 2 {
            StaticBox.effectFileId = fileId
        }
        
        if !StaticBox.originFileId.isEmpty, !StaticBox.effectFileId.isEmpty {
            let originFileId = StaticBox.originFileId
            let denoiseFileId = !StaticBox.denoiseFileId.isEmpty ? StaticBox.denoiseFileId : ""
            let effectFileId = StaticBox.effectFileId
            let denoiseFlag = CameraAdapter.shared().denoiseFlag
            let cpuUsage = "\(ApmContext.cpuUsage)"
            let memTotal = "\(ApmContext.totalMemory)MB"
            let systemFreeMem = "\(ApmContext.systemFreeRam)MB"
            let roomId = XYLiveManager.shared().roomInfo.roomIDStr
            let localDate = fetchLocalDate()
            XYTracker.track { context in
                _ = context.snsLiveCameraFrameUpload
                    .sampleRate(1.0)
                    .originImage(originFileId)
                    .denoiseImage(denoiseFileId)
                    .effectImage(effectFileId)
                    .denoiseFlag(denoiseFlag)
                    .cpuUsage(cpuUsage)
                    .systemCpuUsage(cpuUsage)
                    .memTotal(memTotal)
                    .memFree(systemFreeMem)
                    .timestamp(timestamp)
                    .roomId(roomId)
                    .localDate(localDate)
            }
            StaticBox.originFileId = ""
            StaticBox.denoiseFileId = ""
            StaticBox.effectFileId = ""
            XYLogCollector.xyLiveLogTag("[cameraFrame]", content: "upload succeed: \(originFileId), \(denoiseFileId), \(effectFileId)")
        }
    }

    private func saveImageToPath(_ pathUrl: URL?, image: UIImage?) -> Bool {
        let flag = ConfigCenter.shared.justOnceBool(forKey: "live_ios_save_image_fine", defalut: false)
        if flag {
            if let url = pathUrl, let img = image, let imgData = img.pngData() {
                do {
                    try imgData.write(to: url, options: .atomic)
                    XYLogCollector.xyLiveLogTag("[cameraFrame]", content: "保存图片成功: \(url.path)")
                    return true
                } catch {
                    XYLogCollector.xyLiveLogTag("[cameraFrame]", content: "保存图片失败(flag = \(flag)): \(String(describing: pathUrl))")
                    return false
                }
            }
        } else {
            if let url = pathUrl, let imgRef = image?.cgImage {
                let destintation = CGImageDestinationCreateWithURL(url as CFURL, kUTTypePNG, 1, nil)
                if let dest = destintation {
                    CGImageDestinationAddImage(dest, imgRef, nil)
                    CGImageDestinationFinalize(dest)
                    XYLogCollector.xyLiveLogTag("[cameraFrame]", content: "保存图片成功: \(url.path)")
                    return true
                }
            }
        }
        XYLogCollector.xyLiveLogTag("[cameraFrame]", content: "flag = \(flag), 保存图片失败: \(String(describing: pathUrl))")
        return false
    }
    
    private func fetchLocalDate() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd HH:mm:ss"
        formatter.timeZone = TimeZone(identifier: "Asia/Shanghai")
        return formatter.string(from: Date())
    }
}
