//
//  XYLivePushMultiChatRoomInviteSettingPanelVM.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/28.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatRoomInviteSettingPanelVM.h"
#import <XYAlphaNetwork/XYAlphaNetwork-Swift.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYAPIRoute/XYAPIRoute.h>
#import <XYLiveKit/XYLivePushMultiChatRoomConsts.h>
#import <XYAlphaUtils/NSDictionary+XYLive.h>

@implementation XYLivePushMultiChatRoomInviteSettingPanelVM

// 请求开关状态接口
- (void)requestSwitchStatusWithRoomId:(NSString *)roomId
                           completion:(void(^)(BOOL isOn, NSError *error))completion {
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/line/v1/config_panel/talk_space" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMGET;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"app_id"] = @(1);
    params[@"room_id"] = roomId;
    [api addingQueryItems:params.copy];
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatRoomModule content:[NSString stringWithFormat:@"start request chatroom panel data api, params:{%@}", params]];
    WS
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatRoomModule content:[NSString stringWithFormat:@"chatroom panel response, result:{data:%@, error:%@}", response.result.value, response.error]];
        [response onSuccess:^(id  _Nonnull value) {
            if (completion) {
                NSDictionary *result = XYSAFE_CAST(value, NSDictionary);
                NSNumber *switchStatus = [result xyLive_numberValueForKey:@"viewer_apply_free_approval_switch"];
                // 更新本地缓存
                weak_self.opened = [switchStatus boolValue];
                weak_self.enableGiftSwitch = [[result xyLive_numberValueForKey:@"viewer_enable_send_gift"] boolValue];
                completion(switchStatus ? [switchStatus boolValue] : YES, nil);
            }
        } onFailure:^(NSError * _Nonnull error) {
            if (completion) {
                completion(nil, error);
            }
        }];
    }];
}

// 更新开关状态
- (void)updateStatusWithRoomId:(NSString *)roomId
                      isOpened:(BOOL)isOpened
                    completion:(void(^)(id result, NSError *error))completion {
    [self updateConfigWithRoomId:roomId
                        isOpened:isOpened
                enableGiftSwitch:self.enableGiftSwitch
                      completion:completion];
}

// 更新送礼开关状态
- (void)updateGiftSwitchWithRoomId:(NSString *)roomId
                  enableGiftSwitch:(BOOL)enableGiftSwitch
                        completion:(void(^)(id result, NSError *error))completion {
    [self updateConfigWithRoomId:roomId
                        isOpened:self.isOpened
                enableGiftSwitch:enableGiftSwitch
                      completion:completion];
}


- (void)updateConfigWithRoomId:(NSString *)roomId
                      isOpened:(BOOL)isOpened
              enableGiftSwitch:(BOOL)enableGiftSwitch
                    completion:(void(^)(id result, NSError *error))completion {
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/line/v1/config_panel/talk_space/update" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMPOST;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"app_id"] = @(1);
    params[@"room_id"] = roomId;
    params[@"viewer_apply_free_approval_switch"] = @(isOpened);
    params[@"viewer_enable_send_gift"] = @(enableGiftSwitch);
    [api addingQueryItems:params.copy];
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatRoomModule content:[NSString stringWithFormat:@"start request update chatroom panel data api, params:{%@}", params]];
    WS
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatRoomModule content:[NSString stringWithFormat:@"update chatroom panel data response, result:{data:%@, error:%@}",response.result.value, response.error]];
        [response onSuccess:^(id  _Nonnull value) {
            SS
            // 更新本地缓存
            self.opened = isOpened;
            self.enableGiftSwitch = enableGiftSwitch;
            completion ? completion(value, nil) : nil;
        } onFailure:^(NSError * _Nonnull error) {
            completion ? completion(nil, error) : nil;
        }];
    }];
}

@end
