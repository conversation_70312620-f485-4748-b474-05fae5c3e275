//
//  XYLiveMultiChatInviteListFriendCell.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <XYLiveKit/XYLiveMultiChatInviteListModel.h>
@class XYTrackerEventContext;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiChatInviteListFriendCell : UITableViewCell

// 点击回调
@property (nonatomic, copy) void(^didTapHandler)(XYLiveMultiChatInviteListFriendModel *listItem, NSIndexPath *indexPath);

// 点击埋点
@property (nonatomic, copy) XYTrackerEventContext *(^didTapTrackerHandler)(XYLiveMultiChatInviteListFriendModel *listItem, NSIndexPath *indexPath);


// 数据绑定
- (void)bindListItem:(XYLiveMultiChatInviteListFriendItem *)listItem indexPath:(NSIndexPath *)indexPath;

// 刷新数据
- (void)reloadData;

@end

NS_ASSUME_NONNULL_END
