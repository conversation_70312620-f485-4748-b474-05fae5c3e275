//
//  XYLivePushLinkHostConsts.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/27.
//  Copyright © 2025 XingIn. All rights reserved.
//
#import <XYLiveUIKit/XYLiveUIKit.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineConsts.h>

#ifndef XYLivePushLinkHostConsts_h
#define XYLivePushLinkHostConsts_h

// 模块名称
#define kXYLiveLinkHostModuleName              @"LinkHost"

// 设置面板高度
#define kXYLiveLinkHostSettingPanelH           DeviceUtility.screenHeight * 0.75;

// 状态
typedef NS_ENUM(NSUInteger, XYLiveLinkHostState) {
    XYLiveLinkHostStateIdle,    // 空闲状态
    XYLiveLinkHostStateMatch,   // 匹配中
    XYLiveLinkHostStateLine,    // 连线中
    XYLiveLinkHostStatePK,      // PK中
    XYLiveLinkHostStateLikePK, // 人气PK
};

// 连线限制范围
typedef NS_ENUM(NSUInteger, XYLiveMultiLineLimitType) {
    XYLiveMultiLineLimitTypeAll = 1,       // 所有主播
    XYLiveMultiLineLimitTypeFriend         // 好友
};

// PK限制范围
typedef NS_ENUM(NSUInteger, XYLiveMultiPKLimitType) {
    XYLiveMultiPKLimitTypeAll = 1,        // 所有主播
    XYLiveMultiPKLimitTypeFriend          // 好友
};

#endif /* XYLivePushLinkHostConsts_h */
