//
//  XYLiveMultiChatInviteListVC.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatInviteListVC.h"
#import <XYLiveKit/XYLiveMultiChatInviteListFriendCell.h>
#import <XYLiveKit/XYLiveMultiChatInviteListAudienceCell.h>
#import <XYLiveKit/XYLiveMultiChatInviteListEmptyCell.h>
#import <XYLiveKit/XYLiveMultiChatInviteListViewModel.h>
#import <XYLiveKit/XYLiveMultiChatInviteListModel.h>
#import <XYLiveKit/XYLiveMultiChatInviteListHeaderView.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLiveKit/XYLiveMultiLinkInviteVC.h>
#import <XYLiveKit/XYLivePushMultiLinkBizTracker.h>

// 重用标识符
static NSString *const kXYLiveMultiChatInviteListAudienceCellIde = @"XYLiveMultiChatInviteListAudienceCell";
static NSString *const kXYLiveMultiChatInviteListFriendCellIde = @"XYLiveMultiChatInviteListFriendCell";
static NSString *const kXYLiveMultiChatInviteListEmptyCellIde = @"XYLiveMultiChatInviteListEmptyCell";

@interface XYLiveMultiChatInviteListVC()<XYLiveMultiLinkListener>

@property (nonatomic, assign) XYLiveMultiLinkBizType bizType;
@property (nonatomic, assign) XYLiveMultiLinkInviteSource source;
@property (nonatomic, copy)   NSString *extraInfo;
@property (nonatomic, strong) XYLiveMultiChatInviteListViewModel *viewModel;

@end

@implementation XYLiveMultiChatInviteListVC

/// 初始化
- (instancetype)initWithBizType:(XYLiveMultiLinkBizType)bizType
                         source:(XYLiveMultiLinkInviteSource)source
                      extraInfo:(NSString * _Nullable)extraInfo {
    if (self = [super initWithNibName:nil bundle:nil]) {
        _bizType = bizType;
        _source = source;
        _extraInfo = extraInfo;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // 注册cell
    [self.tableView registerClass:XYLiveMultiChatInviteListFriendCell.class forCellReuseIdentifier:kXYLiveMultiChatInviteListFriendCellIde];
    [self.tableView registerClass:XYLiveMultiChatInviteListAudienceCell.class forCellReuseIdentifier:kXYLiveMultiChatInviteListAudienceCellIde];
    [self.tableView registerClass:XYLiveMultiChatInviteListEmptyCell.class forCellReuseIdentifier:kXYLiveMultiChatInviteListEmptyCellIde];
    // 注册监听
    [self.multiLinkService registerListener:self];
}

#pragma mark - override

- (void)refreshWithType:(XYLiveRefreshDataType)type completion:(void (^)(NSError * _Nonnull))completion {
    // 埋点上报
    if (type == XYLiveRefreshDataTypePull) {
        [XYLivePushMultiLinkBizTracker eventActionId69406WithBizType:self.bizType secondTabName:@"邀请连线"];
    }
    [self.viewModel requestListDataWithRoomId:self.liveInfoService.roomId completion:^(NSArray<XYLiveMultiChatInviteListGroupItem *> * _Nonnull listItems, NSError * _Nonnull error) {
        completion ? completion(error) : nil;
    }];
}

- (BOOL)hasData {
    return YES;
}

- (UIView *)errorResponseView:(NSError *)error {
    return nil;
}

#pragma mark - UITableViewDelegate / UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.viewModel.listItems.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    XYLiveMultiChatInviteListGroupItem *group = self.viewModel.listItems[section];
    return group.items.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    XYLiveMultiChatInviteListGroupItem *group = self.viewModel.listItems[indexPath.section];
    XYLiveMultiChatInviteListItem *listItem = group.items[indexPath.row];
    // 好友列表
    if ([listItem isKindOfClass:XYLiveMultiChatInviteListFriendItem.class]) {
        XYLiveMultiChatInviteListFriendCell *cell = [tableView dequeueReusableCellWithIdentifier:kXYLiveMultiChatInviteListFriendCellIde forIndexPath:indexPath];
        // 数据绑定
        [cell bindListItem:(XYLiveMultiChatInviteListFriendItem *)listItem indexPath:indexPath];
        // 点击邀请
        WS
        cell.didTapHandler = ^(XYLiveMultiChatInviteListFriendModel * _Nonnull item, NSIndexPath * _Nonnull fIndexPath) {
            SS
            if (item.userId.length) {
                // 邀请好友
                [self inviteFriendWithItem:item indexPath:indexPath];
            } else {
                // 邀请更多
                [self inviteMoreWithItem:item indexPath:indexPath];
            }
        };
        cell.didTapTrackerHandler = ^XYTrackerEventContext * _Nonnull(XYLiveMultiChatInviteListFriendModel * _Nonnull listItem, NSIndexPath * _Nonnull indexPath) {
            SS
            if (!listItem.userId.length) {
                // 上报分享更多埋点
                return [XYLivePushMultiLinkBizTracker eventActionId69408];
            }
            // 点击邀请好友
            return [XYLivePushMultiLinkBizTracker eventActionId32872WithBizType:self.bizType thirdTabName:@"我的好友" userId:listItem.userId];
        };
        return cell;
    }
    // 观众列表
    if ([listItem isKindOfClass:XYLiveMultiChatInviteListAudienceItem.class]) {
        XYLiveMultiChatInviteListAudienceCell *cell = [tableView dequeueReusableCellWithIdentifier:kXYLiveMultiChatInviteListAudienceCellIde forIndexPath:indexPath];
        // 数据绑定
        [cell bindListItems:group.items indexPath:indexPath];
        WS
        // 点击邀请
        cell.didTapInviteHandler = ^(XYLiveMultiChatInviteListAudienceItem * _Nonnull item, NSIndexPath * _Nonnull indexPath) {
            SS
            // 更新状态
            item.isInvited = YES;
            // 主播端邀请
            if (self.liveInfoService.isHost) {
                [self.multiLinkService inviteWithBizType:self.bizType targetUserId:item.userId targetRoomId:item.roomId mediaType:XYLiveMultiLinkMediaTypeAudio bizExtraInfo:self.extraInfo];
            } else {
                // 超助处理逻辑
                [self.multiLinkService simpleInviteWithBizType:self.bizType targetUserId:item.userId targetRoomId:item.roomId mediaType:XYLiveMultiLinkMediaTypeAudio bizExtraInfo:self.extraInfo];
            }
        };
        cell.didTapTrackerHandler = ^XYTrackerEventContext * _Nonnull(XYLiveMultiChatInviteListAudienceItem * _Nonnull item, NSIndexPath * _Nonnull indexPath) {
            SS
            // 埋点上报
            return [XYLivePushMultiLinkBizTracker eventActionId32872WithBizType:self.bizType thirdTabName:@"房内观众" userId:item.userId];
        };
        return cell;
    }
    // 空占位
    if ([listItem isKindOfClass:XYLiveMultiChatInviteListEmptyItem.class]) {
        XYLiveMultiChatInviteListEmptyCell *cell = [tableView dequeueReusableCellWithIdentifier:kXYLiveMultiChatInviteListEmptyCellIde forIndexPath:indexPath];
        // 数据绑定
        [cell bindListItem:listItem indexPath:indexPath];
        WS
        cell.didTapRetryHandler = ^{
            SS
            [self startPullToRefresh:YES];
        };
        return cell;
    }
    return nil;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    XYLiveMultiChatInviteListGroupItem *group = self.viewModel.listItems[indexPath.section];
    XYLiveMultiChatInviteListItem *listItem = group.items[indexPath.row];
    if ([listItem isKindOfClass:XYLiveMultiChatInviteListFriendItem.class]) {
        return 80;
    } else if ([listItem isKindOfClass:XYLiveMultiChatInviteListAudienceItem.class]) {
        return 68;
    }
    return kXYLiveMultiChatInviteListEmptyCellHeight;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    XYLiveMultiChatInviteListGroupItem *group = self.viewModel.listItems[section];
    XYLiveMultiChatInviteListItem *listItem = group.items.firstObject;
    return [listItem isKindOfClass:XYLiveMultiChatInviteListFriendItem.class] ? 32 : 44;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    XYLiveMultiChatInviteListGroupItem *group = self.viewModel.listItems[section];
    XYLiveMultiChatInviteListItem *listItem = group.items.firstObject;
    // 好友列表
    if ([listItem isKindOfClass:XYLiveMultiChatInviteListFriendItem.class]) {
        XYLiveMultiChatInviteListFriendHeaderView *headView = [[XYLiveMultiChatInviteListFriendHeaderView alloc] init];
        headView.title = group.title;
        return headView;
    }
    XYLiveMultiChatInviteListHeaderView *headView = [[XYLiveMultiChatInviteListHeaderView alloc] init];
    headView.title = group.title;
    return headView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (self.viewModel.listItems.count && section == self.viewModel.listItems.count - 1) {
        return CGFLOAT_MIN;
    }
    return 8;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    XYLiveMultiChatInviteListGroupItem *group = self.viewModel.listItems[section];
    UIView *footerView = [[UIView alloc] init];
    return footerView;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    XYLiveMultiChatInviteListGroupItem *group = self.viewModel.listItems[indexPath.section];
    XYLiveMultiChatInviteListItem *listItem = group.items[indexPath.row];
    // 点击房内观众,拉起P页
    if ([listItem isKindOfClass:XYLiveMultiChatInviteListAudienceItem.class]) {
        XYLiveMultiChatInviteListAudienceItem *item = XYSAFE_CAST(listItem, XYLiveMultiChatInviteListAudienceItem);
        self.showUserCardHandler ? self.showUserCardHandler(item.userInfo) : nil;
    }
}

#pragma mark - XYLiveMultiLinkInviteListener

- (void)onInviteResponseWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId error:(NSError *)error {
    // 合法性校验
    if (bizType != self.bizType) { return; }
    // 更新状态
    [self.viewModel updateInviteStatusWithUserId:targetUserId isInvited:error == nil];
    [self.tableView reloadData];
}

- (void)onInviteFailWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId failReason:(XYLiveMultiLinkInviteFailReason)failReason {
    // 合法性校验
    if (bizType != self.bizType) { return; }
    // 更新状态
    [self.viewModel updateInviteStatusWithUserId:targetUserId isInvited:NO];
    [self.tableView reloadData];
}

#pragma mark - Private

// 邀请好友
- (void)inviteFriendWithItem:(XYLiveMultiChatInviteListFriendModel *)item indexPath:(NSIndexPath *)indexPath {
    WS
    [self.viewModel inviteFriendWithRoomId:self.liveInfoService.roomId userId:item.userId completion:^(XYPMMessageShareToMessageLiveModel * _Nullable shareModel, XYPMMessageChatUserModel * _Nullable userInfo, NSError * _Nullable error) {
        if (error == nil && userInfo) {
            [XYPMRouteToShareManager sendShareWithModel:shareModel text:nil users:@[userInfo] completionBlock:^{
                SS
                // 埋点上报
                [XYLivePushMultiLinkBizTracker eventActionId33415WithBizType:self.bizType thirdTabName:@"我的好友" userId:item.userId hasGoods:self.hasGoods];
                // Toast提示
                [XYAlert live_showTextItemWithText:@"已发送邀请"];
                // 更新状态
                item.isInvited = YES;
                XYLiveMultiChatInviteListFriendCell *cell = [self.tableView cellForRowAtIndexPath:indexPath];
                [cell reloadData];
            }];
        }
    }];
}

// 邀请更多
- (void)inviteMoreWithItem:(XYLiveMultiChatInviteListFriendModel *)item indexPath:(NSIndexPath *)indexPath {
    [self.viewModel inviteFriendWithRoomId:self.liveInfoService.roomId userId:@"" completion:^(XYPMMessageShareToMessageLiveModel * _Nullable shareModel, XYPMMessageChatUserModel * _Nullable userInfo, NSError * _Nullable error) {
        if (error == nil) {
            // 拉起分享面板
            [XYPMRouteToShareManager routeToShareWithModel:shareModel idList:nil shareDesc:nil source:nil];
        }
    }];
}

- (id<XYLiveInfoServiceProtocol>)liveInfoService {
    return self.liveInfoServiceHandler ? self.liveInfoServiceHandler() : nil;
}

- (id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    return self.multiLinkServiceHandler ? self.multiLinkServiceHandler() : nil;
}

- (BOOL)hasGoods {
    return self.hasGoodsHandler ? self.hasGoodsHandler() : NO;
}

#pragma mark - Lazy

- (XYLiveMultiChatInviteListViewModel *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[XYLiveMultiChatInviteListViewModel alloc] init];
        WS
        _viewModel.didFetchInviteStatusHandler = ^BOOL(XYLiveMultiChatInviteListAudienceItem * _Nonnull listItem) {
            SS
            return [self.multiLinkService isInvitingWithBizType:self.bizType targetUserId:listItem.userId];
        };
    }
    return _viewModel;
}

@end
