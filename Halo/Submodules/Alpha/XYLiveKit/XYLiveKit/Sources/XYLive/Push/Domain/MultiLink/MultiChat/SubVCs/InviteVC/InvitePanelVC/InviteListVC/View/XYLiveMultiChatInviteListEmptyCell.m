//
//  XYLiveMultiChatInviteListEmptyCell.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/23.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatInviteListEmptyCell.h"
#import <XYLiveKit/XYLiveMutiLinkEmptyView.h>
#import <XYLiveFoundation/UIImage+XYLiveKit.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYUIKit/XYEmptyViewUtil.h>
#import <Masonry/Masonry.h>

@interface XYLiveMultiChatInviteListEmptyCell()

@property (nonatomic, strong) XYLiveMutiLinkEmptyView *emptyView;

@end

@implementation XYLiveMultiChatInviteListEmptyCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

/// 数据绑定
- (void)bindListItem:(XYLiveMultiChatInviteListItem *)listItem indexPath:(NSIndexPath *)indexPath {
    XYLiveMultiChatInviteListEmptyItem *item = XYSAFE_CAST(listItem, XYLiveMultiChatInviteListEmptyItem);
    self.emptyView.title =  item.showErrorResponseView ? @"未连接到服务器，刷新一下试试" : @"暂无观众";
    self.emptyView.image = item.showErrorResponseView ? [XYEmptyViewUtil networkImage] : [XYEmptyViewUtil userImage];
    self.emptyView.retryBtnTitle = item.showErrorResponseView ? @"刷新" : nil;
}

- (void)setupSubviews {
    // 创建空占位视图
    [self setupEmptyView];
    
    // 布局
    [self.emptyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
}

#pragma mark - UI

- (void)setupEmptyView {
    XYLiveMutiLinkEmptyView *emptyView = [[XYLiveMutiLinkEmptyView alloc] init];
    WS
    emptyView.didTapRetryHandler = ^{
        SS
        self.didTapRetryHandler ? self.didTapRetryHandler() : nil;
    };
    [self.contentView addSubview:emptyView];
    self.emptyView = emptyView;
}

@end
