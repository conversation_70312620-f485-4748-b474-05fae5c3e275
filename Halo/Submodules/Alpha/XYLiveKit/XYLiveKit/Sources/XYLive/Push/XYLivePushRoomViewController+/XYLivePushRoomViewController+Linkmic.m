//
//  XYLivePushRoomViewController+Linkmic.m
//  XYLiveKit
//
//  Created by 周博立 on 2020/8/19.
//  Copyright © 2020 XingIn. All rights reserved.
//

#import "XYLivePushRoomViewController.h"
#import "XYTrackLiveBroadcastPage.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLivePushUserCardService.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLiveKit/XYLiveMultiLinkSwitchConfig.h>

@import KVOController;
@import XYConfigCenter;
@import XYLiveUIKit;
@import XYLiveFoundation;

@implementation XYLivePushRoomViewController (Linkmic)

- (BOOL)enableNewSwitch {
    return XYConfigCenter().boolForKey(@"ios_old_link_mic_hot_switch", YES);
}

- (void)link_setupKvo {
    __weak typeof(self) wself = self;
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCLinkingHostQuit)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself link_reset];
    }];
    
    [self.KVOController observe:self.decorateVM.linkmicCardContainerViewModel.candidateViewModel keyPath:NSStringFromSelector(@selector(dismissCardContainerView)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself.decorateView.cardContainerView dismissAnimated:YES];
    }];
    
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(notifyVCToShowLinkmicList)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        XYAlertLoadingView *loading = [XYAlertLoadingView live_createAlertLoading];
        [loading showInView:wself.decorateView];
        [wself.decorateVM determineHostInitalLinkmicStatus:^(BOOL success, NSError * _Nullable error) {
            [loading hide];
        }];
    }];
    
    [self.KVOController observe:self.decorateVM.linkmicCardContainerViewModel.switchPanelViewModel keyPath:NSStringFromSelector(@selector(notifyDissmissCardView)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        if (wself.decorateVM.linkmicCardContainerViewModel.switchPanelViewModel.notifyDissmissCardView) {
            [wself.decorateView.cardContainerView dismissAnimated:YES];
        }
    }];
}

- (CGFloat)link_subHostHeight {
    CGFloat subHostWidth = XYLiveLinkmicWindowHeight;
    if (self.linkType == XYLiveIMLinkmicTypeVideo) {
        subHostWidth = XYLiveLinkmicVideoWindowHeight;
    }
    return subHostWidth;
}

- (void)link_hideSubhostBubble {
    XYExecuteOnMainQueue(^{
        [self adjustToShowRightBottomCard];
    });
}

- (void)adjustToShowRightBottomCard {
    [self.decorateView showRightBottomCardAtDefaultPosition];
}

- (void)adjustToShowRightBottomCardForMultiLink:(BOOL)hasLink {
    if (hasLink) {
        [self.decorateView showRightBottomCardAboveSubhost];
    } else {
        [self.decorateView showRightBottomCardAtDefaultPosition];
    }
}

- (void)link_reset {
    [self link_hideSubhostBubble];
}

// 校验麦上用户开启视频采集恢复preView
- (void)checkLinkCameraPreview {
    // 判断是否连麦中
    if (!self.linkService.isLinking) { return; }
    // 开关判断
    if (![XYLiveMultiLinkSwitchConfig enableRTCFixPushGlobalWindowCameraException]) { return; }
    // 事件分发
    [XYLiveManagerSharedInstance.liveImDistributer trigger:@"MANUAL_FLOATING_POP_RESUME_AUDIO_AND_VIDEO" info:nil raw:@{
        @"mute" :@(NO)
    }];
}

- (id<XYLiveMultiLinkPushServiceProtocol>)linkService {
    return [self.serviceManager getServiceWithProtocol:@protocol(XYLiveMultiLinkPushServiceProtocol)];
}

#pragma mark <Rtmp>
#pragma mark Deprecated
- (void)restoreSingleHostPusherAndResetPlayer {
//    if (!RTCSwitch) {
        //[self.pusher applyNormalLivePushConfig];
//        [self.xyPlayer stopPlay];
//        [self.xyPlayer stopPreview];
//    }
}

@end
