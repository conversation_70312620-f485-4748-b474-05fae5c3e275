//
//  XYLiveMultiPKContributorView.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/14.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYLiveUIKit/XYLiveEventPassView.h>
#import <XYLiveServiceProtocol/XYLiveMultiPKConsts.h>
#import <XYLiveKit/XYLiveMultiPKModel.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiPKContributorView : XYLiveEventPassView

// 点击事件回调
@property (nonatomic, copy) void(^didTapUserHandler)(XYLiveUserInfo *userInfo);

// 更新列表数据
- (void)updateLeftTeamInfo:(XYLiveMultiPKTeamInfo *)leftTeamInfo rightTeamInfo:(XYLiveMultiPKTeamInfo *)rightTeamInfo;

// 更新状态
- (void)updateViewStatusWithResult:(XYLiveMultiPKResult)result;

@end



NS_ASSUME_NONNULL_END
