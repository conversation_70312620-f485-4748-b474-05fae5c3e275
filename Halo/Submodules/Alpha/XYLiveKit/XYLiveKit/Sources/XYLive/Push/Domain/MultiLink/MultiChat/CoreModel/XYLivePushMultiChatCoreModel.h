//
//  XYLivePushMultiChatCoreModel.h
//  XYLiveKit
//
//  Created by 大远 on 2025/2/14.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveIMDistributerServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveKit/XYLivePushMultiChatConsts.h>
#import <XYLiveKit/XYLivePushMultiChatCoreModelListener.h>
#import <XYLiveKit/XYLivePushMultiChatConfig.h>

@protocol XYLiveGroupServiceProtocol;

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiChatCoreModel : NSObject

// 直播间id
@property (nonatomic, copy, readonly) NSString *roomId;
// 是否为游戏直播间
@property (nonatomic, assign, readonly) BOOL isGameLive;
// 是否开启
@property (nonatomic, assign, readonly) BOOL isOpened;
// 连麦配置
@property (nonatomic, strong, readonly) XYLivePushMultiChatConfig *config;
// 申请数量
@property (nonatomic, assign, readonly) NSInteger applyNum;
// 是否挂车
@property (nonatomic, assign, readonly) BOOL hasGoods;
// 是否为团播
@property (nonatomic, assign, readonly) BOOL isGroupLive;

/// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                          socketService:(id<XYLiveIMDistributerServiceProtocol>)socketService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService
                       groupLiveService:(id<XYLiveGroupServiceProtocol>)groupLiveService;

/// 拉取基础配置
- (void)loadInitialConfig:(void(^_Nullable)(XYLivePushMultiChatConfig * _Nullable config, NSError * _Nullable error))completion;

/// 更新开关状态
- (void)updateSwitchStatus:(BOOL)isOpened completion:(void(^_Nullable)(NSError *error))completion;

/// 更新布局配置
- (void)updateLayoutType:(XYLiveMultiLinkLayoutType)layoutType completion:(void(^_Nullable)(NSError *error))completion;

/// 更新送礼开关
- (void)updateEnableGiftSwitch:(BOOL)enableGiftSwitch completion:(void (^)(NSError * _Nonnull))completion;

/// 更新连麦条件限制配置
- (void)updateLimitType:(XYLiveMultiChatLimitType)limitType extraInfo:(NSString *_Nullable)extraInfo completion:(void(^_Nullable)(NSError *error))completion;

/// 邀请
- (void)inviteWithUserId:(NSString *)userId source:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString * _Nullable)extraInfo;

/// 注册监听
- (void)registerListener:(id<XYLivePushMultiChatCoreModelListener>)listener;

/// 取消注册监听
- (void)unregisterListener:(id<XYLivePushMultiChatCoreModelListener>)listener;

@end

NS_ASSUME_NONNULL_END
