//
//  XYLiveMultiChatInviteListFriendCell.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatInviteListFriendCell.h"
#import <Masonry/Masonry.h>
#import <XYLiveKit/XYLiveMultiChatInviteListFriendView.h>
#import <XYLiveKit/XYLiveCommonConst.h>

@interface XYLiveMultiChatInviteListFriendCell()

@property (nonatomic, strong) XYLiveMultiChatInviteListFriendView *listView;

@end

@implementation XYLiveMultiChatInviteListFriendCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        // 创建UI
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    // 创建好友列表
    [self setupListView];
    
    // 布局
    [self.listView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
}

// 数据绑定
- (void)bindListItem:(XYLiveMultiChatInviteListFriendItem *)listItem indexPath:(NSIndexPath *)indexPath {
    [self.listView bindListItems:listItem.listItems indexPath:indexPath];
}

// 刷新列表
- (void)reloadData {
    [self.listView reloadData];
}

#pragma mark - UI

- (void)setupListView {
    XYLiveMultiChatInviteListFriendView *listView = [[XYLiveMultiChatInviteListFriendView alloc] init];
    WS
    listView.didTapHandler = ^(XYLiveMultiChatInviteListFriendModel * _Nonnull listItem, NSIndexPath * _Nonnull indexPath) {
        SS
        self.didTapHandler ? self.didTapHandler(listItem, indexPath) : nil;
    };
    listView.didTapTrackerHandler = ^XYTrackerEventContext * _Nonnull(XYLiveMultiChatInviteListFriendModel * _Nonnull listItem, NSIndexPath * _Nonnull indexPath) {
        SS
        return self.didTapTrackerHandler ? self.didTapTrackerHandler(listItem, indexPath) : nil;
    };
    [self.contentView addSubview:listView];
    self.listView = listView;
}

@end
