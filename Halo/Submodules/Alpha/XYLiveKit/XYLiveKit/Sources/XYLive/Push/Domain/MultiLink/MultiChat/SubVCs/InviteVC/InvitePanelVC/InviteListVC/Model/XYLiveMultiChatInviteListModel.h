//
//  XYLiveMultiChatInviteListModel.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <XYLiveFoundation/XYLiveFoundation.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkBizConsts.h>

NS_ASSUME_NONNULL_BEGIN

// 邀请好友列表
@interface XYLiveMultiChatInviteListFriendModel : XYLiveCodableModel

// 用户id
@property (nonatomic, copy)   NSString *userId;
// 昵称
@property (nonatomic, copy)   NSString *nickName;
// 头像
@property (nonatomic, copy)   NSString *avatarUrl;
// 关注状态
@property (nonatomic, assign) XYLiveFollowStatus followStatus;
// 是否发送过邀请
@property (nonatomic, assign) BOOL isInvited;
// 是否曝光过
@property (nonatomic, assign) BOOL hasShow;

@end

// 基础列表Item
@interface XYLiveMultiChatInviteListItem : XYLiveCodableModel

@end

// 空占位Item
@interface XYLiveMultiChatInviteListEmptyItem : XYLiveMultiChatInviteListItem

// 展示错误占位视图
@property (nonatomic, assign) BOOL showErrorResponseView;

@end

// 好友列表
@interface XYLiveMultiChatInviteListFriendItem: XYLiveMultiChatInviteListItem

@property (nonatomic, copy) NSArray<XYLiveMultiChatInviteListFriendModel *> *listItems;

@end

// 邀请观众列表
@interface XYLiveMultiChatInviteListAudienceItem : XYLiveMultiChatInviteListItem

// 用户id
@property (nonatomic, copy)   NSString *userId;
// 昵称
@property (nonatomic, copy)   NSString *nickName;
// 头像
@property (nonatomic, copy)   NSString *avatarURL;
// 关注状态
@property (nonatomic, assign) XYLiveFollowStatus followStatus;
// 粉丝数量
@property (nonatomic, copy)   NSString *fansNum;
// 所在房间的id
@property (nonatomic, copy)   NSString *roomId;
// 送礼金额
@property (nonatomic, assign) NSInteger sendCoins;
// 用户信息模型
@property (nonatomic, strong, readonly) XYLiveUserInfo *userInfo;
// 粉丝团
@property (nonatomic, strong) XYLiveUserFansClubInfo *fansClubInfo;
// 是否已经发出邀请
@property (nonatomic, assign) BOOL isInvited;

@end

@interface XYLiveMultiChatInviteListGroupItem : NSObject

/// 分区标题
@property (nonatomic, copy)   NSString *title;
/// 分区内容
@property (nonatomic, copy)   NSArray<XYLiveMultiChatInviteListItem *> *items;

@end

@interface XYLiveMultiChatInviteListModel : XYLiveCodableModel

// 好友列表
@property (nonatomic, copy) NSArray<XYLiveMultiChatInviteListFriendModel *> *friendList;
// 观众列表
@property (nonatomic, copy) NSArray<XYLiveMultiChatInviteListAudienceItem *> *audienceList;

@end

NS_ASSUME_NONNULL_END
