//
//  XYLivePushHostTaskVC.m
//  XYLiveKit
//
//  Created by gongyidemac on 2025/5/22.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushHostTaskVC.h"
#import "XYLivePushVariousTaskItemView.h"
#import "XYLivePushVariousTaskConfigCenter.h"
#import "XYLivePushHostTaskService.h"

#import <XYLiveKit/XYLiveKit-Swift.h>

@import XYAlphaUtils;
@import XYLiveFoundation;

static NSString *const kXYLivePushVariousTaskFinishedTypeKey = @"live_task_banner_finish";

@interface XYLivePushHostTaskVC() <XYLivePushDisperseEventListener, XYLiveCommonLoopBannerEventObserverProtocol>

@property (nonatomic, strong, nullable) id<PushRoomHostTaskDependProtocol> depend;
@property (nonatomic, strong, readonly) id<XYLiveCommonLoopBannerProtocol> bannerService;

@property (nonatomic, copy) NSArray<XYLiveRightActivityAreaModel *> *taskArray;

@property (nonatomic, strong) XYLiveCommonLoopListView *containerLoopView;// 最外层容器
@property (nonatomic, strong, nullable) XYLiveCommonLoopListView *targetView;// 任务完成时，定位的主任务视图
@property (nonatomic, assign) BOOL targeting;

// 曝光去重
@property (nonatomic, copy) NSSet<NSString *> *subImpressedPool;

@end

@implementation XYLivePushHostTaskVC

- (instancetype)initWithDepend:(id<PushRoomHostTaskDependProtocol> _Nullable)depend {
    if (self = [super init]) {
        self.depend = depend;
    }
    
    return self;
}

#pragma mark - Overrides

- (void)didLoad  {
    [super didLoad];
    
    [self setupEventObserver];
}

#pragma mark - XYLivePushDisperseEventListener

- (void)disperseEventAggregateBusinessInfoDidLoadWithInfo:(XYLiveAggregateBusinessInfo *)businessInfo {
    if (businessInfo) {
        [self handleActivityBizModelArray:businessInfo.banners];
    }
}

#pragma mark - XYLiveCommonLoopBannerEventObserverProtocol

- (void)viewWillDisplayItem:(XYLiveLoopBannerBusinessType)type view:(nonnull UIView *)view {
    if (XYLiveLoopBannerBusinessTypeHostTask == type) {
        if (self.containerLoopView.viewList.count > 1) {
            if (self.targeting) {
                self.targeting = NO;
            } else {
                [self.containerLoopView handleManualScrollWithIndex:0 animated:NO];
            }
            [self.bannerService stopLoop];
            [self.containerLoopView startLoop];
        } else {
            [self.containerLoopView reloadListView];
        }
    }
}

#pragma mark - XYLiveHostTaskServiceDelegate

- (void)openTaskCenter {
    [XYLiveManagerSharedInstance.actionRouteManager performWith:[XYLivePushVariousTaskConfigCenter taskCenterURL]];
}

#pragma mark - Privates

- (void)setupEventObserver {
    /// 开播打散事件监听
    [[self.depend disperseEventService] registerListener:self];
    
    /// 容器事件监听
    [self.bannerService registerEventObserver:self];
    
    __weak typeof(self) wself = self;
    
    /// 任务刷新
    [[self.depend imService] im_registerWithType:kXYLiveVariousRightActivityAreaIMKey key:NSStringFromClass(self.class) completionHandler:^(NSString * _Nonnull _, XYLiveCodableModel * _Nullable __, NSDictionary<NSString *,id> * _Nonnull rawData) {
        XYExecuteOnMainQueue(^{
            NSArray<XYLiveRightActivityAreaModel *> *bizModelArray = [NSArray xy_modelArrayWithClass:XYLiveRightActivityAreaModel.class json:rawData[@"banners"]];
            [wself handleActivityBizModelArray:bizModelArray];
        });
    }];
    
    /// 单个子任务完成
    [[self.depend imService] im_registerWithType:kXYLivePushVariousTaskFinishedTypeKey key:NSStringFromClass(self.class) completionHandler:^(NSString * _Nonnull _, XYLiveCodableModel * _Nullable __, NSDictionary<NSString *,id> * _Nonnull rawData) {
        XYExecuteOnMainQueue(^{
            XYLivePushVariousSubTaskFinish *finishedTask = [XYLivePushVariousSubTaskFinish xy_modelWithDictionary:rawData[@"data"]];
            [wself handleFinishedSubTask:finishedTask];
        });
    }];
}

- (void)handleActivityBizModelArray:(NSArray<XYLiveRightActivityAreaModel *> *)bizModelArray {
    // 过滤task数据，校验刷新
    NSArray<XYLiveRightActivityAreaModel *> *taskArray = [bizModelArray xy_filter:^BOOL(XYLiveRightActivityAreaModel * _Nonnull obj, NSUInteger _) {
        return [obj.bizType isEqualToString:XYLivePushRightActivityBizTypeTask];
    }];
    
    BOOL needReload = [self needReloadBizData:taskArray];
    AlphaLog(AlphaLogTag.Banner, [NSString stringWithFormat:@"v2 host task receive data needReload %@", @(needReload)]);
    if (needReload) {
        self.taskArray = taskArray;
        
        NSMutableArray<UIView *> *itemViewArray = [[NSMutableArray alloc] init];
        for (XYLiveRightActivityAreaModel *model in taskArray) {
            XYLivePushVariousTaskDetailModel *taskModel = SAFE_CAST_CLASS(model.bizDataModel, XYLivePushVariousTaskDetailModel);
            if (taskModel) {
                NSArray<XYLivePushVariousTaskItemView *> *itemViewList = [taskModel.subTaskArr xy_map:^id _Nonnull(XYLivePushVariousSubTaskDetailModel * _Nonnull subModel, NSUInteger index) {
                    XYLivePushVariousTaskItemView *itemView = [XYLivePushVariousTaskItemView new];
                    
                    // 这处赋值理论上不应该放view里，为了节省一次遍历操作故这么写了
                    subModel.bizID = taskModel.bizID;
                    subModel.bizName = taskModel.bizName;
                    [itemView configItemWithDetailModel:subModel];
                    return itemView;
                }];
                
                XYLiveCommonLoopListView *loopView = [self makeItemLoopView];
                [loopView updateViewList:itemViewList];
                
                [itemViewArray addObject:loopView];
            }
        }
       
        [self.containerLoopView updateViewList:itemViewArray];
        if (self.containerLoopView.viewList.count > 0) {
            [self.bannerService updateViews:@[self.containerLoopView] withBizType:XYLiveLoopBannerBusinessTypeHostTask];
        } else {
            [self.bannerService updateViews:@[] withBizType:XYLiveLoopBannerBusinessTypeHostTask];
        }
    }
}

- (BOOL)needReloadBizData:(NSArray<XYLiveRightActivityAreaModel *> *)modelArray {
    if (self.taskArray.count != modelArray.count) {
        return YES;
    }
    
    for (NSUInteger index = 0; index < modelArray.count; index++) {
        if (index >= self.taskArray.count) {
            return YES;
        }
        
        // 比对新旧数据，判断是否需要全量刷新
        XYLiveRightActivityAreaModel *oldModel = self.taskArray[index];
        XYLiveRightActivityAreaModel *newModel = modelArray[index];
        
        XYLivePushVariousTaskDetailModel *oldTaskModel = SAFE_CAST_CLASS(oldModel.bizDataModel, XYLivePushVariousTaskDetailModel);
        XYLivePushVariousTaskDetailModel *newTaskModel = SAFE_CAST_CLASS(newModel.bizDataModel, XYLivePushVariousTaskDetailModel);
        if ([self needReloadCurrentTask:oldTaskModel updateModel:newTaskModel]) {
            return YES;
        }
    }
    
    return NO;
}

- (BOOL)needReloadCurrentTask:(XYLivePushVariousTaskDetailModel *)curTask updateModel:(XYLivePushVariousTaskDetailModel *)updateTask {
    // 大任务的bizID不同，需要更新
    if (updateTask.bizID.length > 0 && ![curTask.bizID isEqualToString:updateTask.bizID]) {
        return YES;
    }
    
    // 子任务数量不同，需要更新
    if (updateTask.subTaskArr.count != curTask.subTaskArr.count) {
        return YES;
    }
    
    for (NSUInteger subIdx = 0; subIdx < curTask.subTaskArr.count; subIdx++) {
        if (subIdx >= updateTask.subTaskArr.count) {
            return YES;
        }
        
        XYLivePushVariousSubTaskDetailModel *subTask = curTask.subTaskArr[subIdx];
        XYLivePushVariousSubTaskDetailModel *updatedSubTask = updateTask.subTaskArr[subIdx];
        if (![subTask.subID isEqualToString:updatedSubTask.subID]) {
            return YES;
        }
        
        // 更新全部信息
        subTask.bizID = curTask.bizID;
        subTask.bizName = curTask.bizName;
        subTask.subName = updatedSubTask.subName;
        subTask.curProgress = updatedSubTask.curProgress;
        subTask.curProgressStr  = updatedSubTask.curProgressStr;
        subTask.targetProgress = updatedSubTask.targetProgress;
        subTask.targetProgressStr = updatedSubTask.targetProgressStr;
        subTask.jumpURL = updatedSubTask.jumpURL;
        [subTask resetProgressTxt];
    }
    
    return NO;
}

// 定位到已完成的子任务
- (void)handleFinishedSubTask:(XYLivePushVariousSubTaskFinish * _Nullable)model {
    AlphaLog(AlphaLogTag.Banner, [NSString stringWithFormat:@"v2 host receive finish task %@ - %@", model.bizID, model.subID]);
    if (model.bizID.length == 0 || model.subID.length == 0) {
        return;
    }
    // 1.寻找子任务下标
    __block NSInteger bizIdx = -999;
    __block NSInteger subBizIdx = -999;
    
    [self.taskArray enumerateObjectsUsingBlock:^(XYLiveRightActivityAreaModel * _Nonnull bizModel, NSUInteger idx, BOOL * _Nonnull stop) {
        XYLivePushVariousTaskDetailModel *taskModel = SAFE_CAST_CLASS(bizModel.bizDataModel, XYLivePushVariousTaskDetailModel);
        if ([taskModel.bizID isEqualToString:model.bizID]) {
            __block BOOL exist = NO;
            [taskModel.subTaskArr enumerateObjectsUsingBlock:^(XYLivePushVariousSubTaskDetailModel * _Nonnull subTask , NSUInteger subIdx, BOOL * _Nonnull subStop) {
                if ([subTask.subID isEqualToString:model.subID]) {
                    subTask.curProgress = subTask.targetProgress;
                    bizIdx = idx;
                    subBizIdx = subIdx;
                    exist = YES;
                    *subStop = YES;
                }
            }];
            if (exist) {
                *stop = YES;
            }
        }
    }];
    AlphaLog(AlphaLogTag.Banner, [NSString stringWithFormat:@"v2 host receive finish task index %@ - %@", @(bizIdx), @(subBizIdx)]);
    // 2.触发锚定逻辑
    if (bizIdx != -999 && subBizIdx != -999) {
        // 2.1 找到主任务视图
        XYLiveCommonLoopListView *target = SAFE_CAST_CLASS(self.containerLoopView.viewList[bizIdx], XYLiveCommonLoopListView);
        // 2.2 记录
        self.targetView = target;
        // 2.3 触发子任务滚动
        [self.targetView handleManualScrollWithIndex:subBizIdx animated:NO];
        // 2.4 触发主任务滚动
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.containerLoopView handleManualScrollWithIndex:bizIdx animated:NO];
            // 2.5 触发容器滚动
            self.targeting = YES;
            dispatch_async(dispatch_get_main_queue(), ^{
                [[self.depend bannerService] scrollToBiz:XYLiveLoopBannerBusinessTypeHostTask atIndex:0];
            });
        });
    }
}

- (void)openSubTaskWithDetail:(XYLivePushVariousSubTaskDetailModel * _Nullable)model {
    if (model.jumpURL.length > 0) {
        XYExecuteOnMainQueue(^{
            // 拼接参数
            NSURL *linkURL = [NSURL URLWithString:model.jumpURL];
            NSDictionary *queryDic = [NSURL URLQueryParameters:linkURL];
            if (!queryDic[@"taskId"]) {
                linkURL = [linkURL URLByAppendingQueryParameters:@{@"taskId" : model.bizID ?: @""}];
            }
            // 打开半层
            [XYLiveManagerSharedInstance.actionRouteManager performWith:model.jumpURL];
        });
    } else {
        AlphaLog(AlphaLogTag.Banner, @"host click, but task null");
    }
}

- (void)trackSubTaskWithDetail:(XYLivePushVariousSubTaskDetailModel * _Nullable)model {
    if (model.subID.length == 0) {
        return;
    }
    if ([self.subImpressedPool containsObject:model.subID]) {
        return;
    }
    
    NSMutableSet<NSString *> *st = [self.subImpressedPool mutableCopy];
    [st addObject:model.subID];
    self.subImpressedPool = st;
    
    [XYLivePushVariousTaskTracker trackID67662ForTaskImpressionWithDetail:model];
}

#pragma mark - Factory

- (XYLiveCommonLoopListView *)makeItemLoopView {
    XYLiveCommonLoopListView *loopView = [[XYLiveCommonLoopListView alloc] initWithVerticalScrolling:YES];
    loopView.loopInterval = [XYLivePushVariousTaskConfigCenter subTaskLoopInterval];
    loopView.isHiddenPageControl = NO;
    
    __weak typeof(loopView) wView = loopView;
    __weak typeof(self) wself = self;
    
    // 每个主任务下的子任务完成一轮循环，当存在不止一个主任务时：
    // 1.暂停子任务（垂直）滚动
    // 2.触发主任务（水平）滚动
    loopView.loopCompletedCallback = ^{
        if (wself.containerLoopView.viewList.count > 1) {
            [wView stopLoop];
            [wself.containerLoopView startLoop];
        }
    };
    
    loopView.viewWillDisplayCallback = ^(UIView * _Nonnull _, id _Nullable model, NSInteger __) {
        XYLivePushVariousSubTaskDetailModel *task = SAFE_CAST_CLASS(model, XYLivePushVariousSubTaskDetailModel);
        [wself trackSubTaskWithDetail:task];
    };
    
    loopView.viewClickedCallback = ^(UIView * _Nonnull _, id _Nullable model, NSInteger __) {
        XYLivePushVariousSubTaskDetailModel *task = SAFE_CAST_CLASS(model, XYLivePushVariousSubTaskDetailModel);
        [wself openSubTaskWithDetail:task];
    };
    
    [loopView xyAutoTrack_registerDidSelectTrackerBlock:^XYTrackerEventContext * _Nullable(NSIndexPath * _Nonnull indexPath) {
        if (indexPath.item >= 0 && indexPath.item < wView.viewList.count) {
            XYLivePushVariousTaskItemView *itemView = SAFE_CAST_CLASS(wView.viewList[indexPath.item], XYLivePushVariousTaskItemView);
            return [XYLivePushVariousTaskTracker trackID67663ForTaskClickedWithDetail:itemView.taskModel];
        }
        return nil;
    }];
    
    return loopView;
}

#pragma mark - Getters

- (id<XYLiveCommonLoopBannerProtocol>)bannerService {
    return [self.depend bannerService];
}

- (XYLiveCommonLoopListView *)containerLoopView {
    if (!_containerLoopView) {
        _containerLoopView = [XYLiveCommonLoopListView new];
        _containerLoopView.loopInterval = [XYLivePushVariousTaskConfigCenter taskLoopInterval];
        __weak typeof(self) wself = self;
        
        _containerLoopView.loopCompletedCallback = ^{// 完成一轮循环后，暂停自己轮播，触发容器轮播
            if (wself.bannerService.elementCount > 1) {
                [wself.containerLoopView stopLoop];
                [wself.bannerService startLoop];
            }
        };
        
        // 每个主任务曝光，如果有多个子任务：
        // 0.其偏移量置为0（每次露出时从头开始轮播）
        // 1.暂停主任务（水平）滚动
        // 2.触发子任务（垂直）滚动
        _containerLoopView.viewWillDisplayCallback = ^(UIView * _Nonnull view, id _Nullable model, NSInteger __) {
            XYLiveActivityBannerModel *bannerInfo = SAFE_CAST_CLASS(model, XYLiveActivityBannerModel);            
            XYLiveCommonLoopListView *lView = SAFE_CAST_CLASS(view, XYLiveCommonLoopListView);
            if (lView.viewList.count > 1) {
                if (lView == wself.targetView) {
                    wself.targetView = nil;
                } else {
                    [lView handleManualScrollWithIndex:0 animated:NO];
                }
                
                [wself.containerLoopView stopLoop];
                [lView startLoop];
            } else {
                [lView reloadListView];
            }
        };
    }
    return _containerLoopView;
}

- (NSSet<NSString *> *)subImpressedPool {
    if (!_subImpressedPool) {
        _subImpressedPool = [NSSet set];
    }
    return _subImpressedPool;
}

@end
