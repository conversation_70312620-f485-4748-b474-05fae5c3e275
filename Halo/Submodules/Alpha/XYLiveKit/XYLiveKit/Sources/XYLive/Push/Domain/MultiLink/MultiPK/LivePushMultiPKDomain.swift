//
//  LivePushMultiPKDomain.swift
//  XYLiveKit
//
//  Created by 大远 on 2025/4/24.
//  Copyright © 2025 XingIn. All rights reserved.
//

import Foundation
import XYAlphaFusion
import XYLiveServiceProtocol

@objcMembers
class LivePushMultiPKDomain: LiveBaseDomain<LivePushMultiPKDependProtocol>, LiveRoomInfoServiceProtocol {
    
    override class func enableDomain() -> Bool {
        return XYAlphaSwitch.enableNewMultiLinkFramework()
    }
    
    // 中间层，用于缓存数据和事件
    private lazy var serviceImpl: XYLivePushMultiPKService = {
        let service = XYLivePushMultiPKService()
        return service
    }()
    
    // 注册服务
    override func doRegisterService(registery: ServiceRegistery) {
        registery.registerService(protocol: XYLivePushMultiPKServiceProtocol.self, service: self.serviceImpl)
    }
    
    // 声明依赖服务
    override func doInjectService(provider: ServiceProvider) -> LivePushMultiPKDependProtocol {
        return LivePushMultiPKDepend(provider)
    }
    
    override func didLoad() {
        super.didLoad()
        // 注册监听
        self.depend?.liveInfoService()?.registerRoomInfoListener(self)
    }
    
    func onRoomInfoDidUpdate(_ oldRoomInfo: XYLiveRoomInfo?, newRoomInfo: XYLiveRoomInfo?) {
        // 合法性校验
        guard let depend = self.depend, let roomInfo = newRoomInfo else { return }
        // 畅聊和游戏直播间不加载
        if roomInfo.isScreenLive || roomInfo.isLiveChat { return }
        // 初始化容器vc
        let vc = XYLivePushMultiPKController(containerVC: self.hostViewController(), depend: depend)
        add(vc)
        // 绑定服务
        serviceImpl.bindTarget(vc)
    }
}

