//
//  XYLivePushRoomViewController+Dismiss.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/1/18.
//  Copyright © 2023 XingIn. All rights reserved.
//

#import "XYLivePushRoomViewController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLivePushRoomVC+UserCardInternal.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLivePushWebNode.h"
#import "XYLivePlayRoomEventService.h"

@implementation XYLivePushRoomViewController (Dismiss)

- (void)notifyAllDismissCardView {
    XYLivePlayRoomEventService *eventService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePlayRoomEventServiceProtocol)];
    [eventService.asXYLivePlayRoomEventServiceProtocol eventDismissAllCardView];
}

- (void)dismissWithoutStopServer {
    // flag
    self.decorateVM.dismissWithoutCallServer = YES;
    // stop gift
    [self.decorateView terminateGiftRendering];
    // kvo
    self.decorateVM.notifyVCToStopPusher = YES;
    self.decorateVM.notifyViewToUpdateStopUI = YES;
    self.decorateVM.notifyVCToDismiss = YES;
}

- (void)mutiLandDismissWithoutStopServer {
    // flag
    self.decorateVM.dismissWithoutCallServer = YES;
    // stop gift
    [self.decorateView terminateGiftRendering];
    // kvo
    self.decorateVM.notifyViewToUpdateStopUI = YES;
    self.decorateVM.notifyVCToDismiss = YES;
}

@end


