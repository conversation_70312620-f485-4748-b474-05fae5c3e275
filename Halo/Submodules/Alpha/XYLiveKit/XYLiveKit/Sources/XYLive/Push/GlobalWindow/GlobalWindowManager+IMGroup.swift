//
//  GlobalWindowManager+IMGroup.swift
//  XYLiveKit
//
//  Created by 王帅 on 2023/5/25.
//  Copyright © 2023 XingIn. All rights reserved.
//

import Foundation

extension GlobalWindowManager: XYIMMessageParserDelegate {
    // 群组解散
    public func msgParser(_ parser: XYIMMessageParser, didRecvGroupDeletedEvent groupID: String?) {
        collectLog(content: "group dismiss \(String(describing: groupID))")
        groupDeletedEvent?(groupID ?? "")
        closeRoomAndDismiss()
    }
    
    public func closeRoomAndDismiss() {
        switch self.role {
        case .audience:
            onMicAudienceClose(selfOp: false)
        case .host:
            hostClose()
        case .undefined:
            break
        @unknown default:
            break
        }
    }
}
