//
//  XYLiveMultiLineInviteMoreVM.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/3.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineInviteMoreVM.h"
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYAlphaNetwork/XYAlphaNetwork-Swift.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYLiveKit/XYLiveLinkHostInviteListModel.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineConsts.h>
#import <XYAPIRoute/XYAPIRoute.h>

@interface XYLiveMultiLineInviteMoreVM()

@property (nonatomic, weak) id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak) id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, copy) NSArray *dataSource;

@end

@implementation XYLiveMultiLineInviteMoreVM

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkSevice {
    if (self = [super init]) {
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkSevice;
    }
    return self;
}

// 请求列表数据
- (void)requestListDataWithCompletion:(void(^)(NSError *error))completion {
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/line/v2/invite_host_list" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMGET;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"app_id"] = @(1);
    params[@"room_id"] = self.liveInfoService.roomId;
    params[@"type"] = @"linkmic";
    [api addingQueryItems:params.copy];
    [XYLogCollector xyLiveLogTag:kXYLiveMultiLineModuleName content:[NSString stringWithFormat:@"start request invite more host list api,{roomId:%@}", self.liveInfoService.roomId]];
    WS
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        [XYLogCollector xyLiveLogTag:kXYLiveMultiLineModuleName content:[NSString stringWithFormat:@"request invite more host list api reposne,{error:%@}", response.error]];
        SS
        // 数据源
        NSMutableArray *dataSourceM = [NSMutableArray array];
        // 处理成功回调
        if (response.error == nil) {
            // 数据转模型
            XYLiveLinkHostInviteListModel *listModel = [XYLiveLinkHostInviteListModel xy_modelWithDictionary:response.result.value];
            // 配置数据源
            [self configureDataSourceWithListModel:listModel];
        } else {
            XYLiveLinkHostInviteListEmptyItem *lisItem = [[XYLiveLinkHostInviteListEmptyItem alloc] init];
            lisItem.showErrorView = YES;
            self.dataSource = @[lisItem];
        }
        completion ? completion(response.error) : nil;
    }];
}

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    // 更新状态
    XYLiveLinkHostInviteeInfo *listItem = [self.dataSource xy_match:^BOOL(id  _Nonnull obj) {
        XYLiveLinkHostInviteeInfo *item = XYSAFE_CAST(obj, XYLiveLinkHostInviteeInfo);
        return [item.userId isEqualToString:inviteeInfo.userId];
    }];
    listItem.isInvited = (state != XYLiveMultiLinkInviteStateIdle);
}

#pragma mark - Private

// 配置数据源
- (void)configureDataSourceWithListModel:(XYLiveLinkHostInviteListModel *)listModel {
    NSMutableArray *dataSourceM = [NSMutableArray array];
    // 判断是否有好友列表
    if (listModel.friendListItems.count) {
        XYLiveLinkHostInviteListTextItem *textItem = [[XYLiveLinkHostInviteListTextItem alloc] init];
        textItem.title = [NSString stringWithFormat:@"在线好友（%@）", @(listModel.friendListItems.count)];
        [dataSourceM addObject:textItem];
        // 添加好友列表
        [dataSourceM addObjectsFromArray:[self buildInviteeListWithListItems:listModel.friendListItems]];
    }
    // 判断是否有推荐列表
    if (listModel.recommendListItems.count) {
        XYLiveLinkHostInviteListTextItem *textItem = [[XYLiveLinkHostInviteListTextItem alloc] init];
        textItem.title = @"推荐主播";
        [dataSourceM addObject:textItem];
        // 添加好友列表
        [dataSourceM addObjectsFromArray:[self buildInviteeListWithListItems:listModel.recommendListItems]];
    }
    // 兜底空占位视图
    if (dataSourceM.count == 0) {
        XYLiveLinkHostInviteListEmptyItem *lisItem = [[XYLiveLinkHostInviteListEmptyItem alloc] init];
        lisItem.showErrorView = NO;
        [dataSourceM addObject:lisItem];
    }
    self.dataSource = dataSourceM.copy;
}

// 构建好友列表
- (NSArray<XYLiveLinkHostInviteeInfo *> *)buildInviteeListWithListItems:(NSArray<XYLiveLinkHostInviteeInfo *> *)listItems {
    NSMutableArray *listItemsM = [NSMutableArray array];
    for (XYLiveLinkHostInviteeInfo *listItem in listItems) {
        // 更新邀请状态
        listItem.isInvited = [self.multiLinkService isInvitingWithBizType:XYLiveMultiLinkBizTypeLine targetUserId:listItem.userId];
        [listItemsM addObject:listItem];
    }
    return listItemsM.copy;
}

@end
