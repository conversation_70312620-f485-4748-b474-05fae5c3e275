//
//  XYLivePushRoomViewController+PK.m
//  XYPostKit
//
//  Created by 周博立 on 2020/3/16.
//  Copyright © 2020 XingIn. All rights reserved.
//

@import XYTracker;
@import KVOController;
@import XYLiveFoundation;
@import XYLiveUIKit;
@import XYLivePusher;
@import XYDevice;
@import XYAnalytics;
@import XYLiveCore;

#import "XYLivePushRoomVC+PKInternal.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLivePKManager.h"
#import "XYLiveManager.h"
#import "XYLivePushDecorateViewModel.h"
#import "XYTrackLiveBroadcastPage.h"
#import "XYLivePushRoomViewController.h"
#import "XYLivePushUserCardService.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLiveKit/XYLiveMultiLinkSwitchConfig.h>

@implementation XYLivePushRoomViewController (PK)

- (void)setupPKVCBlock {
    __weak typeof(self) wself = self;
    self.pkVC.invitePKSwitchRTCBlock = ^(XYLivePushType targetType) {
        RTCVendorType type = RTCVendorTypeTRTC;
        if (targetType == XYLivePushTypeKasa) {
            type = RTCVendorTypeKasa;
        }
        [wself switchPusherToRtcCoreIfNeeded:type];
    };
    
    self.pkVC.didInvitePKBlock = ^(XYLiveUserInfo * _Nonnull userInfo) {
        //这里从互动页面开启PK 需要调用before_Client_Link
        NSString *roomId = XYLiveManager.sharedManager.roomInfo.roomIDStr;
        NSString *otherRoomId = userInfo.roomIDStr;
        NSString *userId = XYLiveManager.sharedManager.userInfo.userID;
        XYLiveRoomPushContentType type = XYLiveManager.sharedManager.roomInfo.contentType;
        [MultiLinkmicCommService getPushConfigV2WithContentType: type otherRoomId:otherRoomId userId:XYLiveManager.sharedManager.userInfo.userID completion:^(XYLivePushDispatchInfoEncode * encode, NSError * error) {
            int destPushType = (int)encode.pushType;
            NSLog(@"didInvitePKBlock encode PushType: %d", destPushType);
            RTCVendorType type = RTCVendorTypeTRTC;
            if (destPushType == 3) {
                type = RTCVendorTypeKasa;
            }
            [wself switchPusherToRtcCoreIfNeeded: type];
        }];
    };
    self.pkVC.didRandomPKBlock = ^{
    };
    self.pkVC.didSelectItemBlock = ^(XYLiveUserInfo * _Nonnull userInfo) {
        id<XYLivePushUserCardServiceProtocol> service = [wself.serviceManager getServiceWithProtocol:@protocol(XYLivePushUserCardServiceProtocol)];
        [service handleFetchingUserInfoAndPresentingWithUser:userInfo otherRole:XYLiveOtherRoleHostLiving sourceType:XYLiveInvokeSourceTypeDefault];
    };
}

- (void)pk_handleRandomResponse:(IMRandomLinkmicResponse *)response {
    [self.pkVC pk_handleRandomResponse:response];
}

@end

