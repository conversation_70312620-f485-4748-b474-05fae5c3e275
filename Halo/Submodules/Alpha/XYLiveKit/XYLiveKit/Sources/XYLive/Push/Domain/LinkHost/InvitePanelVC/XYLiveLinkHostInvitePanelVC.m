//
//  XYLiveLinkHostInvitePanelVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/27.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostInvitePanelVC.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveKit/XYLiveLinkHostInviteListHeaderView.h>
#import <XYLiveKit/XYLiveLinkHostInviteListSectionView.h>
#import <XYLiveKit/XYLiveLinkHostInviteListCell.h>
#import <XYUIKitCore/UIView+XYUIKCPointInside.h>
#import <XYLiveKit/XYLiveLinkHostInviteListVM.h>
#import <XYUITheme/XYThemeManager.h>
#import <XYLiveKit/XYLiveLinkHostBizTracker.h>
#import <XYLiveKit/XYLiveManager.h>
#import <Masonry/Masonry.h>

@interface XYLiveLinkHostInvitePanelVC()

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, strong) UIButton *searchItemBtn;
@property (nonatomic, strong) UIButton *settingItemBtn;
@property (nonatomic, strong) UILabel *titleView;
@property (nonatomic, strong) XYLiveLinkHostInviteListSectionView *sectionView;
@property (nonatomic, strong) XYLiveLinkHostInviteListVM *viewModel;

@end

@implementation XYLiveLinkHostInvitePanelVC

// 初始化
- (instancetype)initWithContianerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
    }
    return self;
}

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo isPK:(BOOL)isPK {
    // 移除loading提示
    [self hideLoading];
    // 更新数据源
    [self.viewModel updateInviteState:state inviteeInfo:inviteeInfo isPK:isPK];
    // 刷新列表
    [self.tableView reloadData];
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // 埋点上报
    [XYLiveLinkHostBizTracker eventActionId80528WithRoomId:self.liveInfoService.roomId hasGoods:self.liveInfoService.roomInfo.hasGoods];
}

#pragma mark - Override

- (void)setupSubViews {
    [super setupSubViews];
    self.containerView.backgroundColor = [XYLiveTokenColor bg0];
    self.contentView.backgroundColor = [XYLiveTokenColor bg0];
    // 创建顶部随机匹配视图
    [self setupTableHeaderView];
    
    // 注册cell
    [self.tableView registerClass:XYLiveLinkHostInviteListTextCell.class forCellReuseIdentifier:kXYLiveLinkHostInviteListTextCellIde];
    [self.tableView registerClass:XYLiveLinkHostInviteListEmptyCell.class forCellReuseIdentifier:kXYLiveLinkHostInviteListEmptyCellIde];
    [self.tableView registerClass:XYLiveLinkHostInviteListItemCell.class forCellReuseIdentifier:kXYLiveLinkHostInviteListItemCellIde];
}

- (void)refreshWithType:(XYLiveRefreshDataType)type completion:(void (^)(NSError * _Nonnull))completion {
    [self loadDataWithIsPK:self.viewModel.isPK isLoading:NO completion:completion];
}

- (BOOL)hasData {
    return self.viewModel.dataSource.count;
}

- (UIView *)errorResponseView:(NSError *)error {
    return nil;
}

#pragma mark - 创建子视图

// 创建匹配视图
- (void)setupTableHeaderView {
    XYLiveLinkHostInviteListHeaderView *headerView = [[XYLiveLinkHostInviteListHeaderView alloc] initWithFrame:CGRectMake(0, 0, self.contentView.xy_width, kXYLiveLinkHostInviteListHeaderH)];
    WS
    headerView.didTapMatchHandler = ^(BOOL isPK) {
        SS
        // 埋点上报
        if (isPK) {
            [XYLiveLinkHostBizTracker eventActionId80509WithRoomId:self.liveInfoService.roomId hasGoods:self.liveInfoService.roomInfo.hasGoods];
        } else {
            [XYLiveLinkHostBizTracker eventActionId80508WithRoomId:self.liveInfoService.roomId hasGoods:self.liveInfoService.roomInfo.hasGoods];
        }
        // 执行事件回调
        self.didTapMatchHandler ? self.didTapMatchHandler(isPK) : nil;
        // 关闭当前面板
        [self.containerVC dismissPopBottomVC:self.navigationChildController dismissAnimated:YES dismissComplete:nil];
    };
    self.tableView.tableHeaderView = headerView;
    [self.tableView layoutIfNeeded];
}

- (void)changeUIInterfaceWithIsDark:(BOOL)isDark {
    [self setupTableHeaderView];
}

#pragma mark - XYLiveNavigationChildControllerProtocol

- (NSString *)XYLiveNav_title {
    return @"主播连线";
}

- (NSArray<UIButton *> *)XYLiveNav_rightButtons {
    return @[self.settingItemBtn, self.searchItemBtn];
}

- (CGFloat)heightWhenPortrait {
    return kXYLiveLinkHostInvitePanelH;
}

- (BOOL)needReachBottom {
    return YES;
}

- (UIColor *)XYLiveNav_headerBGColor {
    return [XYLiveTokenColor bg0];
}

- (BOOL)isRoundedTopCorners {
    return NO;
}

#pragma mark - UITableViewDelegate / UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.viewModel.dataSource.count ? 1 : 0;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.viewModel.dataSource.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    id listItem = self.viewModel.dataSource[indexPath.row];
    if ([listItem isKindOfClass:XYLiveLinkHostInviteListTextItem.class]) {
        return indexPath.row == 0 ? kXYLiveLinkHostListTextCellH : kXYLiveLinkHostListTextSCellH;
    } else if ([listItem isKindOfClass:XYLiveLinkHostInviteListEmptyItem.class]) {
        return kXYLiveLinkHostInvitePanelH - kXYLiveLinkHostInvitePanelNavBarH - kXYLiveLinkHostInviteListHeaderH - kXYLiveLinkHostInviteListSectionH;
    }
    return kXYLiveLinkHostListItemCellH;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    id listItem = self.viewModel.dataSource[indexPath.row];
    XYLiveLinkHostInviteListCell *cell = nil;
    // 纯文本
    if ([listItem isKindOfClass:[XYLiveLinkHostInviteListTextItem class]]) {
        cell = [tableView dequeueReusableCellWithIdentifier:kXYLiveLinkHostInviteListTextCellIde forIndexPath:indexPath];
    } else if ([listItem isKindOfClass:XYLiveLinkHostInviteListEmptyItem.class]) {
        cell = [tableView dequeueReusableCellWithIdentifier:kXYLiveLinkHostInviteListEmptyCellIde forIndexPath:indexPath];
    } else {
        cell = [tableView dequeueReusableCellWithIdentifier:kXYLiveLinkHostInviteListItemCellIde forIndexPath:indexPath];
        XYLiveLinkHostInviteListItemCell *inviteCell = XYSAFE_CAST(cell, XYLiveLinkHostInviteListItemCell);
        WS
        inviteCell.didTapInviteHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull inviteeInfo, NSIndexPath * _Nonnull indexPath) {
            SS
            if (!inviteeInfo.isInvited) {
                // 埋点上报
                [XYLiveLinkHostBizTracker eventActionId80511WithRoomId:self.liveInfoService.roomId channelTabName:inviteeInfo.isPK ? @"PK" : @"connect" index:indexPath.row inviteeUserId:inviteeInfo.userId hasGoods:self.liveInfoService.roomInfo.hasGoods];
            }
            // 展示loading
            [self showLoading];
            // 发起邀请
            self.didTapInviteHandler ? self.didTapInviteHandler(inviteeInfo) : nil;
        };
    }
    // 配置数据源
    [cell bindListItems:self.viewModel.dataSource indexPath:indexPath];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return kXYLiveLinkHostInviteListSectionH;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (_sectionView == nil) {
        _sectionView = [[XYLiveLinkHostInviteListSectionView alloc] init];
        _sectionView.backgroundColor = [XYLiveTokenColor bg];
        _sectionView.checked = self.viewModel.isPK;
        _sectionView.layer.masksToBounds = YES;
        WS
        _sectionView.didTapHandler = ^(BOOL isChecked) {
            SS
            // 埋点上报
            [XYLiveLinkHostBizTracker eventActionId80510WithRoomId:self.liveInfoService.roomId hasGoods:self.liveInfoService.roomInfo.hasGoods];
            // 数据刷新
            [self loadDataWithIsPK:isChecked isLoading:YES completion:nil];
        };
    }
    return _sectionView;
}

- (void)tableView:(UITableView *)tableView willDisplayHeaderView:(UIView *)view forSection:(NSInteger)section {
    // 设置顶部圆角
    [view live_updateMaskWithRoundedRect:view.bounds topLeftRadius:12 topRightRadius:12 bottomLeftRadius:0 bottomRightRadius:0];
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    XYLiveLinkHostInviteeInfo *inviteeInfo = XYSAFE_CAST(self.viewModel.dataSource[indexPath.row], XYLiveLinkHostInviteeInfo);
    // 合法性校验
    if (!inviteeInfo) { return; }
    // 执行事件回调
    self.didTapUserHandler ? self.didTapUserHandler(inviteeInfo) : nil;
}

- (void)scrollViewDidScroll:(UIScrollView *)scrollView {
    // 合法性校验
    if (scrollView.contentOffset.y < 0) { return; }
    // 校验是否超出限制高度
    if (scrollView.contentOffset.y >= kXYLiveLinkHostInviteListHeaderH) {
        self.navigationChildController.headerView.backgroundColor = [XYLiveTokenColor bg];
        self.sectionView.topRadius = 0;
    } else {
        self.navigationChildController.headerView.backgroundColor = [XYLiveTokenColor bg0];
        self.sectionView.topRadius = 12;
    }
}

#pragma mark - Event

- (void)didTapSearch:(UIButton *)sender {
    // 埋点上报
    [XYLiveLinkHostBizTracker eventActionId80512WithRoomId:self.liveInfoService.roomId hasGoods:self.liveInfoService.roomInfo.hasGoods];
    // 执行事件回调
    self.didTapSearchHandler ? self.didTapSearchHandler() : nil;
}

- (void)didTapSetting:(UIButton *)sender {
    // 埋点上报
    [XYLiveLinkHostBizTracker eventActionId80513WithRoomId:self.liveInfoService.roomId hasGoods:self.liveInfoService.roomInfo.hasGoods];
    // 执行事件回调
    self.didTapSettingHandler ? self.didTapSettingHandler() : nil;
}

#pragma mark - Private

// 刷新数据
- (void)loadDataWithIsPK:(BOOL)isPK isLoading:(BOOL)isLoading completion:(void(^_Nullable)(NSError *error))completion {
    // 判断是否展示Loading视图
    isLoading ? [self showLoading] : nil;
    WS
    [self.viewModel requestListDataWithIsPK:isPK completion:^(NSError * _Nonnull error) {
        SS
        // 移除loading状态
        [self hideLoading];
        // 刷新列表
        [self.tableView reloadData];
        // 执行回调
        completion ? completion(error) : nil;
    }];
}

#pragma mark - Lazy

- (UIButton *)searchItemBtn {
    if (_searchItemBtn == nil) {
        _searchItemBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_searchItemBtn setImage:Theme.icon.search.size(24).color(XYLiveTokenColor.title).image forState:UIControlStateNormal];
        _searchItemBtn.xy_responseRectInset = UIEdgeInsetsMake(-5, -5, -5, -5);
        [_searchItemBtn addTarget:self action:@selector(didTapSearch:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _searchItemBtn;
}

- (UIButton *)settingItemBtn {
    if (_settingItemBtn == nil) {
        _settingItemBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [_settingItemBtn setImage:[UIImage xy_liveKitBundleImage:@"link_setting"] forState:UIControlStateNormal];
        _settingItemBtn.xy_responseRectInset = UIEdgeInsetsMake(-5, -5, -5, -5);
        [_settingItemBtn addTarget:self action:@selector(didTapSetting:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _settingItemBtn;
}

- (XYLiveLinkHostInviteListVM *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[XYLiveLinkHostInviteListVM alloc] initWithLiveInfoService:self.liveInfoService multiLinkService:self.multiLinkService];
    }
    return _viewModel;
}

@end
