//
//  XYLivePushMultiLineBizTracker.h
//  XYLiveKit
//
//  Created by 大远 on 2025/6/30.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYAnalytics/XYAnalytics.h>
@class XYTrackerEventContext;

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiLineBizTracker : NSObject

/* 直播开播页/连线中-邀请更多/点击 */
+ (void)eventActionId80514WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods;

/* 直播开播页/连线中-结束邀请/点击 */
+ (void)eventActionId80515WithRoomId:(NSString *)roomId inviteeUserId:(NSString *)inviteeUserId hasGoods:(BOOL)hasGoods;

/* 直播开播页/连线中-头像/点击 */
+ (void)eventActionId80516WithRoomId:(NSString *)roomId inviteeUserId:(NSString *)inviteeUserId hasGoods:(BOOL)hasGoods;

/* 直播开播页/连线中-结束/点击 */
+ (void)eventActionId80517WithRoomId:(NSString *)roomId channelTabName:(NSString *)channelTabName hasGoods:(BOOL)hasGoods;

/* 直播开播页/连线中-玩法操作/点击 */
+ (void)eventActionId80518WithRoomId:(NSString *)roomId channelTabName:(NSString *)channelTabName channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods;

/* 直播开播页/匹配中-放弃匹配/点击 */
+ (void)eventActionId80519WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods;

/* 直播开播页/邀请连线-拒绝/点击 */
+ (void)eventActionId80520WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods;

/* 直播开播页/邀请连线-接受/点击 */
+ (void)eventActionId80521WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods;

/* 直播开播页/邀请连线-多人连线邀请的更多/点击 */
+ (void)eventActionId80522WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods;

/* 直播开播页/邀请连线面板/曝光 */
+ (void)eventActionId80529WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods;

@end

NS_ASSUME_NONNULL_END
