//
//  XYLiveMultiLineOptFuncFriendItemView.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/22.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <XYLiveKit/XYLiveMultiLineOptFuncListItem.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiLineFuncFriendItemSubView : UIView

@property (nonatomic, strong) UIView *borderView;
@property (nonatomic, strong) UIImageView *avatarImgView;
@property (nonatomic, strong) UILabel *nickNameLbl;
@property (nonatomic, strong) UILabel *invitedMaskView;
@property (nonatomic, strong) UIButton *cancelBtn;
// 点击取消
@property (nonatomic, copy)   void(^didTapCancelHandler)(void);

// 数据绑定
- (void)bindListItem:(XYLiveLinkHostInviteeInfo *)listItem;

@end

@interface XYLiveMultiLineOptFuncFriendItemView : UIView

// 点击事件回调
@property (nonatomic, copy) void(^didTapHandler)(XYLiveLinkHostInviteeInfo *listItem, NSIndexPath *indexPath);
// 点击取消事件回调
@property (nonatomic, copy) void(^didTapCancelHandler)(XYLiveLinkHostInviteeInfo *listItem, NSIndexPath *indexPath);

// 数据绑定
- (void)bindListItems:(NSArray<XYLiveLinkHostInviteeInfo *> *)listItems;

@end

NS_ASSUME_NONNULL_END
