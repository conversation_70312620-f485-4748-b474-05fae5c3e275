//
//  XYLiveLinkHostViewModel.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/27.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostViewModel.h"
#import <XYAlphaNetwork/XYAlphaNetwork-Swift.h>
#import <XYAPIRoute/XYAPIRoute.h>
#import <XYLiveKit/XYLiveLinkHostConfigModel.h>
#import <XYLiveKit/XYLiveLinkHostInviteeInfo.h>
@import XYConfigCenter;

@interface XYLiveLinkHostViewModel()

@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLivePushMultiLineServiceProtocol> multiLineService;
@property (nonatomic, weak)   id<XYLivePushMultiPKServiceProtocol> multiPKService;
@property (nonatomic, assign) XYLiveLinkHostState state;

@end

@implementation XYLiveLinkHostViewModel

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLineService:(id<XYLivePushMultiLineServiceProtocol>)multiLineService
                         multiPKService:(id<XYLivePushMultiPKServiceProtocol>)multiPKService {
    if (self = [super init]) {
        _liveInfoService = liveInfoService;
        _multiLineService = multiLineService;
        _multiPKService = multiPKService;
        // 加载配置
        [self loadInitConfigWithCompletion:nil];
    }
    return self;
}

- (BOOL)isMatching {
    return self.multiLineService.isMatching || self.multiPKService.isMatching;
}

- (BOOL)isLinking {
    return self.multiLineService.isLining || self.multiPKService.isPKing;
}

// 发起匹配
- (void)startMatchWithIsPK:(BOOL)isPK {
    if (isPK) {
        [self.multiPKService startMatchWithSource:XYLiveMultiPKMatchSourceBottomBar extraInfo:nil];
    } else {
        [self.multiLineService startMatchWithSource:XYLiveMultiLineMatchSourceBottomBar extraInfo:nil];
    }
}

// 展示匹配中面板
- (void)showMatchPanel {
    if (self.multiLineService.isMatching) {
        [self.multiLineService startMatchWithSource:XYLiveMultiLineMatchSourceBottomBar extraInfo:nil];
    } else {
        [self.multiPKService startMatchWithSource:XYLiveMultiPKMatchSourceBottomBar extraInfo:nil];
    }
}

// 取消匹配
- (void)cancelMatch {
    // 判断是否PK匹配中
    if (self.multiPKService.isMatching) {
        [self.multiPKService cancelMatch];
    } else {
        [self.multiLineService cancelMatch];
    }
}

// 发起邀请
- (void)inviteWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    if (inviteeInfo.isPK) {
        // 执行PK邀请操作
        [self.multiPKService inviteWithInviteeInfo:inviteeInfo source:XYLiveMultiPKInviteSourceBottomBar extraInfo:nil];
    } else {
        // 执行连线邀请操作
        [self.multiLineService inviteWithInviteeInfo:inviteeInfo source:XYLiveMultiLineInviteSourceBottomBar extraInfo:nil];
    }
}

// 取消邀请
- (void)cancelInviteWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    // 取消邀请
    if (inviteeInfo.isPK) {
        [self.multiPKService cancelInviteWithUserId:inviteeInfo.userId];
    } else {
        [self.multiLineService cancelInviteWithUserId:inviteeInfo.userId];
    }
}

// 展示操作面板
- (void)showOptFuncPanel {
    // 判断是否处于PK中
    if (self.multiPKService.isPKing) {
        [self.multiPKService showOptFuncPanelWithSource:XYLiveMultiPKOptFuncPanelSourceBottomBar];
    } else {
        [self.multiLineService showOptFuncPanelWithSource:XYLiveMultiLineOptFuncPanelSourceBottomBar];
    }
}

// 加载配置
- (void)loadInitConfigWithCompletion:(void(^_Nullable)(XYLiveLinkHostConfigModel *configModel, NSError *error))completion {
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/line/v2/config_panel/linking_host" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMGET;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"app_id"] = @(1);
    params[@"room_id"] = self.liveInfoService.roomId;
    [api addingQueryItems:params.copy];
    [XYLogCollector xyLiveLogTag:kXYLiveLinkHostModuleName content:[NSString stringWithFormat:@"start request init config api,{roomId:%@}", self.liveInfoService.roomId]];
    WS
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        SS
        [XYLogCollector xyLiveLogTag:kXYLiveLinkHostModuleName content:[NSString stringWithFormat:@"request init config api reposne,{error:%@}", response.error]];
        if (response.error == nil) {
            // 数据转模型
            XYLiveLinkHostConfigModel *configModel = [XYLiveLinkHostConfigModel xy_modelWithDictionary:response.result.value];
            // 更新缓存
            self.configModel = configModel;
            // 执行回调
            completion ? completion(configModel, nil) : nil;
        } else {
            // 执行回调
            completion ? completion(nil, response.error) : nil;
        }
        
    }];
}

// 更新连线配置
- (void)updateLineSwitch:(BOOL)isOpened layoutType:(XYLiveMultiLinkLayoutType)layoutType limitType:(XYLiveMultiLineLimitType)limitType completion:(void(^_Nullable)(NSError *error))completion {
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/line/v1/config_panel/linking_host/update" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMPOST;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"app_id"] = @(1);
    params[@"room_id"] = self.liveInfoService.roomId;
    params[@"linkmic_switch"] = @(isOpened);
    params[@"layout_type"] = @(layoutType);
    params[@"linkmic_invited_scope"] = @(limitType);
    [api addingQueryItems:params.copy];
    [XYLogCollector xyLiveLogTag:kXYLiveLinkHostModuleName content:[NSString stringWithFormat:@"start request update line config api,{roomId:%@, isOpened:%d, layoutType:%@, limitType:%@}", self.liveInfoService.roomId, isOpened, @(layoutType), @(limitType)]];
    WS
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        SS
        [XYLogCollector xyLiveLogTag:kXYLiveLinkHostModuleName content:[NSString stringWithFormat:@"request update line config api reposne,{error:%@}", response.error]];
        if (response.error == nil) {
            // 更新缓存
            self.configModel.lineSwitch = isOpened;
            self.configModel.layoutType = layoutType;
            self.configModel.lineLimitType = limitType;
        } else {
            // Toast提示
            [XYAlert live_showTextItemWithError:response.error];
        }
        completion ? completion(response.error) : nil;
    }];
}

// 更新PK配置
- (void)updatePKSwtich:(BOOL)isOpened limitType:(XYLiveMultiPKLimitType)limitType completion:(void(^_Nullable)(NSError *error))completion {
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/pk/v2/config_panel/update" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMPOST;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"app_id"] = @(1);
    params[@"room_id"] = self.liveInfoService.roomId;
    params[@"pk_switch"] = @(isOpened);
    params[@"pk_invited_scope"] = @(limitType);
    [api addingQueryItems:params.copy];
    [XYLogCollector xyLiveLogTag:kXYLiveLinkHostModuleName content:[NSString stringWithFormat:@"start request update pk config api,{roomId:%@, isOpened:%d, limitType:%@}", self.liveInfoService.roomId, isOpened, @(limitType)]];
    WS
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        SS
        [XYLogCollector xyLiveLogTag:kXYLiveLinkHostModuleName content:[NSString stringWithFormat:@"request update pk config api reposne,{error:%@}", response.error]];
        if (response.error == nil) {
            // 更新缓存
            self.configModel.pkSwitch = isOpened;
            self.configModel.pkLimitType = limitType;
        } else {
            // Toast提示
            [XYAlert live_showTextItemWithError:response.error];
        }
        completion ? completion(response.error) : nil;
    }];
}

#pragma mark - XYLivePushMultiLineListener

- (void)onLineUpdateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    [self updateInviteState:state inviteeInfo:inviteeInfo isPK:NO];
}

- (void)onLineUpdateMatchState:(XYLiveMultiLinkMatchState)state {
    // 更新状态
    XYLiveLinkHostState linkState = state == XYLiveMultiLinkMatchStateMatching ? XYLiveLinkHostStateMatch : XYLiveLinkHostStateIdle;
    [self updateState:linkState source:@"LINE"];
}

- (void)onLineUpdateState:(XYLiveMultiLineState)state {
    // 更新状态
    XYLiveLinkHostState linkState = state != XYLiveMultiLineStateIdle ? XYLiveLinkHostStateLine : XYLiveLinkHostStateIdle;
    [self updateState:linkState source:@"LINE"];
}

#pragma mark - XYLivePushMultiPKListener

- (void)onPKUpdateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    [self updateInviteState:state inviteeInfo:inviteeInfo isPK:YES];
}

- (void)onPKUpdateMatchState:(XYLiveMultiLinkMatchState)state {
    // 更新状态
    XYLiveLinkHostState linkState = state == XYLiveMultiLinkMatchStateMatching ? XYLiveLinkHostStateMatch : XYLiveLinkHostStateIdle;
    [self updateState:linkState source:@"PK"];
}

- (void)onPKUpdateState:(XYLiveMultiPKState)state {
    XYLiveLinkHostState linkState = XYLiveLinkHostStateIdle;
    // 更新状态
    if (state != XYLiveMultiPKStateIdle) {
        linkState = self.multiPKService.bizType == XYLiveMultiLinkBizTypePKGift ? XYLiveLinkHostStatePK : XYLiveLinkHostStateLikePK;
    }
    [self updateState:linkState source:@"PK"];
}

#pragma mark - Private

// 更新状态
- (void)updateState:(XYLiveLinkHostState)state source:(NSString *)source {
    // 合法性校验
    if (self.state == state) { return; }
    [XYLogCollector xyLiveLogTag:kXYLiveLinkHostModuleName content:[NSString stringWithFormat:@"update state,{state:%@, source:%@}", @(state), source]];
    // 更新缓存
    self.state = state;
    // 事件分发
    if ([self.delegate respondsToSelector:@selector(onLinkUpdateState:)]) {
        [self.delegate onLinkUpdateState:state];
    }
}

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo isPK:(BOOL)isPK {
    [XYLogCollector xyLiveLogTag:kXYLiveLinkHostModuleName content:[NSString stringWithFormat:@"update invite state,{state:%@, userId:%@, isPK:%d}", @(state), inviteeInfo.userId, isPK]];
    // 事件分发
    if ([self.delegate respondsToSelector:@selector(onLinkUpdateInviteState:inviteeInfo:isPK:)]) {
        [self.delegate onLinkUpdateInviteState:state inviteeInfo:inviteeInfo isPK:isPK];
    }
}

#pragma mark - Lazy

- (XYLiveLinkHostResource *)resource {
    if (_resource == nil) {
        NSDictionary *remoteResource =  XYConfigCenter().justOnceDictionaryForKey(@"ios_link_host_resource", @{});
        if (remoteResource.count) {
            _resource = [XYLiveLinkHostResource xy_modelWithDictionary:remoteResource];
        } else {
            _resource = [[XYLiveLinkHostResource alloc] init];
        }
    }
    return _resource;
}

@end
