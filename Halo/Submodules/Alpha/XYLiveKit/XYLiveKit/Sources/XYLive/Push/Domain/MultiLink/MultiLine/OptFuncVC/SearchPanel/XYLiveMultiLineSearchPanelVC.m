//
//  XYLiveMultiLineSearchPanelVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/3.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineSearchPanelVC.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveKit/XYLiveLinkHostSearchNavBar.h>
#import <XYLiveKit/XYLiveMutiLinkEmptyView.h>
#import <XYLiveKit/XYLiveLinkHostInviteListCell.h>
#import "XYLiveMultiLineSearchPanelConsts.h"
#import "XYLiveMultiLineSearchVM.h"
#import "XYLiveMultiLineSearchSectionView.h"
#import <Masonry/Masonry.h>

@interface XYLiveMultiLineSearchPanelVC()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, strong) XYLiveLinkHostSearchNavBar *searchBarView;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) XYLiveMutiLinkEmptyView *emptyView;
@property (nonatomic, strong) XYLiveMultiLineSearchSectionView *sectionView;
@property (nonatomic, strong) XYLiveMultiLineSearchVM *viewModel;

@end

@implementation XYLiveMultiLineSearchPanelVC

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
    }
    return self;
}

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    // 更新数据源
    [self.viewModel updateInviteState:state inviteeInfo:inviteeInfo];
    // 刷新列表
    [self.tableView reloadData];
}

#pragma mark - Override

- (void)setupSubViews {
    [super setupSubViews];
    self.containerView.backgroundColor = [XYLiveTokenColor bg];
    self.contentView.backgroundColor = [XYLiveTokenColor bg];
    
    // 创建搜索框
    [self setupSearchBar];
    // 创建UITableView
    [self setupTableView];
    
    // 布局
    [self.searchBarView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.top.equalTo(self.contentView);
        make.height.mas_equalTo(kXYLiveMultiLineSearchListNavBarH);
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self.contentView);
        make.top.equalTo(self.searchBarView.mas_bottom);
    }];
}

- (CGFloat)heightWhenPortrait {
    return kXYLiveMultiLineSearchPanelH;
}

- (BOOL)needReachBottom {
    return YES;
}

#pragma mark - 创建子视图

- (void)setupSearchBar {
    XYLiveLinkHostSearchNavBar *searchBar = [[XYLiveLinkHostSearchNavBar alloc] init];
    WS
    searchBar.didTapCancelHandler = ^{
        SS
        [self.containerVC dismissPopBottomVC:self dismissAnimated:NO dismissComplete:nil];
    };
    searchBar.didTapSearchHandler = ^(NSString * _Nonnull searchText) {
        SS
        if (searchText.length == 0) {
            [XYAlert live_showTextItemWithText:@"请输入搜索内容"];
            return;
        }
        // 发起搜索
        [self startSearchWithKey:searchText];
    };
    [self.contentView addSubview:searchBar];
    self.searchBarView = searchBar;
}

- (void)setupTableView {
    UITableView *tableView = [[UITableView alloc] init];
    tableView.backgroundColor = [UIColor clearColor];
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    tableView.showsVerticalScrollIndicator = NO;
    tableView.delegate = self;
    tableView.dataSource = self;
    if (@available(iOS 15.0, *)) {
        tableView.sectionHeaderTopPadding = 0;
    }
    [self.contentView addSubview:tableView];
    self.tableView = tableView;
    
    // 注册cell
    [tableView registerClass:XYLiveLinkHostInviteListItemCell.class forCellReuseIdentifier:kXYLiveMultiLineSearchListItemCelllIde];
}

#pragma mark - UITableViewDataSource / UITableViewDelegate

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.viewModel.dataSource.count > 0 ? 1 : 0;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.viewModel.dataSource.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    XYLiveLinkHostInviteListItemCell *cell = [tableView dequeueReusableCellWithIdentifier:kXYLiveMultiLineSearchListItemCelllIde forIndexPath:indexPath];
    WS
    cell.didTapInviteHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull inviteeInfo, NSIndexPath * _Nonnull indexPath) {
        SS
        self.didTapInviteHandler ? self.didTapInviteHandler(inviteeInfo) : nil;
    };
    // 数据绑定
    [cell bindListItems:self.viewModel.dataSource indexPath:indexPath];
    return cell;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return kXYLiveMultiLineSearchListItemCellH;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    if (_sectionView == nil) {
        _sectionView = [[XYLiveMultiLineSearchSectionView alloc] init];
    }
    return _sectionView;
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return kXYLiveMultiLineSearchListSectionH;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    XYLiveLinkHostInviteeInfo *inviteeInfo = XYSAFE_CAST(self.viewModel.dataSource[indexPath.row], XYLiveLinkHostInviteeInfo);
    // 执行事件回调
    self.didTapUserHandler ? self.didTapUserHandler(inviteeInfo) : nil;
}

- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView {
    [self.searchBarView resignFirstResponder];
}
    
#pragma mark - Private

- (void)startSearchWithKey:(NSString *)key {
    // base64编码
    NSString *base64Key = [key xy_base64EncodedString];
    // 展示loading提示
    [self showLoading];
    WS
    // 发起网路请求
    [self.viewModel requestSearchResultWithKey:base64Key completion:^(NSError * _Nonnull error) {
        SS
        // 移除Loading提示
        [self hideLoading];
        // 移除占位
        [self hideEmptyView];
        // 处理成功逻辑
        if (error == nil) {
            if (self.viewModel.dataSource.count == 0) {
                [self showEmptyViewWithError:nil];
            }
        } else {
            [self showEmptyViewWithError:error];
        }
        [self.tableView reloadData];
    }];
}

- (void)showEmptyViewWithError:(NSError *_Nullable)error {
    XYLiveMutiLinkEmptyView *emptyView = [[XYLiveMutiLinkEmptyView alloc] init];
    emptyView.title = error ? @"未连接到服务器，刷新一下试试" : @"未找到相关用户，换个词试试吧";
    emptyView.image = error ? [XYEmptyViewUtil networkImage] : [XYEmptyViewUtil userImage];
    [self.contentView addSubview:emptyView];
    self.emptyView = emptyView;
    
    [self.emptyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.tableView);
    }];
}

- (void)hideEmptyView {
    [self.emptyView removeFromSuperview];
    self.emptyView = nil;
}

#pragma mark - Lazy

- (XYLiveMultiLineSearchVM *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[XYLiveMultiLineSearchVM alloc] initWithLiveInfoService:self.liveInfoService multiLinkService:self.multiLinkService];
    }
    return _viewModel;
}

@end
