//
//  BindLeadCardBottomView.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/4/30.
//  Copyright © 2025 XingIn. All rights reserved.
//

@objcMembers
@objc(XYBindLeadCardBottomView)
class BindLeadCardBottomView: UIView {
    
    private var bottomBarManager: BindLeadCardBottomBarManager?
    private weak var pageVC: BindLeadCardPageViewController?
    private var allSelectBtnCallback: ((Bool) -> Void)?
    private var completionBtnCallback: (() -> Void)?
    private var isAllowSelectNone = false
    
    override init(frame: CGRect) {
        super.init(frame: frame)
    }
    
    convenience init(pageVC: BindLeadCardPageViewController, isAllowSelectNone: Bool, allSelectHandler: ((Bool) -> Void)?, completionHandler: (() -> Void)?) {
        self.init(frame: CGRectZero)
        self.pageVC = pageVC
        self.isAllowSelectNone = isAllowSelectNone
        self.allSelectBtnCallback = allSelectHandler
        self.completionBtnCallback = completionHandler
        self.setupUI()
        self.isHidden = true
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        self.backgroundColor = .ReDs.background.light()
        // 初始化所有底部栏
        bottomBarManager = BindLeadCardBottomBarManager.setupAllBottomBars(in: self, isAllowSelectNone: self.isAllowSelectNone, completion: completionBtnCallback, allSelect: allSelectBtnCallback, selectedCountHandler: { [weak self] in
            return self?.getSelectedCardIds() ?? 0
        }, limitedCountHandler: { [weak self] in
            return self?.getLimitedCardIds() ?? 0
        })
  
        // 默认显示全选底部栏
        bottomBarManager?.showBottomBar(shouldShowAllSelectBtn: true, shouldEnableCompleteBtn: true, shouldShowBottomBar: true, cardType: .complete)
    }
    
    private func getSelectedCardIds() -> Int {
        // 获取表单留资中选中的卡片数量
        let formSelectedCount = self.pageVC?.bindLeadCardFormMsgVC.leadCardDataSource
            .filter { $0.selected == true }
            .count ?? 0
            
        // 获取私信咨询中选中的卡片数量
        let consultSelectedCount = self.pageVC?.bindLeadCardConsultVC.leadCardDataSource
            .filter { $0.selected == true }
            .count ?? 0
            
        // 返回总数
        return formSelectedCount + consultSelectedCount
    }
    
    private func getLimitedCardIds() -> Int {
        let limitedCount = self.pageVC?.bindLeadCardFormMsgVC.limitCount
        return limitedCount ?? 0
    }
    
    // 获取指定类型的底部栏
    func getBottomBar(type: BindLeadCardBottomBarType) -> UIView? {
        return bottomBarManager?.bottomBar(for: type)
    }
    
    func updateBottomBar(shouldShowAllSelectBtn: Bool, shouldEnableCompleteBtn: Bool, shouldShowBottomBar:Bool, cardType: LeadCardType) {
        switch cardType {
        case .formMsg: // 去留资
            bottomBarManager?.showBottomBar(shouldShowAllSelectBtn: shouldShowAllSelectBtn, shouldEnableCompleteBtn: shouldEnableCompleteBtn, shouldShowBottomBar: shouldShowBottomBar, cardType: .allSelect)
        case .consult: // 去咨询
            bottomBarManager?.showBottomBar(shouldShowAllSelectBtn: shouldShowAllSelectBtn, shouldEnableCompleteBtn: shouldEnableCompleteBtn, shouldShowBottomBar: shouldShowBottomBar, cardType: .complete)
        default:
            break
        }
    }
    
    func updateBottomBar(limitCount: Int, selectedCount: Int, cardType: LeadCardType) {
        bottomBarManager?.updateBottomBar(limitCount: limitCount, selectedCount: selectedCount, cardType: cardType)
    }
}
