//
//  BindAdsLeadsCardErrorView.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/5/22.
//  Copyright © 2025 XingIn. All rights reserved.
//

@objcMembers
class BindAdsLeadsCardErrorView: UIView {
    var refreshAction: (() -> Void)?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    private func setupUI() {
        self.backgroundColor = .ReDs.background.light()
        self.addSubview(emptyImage)
        self.addSubview(emptyLabel)
        self.addSubview(emptyBtn)
        
        self.emptyImage.snp.makeConstraints { make in
            make.width.equalTo(80.0)
            make.height.equalTo(80.0)
            make.top.equalToSuperview().offset(141.0)
            make.centerX.equalToSuperview()
        }
        
        self.emptyLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(48.0)
            make.right.equalToSuperview().offset(-48.0)
            make.top.equalTo(self.emptyImage.snp.bottom).offset(16.0)
            make.centerX.equalTo(self.emptyImage)
        }
        
        self.emptyBtn.snp.makeConstraints { make in
            make.width.equalTo(82.0)
            make.height.equalTo(36.0)
            make.top.equalTo(self.emptyLabel.snp.bottom).offset(20.0)
            make.centerX.equalTo(self.emptyLabel)
        }
    }
    
    private lazy var emptyImage = {
        let imageView = UIImageView()
        imageView.image = UIImage.xy_liveKitBundleImage("live_ads_error_page_icon")
        
        return imageView
    }()
    
    private lazy var emptyLabel = {
        let label = UILabel()
        label.text = AdsLeadGenerationTools.getI18NLanguage(from: "卡片获取失败，请返回或关闭后重试")
        label.textColor = .ReDs.description.light()
        label.font = Theme.fontXMedium
        label.textAlignment = .center
        label.numberOfLines = 0
        
        return label
    }()
    
    private lazy var emptyBtn = {
        let button = UIButton(type: .custom)
        button.setTitle(AdsLeadGenerationTools.getI18NLanguage(from: "刷新"), for: .normal)
        button.setTitleColor(.ReDs.title.light(), for: .normal)
        button.titleLabel?.font = Theme.fontXMedium
        button.titleLabel?.theme.borderColor = .ReDs.separator2.light()
        button.layer.borderWidth = 0.5
        button.layer.cornerRadius = 18.0
        
        button.addTarget(self, action: #selector(emptyBtnDidPressed), for: .touchUpInside)

        return button
    }()
    
    func emptyBtnDidPressed() {
        self.refreshAction?()
    }
}
