//
//  XYLivePushMultiChatCoreModel.m
//  XYLiveKit
//
//  Created by 大远 on 2025/2/14.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatCoreModel.h"
#import <XYLiveCore/XYLiveCore-Swift.h>
#import <XYAlphaNetwork/XYAlphaNetwork-Swift.h>
#import <XYLiveKit/XYLivePushMultiChatConsts.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYAPIRoute/XYAPIRoute.h>
#import <XYAlphaUtils/NSDictionary+XYLive.h>
#import <XYAlphaUtils/XYLiveIMMacro.h>
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLivePushMultiLinkBizTracker.h>

@interface XYLivePushMultiChatCoreModel()<XYLiveMultiLinkListener>

@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveIMDistributerServiceProtocol> socketService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, weak)   id<XYLiveGroupServiceProtocol> groupLiveService;
@property (nonatomic, strong) XYLivePushMultiChatConfig *config;
@property (nonatomic, assign) NSInteger applyNum;
@property (nonatomic, copy)   NSString *sessionId;
@property (nonatomic, strong) NSHashTable<id<XYLivePushMultiChatCoreModelListener>> *listeners;

@end

@implementation XYLivePushMultiChatCoreModel

- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                          socketService:(id<XYLiveIMDistributerServiceProtocol>)socketService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService groupLiveService:(id<XYLiveGroupServiceProtocol>)groupLiveService {
    if (self = [super init]) {
        _liveInfoService = liveInfoService;
        _socketService = socketService;
        _multiLinkService = multiLinkService;
        _groupLiveService = groupLiveService;

        // 注册监听
        [self registerObservers];
        // 初始化配置
        [self loadInitialConfig:nil];
    }
    return self;
}

#pragma mark - 公用方法

- (void)loadInitialConfig:(void(^_Nullable)(XYLivePushMultiChatConfig * _Nullable config, NSError * _Nullable error))completion {
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/line/v1/config_panel/linking_viewer" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMGET;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"app_id"] = @(1);
    params[@"room_id"] = self.liveInfoService.roomId;
    [api addingQueryItems:params.copy];
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"load config api, params:{%@}", params]];
    WS
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"load config api response, result:{value:%@, error:%@}", response.value, response.error]];
        [response onSuccess:^(id  _Nonnull value) {
            SS
            // 数据转模型
            XYLivePushMultiChatConfig *config = [XYLivePushMultiChatConfig xy_modelWithDictionary:value];
            self.config = config;
            // 刷新本地缓存
            [self refreshSwitchStatusWithSource:@"Init" isOpened:config.isOpened];
            [self refreshLayoutTypeWithSource:@"Init" layoutType:config.layoutType];
            [self refreshLimitTypeWithSource:@"Init" limitType:config.limitType extraInfo:config.applyCoins];
            [self refreshGiftSwitchWithSource:@"Init" enableGiftSwitch:config.enableGiftSwitch];
            completion ? completion(config, nil) : nil;
        } onFailure:^(NSError * _Nonnull error) {
            completion ? completion(nil, error) : nil;
        }];
    }];
}

- (void)updateSwitchStatus:(BOOL)isOpened completion:(void(^_Nullable)(NSError *error))completion {
    WS
    [self refreshConfigWithIsOpened:isOpened layoutType:XYLiveMultiLinkLayoutTypeFloat limitType:XYLiveMultiChatLimitTypeAll extraInfo:nil enableGiftSwitch:self.config.enableGiftSwitch completion:^(BOOL result, NSError *error) {
        SS
        if (error == nil) {
            // 刷新本地缓存
            [self refreshSwitchStatusWithSource:@"API" isOpened:isOpened];
        }
        completion ? completion(error) : nil;
    }];
}

- (void)updateLayoutType:(XYLiveMultiLinkLayoutType)layoutType completion:(void (^)(NSError * _Nonnull))completion {
    WS
    [self refreshConfigWithIsOpened:YES layoutType:layoutType limitType:self.config.limitType extraInfo:self.config.applyCoins enableGiftSwitch:self.config.enableGiftSwitch completion:^(BOOL result, NSError *error) {
        SS
        if (error == nil) {
            // 刷新本地缓存
            [self refreshLayoutTypeWithSource:@"API" layoutType:layoutType];
        }
        completion ? completion(error) : nil;
    }];
}

- (void)updateLimitType:(XYLiveMultiChatLimitType)limitType extraInfo:(NSString *)extraInfo completion:(void (^)(NSError * _Nonnull))completion {
    WS
    [self refreshConfigWithIsOpened:YES layoutType:self.config.layoutType limitType:limitType extraInfo:extraInfo enableGiftSwitch:self.config.enableGiftSwitch completion:^(BOOL result, NSError *error) {
        SS
        if (error == nil) {
            // 刷新本地缓存
            [self refreshLimitTypeWithSource:@"API" limitType:limitType extraInfo:extraInfo];
        }
        completion ? completion(error) : nil;
    }];
}

- (void)updateEnableGiftSwitch:(BOOL)enableGiftSwitch completion:(void (^)(NSError * _Nonnull))completion {
    WS
    [self refreshConfigWithIsOpened:YES layoutType:self.config.layoutType limitType:self.config.limitType extraInfo:self.config.applyCoins enableGiftSwitch:enableGiftSwitch completion:^(BOOL result, NSError *error) {
        SS
        if (error == nil) {
            // 刷新本地缓存
            [self refreshGiftSwitchWithSource:@"API" enableGiftSwitch:enableGiftSwitch];
        }
        completion ? completion(error) : nil;
    }];
}


- (void)inviteWithUserId:(NSString *)userId source:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString * _Nullable)extraInfo {
    NSString *sessionId = [self.multiLinkService inviteWithBizType:XYLiveMultiLinkBizTypeChat targetUserId:userId targetRoomId:self.liveInfoService.roomId mediaType:XYLiveMultiLinkMediaTypeAudio bizExtraInfo:extraInfo];
    // 来源于个人资料卡，则缓存会话标识
    if (source == XYLiveMultiLinkInviteSourceProfile) {
        self.sessionId = sessionId;
    }
}

/// 注册监听
- (void)registerListener:(id<XYLivePushMultiChatCoreModelListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call or listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    // 开关状态变化事件补发
    if ([listener respondsToSelector:@selector(onUpdateSwitchStatus:)]) {
        [listener onUpdateSwitchStatus:self.isOpened];
    }
    // 申请人数变化事件补发
    if ([listener respondsToSelector:@selector(onUpdateApplyNum:)]) {
        [listener onUpdateApplyNum:self.applyNum];
    }
    [self.listeners addObject:listener];
}

/// 取消注册监听
- (void)unregisterListener:(id<XYLivePushMultiChatCoreModelListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call or listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners removeObject:listener];
}

- (BOOL)isOpened {
    return self.config.isOpened;
}

- (BOOL)hasGoods {
    return self.liveInfoService.roomInfo.hasGoods;
}

- (NSString *)roomId {
    return self.liveInfoService.roomId;
}

- (BOOL)isGameLive {
    return self.liveInfoService.roomInfo.isScreenLive;
}

- (BOOL)isGroupLive {
    return [self.groupLiveService isGroupLiveRoom];
}

#pragma mark - 私有方法

- (void)registerObservers {
    // 监听多人互动服务
    [self.multiLinkService registerListener:self];
    // 监听IM
    [self registerIM];
}

- (void)registerIM {
    WS
    // 注册监听连麦配置变化
    [self.socketService im_registerWithType:kIM_CUSTOM_LINKMIC_CONFIG_CHANGE key:kXYLivePushMultiChatModule completionHandler:^(NSString * _Nonnull type, XYLiveCodableModel * _Nullable rawModel, NSDictionary<NSString *,id> * _Nonnull rawData) {
        // 数据转模型
        XYLiveLinkmicConfigInfoModel *model = [XYLiveLinkmicConfigInfoModel xy_modelWithDictionary:rawData];
        XYExecuteOnMainQueue(^{
            // 更新缓存
            [weak_self refreshSwitchStatusWithSource:@"IM" isOpened:model.viewerLinkmicSwitch];
            [weak_self refreshLayoutTypeWithSource:@"IM" layoutType:model.layoutType];
            [weak_self refreshLimitTypeWithSource:@"IM" limitType:model.viewerApplyRestrictType extraInfo:[NSString stringWithFormat:@"%@",@(model.viewerApplyCoins)]];
        });
    }];
    
    // 注册监听申请人数变化
    [self.socketService im_registerWithType:XYIM_CUSTOM_LINKMIC_APPLY_COUNT_CHANGE key:kXYLivePushMultiChatModule completionHandler:^(NSString * _Nonnull type, XYLiveCodableModel * _Nullable rawModel, NSDictionary<NSString *,id> * _Nonnull rawData) {
        // 数据转模型
        IMLinkmicApplyCountChange *model = [IMLinkmicApplyCountChange xy_modelWithDictionary:rawData];
        XYExecuteOnMainQueue(^{
            // 本地缓存
            weak_self.applyNum = model.applyCount;
            // 事件分发
            [weak_self.listeners.allObjects xy_each:^(id<XYLivePushMultiChatCoreModelListener>  _Nonnull obj) {
                if ([obj respondsToSelector:@selector(onUpdateApplyNum:)]) {
                    [obj onUpdateApplyNum:model.applyCount];
                }
            }];
        });
    }];
}

// 更新配置数据
- (void)refreshConfigWithIsOpened:(BOOL)isOpened layoutType:(XYLiveMultiLinkLayoutType)layoutType limitType:(XYLiveMultiChatLimitType)limitType extraInfo:(NSString *_Nullable)extraInfo enableGiftSwitch:(BOOL)enableGiftSwitch completion:(void(^)(BOOL result, NSError *error))completion {
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/line/v1/config_panel/linking_viewer/update" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMPOST;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"app_id"] = @(1);
    params[@"room_id"] = self.liveInfoService.roomId;
    params[@"layout_type"] = @(layoutType);
    params[@"linkmic_switch"] = @(isOpened);
    params[@"viewer_apply_restrict_type"] = @(limitType);
    params[@"viewer_apply_coins"] = extraInfo;
    params[@"viewer_enable_send_gift"] = @(enableGiftSwitch);
    [api addingQueryItems:params.copy];
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"start request update config api, params:{%@}", params]];
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"update config api response, result:{value:%@, error:%@}", response.value, response.error]];
        completion ? completion(response.success, response.error) : nil;
    }];
}

// 刷新连麦开关状态
- (void)refreshSwitchStatusWithSource:(NSString *)source isOpened:(BOOL)isOpened {
    // 防重入
    if (self.isOpened == isOpened) { return; }
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"--updateSwitchStatus--{source:%@, isOpened:%d}", source, isOpened]];
    self.config.isOpened = isOpened;
    // 事件分发
    [self.listeners.allObjects xy_each:^(id<XYLivePushMultiChatCoreModelListener>  _Nonnull listener) {
        if ([listener respondsToSelector:@selector(onUpdateSwitchStatus:)]) {
            [listener onUpdateSwitchStatus:isOpened];
        }
    }];
}

// 刷新布局方式
- (void)refreshLayoutTypeWithSource:(NSString *)source layoutType:(XYLiveMultiLinkLayoutType)layoutType {
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"--updateLayoutType--{source:%@, layoutType:%@}", source, @(layoutType)]];
    self.config.layoutType = layoutType;
}

// 刷新条件限制
- (void)refreshLimitTypeWithSource:(NSString *)source limitType:(XYLiveMultiChatLimitType)limitType extraInfo:(NSString *_Nullable)extraInfo {
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"--updateLimitType--{source:%@, limitType:%@, extraInfo:%@}", source, @(limitType), extraInfo]];
    self.config.limitType = limitType;
    self.config.applyCoins = extraInfo;
}

// 刷新送礼按钮开关
- (void)refreshGiftSwitchWithSource:(NSString *)source enableGiftSwitch:(BOOL)enableGiftSwitch {
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"--updateGiftSwitch--{source:%@, enableGiftSwitch:%@}", source, @(enableGiftSwitch)]];
    self.config.enableGiftSwitch = enableGiftSwitch;
}

#pragma mark - XYLiveMultiLinkLisener

- (void)onInviteResponseWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId error:(NSError *)error {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeChat) { return; }
    if (error == nil) {
        // 如果来源于P页触发，则进行埋点上报
        if ([self.sessionId isEqualToString:sessionId]) {
            // 上报埋点
            [XYLivePushMultiLinkBizTracker eventActionId34903WithUserId:targetUserId];
            // 清除会话标识
            self.sessionId = nil;
        }
        // Toast提示
        [XYAlert live_showTextItemWithText:@"已发送邀请"];
        // 埋点上报
        [XYLivePushMultiLinkBizTracker eventActionId33415WithBizType:XYLiveMultiLinkBizTypeChat thirdTabName:@"房内观众" userId:targetUserId hasGoods:self.hasGoods];
    } else {
        // Toast提示
        [XYAlert live_showTextItemWithError:error];
    }
}

- (void)onInviteFailWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId failReason:(XYLiveMultiLinkInviteFailReason)failReason {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeChat) { return; }
    // 如果被拒则Toast提示
    if (failReason == XYLiveMultiLinkInviteFailReasonReject) {
        [XYAlertCenter live_showTextItemWithText:@"对方拒绝了你的连线邀请"];
    }
}

#pragma mark - 懒加载

- (NSHashTable<id<XYLivePushMultiChatCoreModelListener>> *)listeners {
    if (_listeners == nil) {
        _listeners = [NSHashTable weakObjectsHashTable];
    }
    return _listeners;
}

- (XYLivePushMultiChatConfig *)config {
    if (_config == nil) {
        _config = [[XYLivePushMultiChatConfig alloc] init];
    }
    return _config;
}

@end
