//
//  XYLivePushRoomVC+RTCInternal.h
//  XYLiveKit
//
//  Created by 周博立 on 2020/8/4.
//  Copyright © 2020 XingIn. All rights reserved.
//
#import <XYLiveKit/XYLivePushRoomViewController.h>

@class RTCPusher;
@class RTCCore;
@class XYLiveRtcCoreService;
@class RTCPresetImageUtil;
@interface XYLivePushRoomViewController ()

@property (nonatomic, readonly, nullable) RTCCore *rtcCore;
@property (nonatomic, strong, nullable) XYLiveRtcCoreService *rtcCoreService;
@property (nonatomic, strong, nullable) RTCPusher *rtcPusher;
@property (nonatomic, strong, nonnull) RTCPresetImageUtil *presetImageUtil;
@property (nonatomic, assign) BOOL pushBreakOff;
@property (nonatomic, assign) CFAbsoluteTime viewDidLoadTime;
@property (nonatomic, assign) BOOL fatalAlerted;
@property (nonatomic, strong, nullable) NSDate *muteVadMark;

@property (nonatomic, copy) void (^onLocalJoinedSuccBlock)(void);
@property (nonatomic, copy) void (^onRemoteJoinedSuccBlock)(void);
@property (nonatomic, copy) void (^onSentLocalFirstVideoFrameBlock)(void);
@property (nonatomic, copy) void (^onRemoteVideoAvailableBlock)(NSString *remoteUserID, BOOL available);

@end
