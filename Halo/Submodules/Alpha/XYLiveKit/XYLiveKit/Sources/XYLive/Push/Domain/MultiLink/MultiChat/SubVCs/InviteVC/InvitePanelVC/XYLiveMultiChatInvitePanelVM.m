//
//  XYLiveMultiChatInvitePanelVM.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/24.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatInvitePanelVM.h"
#import <XYAlphaShare/XYAlphaShare-Swift.h>
#import <XYAlphaShare/XYAlphaShare.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveKit/XYLivePushMultiChatConsts.h>

@implementation XYLiveMultiChatInvitePanelVM

/// 打开分享面板
- (void)openShareVCWithRoomId:(NSString *)roomId
                   completion:(void(^)(XYPMMessageShareToMessageLiveModel *shareModel, NSError *error)) completion {
    XYLiveShareSourceInfo *sourceInfo = [[XYLiveShareSourceInfo alloc] init];
    sourceInfo.roomID = roomId;
    [XYLiveShareNetworking reqAppServLiveShareCompletionInfoWithSoureInfo:sourceInfo succ:^(id  _Nullable value, NSError * _Nullable error) {
        [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"share response, result:{error:%@}", error]];
        XYLiveShareInfo *sinfo = XYSAFE_CAST(value, XYLiveShareInfo);
        if (sinfo != nil && error == nil) {
            XYPMMessageShareToMessageLiveModel *model = [[XYPMMessageShareToMessageLiveModel alloc] init];
            model.link = sinfo.link;
            model.image = sinfo.bigImgURL;
            model.username = sinfo.nickName;
            model.avatar = sinfo.avatar;
            model.title = sinfo.desc;
            model.tagName = sinfo.tagName;
            if (completion) {
                completion(model, error);
            }
        }
    } fail:^(id  _Nullable value, NSError * _Nullable error) {
        [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"share response fail, result:{error:%@}", error]];
        if (completion) {
            completion(nil, error);
        }
    }];
}

@end
