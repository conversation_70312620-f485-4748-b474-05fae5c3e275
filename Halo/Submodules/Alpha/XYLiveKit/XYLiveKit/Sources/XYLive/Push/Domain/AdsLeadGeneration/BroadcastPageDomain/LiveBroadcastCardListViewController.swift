//
//  LiveBroadcastCardListViewController.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/5/12.
//  Copyright © 2025 XingIn. All rights reserved.
//

import Foundation
import XYLiveUIKit
import XYTracker
import XY<PERSON>l<PERSON>Utils

enum LiveBroadcastCardListViewDisplayType: Int {
    case unknownPage = -1
    case additonPage = 0
    case errorPage = 1
    case listPage = 2
}

@objcMembers
class LiveBroadcastCardListViewController: UIViewController {
    
    private var cardType: LeadCardType = .consult
    
    private lazy var bulletManager = {
        let manager = PushLeadsBulletManager.shared
        manager.cardOptionDidTrigerAction = { [weak self] optionType, cardMode in
            guard let cardMode = cardMode else {
                return
            }
            
            var channelTab = "0"
            switch optionType {
            case .startExplain:
                channelTab = "2"
            case .bulletCard:
                channelTab = "1"
            case .stopExplain:
                channelTab = "3"
            case .unknown:
                break
            }
            self?.track79460(channelTab: String(channelTab), cardId: cardMode.cardId ?? "")
        }
        return manager
    }()
    
    private lazy var containerView: XYLiveTopCornerView = {
        let view: XYLiveTopCornerView = XYLiveTopCornerView()
        view.backgroundColor = .ReDs.background.light()
        return view
    }()
    private lazy var consultListView = {
        let listView = LiveConsultListContentView()
        self.bulletManager.registerListenerIfNeeded(listener: listView)
        return listView
    }()
    
    private lazy var formMsgListView = {
        let listView = LiveFormMsgListContentView()
        self.bulletManager.registerListenerIfNeeded(listener: listView)
        return listView
    }()
    
    private lazy var seperateLine = {
        let lineView = UIView()
        lineView.backgroundColor = .ReDs.fill3.light()
        return lineView
    }()
    
    private lazy var consultLabel = {
        let label = UILabel()
        label.textColor = .ReDs.title.light()
        label.font = Theme.fontLeSmall
        label.text = "本场私信进入次数：0"
        
        return label
    }()
    
    private lazy var formMsgLabel = {
        let label = UILabel()
        label.textColor = .ReDs.title.light()
        label.font = Theme.fontLeSmall
        label.text = "表单提交：0"
        
        return label
    }()
    
    private lazy var consultBtn = {
        let button = UIButton(type: .custom)
        button.backgroundColor = Color.gray50.light()
        button.layer.cornerRadius = 16.0
        button.setTitle(AdsLeadGenerationTools.getI18NLanguage(from: "私信咨询"), for: .normal)
        button.setTitleColor(Color.gray1000.light(), for: .normal)
        button.titleLabel?.font = Theme.fontXMedium
        button.addTarget(self, action: #selector(consultBtnDidPressed(sender:)), for: .touchUpInside)
        
        return button
    }()
    
    private lazy var formMsgBtn = {
        let button = UIButton(type: .custom)
        button.backgroundColor = Color.gray50.light()
        button.layer.cornerRadius = 16.0
        button.setTitle(AdsLeadGenerationTools.getI18NLanguage(from: "表单留资"), for: .normal)
        button.setTitleColor(Color.gray1000.light(), for: .normal)
        button.titleLabel?.font = Theme.fontXMedium
        button.addTarget(self, action: #selector(formMsgBtnDidPressed(sender:)), for: .touchUpInside)
        
        return button
    }()
    
    private lazy var refreshBtn: UIButton = {
        let button = UIButton()
        let imageView = UIImageView(image: Theme.icon.refresh_b.color(UIColor.xy.gray1000.light()).size(18).image)
        let label = UILabel()
        label.textColor = UIColor.xy.gray1000.light()
        label.font = Theme.fontXXSmall
        label.text = AdsLeadGenerationTools.getI18NLanguage(from: "刷新")
        button.addSubview(imageView)
        button.addSubview(label)
        imageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
        }
        label.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.centerX.equalToSuperview()
        }
        button.addTarget(self, action: #selector(refreshBtnDidPressed), for: .touchUpInside)
        button.xypk_setEnlargeEdge(5)
        button.xyAutoTrack_registerEventTrackerBlock { [weak self] () -> XYTrackerEventContext? in
            guard let self = self else { return nil }
            return self.track79461Context(channelTab: "2")?.event.action(.click)
        }
        
        return button
    }()
    
    func refreshBtnDidPressed() {
        self.refreshDataSource()
    }
    
    private func refreshDataSource() {
        switch self.cardType {
        case .unknown:
            return
        case .formMsg:
            self.formMsgListView.setupData()
            self.formMsgListView.isHidden = false
            self.consultListView.isHidden = true
        case .consult:
            self.consultListView.setupData()
            self.consultListView.isHidden = false
            self.formMsgListView.isHidden = true
        }
    }
    
    private func loadAllDataSource(isShowHUD: Bool = true) {
        self.formMsgListView.setupData(isShowHUD: isShowHUD)
        self.consultListView.setupData(isShowHUD: isShowHUD)
    }
    
    private func switchSelectedBtn() {
        switch self.cardType {
        case .unknown:
            return
        case .formMsg:
            self.consultBtn.backgroundColor = .ReDs.background.light()
            self.consultBtn.setTitleColor(.ReDs.description.light(), for: .normal)
            self.formMsgBtn.backgroundColor = Color.gray50.light()
            self.formMsgBtn.setTitleColor(Color.gray1000.light(), for: .normal)
            self.formMsgListView.isHidden = false
            self.consultListView.isHidden = true
        case .consult:
            self.formMsgBtn.backgroundColor = .ReDs.background.light()
            self.formMsgBtn.setTitleColor(.ReDs.description.light(), for: .normal)
            self.consultBtn.backgroundColor = Color.gray50.light()
            self.consultBtn.setTitleColor(Color.gray1000.light(), for: .normal)
            self.formMsgListView.isHidden = true
            self.consultListView.isHidden = false
        }
    }
    
    func consultBtnDidPressed(sender: UIButton) {
        self.cardType = .consult
        self.switchSelectedBtn()
        self.track79459(channelTab: "1")
    }
    
    func formMsgBtnDidPressed(sender: UIButton) {
        self.cardType = .formMsg
        self.switchSelectedBtn()
        self.track79459(channelTab: "2")
    }
    
    func showIn(viewController vc: UIViewController?) {
        guard let vc = vc else {
            return
        }
        
        self.liveContentView = containerView
        live_show(inViewController: vc,
                  height: StandardContentHeight.height75PercentOfScreen,
                  bgAlpha: 0,
                  preparation: nil,
                  dismiss: nil) { [weak self] isSuccess in
            guard self?.isViewLoaded == true else {
                return
            }
            
            self?.loadAllDataSource()
        }
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.setupUI()
        self.loadAllDataSource()
        self.switchSelectedBtn()
    }
    
    private func setupUI() {
        self.view.addSubview(self.containerView)
        self.view.backgroundColor = .ReDs.background.light()
        
        self.consultListView.refreshBothListPageAction = { [weak self] in
            self?.loadAllDataSource(isShowHUD: false)
        }
        
        self.formMsgListView.refreshBothListPageAction = { [weak self] in
            self?.loadAllDataSource(isShowHUD: false)
        }
        
        self.configAdditonPageDatasource(listViewPage: self.consultListView)
        self.configAdditonPageDatasource(listViewPage: self.formMsgListView)
        
        self.containerView.addSubview(self.formMsgLabel)
        self.containerView.addSubview(self.consultLabel)
        self.containerView.addSubview(self.consultBtn)
        self.containerView.addSubview(self.formMsgBtn)
        self.containerView.addSubview(self.seperateLine)
        self.containerView.addSubview(self.consultListView)
        self.containerView.addSubview(self.formMsgListView)
        self.containerView.addSubview(self.refreshBtn)
        
        self.consultListView.isHidden = false
        self.formMsgListView.isHidden = true
        
        self.consultListView.dataSourceDidChanggedHandler = { [weak self] listModel, error in
            
            guard let self = self else { return }
            
            if error != nil {
                self.consultListView.resetPageViewVisibleStatus(displayType: .errorPage)
                return
            }
            // 更新UI
            self.consultLabel.text = "本场私信进入次数：" + String(listModel?.inLineCnt ?? 0)
            self.formMsgLabel.text = "表单提交：" + String(listModel?.formSubmitCnt ?? 0)
            
            self.consultLabel.sizeToFit()
            self.consultLabel.snp.updateConstraints { make in
                make.width.equalTo(self.consultLabel.frame.width)
            }
            
            self.formMsgLabel.sizeToFit()
            self.formMsgLabel.snp.updateConstraints { make in
                make.width.equalTo(self.formMsgLabel.frame.width)
            }
            
            self.consultListView.update(leadCardCellModels: listModel?.cardList ?? [AdsLeadBindCardModel]())
            self.consultListView.resetPageViewVisibleStatus(displayType: (listModel?.cardList?.count ?? 0) > 0 ? .listPage : .additonPage)
        }
        
        self.formMsgListView.dataSourceDidChanggedHandler = { [weak self] listModel, error in
            
            guard let self = self else { return }
            
            if error != nil {
                self.formMsgListView.resetPageViewVisibleStatus(displayType: .errorPage)
                return
            }
            // 更新UI
            self.consultLabel.text = "本场私信进入次数：" + String(listModel?.inLineCnt ?? 0)
            self.formMsgLabel.text = "表单提交：" + String(listModel?.formSubmitCnt ?? 0)
            
            self.consultLabel.sizeToFit()
            self.consultLabel.snp.updateConstraints { make in
                make.width.equalTo(self.consultLabel.frame.width)
            }
            
            self.formMsgLabel.sizeToFit()
            self.formMsgLabel.snp.updateConstraints { make in
                make.width.equalTo(self.formMsgLabel.frame.width)
            }
            
            self.formMsgListView.update(leadCardCellModels: listModel?.cardList ?? [AdsLeadBindCardModel]())
            self.formMsgListView.resetPageViewVisibleStatus(displayType: (listModel?.cardList?.count ?? 0) > 0 ? .listPage : .additonPage)
        }
        
        self.consultLabel.sizeToFit()
        self.consultLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12.0)
            make.top.equalToSuperview().offset(16.0)
            make.width.equalTo(self.consultLabel.frame.width)
            make.height.equalTo(16.0)
        }
        
        self.seperateLine.snp.makeConstraints { make in
            make.left.equalTo(self.consultLabel.snp.right).offset(12.0)
            make.centerY.equalTo(self.consultLabel)
            make.width.equalTo(0.5)
            make.height.equalTo(12.0)
        }
        
        self.formMsgLabel.sizeToFit()
        self.formMsgLabel.snp.makeConstraints { make in
            make.left.equalTo(self.seperateLine.snp.right).offset(12.0)
            make.width.equalTo(self.formMsgLabel.frame.width)
            make.height.equalTo(16.0)
            make.centerY.equalTo(self.consultLabel)
        }
        
        self.refreshBtn.snp.makeConstraints { make in
            make.size.equalTo(CGSize(width: 20, height: 34))
            make.right.equalToSuperview().offset(-18.0)
            make.centerY.equalTo(self.formMsgLabel)
        }
        
        self.consultBtn.snp.makeConstraints { make in
            make.width.equalTo(80.0)
            make.height.equalTo(28.0)
            make.left.equalToSuperview().offset(12.0)
            make.top.equalToSuperview().offset(56.0)
        }
        
        self.formMsgBtn.snp.makeConstraints { make in
            make.width.equalTo(80.0)
            make.height.equalTo(28.0)
            make.left.equalTo(self.consultBtn.snp.right).offset(8.0)
            make.centerY.equalTo(self.consultBtn)
        }
        
        self.consultListView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.top.equalTo(self.consultBtn.snp.bottom).offset(6.0)
        }
        
        self.formMsgListView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.top.equalTo(self.formMsgBtn.snp.bottom).offset(6.0)
        }
    }
    
    private func configAdditonPageDatasource(listViewPage: LiveBroadcastCardListContentView) {
        listViewPage.addittionCardPage.consultCardListCallback = { [weak self] in
            guard let self = self else {
                return [AdsLeadBindCardModel]()
            }
            
            return self.consultListView.leadCardDataSource
        }
        
        listViewPage.addittionCardPage.formMsgCardListCallback = { [weak self] in
            guard let self = self else {
                return [AdsLeadBindCardModel]()
            }
            
            return self.formMsgListView.leadCardDataSource
        }
    }
}

extension LiveBroadcastCardListViewController {
    public override func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
        if enableAccessibility() {
            return touch.view == self.backgroundTopView
        }
        return touch.view == self.view
    }
}

extension LiveBroadcastCardListViewController {
    // Track
    func baseTrackContext() -> XYTrackerEventContext {
        let noteID = XYLiveAnalyticsDataSource.sharedInstance().sourceNoteID
        let noteTrackID = XYLiveAnalyticsDataSource.sharedInstance().noteTrackID
        let adsTrackID = XYLiveAnalyticsDataSource.sharedInstance().adsTrackID
        let adsTrackUrl = XYLiveAnalyticsDataSource.sharedInstance().adsTrackUrl
        let liveType = "Interaction"

        return XYLiveTrackBasePage.sharedInstance().hostAnalyticsOrganizer()
            .ads
            .trackId(adsTrackID)
            .trackURL(adsTrackUrl)
            .note
            .noteId(noteID)
            .trackId(noteTrackID)
            .page
            .pageInstance(.liveBroadcastPage)
            .live
            .liveType(liveType)
            .event
    }
    
    func track79459(channelTab: String) {
        baseTrackContext()
        .event
            .action(.click)
            .pointId(79459 /* 直播开播页/B-卡片管理- tab点击/点击 */)
            .isGoto(1)
            .moduleId(47088)
            .index
            .channelTabName(channelTab)
        .send()
    }
    
    func track79460(channelTab: String, cardId: String) {
        baseTrackContext()
            .ads
            .cardId(cardId)
            .event
            .action(.click)
            .pointId(79460 /* 直播开播页/B-卡片管理-讲解/弹卡按钮点击/点击 */)
            .isGoto(1)
            .moduleId(47089)
            .index
            .channelTabName(channelTab)
            .send()
    }
    
    func track79461Context(channelTab: String) -> XYTrackerEventContext? {
        return baseTrackContext()
            .pointId(79461 /* 直播开播页/B-卡片管理-管理/刷新点击/点击 */)
            .isGoto(1)
            .moduleId(47092)
            .index
            .channelTabName(channelTab)
            .event
    }
}

@objcMembers
class LiveConsultListContentView: LiveBroadcastCardListContentView {
    
    fileprivate override func setupData(isShowHUD: Bool = true) {
        super.setupData()
        // Loading
        if isShowHUD { self.loadingView.show(in: self) }
        self.serviceModel.fetchAdsLeadDisplayCardList(roomId: Int64(XYLiveManager.shared().roomInfo.roomID), cardType: 2) { [weak self] displayCardListModel, error in
            guard let self = self else {
                return
            }
            
            if isShowHUD { self.loadingView.hide() }
            self.dataSourceDidChanggedHandler?(displayCardListModel, error)
        }
    }
    
    override func getCardType() -> LeadCardType {
        return .consult
    }
}

@objcMembers
class LiveFormMsgListContentView: LiveBroadcastCardListContentView {
    fileprivate override func setupData(isShowHUD: Bool = true) {
        super.setupData()
        // Loading
        if isShowHUD { self.loadingView.show(in: self) }
        self.serviceModel.fetchAdsLeadDisplayCardList(roomId: Int64(XYLiveManager.shared().roomInfo.roomID), cardType: 1) { [weak self] displayCardListModel, error in
            guard let self = self else {
                return
            }
            
            if isShowHUD { self.loadingView.hide() }
            self.dataSourceDidChanggedHandler?(displayCardListModel, error)
        }
    }
    
    override func getCardType() -> LeadCardType {
        return .formMsg
    }
}

@objcMembers
class LiveBroadcastCardListContentView: UIView, UITableViewDelegate, UITableViewDataSource, PushLeadsBulletManagerProtocol {
    
    func onFinishTime(model: AdsLeadBindCardModel, shouldReload: Bool) {
    }
    
    func onUpdateTime(model: AdsLeadBindCardModel, shouldReload: Bool) {
        guard shouldReload else {
            return
        }
        self.innerListView.reloadData()
    }
    
    func onReloadDataTime() {
        guard !self.isHidden else {
            return
        }
        // 刷新数据
        self.refreshBothListPageAction?()
    }
    
    func getCardType() -> LeadCardType {
        return .unknown
    }
    
    func resetPageViewVisibleStatus(displayType: LiveBroadcastCardListViewDisplayType) {
        switch displayType {
        case .additonPage:
            self.addittionCardPage.bindCompletionAction = self.refreshBothListPageAction
            self.addittionCardPage.isHidden = false
            self.errorPage.isHidden = true
            self.innerListView.isHidden = true
        case .errorPage:
            self.errorPage.isHidden = false
            self.addittionCardPage.isHidden = true
            self.innerListView.isHidden = true
        case .listPage:
            self.innerListView.isHidden = false
            self.errorPage.isHidden = true
            self.addittionCardPage.isHidden = true
        case .unknownPage:
            break
        }
    }
    
    fileprivate var refreshBothListPageAction: (() -> Void)?
    fileprivate var leadCardDataSource: [AdsLeadBindCardModel] = [AdsLeadBindCardModel]()
    fileprivate let serviceModel = AdsDisplayLeadCardServiceModel()
    fileprivate var dataSourceDidChanggedHandler: RequestCardListReqCompletion?
    fileprivate lazy var addittionCardPage = LiveAdsLeadAddittionCardPage(type: self.getCardType())
    
    fileprivate lazy var errorPage = {
        let errorView = BindAdsLeadsCardErrorView()
        errorView.refreshAction = { [weak self] in
            self?.setupData()
        }
        
        return errorView
    }()
   
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.setupUI()
        self.setupData()
    }
    
    fileprivate func setupData(isShowHUD: Bool = true) {
    }
    
    fileprivate func setupUI() {
        self.backgroundColor = .ReDs.background.light()
        self.addSubview(self.errorPage)
        self.addSubview(self.addittionCardPage)
        self.addSubview(self.innerListView)
        self.innerListView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        self.addittionCardPage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        self.errorPage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    fileprivate lazy var loadingView = XYAlertLoadingView.live_createAlertLoading()
    
    private lazy var innerListView = {
        let listView = UITableView(frame: .zero, style: .plain)
        listView.delegate = self
        listView.dataSource = self
        listView.register(DisplayLeadCardCell.self, forCellReuseIdentifier: DisplayLeadCardCellConstant.displayLeadCardCellReuseIdentifier)
        listView.backgroundColor = .ReDs.background.light()
        listView.separatorStyle = .none
        listView.showsVerticalScrollIndicator = false
        
        return listView
    }()
    
    func update(leadCardCellModels: [AdsLeadBindCardModel]) {
        self.leadCardDataSource = leadCardCellModels
        self.innerListView.reloadData()
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return DisplayLeadCardCellConstant.displayLeadCardCellHeight
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.leadCardDataSource.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if let cell = tableView.dequeueReusableCell(withIdentifier: DisplayLeadCardCellConstant.displayLeadCardCellReuseIdentifier, for: indexPath) as? DisplayLeadCardCell,
           let itemData = cellModel(at: indexPath) {
            cell.updateCell(with: itemData)
            return cell
        }
        
        return UITableViewCell()
    }
}

extension LiveBroadcastCardListContentView {
    // 获取对应的 model 对象
    func cellModel(at indexPath: IndexPath) -> AdsLeadBindCardModel? {
        if self.leadCardDataSource.indices.contains(indexPath.row) {
            return self.leadCardDataSource[indexPath.row]
        }
        return nil
    }
}
