//
//  XYLiveLinkHostInviteListVM.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/20.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostInviteListVM.h"
#import <XYAlphaNetwork/XYAlphaNetwork-Swift.h>
#import <XYAPIRoute/XYAPIRoute.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLiveLinkHostInviteListModel.h>
#import <XYLiveKit/XYLiveLinkHostInvitePanelConsts.h>
#import <XYStorageCore/XYStorageCenter.h>

@interface XYLiveLinkHostInviteListVM()

@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, copy)   NSArray *dataSource;
@property (nonatomic, assign) BOOL isPK;

@end

@implementation XYLiveLinkHostInviteListVM

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
        _isPK = [[XYStorageCenter defaultCenter].permanent getBoolForKey:kXYLiveLinkHostInviteListBizKey defaultValue:NO];
    }
    return self;
}

// 请求列表数据
- (void)requestListDataWithIsPK:(BOOL)isPK completion:(void(^)(NSError *error))completion {
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/line/v2/invite_host_list" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMGET;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"app_id"] = @(1);
    params[@"room_id"] = self.liveInfoService.roomId;
    params[@"type"] = isPK ? @"pk" : @"linkmic";
    params[@"sub_biz_type"] = isPK ? @"gift" : @"";
    [api addingQueryItems:params.copy];
    [XYLogCollector xyLiveLogTag:kXYLiveLinkHostModuleName content:[NSString stringWithFormat:@"start request invite host list api,{isPK:%d, roomId:%@}", isPK, self.liveInfoService.roomId]];
    WS
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        [XYLogCollector xyLiveLogTag:kXYLiveLinkHostModuleName content:[NSString stringWithFormat:@"request invite host list api reposne,{error:%@}", response.error]];
        SS
        // 刷新缓存
        self.isPK = isPK;
        [[XYStorageCenter defaultCenter].permanent setBool:isPK forKey:kXYLiveLinkHostInviteListBizKey];
        // 处理成功回调
        if (response.error == nil) {
            // 数据转模型
            XYLiveLinkHostInviteListModel *listModel = [XYLiveLinkHostInviteListModel xy_modelWithDictionary:response.result.value];
            // 构建数据源
            [self configureDataSourceWithListModel:listModel];
        } else {
            XYLiveLinkHostInviteListEmptyItem *lisItem = [[XYLiveLinkHostInviteListEmptyItem alloc] init];
            lisItem.showErrorView = YES;
            self.dataSource = @[lisItem];
        }
        completion ? completion(response.error) : nil;
    }];
}

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo isPK:(BOOL)isPK {
    // 合法性校验
    if (self.isPK != isPK) { return; }
    // 更新数据源
    [self.dataSource enumerateObjectsUsingBlock:^(id  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        XYLiveLinkHostInviteeInfo *listItem = XYSAFE_CAST(obj, XYLiveLinkHostInviteeInfo);
        if ([listItem.userId isEqualToString:inviteeInfo.userId]) {
            listItem.isInvited = state != XYLiveMultiLinkInviteStateIdle;
        }
    }];
}

#pragma mark - Private

// 配置数据源
- (void)configureDataSourceWithListModel:(XYLiveLinkHostInviteListModel *)listModel {
    NSMutableArray *dataSourceM = [NSMutableArray array];
    // 判断是否有好友列表
    if (listModel.friendListItems.count) {
        XYLiveLinkHostInviteListTextItem *textItem = [[XYLiveLinkHostInviteListTextItem alloc] init];
        textItem.title = [NSString stringWithFormat:@"在线好友（%@）", @(listModel.friendListItems.count)];
        [dataSourceM addObject:textItem];
        // 添加好友列表
        [dataSourceM addObjectsFromArray:[self buildInviteeListWithListItems:listModel.friendListItems]];
    }
    // 判断是否有推荐列表
    if (listModel.recommendListItems.count) {
        XYLiveLinkHostInviteListTextItem *textItem = [[XYLiveLinkHostInviteListTextItem alloc] init];
        textItem.title = @"推荐主播";
        [dataSourceM addObject:textItem];
        // 添加好友列表
        [dataSourceM addObjectsFromArray:[self buildInviteeListWithListItems:listModel.recommendListItems]];
    }
    // 兜底空占位视图
    if (dataSourceM.count == 0) {
        XYLiveLinkHostInviteListEmptyItem *lisItem = [[XYLiveLinkHostInviteListEmptyItem alloc] init];
        lisItem.showErrorView = NO;
        [dataSourceM addObject:lisItem];
    }
    self.dataSource = dataSourceM.copy;
}

// 构建好友列表
- (NSArray<XYLiveLinkHostInviteeInfo *> *)buildInviteeListWithListItems:(NSArray<XYLiveLinkHostInviteeInfo *> *)listItems {
    NSMutableArray *listItemsM = [NSMutableArray array];
    for (XYLiveLinkHostInviteeInfo *listItem in listItems) {
        // 更新邀请状态
        XYLiveMultiLinkBizType bizType = self.isPK ? XYLiveMultiLinkBizTypePKGift : XYLiveMultiLinkBizTypeLine;
        listItem.isInvited = [self.multiLinkService isInvitingWithBizType:bizType targetUserId:listItem.userId];
        // 更新PK状态
        listItem.isPK = self.isPK;
        [listItemsM addObject:listItem];
    }
    return listItemsM.copy;
}


@end
