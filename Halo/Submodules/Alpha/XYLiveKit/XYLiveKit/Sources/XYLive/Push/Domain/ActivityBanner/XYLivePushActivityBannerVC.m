//
//  XYLivePushActivityBannerVC.m
//  XYLiveKit
//
//  Created by gongyidemac on 2025/5/22.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushActivityBannerVC.h"
#import "XYLivePushBannerItemView.h"

#import <XYLiveKit/XYLiveKit-Swift.h>

@import XYAlphaUtils;
@import XYLiveFoundation;

@interface XYLivePushActivityBannerVC () <XYLivePushDisperseEventListener, XYLiveCommonLoopBannerEventObserverProtocol>

@property (nonatomic, strong, nullable) id<PushActivityBannerDependProtocol> depend;
@property (nonatomic, strong, readonly) id<XYLiveCommonLoopBannerProtocol> bannerService;

@property (nonatomic, strong) XYLiveCommonLoopListView *loopView;// 垂直轮播容器
@property (nonatomic, copy) NSArray<XYLiveRightActivityAreaModel *> *bannerArray;

@end

@implementation XYLivePushActivityBannerVC

- (instancetype)initWithDepend:(id<PushActivityBannerDependProtocol> _Nullable)depend {
    if (self = [super init]) {
        self.depend = depend;
    }
    
    return self;
}

#pragma mark - Overrides

- (void)didLoad  {
    [super didLoad];
    
    [self setupEventObserver];
}

#pragma mark - XYLivePushDisperseEventListener

- (void)disperseEventAggregateBusinessInfoDidLoadWithInfo:(XYLiveAggregateBusinessInfo *)businessInfo {
    if (businessInfo) {
        [self handleActivityBizModelArray:businessInfo.banners];
    }
}

#pragma mark - XYLiveCommonLoopBannerEventObserverProtocol

- (void)viewWillDisplayItem:(XYLiveLoopBannerBusinessType)type view:(nonnull UIView *)view {
    if (XYLiveLoopBannerBusinessTypeActivity == type) {
        if (self.loopView.viewList.count > 1) {
            [self.loopView handleManualScrollWithIndex:0 animated:NO];
            [self.bannerService stopLoop];
            [self.loopView startLoop];
        } else {
            [self.loopView reloadListView];
        }
    }
}

#pragma mark - Privates

- (void)setupEventObserver {
    // 开播打散事件监听
    [[self.depend disperseEventService] registerListener:self];
    
    /// 容器事件监听
    [self.bannerService registerEventObserver:self];
    
    // Banner刷新事件监听
    __weak typeof(self) wself = self;
    [[self.depend imService] im_registerWithType:kXYLiveVariousRightActivityAreaIMKey key:NSStringFromClass(self.class) completionHandler:^(NSString * _Nonnull _, XYLiveCodableModel * _Nullable __, NSDictionary<NSString *,id> * _Nonnull rawData) {
        XYExecuteOnMainQueue(^{
            // 获取全量数据
            NSArray<XYLiveRightActivityAreaModel *> *bizModelArray = [NSArray xy_modelArrayWithClass:XYLiveRightActivityAreaModel.class json:rawData[@"banners"]];
            [wself handleActivityBizModelArray:bizModelArray];
        });
    }];
}

- (void)handleActivityBizModelArray:(NSArray<XYLiveRightActivityAreaModel *> *)bizModelArray {
    // 过滤Banner数据，校验刷新
    NSArray<XYLiveRightActivityAreaModel *> *bannerArray = [bizModelArray xy_filter:^BOOL(XYLiveRightActivityAreaModel * _Nonnull obj, NSUInteger _) {
        return [obj.bizType isEqualToString:XYLivePushRightActivityBizTypeBanner];
    }];
    
    BOOL needReload = [self needReloadBizData:bannerArray];
    AlphaLog(AlphaLogTag.Banner, [NSString stringWithFormat:@"v2 host banner receive data needReload %@", @(needReload)]);
    if (needReload) {
        self.bannerArray = bannerArray;
        
        NSMutableArray<UIView *> *itemViewArray = [[NSMutableArray alloc] init];
        for (XYLiveRightActivityAreaModel *model in bannerArray) {
            XYLiveActivityBannerModel *bannerModel = SAFE_CAST_CLASS(model.bizDataModel, XYLiveActivityBannerModel);
            if (bannerModel) {
                XYLivePushBannerItemView *bannerItemView = [self buildBannerItemViewWithInfo:bannerModel];
                [itemViewArray addObject:bannerItemView];
            }
        }
        
        [self.loopView updateViewList:itemViewArray];
        
        if (self.loopView.viewList.count > 0) {
            [self.bannerService updateViews:@[self.loopView] withBizType:XYLiveLoopBannerBusinessTypeActivity];
        } else {
            [self.bannerService updateViews:@[] withBizType:XYLiveLoopBannerBusinessTypeActivity];
        }
    }
}

- (BOOL)needReloadBizData:(NSArray<XYLiveRightActivityAreaModel *> *)modelArray {
    if (self.bannerArray.count != modelArray.count) {
        return YES;
    }
    
    for (NSUInteger index = 0; index < modelArray.count; index++) {
        if (index >= self.bannerArray.count) {
            return YES;
        }
        
        // 比对新旧数据，判断是否需要全量刷新
        XYLiveRightActivityAreaModel *oldModel = self.bannerArray[index];
        XYLiveRightActivityAreaModel *newModel = modelArray[index];
        
        XYLiveActivityBannerModel *oldBannerModel = SAFE_CAST_CLASS(oldModel.bizDataModel, XYLiveActivityBannerModel);
        XYLiveActivityBannerModel *newBannerModel = SAFE_CAST_CLASS(newModel.bizDataModel, XYLiveActivityBannerModel);
    
        if ([self needReloadCurrentBanner:oldBannerModel updateBanner:newBannerModel]) {
            return YES;
        }
    }
    
    return NO;
}

- (BOOL)needReloadCurrentBanner:(XYLiveActivityBannerModel *)curBanner updateBanner:(XYLiveActivityBannerModel *)updateBanner {
    if (updateBanner.bannerId.length > 0 && ![curBanner.bannerId isEqualToString:updateBanner.bannerId]) {
        return YES;
    }
    curBanner.title = updateBanner.title;
    curBanner.desc = updateBanner.desc;
    curBanner.bgUrl = updateBanner.bgUrl;
    curBanner.iconUrl = updateBanner.iconUrl;
    curBanner.linkUrl = updateBanner.linkUrl;
    return NO;
}

- (void)openDetailBannerWithInfo:(XYLiveActivityBannerModel *)model {
    if (model.linkUrl.length > 0) {
        XYExecuteOnMainQueue(^{
            [XYLiveManagerSharedInstance.actionRouteManager performWith:model.linkUrl];
        });
    } else {
        AlphaLog(AlphaLogTag.Banner, @"host click, but banner null");
    }
}

- (XYLivePushBannerItemView *)buildBannerItemViewWithInfo:(XYLiveActivityBannerModel *)bannerInfo {
    XYLivePushBannerItemView *bannerView = [[XYLivePushBannerItemView alloc] initWithBannerInfo:bannerInfo];
    __weak typeof(self) weakSelf = self;
    bannerView.bannerItemClick = ^(XYLiveActivityBannerModel * _Nullable bannerInfo) {
        [weakSelf openDetailBannerWithInfo:bannerInfo];
    };

    return bannerView;
}

#pragma mark - Getters

- (id<XYLiveCommonLoopBannerProtocol>)bannerService {
    return [self.depend bannerService];
}

- (XYLiveCommonLoopListView *)loopView {
    if (!_loopView) {
        _loopView = [[XYLiveCommonLoopListView alloc] initWithVerticalScrolling:YES];
        _loopView.loopInterval = 3;
        _loopView.isHiddenPageControl = NO;
        
        __weak typeof(self) wself = self;
        _loopView.loopCompletedCallback = ^{// 完成一轮循环后，暂停自己轮播，触发容器轮播
            if (wself.bannerService.elementCount > 1) {
                [wself.loopView stopLoop];
                [wself.bannerService startLoop];
            }
        };
        // 主播端无埋点，暂不实现
        //        _loopView.viewWillDisplayCallback = ^(UIView * _Nonnull, id _Nullable, NSInteger) {
        //
        //        }
    }
    
    return _loopView;
}

@end
