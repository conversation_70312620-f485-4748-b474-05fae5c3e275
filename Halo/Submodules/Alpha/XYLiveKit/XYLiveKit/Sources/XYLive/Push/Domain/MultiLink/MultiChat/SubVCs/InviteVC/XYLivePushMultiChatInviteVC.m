//
//  XYLivePushMultiChatInviteVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/2/14.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatInviteVC.h"
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveKit/XYLiveMultiChatInviteAuthPanelVC.h>
#import <XYLiveKit/XYLivePushMultiChatInviteSettingPanelVC.h>
#import <XYLiveKit/XYLiveMultiChatInvitePanelVC.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
@interface XYLivePushMultiChatInviteVC()<XYLivePushMultiChatCoreModelListener>

@property (nonatomic, weak) UIViewController *containerVC;
@property (nonatomic, weak) XYLivePushMultiChatCoreModel *coreModel;
@property (nonatomic, weak) id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak) id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, weak) XYLiveNavigationChildController *invitePanelVC;
@property (nonatomic, weak) XYLiveNavigationChildController *inviteAuthPanelVC;
@property (nonatomic, weak) XYLiveNavigationChildController *settingPanelVC;

@end

@implementation XYLivePushMultiChatInviteVC

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                          coreModel:(XYLivePushMultiChatCoreModel *)coreModel
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _coreModel = coreModel;
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    // 注册监听
    [self.coreModel registerListener:self];
}

#pragma mark - Public

/// 展示邀请面板
- (void)showInvitePanelWithSource:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString * _Nullable)extraInfo {
    // 优先判断开关是否打开
    if (self.coreModel.isOpened) {
        // 展示邀请面板
        [self showInvitePanelVCWithSource:source extraInfo:extraInfo];
    } else {
        // 展示授权面板
        [self showInviteAuthPanelVCWithSource:source extraInfo:extraInfo];
    }
}

/// 定向邀请
- (void)inviteWithUserId:(NSString *)userId source:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString * _Nullable)extraInfo {
    [self.coreModel inviteWithUserId:userId source:source extraInfo:extraInfo];
}

#pragma mark - Private

// 展示无权限面板
- (void)showInviteAuthPanelVCWithSource:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString * _Nullable)extraInfo {
    XYLiveMultiChatInviteAuthPanelVC *panelVC = [[XYLiveMultiChatInviteAuthPanelVC alloc] initWithContainerVC:self.containerVC coreModel:self.coreModel];
    WS
    panelVC.openSwitchHandler = ^{
        SS
        // 拉起邀请
        [self showInvitePanelVCWithSource:source extraInfo:extraInfo];
    };
    XYLiveNavigationChildController *nav = [[XYLiveNavigationChildController alloc] initWithRootChildController:panelVC];
    nav.portraitHeight = ^CGFloat{
        return kXYLiveMultiChatInvitePanelHeight;
    };
    nav.needReachBottom = YES;
    [self.containerVC showPopBottomVC:nav];
    self.inviteAuthPanelVC = nav;
}

// 展示邀请面板
- (void)showInvitePanelVCWithSource:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString * _Nullable)extraInfo {
    XYLiveMultiChatInvitePanelVC *panelVC = [[XYLiveMultiChatInvitePanelVC alloc] initWithBizType:XYLiveMultiLinkBizTypeChat source:source extraInfo:extraInfo];
    WS
    panelVC.moveToIndexHandler = ^NSInteger{
        SS
        return self.coreModel.applyNum <= 0 ? 1 : 0;
    };
    panelVC.didTapSettingHandler = ^{
        SS
        [self showSettingPanelVC];
    };
    panelVC.showUserCardHandler = ^(XYLiveUserInfo * _Nonnull userInfo) {
        SS
        self.showUserCardHandler ? self.showUserCardHandler(userInfo) : nil;
    };
    panelVC.liveInfoServiceHandler = ^id<XYLiveInfoServiceProtocol> _Nonnull{
        SS
        return self.liveInfoService;
    };
    panelVC.multiLinkServiceHandler = ^id<XYLiveMultiLinkServiceProtocol> _Nonnull{
        SS
        return self.multiLinkService;
    };
    panelVC.hasGoodsHandler = ^BOOL{
        SS
        return self.coreModel.hasGoods;
    };
    XYLiveNavigationChildController *nav = [[XYLiveNavigationChildController alloc] initWithRootChildController:panelVC];
    nav.portraitHeight = ^CGFloat{
        return kXYLiveMultiChatInvitePanelHeight;
    };
    nav.needReachBottom = YES;
    [self.containerVC showPopBottomVC:nav];
    self.invitePanelVC = nav;
}

// 展示连麦设置面板
- (void)showSettingPanelVC {
    XYLivePushMultiChatInviteSettingPanelVC *panelVC = [[XYLivePushMultiChatInviteSettingPanelVC alloc] initWithContainerVC:self.containerVC liveInfoService:self.liveInfoService coreModel:self.coreModel];
    XYLiveNavigationChildController *nav = [[XYLiveNavigationChildController alloc] initWithRootChildController:panelVC];
    nav.portraitHeight = ^CGFloat{
        return kXYLiveMultiChatInvitePanelHeight;
    };
    nav.needReachBottom = YES;
    [self.containerVC showPopBottomVC:nav];
    self.settingPanelVC = nav;
}

#pragma mark - XYLivePushMultiChatCoreModelListener

- (void)onUpdateSwitchStatus:(BOOL)isOpened {
    if (!isOpened) {
        // 关闭邀请面板
        [self.containerVC dismissPopBottomVC:self.invitePanelVC dismissAnimated:NO dismissComplete:nil];
        // 关闭设置面板
        [self.containerVC dismissPopBottomVC:self.settingPanelVC dismissAnimated:YES dismissComplete:nil];
    } else {
        // 关闭授权面板
        [self.containerVC dismissPopBottomVC:self.inviteAuthPanelVC dismissAnimated:YES dismissComplete:nil];
    }
}

@end
