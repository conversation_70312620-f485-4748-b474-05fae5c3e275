//
//  LivePushMultiPKDepend.swift
//  XYLiveKit
//
//  Created by 大远 on 2025/4/24.
//  Copyright © 2025 XingIn. All rights reserved.
//

import Foundation
import XYLiveServiceProtocol

@objc(XYLivePushMultiPKDependProtocol)
public protocol LivePushMultiPKDependProtocol: NSObjectProtocol {
    
    // 直播间基础服务
    func liveInfoService() -> XYLiveInfoServiceProtocol?
    // 长连接服务
    func socketService() -> XYLiveIMDistributerServiceProtocol?
    // 多人互动服务
    func multiLinkService() -> XYLiveMultiLinkServiceProtocol?
    // 容器服务
    func adapterService() -> XYLivePushAdapterServiceProtocol?
    // 个人信息卡服务
    func userCardService() -> XYLivePushUserCardServiceProtocol?
    // 连主播服务
    func linkHostService() -> XYLiveLinkHostServiceProtocol?
    
}

@objc(XYLivePushMultiPKDepend)
@objcMembers
class LivePushMultiPKDepend: NSObject, LivePushMultiPKDependProtocol {
    
    private weak var provider: ServiceProvider?
    
    init(_ provider:ServiceProvider) {
        self.provider = provider
    }
    
    func liveInfoService() -> XYLiveInfoServiceProtocol? {
        return provider?.getService(XYLiveInfoServiceProtocol.self)
    }
    
    func socketService() -> XYLiveIMDistributerServiceProtocol? {
        return provider?.getService(XYLiveIMDistributerServiceProtocol.self)
    }
    
    func multiLinkService() -> XYLiveMultiLinkServiceProtocol? {
        return provider?.getService(XYLiveMultiLinkServiceProtocol.self)
    }
    
    func userCardService() -> XYLivePushUserCardServiceProtocol? {
        return provider?.getService(XYLivePushUserCardServiceProtocol.self)
    }
    
    func linkHostService() -> XYLiveLinkHostServiceProtocol? {
        return provider?.getService(XYLiveLinkHostServiceProtocol.self)
    }
    
    func adapterService() -> XYLivePushAdapterServiceProtocol? {
        return provider?.getService(XYLivePushAdapterServiceProtocol.self)
    }
}
