//
//  XYLivePushRoomViewController+Setup.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/7/3.
//  Copyright © 2023 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushRoomViewController.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLivePlayRoomEventService.h"
#import "XYLiveInfoService.h"
#import "XYLiveRtcCoreService.h"
#import "XYLivePlayingRenderContainerView.h"

@import XYLiveFoundation;

#import "XYLiveEnterConfig.h"
#import "XYLiveCountdownView.h"



@import XYConfigCenter;
@import XYAlphaUtils;

static NSInteger pushRetryCount = 10;

@implementation XYLivePushRoomViewController (Setup)

- (void)setup_analyticsDataSource {
    XYLiveAnalyticsDataSource *dataSource = XYLiveAnalyticsDataSource.sharedInstance;
    dataSource.roomID = XYLiveManagerSharedInstance.roomInfo.roomIDStr ?: @"";
    dataSource.hostID = XYLiveManagerSharedInstance.hostInfo.userID ?: @"";
}

- (void)setup_resetParameters {
    self.pushReconnChance = pushRetryCount;
    self.pushAudioCheckCountdown = pushAudioCheckInterval;
    self.decorateVM.shouldStopPush = self.shouldStopPush;
}

- (void)setup_BindViewAndVM {
    self.decorateView.viewModel = self.decorateVM;
    __weak typeof(self) wself = self;
    self.decorateView.didTapOnDecorateView = ^(BOOL isDoubleClick, BOOL isLikeAction, CGPoint tapPoint) {
        if (!isDoubleClick && !isLikeAction) {
            CGPoint point = [wself.view convertPoint:tapPoint fromView:wself.decorateView];
            if ([XYAlphaSwitch cameraPreviewControlEnable]) {
                if ([wself.decorateView canControlFoucus]) {
                    [wself.decorateView showFocusViewWithTouchPoint:point];
                }
            } else {
                [XYLiveManagerSharedInstance.coreManager.media setCameraFocusWithTouchPoint:point];
            }
        }
    };
    self.decorateView.onLayoutBlock = ^(UIView * _Nonnull view) {
        [wself.hierarchyManager triggerLayoutSubviews:view];
    };
    [self.decorateView setupVMBinding];
}

- (void)setupOnInitWithRoomInfo:(XYLiveRoomInfo * _Nullable)roomInfo loginParam:(XYLiveLoginParam * _Nullable)loginParam rtcSession:(RTCChannelSession * _Nullable)rtcSession {
    self.needInitLivePush = YES;
    self.roomInfo = roomInfo;
    self.loginParam = loginParam;
    self.channelSession = rtcSession;
}

- (void)setupUI {
    self.view.backgroundColor = Theme.color.black;
    [self.view addSubview:self.blurBGView];
    
    if ([self shouldLoadstopPushBGView]) {
        [self.view addSubview:self.stopPushBGView];
        self.stopPushBGView.frame = self.view.bounds;
        self.stopPushBGView.hidden = !self.shouldStopPush && !self.shouldScreenPushing;
        if (self.bizPushStyle == XYLiveRoomBizPushStyleOBS) {
            self.stopPushBGView.type = XYLiveStopPushBackgroundTypePC;
        } else if (self.bizPushStyle == XYLiveRoomBizPushStyleGuideStation) {
            self.stopPushBGView.type = XYLiveStopPushBackgroundTypeGuideStation;
        } else if (self.shouldScreenPushing) {
            if (XYLivePushPrepParamCache.pushLandscape) {
                self.stopPushBGView.type = XYLiveStopPushBackgroundTypeGameLandscape;
            } else {
                self.stopPushBGView.type = XYLiveStopPushBackgroundTypeGamePortrait;
            }
        }
    }
    
    [self.view addSubview:self.playerRenderingView];
    [self.view addSubview:self.renderContainerView];
    [self.view addSubview:self.decorateView];
    self.playerRenderingView.frame = self.view.bounds;
    self.blurBGView.frame = self.view.bounds;
    self.renderContainerView.frame = self.view.bounds;
    self.decorateView.frame = self.view.bounds;
}

- (BOOL)shouldLoadstopPushBGView {
    return self.shouldStopPush || self.shouldScreenPushing;
}

- (void)setupBaseService {
    XYLiveInfoService *infoService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveInfoServiceProtocol)];
    infoService.isHost = YES;
    infoService.preRoomInfo = self.roomInfo;
}

- (void)refreshHostSetupInfo {
    [XYLiveManagerSharedInstance.groupEventHandler fetchHostSetupInfoWithRoomID:self.roomInfo.roomIDStr succ:^(XYLiveHostSetupInfo * _Nonnull setupInfo) {
        
        XYLivePlayRoomEventService *eventService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePlayRoomEventServiceProtocol)];
        [eventService.asXYLivePlayRoomEventServiceProtocol eventOnRefershHostInfo:setupInfo];
        [XYLiveEnterConfig updateGiftStatus: setupInfo.allowSendGiftSwitch == 0 ? XYLiveEnterConfigStatusOn : XYLiveEnterConfigStatusDisabled];
        // 更新语音播报全局状态，只消费总开关字段
        XYLiveManagerSharedInstance.isVoiceAnnounceEnable = setupInfo.voiceAnnouncementConfig.show;
        BOOL disableVoice = XYConfigCenter().justOnceBoolForKey(@"ios_voice_announce_disable", YES);
        if (disableVoice) {
            [self.decorateVM.announceVM setupConfigWithVoiceOn:NO];
        } else {
            [self.decorateVM.announceVM setupConfigWithVoiceOn:XYLiveManagerSharedInstance.isVoiceAnnounceEnable];
        }
    } fail:^(NSError * _Nonnull error) {
        XYLogDebug(@"alpha push room vc fetch host setup info error: %@", error);
    }];
}

- (void)showCountdownView:(void(^)(void))completion {
    if ([XYLiveConfigCenter pushSupportCountdownWithContentType:self.roomInfo.contentType]) {
        XYLiveCountdownView *cdView = [XYLiveCountdownView new];
        cdView.frame = self.view.bounds;
        __block UIWindow *countdownWindow = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
        countdownWindow.userInteractionEnabled = false;
        cdView.userInteractionEnabled = false;
        countdownWindow.windowLevel = UIWindowLevelAlert;
        countdownWindow.backgroundColor = [UIColor clearColor];
        countdownWindow.rootViewController = [[UIViewController alloc] init];
        countdownWindow.rootViewController.view.backgroundColor = [UIColor clearColor];
        [countdownWindow.rootViewController.view addSubview:cdView];
        [countdownWindow makeKeyAndVisible];
        
        __weak __typeof(cdView) wcdView = cdView;
        [cdView playWithCompletion:^{
            countdownWindow.hidden = YES;
            countdownWindow = nil;
            [wcdView removeFromSuperview];
            EXECUTE_BLOCK_SAFELY(completion);
        }];
    } else {
        EXECUTE_BLOCK_SAFELY(completion);
    }
}

@end
