//
//  XYLivePushRoomViewController+PlayerDelegate.m
//  XYLiveKit
//
//  Created by 周博立 on 2020/12/1.
//  Copyright © 2020 XingIn. All rights reserved.
//

#import "XYLivePushRoomViewController.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLivePlayingRenderContainerView.h"

@import XYLiveCore;
@import XYLivePlayManager;
@import XYConfigCenter;

@implementation XYLivePushRoomViewController (PlayerDelegate)

- (void)livePlayer_startHostPlayWithStreamInfo:(LiveStreamInfo * _Nullable)streamInfo {
    if ([self livePlayer_judgeShouldSupportPlayer]) {
        BOOL disableLog = XYConfigCenter().justOnceBoolForKey(@"ios_live_push_player_log_disable", YES);
        XYLivePlaybackStrategyBuilder *build = [[XYLivePlaybackStrategyBuilder alloc] init].url(streamInfo.pullInfo.URLs.flv).delegate(self).containerView(self.playerRenderingView).autoPlay(YES).source(@"live_push_room_host").disableLog(disableLog);
        XYLivePlaybackStrategyModel *model = [[XYLivePlaybackStrategyModel alloc] initWithBuilder:build];
        [XYLiveManagerSharedInstance.coreManager createPlayerWithModel:model];
        [XYLiveManagerSharedInstance.coreManager play];
    }
}

- (void)livePlayer_stopHostPlay {
    if ([self livePlayer_judgeShouldSupportPlayer]) {
        [XYLiveManagerSharedInstance.coreManager stop];
    }
}

- (BOOL)livePlayer_judgeShouldSupportPlayer {
    return self.shouldStopPush && XYConfigCenter().boolForKey(@"ios_live_host_player_support", NO);
}

#pragma mark - XYLivePlayerEventDelegate

- (void)livePlayer:(id<XYLivePlaybackDelegate>)player didRecvFirstIFrame:(NSDictionary *)param {
    if ([self livePlayer_judgeShouldSupportPlayer]) {
        self.playerRenderingView.frame = [XYLivePlayerSharedInstance.playRenderer renderingFrameWithParam:param];
        self.playerRenderingView.hidden = NO;
        self.stopPushBGView.hidden = YES;
    }
}

@end
