//
//  LivePushMultiLinkAdapterDepend.swift
//  XYLiveKit
//
//  Created by 大远 on 2025/6/12.
//  Copyright © 2025 XingIn. All rights reserved.
//

import Foundation
import XYLiveServiceProtocol

@objc(XYLivePushMultiLinkAdapterDependProtocol)
public protocol LivePushMultiLinkAdapterDependProtocol: NSObjectProtocol {
    // 直播间信息服务
    func liveInfoService() -> XYLiveInfoServiceProtocol?
    // 长链接服务
    func imDistributeService() -> XYLiveIMDistributerServiceProtocol?
    // 多人互动服务
    func multiLinkService() -> XYLiveMultiLinkServiceProtocol?
    // 直播间适配层
    func adapterService() -> XYLivePushAdapterServiceProtocol?
    // 旧框架连麦服务
    func linkService() -> LiveMultiLinkPushServiceProtocol?
}

@objc(XYLivePushMultiLinkAdapterDepend)
@objcMembers
class LivePushMultiLinkAdapterDepend: NSObject, LivePushMultiLinkAdapterDependProtocol {
    
    private weak var provider: ServiceProvider?
    
    init(_ provider: ServiceProvider) {
        self.provider = provider
    }
    
    public func liveInfoService() -> XYLiveInfoServiceProtocol? {
        return provider?.getService(XYLiveInfoServiceProtocol.self)
    }
    
    public func imDistributeService() -> XYLiveIMDistributerServiceProtocol? {
        return provider?.getService(XYLiveIMDistributerServiceProtocol.self)
    }
    
    public func multiLinkService() -> XYLiveMultiLinkServiceProtocol? {
        return provider?.getService(XYLiveMultiLinkServiceProtocol.self)
    }
    
    public func adapterService() -> XYLivePushAdapterServiceProtocol? {
        return provider?.getService(XYLivePushAdapterServiceProtocol.self)
    }
    
    public func linkService() -> LiveMultiLinkPushServiceProtocol? {
        return provider?.getService(LiveMultiLinkPushServiceProtocol.self)
    }
}

