//
//  XYLiveMultiPKViewModel.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/31.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveIMDistributerServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveServiceProtocol/XYLiveMultiPKConsts.h>
#import <XYLiveKit/XYLiveMultiPKModel.h>

NS_ASSUME_NONNULL_BEGIN

@protocol XYLiveMultiPKDelegate <NSObject>

@optional
// PK开始
- (void)onPKStart:(XYLiveMultiLinkBizType)bizType pkInfo:(XYLiveMultiPKModel *)pkInfo;

// 房间信息变更
- (void)onPKUpdateRoomInfo:(XYLiveMultiLinkRoomInfo *)roomInfo;

// PK状态变化
- (void)onPKUpdateState:(XYLiveMultiPKState)state pkInfo:(XYLiveMultiPKModel *)pkInfo;

// PK战队信息更新
- (void)onPKUpdateTeamInfo:(XYLiveMultiPKTeamInfo *)leftTeamInfo rightTeamInfo:(XYLiveMultiPKTeamInfo *)rightTeamInfo;

// PK倒计时
- (void)onPKTrickCountDown:(NSInteger)interval pkInfo:(XYLiveMultiPKModel *)pkInfo;

// PK结果变化
- (void)onPKUpdateResult:(XYLiveMultiPKResult)result pkInfo:(XYLiveMultiPKModel *)pkInfo;

// PK结束
- (void)onPKEnd:(XYLiveMultiLinkBizType)bizType;

@end

@interface XYLiveMultiPKViewModel : NSObject<XYLiveMultiLinkListener>

// 代理
@property (nonatomic, weak) id<XYLiveMultiPKDelegate> delegate;
// 是否主播端
@property (nonatomic, assign, readonly) BOOL isHost;
// 是否处于PK中
@property (nonatomic, assign, readonly) BOOL isPKing;
// 子玩法类型
@property (nonatomic, assign, readonly) XYLiveMultiLinkBizType bizType;
// 渲染区域
@property (nonatomic, assign, readonly) CGRect renderAreaFrame;
// PK状态
@property (nonatomic, assign, readonly) XYLiveMultiPKState state;
// PK结果
@property (nonatomic, assign, readonly) XYLiveMultiPKResult result;
// 我方战队信息
@property (nonatomic, strong, readonly) XYLiveMultiPKTeamInfo *leftTeamInfo;
// 对方战队信息
@property (nonatomic, strong, readonly) XYLiveMultiPKTeamInfo *rightTeamInfo;

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                          socketService:(id<XYLiveIMDistributerServiceProtocol>)socketService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

// 结束连接
- (void)stopLink;

// 再来一局
- (void)startPKAgain;

// 结束PK
- (void)stopPK;

// 静音
- (void)muteSpeakerWithUserId:(NSString *)userId isMute:(BOOL)isMute completion:(void(^_Nullable)(NSError *error))completion;

@end

NS_ASSUME_NONNULL_END
