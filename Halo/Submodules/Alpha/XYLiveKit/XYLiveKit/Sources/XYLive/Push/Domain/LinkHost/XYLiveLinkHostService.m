//
//  XYLiveLinkHostService.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/18.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostService.h"

@interface XYLiveLinkHostService()

@property (nonatomic, weak) XYLiveLinkHostController *target;

@end

@implementation XYLiveLinkHostService

- (void)bindTarget:(XYLiveLinkHostController *)target {
    _target = target;
}

#pragma mark - XYLiveLinkHostServiceProtocol

- (void)updateLayoutType:(XYLiveMultiLinkLayoutType)layoutType completion:(void (^)(NSError * _Nonnull))completion {
    [self.target updateLayoutType:layoutType completion:completion];
}

- (void)showLinkHostSettingPanel {
    [self.target showLinkHostSettingPanel];
}

@end
