//
//  XYLiveMultiChatApplyListViewModel.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveKit/XYLiveMultiChatApplyListModel.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiChatApplyListViewModel : NSObject

// 申请列表数据源
@property (nonatomic, copy, readonly) NSArray<XYLiveMultiChatApplyListItem *> *listItems;
// 排序数据源
@property (nonatomic, copy, readonly) NSArray<XYLiveMultiChatApplyListSortItem *> *sortListItems;
// 排序方式
@property (nonatomic, copy, readonly) NSString *sortType;

// 请求数据
- (void)requestListDataWithRoomId:(NSString *)roomId
                         sortType:(NSString *)sortType
                       completion:(void(^)(XYLiveMultiChatApplyListModel *listModel, NSError *error))completion;

// 删除数据
- (void)deleteItemWithUserId:(NSString *)userId;

// 更新交互状态
- (void)updateItemStatusWithUserId:(NSString *)userId disable:(BOOL)disable;

@end

NS_ASSUME_NONNULL_END
