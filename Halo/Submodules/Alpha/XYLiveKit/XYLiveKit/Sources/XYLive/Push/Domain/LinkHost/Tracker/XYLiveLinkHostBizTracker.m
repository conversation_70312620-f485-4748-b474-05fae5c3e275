//
//  XYLiveLinkHostBizTracker.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/30.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostBizTracker.h"
#import <XYSessionManager/XYSessionManager.h>

@implementation XYLiveLinkHostBizTracker

/* 直播开播页/连主播-随机连线/点击 */
+ (void)eventActionId80508WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80508)/* 直播开播页/连主播-随机连线/点击 */._igoto(1)._mid(47932)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/连主播-随机PK/点击 */
+ (void)eventActionId80509WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80509)/* 直播开播页/连主播-随机PK/点击 */._igoto(1)._mid(47933)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/连主播-和TA进行PK选项/点击 */
+ (void)eventActionId80510WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80510)/* 直播开播页/连主播-和TA进行PK选项/点击 */._igoto(1)._mid(47934)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/连主播-邀请按钮/点击 */
+ (void)eventActionId80511WithRoomId:(NSString *)roomId channelTabName:(NSString *)channelTabName index:(NSInteger)index inviteeUserId:(NSString *)inviteeUserId hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._channelTabName(channelTabName)._safeobjp((int32_t)index)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80511)/* 直播开播页/连主播-邀请按钮/点击 */._igoto(1)._mid(47935)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")._targetAnchorId(inviteeUserId)
     send];
}

/* 直播开播页/连主播-搜索/点击 */
+ (void)eventActionId80512WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80512)/* 直播开播页/连主播-搜索/点击 */._igoto(1)._mid(47936)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/连主播-设置/点击 */
+ (void)eventActionId80513WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80513)/* 直播开播页/连主播-设置/点击 */._igoto(1)._mid(47937)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/主播连线面板/曝光 */
+ (void)eventActionId80528WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Impression)._pid(80528)/* 直播开播页/主播连线面板/曝光 */._igoto(2)._mid(48096)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/连线设置-布局/点击 */
+ (void)eventActionId80525WithRoomId:(NSString *)roomId channelTabName:(NSString *)channelTabName hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._channelTabName(channelTabName)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80525)/* 直播开播页/连线设置-布局/点击 */._igoto(1)._mid(47949)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/连线设置-接受连线邀请/点击 */
+ (void)eventActionId80526WithRoomId:(NSString *)roomId status:(NSString *)status hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._status(status)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80526)/* 直播开播页/连线设置-接受连线邀请/点击 */._igoto(1)._mid(47950)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/连线设置-接受PK邀请/点击 */
+ (void)eventActionId80527WithRoomId:(NSString *)roomId status:(NSString *)status hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._status(status)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80527)/* 直播开播页/连线设置-接受PK邀请/点击 */._igoto(1)._mid(47951)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/连线设置-连线范围/点击 */
+ (void)eventActionId80532WithRoomId:(NSString *)roomId channelTabName:(NSString *)channelTabName channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._channelTabName(channelTabName)._channelTabType(channelTabType)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80532)/* 直播开播页/连线设置-连线范围/点击 */._igoto(1)._mid(48099)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

@end
