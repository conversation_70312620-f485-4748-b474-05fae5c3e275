//
//  XYLivePushMultiChatOperateVC.h
//  XYLiveKit
//
//  Created by 大远 on 2025/2/20.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveKit/XYLivePushMultiChatCoreModel.h>
#import <XYLiveKit/XYLivePushMultiChatConsts.h>
@protocol XYLiveMultiLinkPushServiceProtocol;
@class XYLiveOnmicUserModel;

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiChatOperateVC : XYViewController

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                          coreModel:(XYLivePushMultiChatCoreModel *)coreModel
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                        linkService:(id<XYLiveMultiLinkPushServiceProtocol>)linkService;


/// 展示操作面板
- (void)showOperatePanelWithUserInfo:(XYLiveOnmicUserModel *)userInfo;

@end

NS_ASSUME_NONNULL_END
