//
//  XYLiveLinkHostSettingPanelVC.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/20.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveMultiLinkSettingBasePanelVC.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLivePushMultiLineServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLivePushMultiPKServiceProtocol.h>
#import <XYLiveKit/XYLiveLinkHostViewModel.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveLinkHostSettingPanelVC : XYLiveMultiLinkSettingBasePanelVC

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                     multiPKService:(id<XYLivePushMultiPKServiceProtocol>)multiPKService
                          viewModel:(XYLiveLinkHostViewModel *)viewModel;

@end

NS_ASSUME_NONNULL_END
