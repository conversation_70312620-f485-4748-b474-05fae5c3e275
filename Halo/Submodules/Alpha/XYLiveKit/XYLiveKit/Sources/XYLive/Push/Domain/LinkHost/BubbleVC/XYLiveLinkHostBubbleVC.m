//
//  XYLiveLinkHostBubbleVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/5.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostBubbleVC.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveUIKit/XYLiveCommonBubbleView.h>
#import <XYLiveKit/XYLivePushDecorateView.h>
#import "XYLiveLinkHostBubbleVM.h"

@interface XYLiveLinkHostBubbleVC()

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLivePushAdapterServiceProtocol> adapterService;
@property (nonatomic, strong) XYLiveLinkHostBubbleVM *viewModel;

@end

@implementation XYLiveLinkHostBubbleVC

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                     adapterService:(id<XYLivePushAdapterServiceProtocol>)adapterService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _liveInfoService = liveInfoService;
        _adapterService = adapterService;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    // 展示PK功能迁移气泡
    [self showPKBubbleIfNeed];
}

// 更新连接状态
- (void)updateLinkState:(XYLiveLinkHostState)state {
    // 判断是否处于连线中
    if (state == XYLiveLinkHostStateLine) {
        // 展示玩法切换气泡
        [self showSwitchBizBubbleIfNeed];
    }
}

#pragma mark - Private

// 展示PK引导气泡
- (void)showPKBubbleIfNeed {
    // 判断是否允许展示
    if ([self.viewModel enableBubbleWithType:XYLiveLinkHostBubbleTypePK]) {
        // 展示引导气泡
        XYLiveLinkHostBubbleModel *model = [self.viewModel bubbleModelWithType:XYLiveLinkHostBubbleTypePK];
        [self showBubbleViewWithTitle:model.text duration:model.duration];
        // 更新缓存
        [self.viewModel disableBubbleWithType:XYLiveLinkHostBubbleTypePK];
    }
}

// 展示玩法切换引导气泡
- (void)showSwitchBizBubbleIfNeed {
    // 判断是否允许展示
    if ([self.viewModel enableBubbleWithType:XYLiveLinkHostBubbleTypeSwitchBiz]) {
        // 展示引导气泡
        XYLiveLinkHostBubbleModel *model = [self.viewModel bubbleModelWithType:XYLiveLinkHostBubbleTypeSwitchBiz];
        [self showBubbleViewWithTitle:model.text duration:model.duration];
        // 更新缓存
        [self.viewModel disableBubbleWithType:XYLiveLinkHostBubbleTypeSwitchBiz];
    }
}

// 展示气泡视图
- (void)showBubbleViewWithTitle:(NSString *)title duration:(NSTimeInterval)duration {
    // 参考视图
    UIView *refView = (UIView *)self.adapterService.decorateView.bottomView.linkHostButton;
    // 容器视图
    UIView *containerView = self.containerVC.view;
    // 合法性校验
    if (!refView || !containerView) {
        [XYLogCollector xyLiveLogTag:kXYLiveLinkHostModuleName content:@"refView or conatinerView is nil, cannot show bubble view."];
        return;
    }
    XYLiveCommonBubbleView *tipView = [XYLiveCommonBubbleView new];
    tipView.titleColor = [XYLiveTokenColor alwaysDarkTitle];
    tipView.bgColorArray = @[XYLiveTokenColor.alwaysLightFill5];
    tipView.numberOfLines = 1;
    tipView.radius = 8;
    tipView.title = title;
    tipView.showDuration = duration <= 0 ? 3 : duration;
    tipView.bubbleInset = UIEdgeInsetsMake(7, 12, 7, 12);
    [tipView showInView:containerView targetView:refView arrowStyle:XYLiveCommonBubbleViewArrowStyleDown];
}
    

#pragma mark - Lazy

- (XYLiveLinkHostBubbleVM *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[XYLiveLinkHostBubbleVM alloc] init];
    }
    return _viewModel;
}

@end
