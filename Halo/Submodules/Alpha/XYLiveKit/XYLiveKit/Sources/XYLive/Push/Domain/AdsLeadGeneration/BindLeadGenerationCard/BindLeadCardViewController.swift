//
//  BindLeadCardViewController.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/4/25.
//  Copyright © 2025 XingIn. All rights reserved.
//

import Foundation
import XYLiveUIKit
import XYAlertCenter

@objcMembers
@objc(XYBindLeadCardViewController)
public class BindLeadCardViewController: UIViewController {
    
    convenience init(completionHandler: ((Int64?) -> Void)?) {
        self.init()
        // 请求成功后回调
        self.completionHandler = completionHandler
    }
    private lazy var bulletManager = PushLeadsBulletManager.shared
    private lazy var attachCardReuqestService = AdsAttachLeadCardServiceModel()
    private var completionHandler: ((Int64?) -> Void)?
    var roomId: String = ""
    var selectedTab = 0
    var isLivingTime = false
    var isAllowSelectNone = false
    var pageTitle: String?
    var originFormMsgDataSource = [AdsLeadBindCardModel]()
    var originConsultDataSource = [AdsLeadBindCardModel]()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .ReDs.background.light()
        return view
    }()
    
    private lazy var pageVC: BindLeadCardPageViewController = {
        let pageVC = BindLeadCardPageViewController(selectedTab: self.selectedTab, isLivingTime: self.isLivingTime, formMsgDatasource: self.originFormMsgDataSource, consultDatasource: self.originConsultDataSource) { [weak self] in
            self?.bottomView.isHidden = false
        }
        pageVC.bottomBarUpdateIfNeededAction = { [weak self] shouldShow, shouldEnableCompleteBtn, shouldShowBottomBar, cardType in
            self?.bottomView.updateBottomBar(shouldShowAllSelectBtn: shouldShow, shouldEnableCompleteBtn: shouldEnableCompleteBtn, shouldShowBottomBar:shouldShowBottomBar, cardType: cardType)
        }
        pageVC.bottomBarChangedHandler = { [weak self] selectedCount, limitCount, cardType in
            self?.bottomView.updateBottomBar(limitCount: limitCount, selectedCount: selectedCount, cardType: cardType)
        }

        return pageVC
    }()
    
    private lazy var headerView: BindLeadCardTopView = {
        let header = BindLeadCardTopView()
        header.closeClosure = { [weak self] in
            guard let self = self else { return }
            self.exitViewController(animated: true)
        }
        header.backgroundColor = .ReDs.background.light()
        
        return header
    }()
    
    private lazy var bottomView: BindLeadCardBottomView = {
        let bottomView = BindLeadCardBottomView(pageVC: self.pageVC, isAllowSelectNone: self.isAllowSelectNone) { [weak self] isSelected in
            self?.dealWithAllSelectItemAction(isSelected: isSelected)
        } completionHandler: { [weak self] in
            self?.dealWithCompletionItemAction()
        }
        bottomView.backgroundColor = .ReDs.background.light()
        
        return bottomView
    }()
    
    func dealWithAllSelectItemAction(isSelected: Bool) {
        self.pageVC.bindLeadCardFormMsgVC.leadCardDataSource.forEach { cardModel in
            cardModel.selected = isSelected
        }
        
        let selectedCount = isSelected ? self.pageVC.bindLeadCardFormMsgVC.leadCardDataSource.count : 0
        // limitCount 为当前的卡片总数
        self.bottomView.updateBottomBar(limitCount: self.pageVC.bindLeadCardFormMsgVC.leadCardDataSource.count, selectedCount: selectedCount, cardType: .formMsg)
        self.pageVC.bindLeadCardFormMsgVC.reloadData()
        self.pageVC.checkConsultCardWhenCardClicked()
    }
    
    private func getSelectedCardIds() -> [String] {
        // 获取表单留资中选中的卡片ID
        let formSelectedIds = pageVC.bindLeadCardFormMsgVC.leadCardDataSource
            .filter { $0.selected == true }
            .compactMap { $0.cardId }
            
        // 获取私信咨询中选中的卡片ID
        let consultSelectedIds = pageVC.bindLeadCardConsultVC.leadCardDataSource
            .filter { $0.selected == true }
            .compactMap { $0.cardId }
            
        // 合并两个数组
        return formSelectedIds + consultSelectedIds
    }
    
    private func getFormMsgCardIds() -> [String] {
        // 获取表单留资中选中的卡片ID
        let formSelectedIds = pageVC.bindLeadCardFormMsgVC.leadCardDataSource
            .filter { $0.selected == true }
            .compactMap { $0.cardId }

        return formSelectedIds
    }

    func dealWithCompletionItemAction() {
        let selectedCardIds = getSelectedCardIds()
        let formSelectedIds = getFormMsgCardIds()
        
        // 没选卡
        guard selectedCardIds.count > 0 else {
            
            XYAlert.createDefaultRedModal(title: AdsLeadGenerationTools.getI18NLanguage(from: "温馨提示"),
                                          plainDesc: AdsLeadGenerationTools.getI18NLanguage(from: "当前未选择任何线索卡片，是否确认继续"),
                                          mainActionTitle: AdsLeadGenerationTools.getI18NLanguage(from: "确认"),
                                          secondaryActionTitle: AdsLeadGenerationTools.getI18NLanguage(from: "取消")) { [weak self] isConfirmed in
                if isConfirmed {
                    self?.requestAdsLeadAttachCardList(selectedCardIds)
                }
            }.show()
            
            return
        }
        
        // 选超了
        guard formSelectedIds.count <= self.pageVC.bindLeadCardFormMsgVC.limitCount else {
            XYAlert.createDefaultRedModal(title: AdsLeadGenerationTools.getI18NLanguage(from: "温馨提示"),
                                          plainDesc: AdsLeadGenerationTools.getI18NLanguage(from: "当前选择线索卡片的数目已超可选上限"),
                                          mainActionTitle: AdsLeadGenerationTools.getI18NLanguage(from: "好的")).show()
            return
        }
        
        // 当前讲解卡不在选中卡里面
        if let currentCardItem = bulletManager.currentCardItem,
           let explainCardId = currentCardItem.cardId,
           currentCardItem.explaining == true,
           !selectedCardIds.contains(where: { $0 == explainCardId }) {
            XYAlert.createDefaultRedModal(title: AdsLeadGenerationTools.getI18NLanguage(from: "温馨提示"),
                                          plainDesc: AdsLeadGenerationTools.getI18NLanguage(from: "检测到取消绑定的卡片有讲解中的状态，确定要解绑吗？"),
                                          mainActionTitle: AdsLeadGenerationTools.getI18NLanguage(from: "确定"),
                                          secondaryActionTitle: AdsLeadGenerationTools.getI18NLanguage(from: "取消")) { [weak self] isConfirmed in
                if isConfirmed {
                    self?.bulletManager.stopExplainAdsLeads(leadsCardItem: self?.bulletManager.currentCardItem)
                    self?.requestAdsLeadAttachCardList(selectedCardIds)
                }
            }.show()
            
            return
        }
        
        self.requestAdsLeadAttachCardList(selectedCardIds)
    }
    
    private func requestAdsLeadAttachCardList(_ cardIdList: [String]) {
        let cardListString = cardIdList.joined(separator: ",")
        self.trackCompleteBtnClickReport(cardListString)
        
        self.attachCardReuqestService.requestAdsLeadAttachCardList(
            roomId: Int64(self.roomId) ?? 0,
            cradList: cardIdList,
            completion: { [weak self] isSuccess, cardCount, message in
                // 处理请求回调
                guard isSuccess == true else {
                    AdsLeadGenerationTools.showToast(message:message ?? "绑卡失败，请稍后重试")
                    return
                }
                
                if cardCount ?? 0 > 0 {
                    AdsLeadGenerationTools.showToast(message: "线索卡片绑定成功")
                }
                
                // 回调档前绑定的卡片数到预开播页面
                self?.completionHandler?(cardCount)
                self?.exitViewController(animated: true)
            }
        )
    }
            
    public override func viewDidLoad() {
        super.viewDidLoad()
        self.setupUI()
    }
    
    public override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        navigationController?.setNavigationBarHidden(true, animated: true)
    }
    
    public override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
    }
    
    public override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        headerView.live_updateMask(roundedRect: headerView.bounds,
                                   topLeftRadius: 12.0,
                                   topRightRadius: 12.0,
                                   bottomLeftRadius: 0.0,
                                   bottomRightRadius: 0.0)
    }
    
    func setupUI() {
        // 页面布局
        var safeBottomMargin: CGFloat = 0
        if #available(iOS 11, *) {
            if let safeAreaInsetsBottom = UIApplication.shared.keyWindow?.safeAreaInsets.bottom {
                safeBottomMargin = safeAreaInsetsBottom
            }
        }

        let topHeight = 44
        let ratio = 0.75
        let contentHeight = DeviceUtility.screenHeight * CGFloat(ratio)
        
        self.view.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.left.right.bottom.equalTo(self.view)
            make.height.equalTo(contentHeight)
        }
        contentView.addSubview(headerView)
        headerView.snp.makeConstraints { make in
            make.top.left.right.equalTo(self.contentView)
            make.height.equalTo(topHeight)
        }
        contentView.addSubview(bottomView)
        bottomView.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(56.0 + safeBottomMargin)
        }
        
        // 增加编辑页面的根控制器
        self.addChild(pageVC)
        contentView.addSubview(pageVC.view)
        pageVC.didMove(toParent: self)
        
        pageVC.view.snp.makeConstraints { make in
            make.top.equalTo(self.headerView.snp.bottom)
            make.left.right.equalTo(self.contentView)
            make.bottom.equalTo(self.bottomView.snp.top)
        }
    }
    
    func exitViewController(animated: Bool) {
        if self.presentingViewController != nil {
            self.dismiss(animated: animated, completion: nil)
            return
        }
        if let nav = self.navigationController {
            if nav.presentingViewController != nil {
                nav.dismiss(animated: animated, completion: nil)
                return
            }
            nav.popViewController(animated: animated)
            return
        }
        if self.parent != nil {
            self.willMove(toParent: nil)
            self.view.removeFromSuperview()
            self.removeFromParent()
            return
        }
    }
}

extension BindLeadCardViewController {
    func trackCompleteBtnClickReport(_ cardId: String) {
        XYAnalyticsOrganizer._
        .ads
            .cardId(cardId)
        .page
            .pageInstance(.livePreparePage)
        .event
            .action(.click)
            .pointId(79448 /* 直播准备页/B-选卡页-添加按钮点击/点击 */)
            .isGoto(1)
            .moduleId(47077)
        .send()
    }
}

