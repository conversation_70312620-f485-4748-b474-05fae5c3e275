//
//  XYLiveLinkHostResource.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/16.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostResource.h"

@implementation XYLiveLinkHostResource

- (instancetype)init {
    if (self = [super init]) {
        // 初始化默认配置
        _matchingLottie = @{
            @"title": @"匹配中",
            @"isLottie": @(YES),
            @"url": @"https://fe-video-qc.xhscdn.com/fe-platform-file/104101b831jbmf152j406crss5gt00000000000g6hb5b6",
            @"md5": @"0e2580a2a16dfac40ccf7bfd36441fb2",
            @"icon_url": @"",
            @"filename": @"random_match/lottie.json"
        };
        _normalImage = @{
            @"type": @"linkHost",
            @"title": @"连主播",
            @"disabled_url": @"https://picasso-static.xiaohongshu.com/fe-platform/b1456e3550575c5ca15d973d7ae001aa6eaa957a.png",
            @"url": @"https://picasso-static.xiaohongshu.com/fe-platform/b1456e3550575c5ca15d973d7ae001aa6eaa957a.png"
        };
        _pkingImage = @{
            @"type": @"linkHost",
            @"title": @"进行中",
            @"disabled_url": @"https://fe-platform.xhscdn.com/platform/104101l031ite4k8l1476crss5gt00000000000l2thq1m",
            @"url": @"https://fe-platform.xhscdn.com/platform/104101l031ite4k8l1476crss5gt00000000000l2thq1m"
        };
        _liningImage = @{
            @"type": @"linkHost",
            @"title": @"进行中",
            @"disabled_url": @"https://fe-platform.xhscdn.com/platform/104101l031ite4k7d1406crss5gt00000000000lg27seg",
            @"url": @"https://fe-platform.xhscdn.com/platform/104101l031ite4k7d1406crss5gt00000000000lg27seg"
        };
        _likePKingImage = @{
            @"type": @"linkHost",
            @"title": @"进行中",
            @"disabled_url": @"https://fe-platform.xhscdn.com/platform/104101l031ite4k6ahe06crss5gt00000000000gmtrh0m",
            @"url": @"https://fe-platform.xhscdn.com/platform/104101l031ite4k6ahe06crss5gt00000000000gmtrh0m"
        };
    }
    return self;
}

@end
