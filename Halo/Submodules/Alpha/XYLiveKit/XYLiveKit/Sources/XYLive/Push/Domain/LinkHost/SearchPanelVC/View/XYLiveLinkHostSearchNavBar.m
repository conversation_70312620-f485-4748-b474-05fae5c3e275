//
//  XYLiveLinkHostSearchNavBar.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/21.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostSearchNavBar.h"
#import "XYLiveLinkHostSearchBar.h"
#import <Masonry/Masonry.h>


@interface XYLiveLinkHostSearchNavBar()<XYSearchBarDelegate>

@property (nonatomic, strong) XYLiveLinkHostSearchBar *searchBar;

@end

@implementation XYLiveLinkHostSearchNavBar

- (instancetype)initWithFrame:(CGRect)frame{
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

// 取消键盘
- (void)resignFirstResponder {
    [self.searchBar resignFirstResponder];
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 创建搜索框
    [self setupSearchBar];
    
    // 布局
    [self.searchBar mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16);
        make.right.mas_equalTo(-16);
        make.centerY.equalTo(self);
        make.height.mas_equalTo(36);
    }];
}

- (void)setupSearchBar {
    XYLiveLinkHostSearchBar *searchBar = [[XYLiveLinkHostSearchBar alloc] init];
    searchBar.placeholder = @"搜索正在开播的主播";
    searchBar.delegate = self;
    [searchBar becomeFirstResponder];
    [self addSubview:searchBar];
    self.searchBar = searchBar;
}

#pragma mark - XYSearchBarDelegate

- (void)searchBarSearchButtonClicked:(XYSearchBar *)searchBar {
    if (searchBar.text.length) {
        // 取消键盘
        [searchBar resignFirstResponder];
    }
    // 执行事件回调
    self.didTapSearchHandler ? self.didTapSearchHandler(searchBar.text) : nil;
}

- (void)searchBarCancelButtonClicked:(XYSearchBar *)searchBar {
    // 执行事件回调
    self.didTapCancelHandler ? self.didTapCancelHandler() : nil;
}

@end
