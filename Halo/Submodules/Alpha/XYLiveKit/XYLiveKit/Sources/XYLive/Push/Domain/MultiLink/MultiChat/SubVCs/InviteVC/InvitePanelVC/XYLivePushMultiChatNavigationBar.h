//
//  XYLivePushMultiChatNavigationBar.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/22.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <UIKit/UIKit.h>
@class XYTrackerEventContext;

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiChatNavigationBar : UIView

// 点击分享
@property (nonatomic, copy) void(^didTapShareHandler)(void);
// 点击分享埋点上报
@property (nonatomic, copy) XYTrackerEventContext *(^didTapShareTrackerHandler)(void);

// 点击设置
@property (nonatomic, copy) void(^didTapSettingHandler)(void);
// 点击设置埋点上报
@property (nonatomic, copy) XYTrackerEventContext *(^didTapSettingTrackerHandler)(void);

@end

NS_ASSUME_NONNULL_END
