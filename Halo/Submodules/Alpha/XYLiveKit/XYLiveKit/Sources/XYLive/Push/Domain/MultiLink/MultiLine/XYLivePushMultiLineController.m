//
//  XYLivePushMultiLineController.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/30.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLivePushMultiLineController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveKit/XYLiveLinkHostInviteeInfo.h>
#import <XYLiveKit/XYLiveMultiLineOptFuncVC.h>
#import <XYLiveKit/XYLivePushUserCardService.h>
#import <XYLiveKit/XYLivePushDecorateView+Private.h>
#import <XYLiveKit/XYLivePushRoomViewController.h>

@interface XYLivePushMultiLineController()<
XYLiveMultiLineInviteListener,
XYLiveMultiLineMatchListener,
XYLiveMultiLineCoreListener
>

@property (nonatomic, weak) UIViewController *containerVC;
@property (nonatomic, weak) id<XYLivePushMultiLineDependProtocol>depend;
@property (nonatomic, weak) XYLiveMultiLineOptFuncVC *optFuncVC;
@property (nonatomic, strong) NSHashTable<id<XYLivePushMultiLineListener>> *listeners;

@end

@implementation XYLivePushMultiLineController

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                             depend:(id<XYLivePushMultiLineDependProtocol>)depend {
    if (self = [super init]) {
        _containerVC = containerVC;
        _depend = depend;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    // 初始化子vc
    [self setupSubVCs];
    // 注册监听
    [self registerAllObservers];
    // 注册deeplink
    [self registerActionLink];
}

// 展示操作面板
- (void)showOptFuncPanelWithSource:(XYLiveMultiLineOptFuncPanelSource)source {
    [self.optFuncVC showOptFuncPanelWithInviteeInfoList:self.inviteVC.inviteeInfoList source:source];
}

- (void)registerListener:(id<XYLivePushMultiLineListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners addObject:listener];
}

- (void)unregisterListener:(id<XYLivePushMultiLineListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners removeObject:listener];
}

- (void)registerActionLink {
    WS
    // 连线能力聚合
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"emcee_link" action:^(NSString * _Nonnull key, NSDictionary<NSString *,id> * _Nonnull param) {
        // 判断是否命中新框架
        if (![XYAlphaSwitch enableNewMultiLinkFramework]) { return; }
        // 1.随机
        NSString *isRandom = param[@"random"];
        if ([isRandom isEqualToString:@"1"]) {
            // 发起匹配
            [weak_self.matchVC startMatchWithSource:XYLiveMultiLineMatchSourceDeepLink extraInfo:nil];
        } else {
            NSString *userId = param[@"user_id"];
            NSString *roomId = param[@"room_id"];
            // 优先判断是否跨房
            if ([weak_self.depend.liveInfoService.roomId isEqualToString:roomId]) { return; }
            // 合法性校验
            if (!userId || !roomId) { return; }
            XYLiveLinkHostInviteeInfo *inviteeInfo = [[XYLiveLinkHostInviteeInfo alloc] init];
            inviteeInfo.roomId = roomId;
            inviteeInfo.userId = userId;
            [weak_self.inviteVC inviteWithInviteeInfo:inviteeInfo source:XYLiveMultiLineInviteSourceDeepLink extraInfo:nil];
        }
    }];
    
    // 邀请主播连线
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"link_invite_host" action:^(NSString * _Nonnull key, NSDictionary<NSString *,id> * _Nonnull param) {
        // 判断是否命中新框架
        if (![XYAlphaSwitch enableNewMultiLinkFramework]) { return; }
        NSString *userId = param[@"to_user_id"];
        NSString *roomId = param[@"to_room_id"];
        // 合法性校验
        if (!userId || !roomId) { return; }
        XYLiveLinkHostInviteeInfo *inviteeInfo = [[XYLiveLinkHostInviteeInfo alloc] init];
        inviteeInfo.roomId = roomId;
        inviteeInfo.userId = userId;
        [weak_self.inviteVC inviteWithInviteeInfo:inviteeInfo source:XYLiveMultiLineInviteSourceDeepLink extraInfo:nil];
    }];
    
}

#pragma mark - XYLiveMultiLineInviteListener

- (void)onLineUpdateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    // 更新邀请状态
    [self.optFuncVC updateInviteState:state inviteeInfo:inviteeInfo];
    // 事件分发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLivePushMultiLineListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onLineUpdateInviteState:inviteeInfo:)]) {
            [obj onLineUpdateInviteState:state inviteeInfo:inviteeInfo];
        }
    }];
}

#pragma mark - XYLiveMultiLineMatchListener

- (void)onLineUpdateMatchState:(XYLiveMultiLinkMatchState)state {
    // 事件分发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLivePushMultiLineListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onLineUpdateMatchState:)]) {
            [obj onLineUpdateMatchState:state];
        }
    }];
}

#pragma mark - XYLiveMultiLineCoreListener

- (void)onLineStart {
    // 调整氛围包框层级
    [self updateFeelBorderViewHierarcyWithIsLinking:YES];
    // 更新调焦视图
    [self updateCameraControlViewStatusWithIsLinking:YES];
    // 事件分发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLivePushMultiLineListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onLineStart)]) {
            [obj onLineStart];
        }
    }];
}

- (void)onLineUpdateState:(XYLiveMultiLineState)state {
    // 事件分发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLivePushMultiLineListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onLineUpdateState:)]) {
            [obj onLineUpdateState:state];
        }
    }];
}

- (void)onLineUpdateRoomInfo:(XYLiveMultiLinkRoomInfo *)roomInfo {
    // 更新商卡状态
    [self updateRightBottomCardStatusWithIsLinking:YES];
    // 更新评论区
    [self updateCommentTopYWithIsLinking:YES];
    // 事件分发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLivePushMultiLineListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onLineUpdateRoomInfo:)]) {
            [obj onLineUpdateRoomInfo:roomInfo];
        }
    }];
}

- (void)onLineEnd {
    // 恢复氛围包框层级
    [self updateFeelBorderViewHierarcyWithIsLinking:NO];
    // 恢复调焦视图状态
    [self updateCameraControlViewStatusWithIsLinking:NO];
    // 恢复商卡状态
    [self updateRightBottomCardStatusWithIsLinking:NO];
    // 恢复评论区
    [self updateCommentTopYWithIsLinking:NO];
    // 关闭弹窗和面板
    [self hidelAllPanel];
    // 事件分发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLivePushMultiLineListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onLineEnd)]) {
            [obj onLineEnd];
        }
    }];
}

#pragma mark - 初始化子vc

- (void)setupSubVCs {
    // 初始化邀请vc
    [self setupInviteVC];
    // 初始化匹配vc
    [self setupMatchVC];
    // 初始化核心vc
    [self setupCoreVC];
    // 初始化操作面板vc
    [self setupOptFuncVC];
}

// 初始化邀请vc
- (void)setupInviteVC {
    XYLiveMultiLineInviteVC *inviteVC = [[XYLiveMultiLineInviteVC alloc] initWithContainerVC:self.containerVC
                                                                             liveInfoService:self.depend.liveInfoService
                                                                            multiLinkService:self.depend.multiLinkService];
    WS
    inviteVC.didTapUserHandler = ^(XYLiveUserInfo * _Nonnull userInfo) {
        SS
        [self showUserCardWithUserInfo:userInfo];
    };
    [self addViewController:inviteVC];
    self.inviteVC = inviteVC;
}

// 初始化匹配vc
- (void)setupMatchVC {
    XYLiveMultiLineMatchVC *matchVC = [[XYLiveMultiLineMatchVC alloc] initWithContainerVC:self.containerVC
                                                                          liveInfoService:self.depend.liveInfoService
                                                                         multiLinkService:self.depend.multiLinkService];
    [self addViewController:matchVC];
    self.matchVC = matchVC;
}

// 初始化操作功能vc
- (void)setupOptFuncVC {
    XYLiveMultiLineOptFuncVC *optFuncVC = [[XYLiveMultiLineOptFuncVC alloc] initWithContainerVC:self.containerVC liveInfoService:self.depend.liveInfoService multiLinkService:self.depend.multiLinkService linkHostService:self.depend.linkHostService];
    WS
    optFuncVC.didTapCancelHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull inviteeInfo) {
        SS
        [self.inviteVC cancelInviteWithUserId:inviteeInfo.userId];
    };
    optFuncVC.didTapInviteHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull inviteeInfo) {
        SS
        [self.inviteVC inviteWithInviteeInfo:inviteeInfo source:XYLiveMultiLineInviteSourceUnknown extraInfo:nil];
    };
    optFuncVC.didTapUserHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull inviteeInfo) {
        SS
        [self showUserCardWithUserInfo:inviteeInfo.userInfo];
    };
    optFuncVC.didTapEndLinkHandler = ^{
        SS
        [self.coreVC stopLink];
    };
    optFuncVC.didTapRematchHandler = ^{
        SS
        [self.matchVC startReMatchWithExtraInfo:nil];
    };
    [self addViewController:optFuncVC];
    self.optFuncVC = optFuncVC;
}

// 初始化核心VC
- (void)setupCoreVC {
    XYLiveMultiLineCoreVC *coreVC = [[XYLiveMultiLineCoreVC alloc] initWithContainerVC:self.containerVC
                                                                       liveInfoService:self.depend.liveInfoService
                                                                         socketService:self.depend.socketService
                                                                      multiLinkService:self.depend.multiLinkService];
    WS
    coreVC.didTapUserHandler = ^(XYLiveUserInfo * _Nonnull userInfo) {
        SS
        [self showUserCardWithUserInfo:userInfo];
    };
    coreVC.didTapSwitchLayoutHandler = ^(XYLiveMultiLinkLayoutType layoutType) {
        SS
        [self.depend.linkHostService updateLayoutType:layoutType completion:nil];
    };
    [self addViewController:coreVC];
    self.coreVC = coreVC;
}

#pragma mark - 注册监听

- (void)registerAllObservers {
    [self.inviteVC registerListener:self];
    [self.matchVC registerListener:self];
    [self.coreVC registerListener:self];
}

#pragma mark - Private

// 展示个人信息卡
- (void)showUserCardWithUserInfo:(XYLiveUserInfo *)userInfo {
    WS
    [self.depend.userCardService handleUserInfoFetchForUserCardWithUserID:userInfo.userID scene:@"onMicUser" completion:^(XYLiveUserInfo * _Nullable user, NSError * _Nullable error) {
        SS
        if (error) {
            [XYAlert live_showTextItemWithError:error];
        } else {
            // 更新roomId
            user.roomID = userInfo.roomID;
            // 拉起个人资料卡
            [self.depend.userCardService handleUserCardPresentingWithUser:user otherRole:XYLiveOtherRoleHostLiving sourceType:XYLiveInvokeSourceTypeLink];
        }
    }];
}

// 关闭全部面板和弹窗
- (void)hidelAllPanel {
    [self.optFuncVC hideAllPanel];
    [self.coreVC hideAllPanel];
}

// 更新评论区顶部高度
- (void)updateCommentTopYWithIsLinking:(BOOL)isLinking {
    [self.depend.adapterService.decorateView layoutLeftBottom];
}

// 更新焦距视图状态
- (void)updateCameraControlViewStatusWithIsLinking:(BOOL)isLinking {
    self.depend.adapterService.decorateView.cameraControlView.hidden = isLinking;
}

// 更新商卡状态
- (void)updateRightBottomCardStatusWithIsLinking:(BOOL)isLinking {
    if (isLinking && self.coreVC.layoutType == XYLiveMultiLinkLayoutTypeFloat) {
        [self.depend.adapterService.decorateView showRightBottomCardAboveSubhost];
    } else {
        [self.depend.adapterService.decorateView showRightBottomCardAtDefaultPosition];
    }
}

// 更新氛围包框
- (void)updateFeelBorderViewHierarcyWithIsLinking:(BOOL)isLinking {
    if (isLinking) {
        [self.depend.adapterService.pushViewController.feelBorderDomain adjustViewHierarchyAboveView:self.depend.multiLinkService.view];
    } else {
        [self.depend.adapterService.pushViewController.feelBorderDomain adjustViewHierarchyAboveView:nil];
    }
}

#pragma mark - Lazy

- (NSHashTable<id<XYLivePushMultiLineListener>> *)listeners {
    if (_listeners == nil) {
        _listeners = [NSHashTable weakObjectsHashTable];
    }
    return _listeners;
}

@end
