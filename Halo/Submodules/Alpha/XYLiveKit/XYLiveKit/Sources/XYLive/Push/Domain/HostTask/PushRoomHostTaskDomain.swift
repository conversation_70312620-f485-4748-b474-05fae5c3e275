//
//  PushRoomHostTaskDomain.swift
//  XYLiveKit
//
//  Created by gongyidemac on 2025/5/16.
//  Copyright © 2025 XingIn. All rights reserved.
//

import UIKit
import XYLiveServiceProtocol

@objc public protocol PushRoomHostTaskDependProtocol {
    /// 直播间控制器
    func pushAdapterService() -> XYLivePushAdapterServiceProtocol?
    /// 主播打散事件
    func disperseEventService() -> XYLivePushDisperseEventServiceProtocol?
    // 直播间基础信息
    func liveInfoService() -> XYLiveInfoServiceProtocol?
    /// banner 容器
    func bannerService() -> XYLiveCommonLoopBannerProtocol?
    /// 长链接订阅
    func imService() -> XYLiveIMDistributerServiceProtocol?
}

@objcMembers
class PushRoomHostTaskDomain: LiveBaseDomain<PushRoomHostTaskDependProtocol> {
    private lazy var service: XYLivePushHostTaskService = {
        let service: XYLivePushHostTaskService = XYLivePushHostTaskService()
        return service
    }()
    
    private var taskVC: XYLivePushHostTaskVC?
    
    override class func enableDomain() -> Bool {
        LiveConfigCenter.enableLoopBannerOpt()
    }
    
    override func doRegisterService(registery: ServiceRegistery) {
        registery.registerService(protocol: XYLivePushHostTaskServiceProtocol.self, service: self.service)
    }
    
    override func doInjectService(provider: ServiceProvider) -> PushRoomHostTaskDependProtocol {
        @objcMembers
        class PushHostTaskDepend: PushRoomHostTaskDependProtocol {
            let serviceProvider: ServiceProvider
            
            init(serviceProvider: ServiceProvider) {
                self.serviceProvider = serviceProvider
            }
            
            func pushAdapterService() -> (any XYLivePushAdapterServiceProtocol)? {
                return self.serviceProvider.getService(XYLivePushAdapterServiceProtocol.self)
            }
            
            func disperseEventService() -> (any XYLivePushDisperseEventServiceProtocol)? {
                return self.serviceProvider.getService(XYLivePushDisperseEventServiceProtocol.self)
            }
            
            func liveInfoService() -> (any XYLiveInfoServiceProtocol)? {
                return self.serviceProvider.getService(XYLiveInfoServiceProtocol.self)
            }
            
            func imService() -> (any XYLiveIMDistributerServiceProtocol)? {
                return self.serviceProvider.getService(XYLiveIMDistributerServiceProtocol.self)
            }
            
            func bannerService() -> (any XYLiveCommonLoopBannerProtocol)? {
                return serviceProvider.getService(XYLiveCommonLoopBannerProtocol.self)
            }
        }
        
        return PushHostTaskDepend(serviceProvider: provider)
    }
    
    override func didLoad() {
        super.didLoad()
        
        if let depend = self.depend {
            let vc = XYLivePushHostTaskVC(depend: depend)
            service.delegate = vc
            add(vc)
            taskVC = vc
        }
    }
}
