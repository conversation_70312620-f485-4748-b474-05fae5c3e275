//
//  LiveLinkHostDepend.swift
//  XYLiveKit
//
//  Created by 大远 on 2025/5/27.
//  Copyright © 2025 XingIn. All rights reserved.
//

import Foundation
import XYLiveServiceProtocol

@objc(XYLiveLinkHostDependProtocol)
public protocol LiveLinkHostDependProtocol: NSObjectProtocol {
    
    // 直播间基础服务
    func liveInfoService() -> XYLiveInfoServiceProtocol?
    // 长连接服务
    func socketService() -> XYLiveIMDistributerServiceProtocol?
    // 多人互动框架服务
    func multiLinkService() -> XYLiveMultiLinkServiceProtocol?
    // 多人连线服务
    func multiLineService() -> XYLivePushMultiLineServiceProtocol?
    // 多人PK服务
    func multiPKService() -> XYLivePushMultiPKServiceProtocol?
    // 容器服务
    func adapterService() -> XYLivePushAdapterServiceProtocol?
    // 用户信息卡服务
    func userCardService() -> XYLivePushUserCardServiceProtocol?
    
}

@objc(XYLiveLinkHostDepend)
@objcMembers
class LiveLinkHostDepend: NSObject, LiveLinkHostDependProtocol {
    
    private weak var provider: ServiceProvider?
    
    init(_ provider:ServiceProvider) {
        self.provider = provider
    }
    
    func liveInfoService() -> XYLiveInfoServiceProtocol? {
        return provider?.getService(XYLiveInfoServiceProtocol.self)
    }
    
    func socketService() -> XYLiveIMDistributerServiceProtocol? {
        return provider?.getService(XYLiveIMDistributerServiceProtocol.self)
    }
    
    func multiLinkService() -> XYLiveMultiLinkServiceProtocol? {
        return provider?.getService(XYLiveMultiLinkServiceProtocol.self)
    }
    
    func multiLineService() -> XYLivePushMultiLineServiceProtocol? {
        return provider?.getService(XYLivePushMultiLineServiceProtocol.self)
    }
    
    func multiPKService() -> XYLivePushMultiPKServiceProtocol? {
        return provider?.getService(XYLivePushMultiPKServiceProtocol.self)
    }
    
    func adapterService() -> XYLivePushAdapterServiceProtocol? {
        return provider?.getService(XYLivePushAdapterServiceProtocol.self)
    }
    
    func userCardService() -> XYLivePushUserCardServiceProtocol? {
        return provider?.getService(XYLivePushUserCardServiceProtocol.self)
    }
}
