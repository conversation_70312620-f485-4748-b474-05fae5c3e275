//
//  LiveAdsLeadBroadcastPageDomain.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/4/24.
//  Copyright © 2025 XingIn. All rights reserved.
//

import Foundation
import XYAlphaFusion
import XYLiveServiceProtocol

public protocol LiveAdsLeadBroadcastPageDependProtocol {
    func eventService() -> XYLivePlayRoomEventService?
}

@objcMembers
@objc(XYLiveAdsLeadBroadcastPageDomain)
public class LiveAdsLeadBroadcastPageDomain: LiveBaseDomain <LiveAdsLeadBroadcastPageDependProtocol>, LiveAdsBroadcastListPageProtocol, LiveRoomEventServiceProtocol {
    
    private lazy var cardListVC = LiveBroadcastCardListViewController()
    private lazy var serviceModel = AdsLeadCardClueAuthServiceModel()
    
    // 打开卡片列表页
    public func showAdsLeadsPageListVC() {
        cardListVC.showIn(viewController: self.hostViewController())
        self.track79458()
    }
    
    public func getBroadcastListPage() -> UIView {
        return cardListVC.view
    }
    
    // 注册服务
    public override func doRegisterService(registery: any ServiceRegistery) {
        registery.registerService(protocol: LiveAdsBroadcastListPageProtocol.self, service: self)
    }
    
    // 声明依赖服务
    public override func doInjectService(provider: any ServiceProvider) -> any LiveAdsLeadBroadcastPageDependProtocol {

        @objcMembers
        class LiveAdsLeadBroadcastPageDependProtocolDepend: NSObject, LiveAdsLeadBroadcastPageDependProtocol {

            weak var serviceProvider: ServiceProvider?

            init(serviceProvider: ServiceProvider) {
                self.serviceProvider = serviceProvider
            }
            
            func eventService() -> XYLivePlayRoomEventService? {
                return self.serviceProvider?.getService(XYLivePlayRoomEventService.self)
            }
        }
        
        return LiveAdsLeadBroadcastPageDependProtocolDepend(serviceProvider: provider)
    }
    
    public override class func enableDomain() -> Bool {
        return !ConfigCenter.shared.bool(forKey: "ios_live_leads_ads_pagelist_domain_forbidden", defalut: false)
    }
    
    public override func didLoad() {
        self.depend?.eventService()?.registerListener(self)
    }
}

extension LiveAdsLeadBroadcastPageDomain {
    // Track
    func baseTrackContext() -> XYTrackerEventContext {
        let noteID = XYLiveAnalyticsDataSource.sharedInstance().sourceNoteID
        let noteTrackID = XYLiveAnalyticsDataSource.sharedInstance().noteTrackID
        let preSource = XYLiveAnalyticsDataSource.sharedInstance().preSource
        let liveType = "Interaction"
        
        return XYLiveTrackBasePage.sharedInstance().hostAnalyticsOrganizer()
            .note
            .noteId(noteID)
            .trackId(noteTrackID)
            .page
            .pageInstance(.liveBroadcastPage)
            .live
            .liveType(liveType)
            .preSource(preSource)
            .event
    }
    
    func track79458() {
        baseTrackContext()
        .event
            .action(.impression)
            .pointId(79458 /* 直播开播页/B-卡片管理-浮层曝光/曝光 */)
            .isGoto(2)
            .moduleId(47087)
        .send()
    }
}
