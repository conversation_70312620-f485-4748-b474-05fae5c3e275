//
//  LiveAdsLeadBroadcastBulletCardCountDownProcessor.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/5/27.
//  Copyright © 2025 XingIn. All rights reserved.
//

import XYCanvas

@objcMembers
@objc(XYLiveAdsLeadBroadcastBulletCardCountDownProcessor)
class LiveAdsLeadBroadcastBulletCardCountDownProcessor: NSObject {

    weak var viewNode: XYCViewNode?
    private var timestamp = 15
    private var countDownCompletedHandler: (() -> Void)?

    convenience init(completion: (() -> Void)?) {
        self.init()
        self.countDownCompletedHandler = completion
    }

    func start(node: XYCViewNode?) {
        self.viewNode = node
        self.timestamp = 15
        startTimerIfNeeded()
    }

    private var weakTimer: XYWeakTimer?

    private func startTimerIfNeeded() {
        self.stopTimer()
        weakTimer = XYWeakTimer.scheduledTimer(
            withTimeInterval: 1.0,
            target: self,
            selector: #selector(timerTick),
            userInfo: nil,
            repeats: true
        )
        weakTimer?.add(to: RunLoop.main, forMode: .common)
    }

    private func stopTimer() {
        weakTimer?.invalidate()
        weakTimer = nil
    }

    @objc private func timerTick() {
        let currentTimestamp = timestamp
        if currentTimestamp <= 1 {
            self.countDownCompletedHandler?()
            stopTimer()
            return
        }

        timestamp = currentTimestamp - 1
        updateLabel()
    }

    private func updateLabel() {
        let timeString = String(timestamp)
        
        let countDownComponent = self.viewNode?.kittComponent?.findByTagName("host_popUp_timer") as? DslTextComponent
        countDownComponent?.attributedString = NSMutableAttributedString(string: timeString)
        countDownComponent?.textContent = timeString
        countDownComponent?.labelView?.text = timeString
        countDownComponent?.labelView?.sizeToFit()
        countDownComponent?.requestLayout()
    }

    deinit {
        stopTimer()
    }
}
