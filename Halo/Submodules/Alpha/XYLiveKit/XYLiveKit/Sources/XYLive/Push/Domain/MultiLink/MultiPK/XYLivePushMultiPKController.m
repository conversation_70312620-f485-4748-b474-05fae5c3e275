//
//  XYLivePushMultiPKController.m
//  XYLiveKit
//
//  Created by 大远 on 2025/4/24.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushMultiPKController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLiveKit/XYLiveMultiPKOptFuncVC.h>
#import <XYLiveKit/XYLivePushUserCardService.h>
#import <XYLiveKit/XYLivePushRoomViewController.h>
#import <XYLiveKit/XYLivePushDecorateView+Private.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>

@interface XYLivePushMultiPKController()<
XYLiveMultiPKInviteListener,
XYLiveMultiPKMatchListener,
XYLiveMultiPKCoreListener
>

@property (nonatomic, weak) UIViewController *containerVC;
@property (nonatomic, weak) id<XYLivePushMultiPKDependProtocol> depend;
@property (nonatomic, weak) XYLiveMultiPKOptFuncVC *optFuncVC;
@property (nonatomic, strong) NSHashTable<id<XYLivePushMultiPKListener>> *listeners;

@end

@implementation XYLivePushMultiPKController

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                             depend:(id<XYLivePushMultiPKDependProtocol>)depend {
    if (self = [super init]) {
        _containerVC = containerVC;
        _depend = depend;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    // 初始化子vc
    [self setupSubVCs];
    // 注册监听
    [self registerAllObservers];
}

// 展示操作面板
- (void)showOptFuncPanelWithSource:(XYLiveMultiPKOptFuncPanelSource)source {
    [self.optFuncVC showOptFuncPanelWithSource:source];
}

// 注册监听
- (void)registerListener:(id<XYLivePushMultiPKListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners addObject:listener];
}

// 取消注册监听
- (void)unregisterListener:(id<XYLivePushMultiPKListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners removeObject:listener];
}

#pragma mark - XYLiveMultiPKInviteListener

- (void)onPKUpdateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    // 事件转发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLivePushMultiPKListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onPKUpdateInviteState:inviteeInfo:)]) {
            [obj onPKUpdateInviteState:state inviteeInfo:inviteeInfo];
        }
    }];
}

#pragma mark - XYLiveMultiPKMatchListener

- (void)onPKUpdateMatchState:(XYLiveMultiLinkMatchState)state {
    // 事件转发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLivePushMultiPKListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onPKUpdateMatchState:)]) {
            [obj onPKUpdateMatchState:state];
        }
    }];
}

#pragma mark - XYLiveMultiPKCoreListener

- (void)onPKStart {
    // 关闭焦距视图
    [self updateCameraControlViewStatusWithIsLinking:YES];
    // 更新氛围包框视图
    [self updateFeelBorderViewHierarcyWithIsLinking:YES];
    // 更新商卡视图
    [self updateRightBottomCardStatusWithIsLinking:YES];
    // 恢复评论区高度
    [self updateCommentTopYWithIsLinking:YES];
    // 事件转发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLivePushMultiPKListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onPKStart)]) {
            [obj onPKStart];
        }
    }];
}

- (void)onPKUpdateState:(XYLiveMultiPKState)state {
    // 事件转发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLivePushMultiPKListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onPKUpdateState:)]) {
            [obj onPKUpdateState:state];
        }
    }];
}

- (void)onPKUpdateRoomInfo:(XYLiveMultiLinkRoomInfo *)roomInfo {
    // 事件转发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLivePushMultiPKListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onPKUpdateRoomInfo:)]) {
            [obj onPKUpdateRoomInfo:roomInfo];
        }
    }];
}

- (void)onPKEnd {
    // 恢复商卡状态
    [self updateRightBottomCardStatusWithIsLinking:NO];
    // 恢复焦距视图
    [self updateCameraControlViewStatusWithIsLinking:NO];
    // 恢复氛围包框视图
    [self updateFeelBorderViewHierarcyWithIsLinking:NO];
    // 恢复评论区高度
    [self updateCommentTopYWithIsLinking:NO];
    // 关闭面板和弹窗
    [self hideAllPanel];
    // 事件转发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLivePushMultiPKListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onPKEnd)]) {
            [obj onPKEnd];
        }
    }];
}

#pragma mark - 初始化子vc

- (void)setupSubVCs {
    // 初始化邀请vc
    [self setupInviteVC];
    // 初始化匹配vc
    [self setupMatchVC];
    // 初始化核心VC
    [self setupCoreVC];
    // 初始化操作面板vc
    [self setupOptFuncVC];
}

// 初始化邀请vc
- (void)setupInviteVC {
    XYLiveMultiPKInviteVC *inviteVC = [[XYLiveMultiPKInviteVC alloc] initWithContainerVC:self.containerVC liveInfoService:self.depend.liveInfoService multiLinkService:self.depend.multiLinkService];
    [self addViewController:inviteVC];
    self.inviteVC = inviteVC;
}

// 初始化匹配vc
- (void)setupMatchVC {
    XYLiveMultiPKMatchVC *matchVC = [[XYLiveMultiPKMatchVC alloc] initWithContainerVC:self.containerVC
                                                                      liveInfoService:self.depend.liveInfoService
                                                                     multiLinkService:self.depend.multiLinkService];
    [self addViewController:matchVC];
    self.matchVC = matchVC;
}

// 初始容器vc
- (void)setupCoreVC {
    XYLiveMultiPKCoreVC *coreVC = [[XYLiveMultiPKCoreVC alloc] initWithContainerVC:self.containerVC
                                                                   liveInfoService:self.depend.liveInfoService
                                                                     socketService:self.depend.socketService
                                                                  multiLinkService:self.depend.multiLinkService];
    WS
    coreVC.didTapReMatchHandler = ^{
        SS
        [self.matchVC startReMatchWithExtraInfo:nil];
    };
    coreVC.didTapUserHandler = ^(XYLiveUserInfo * _Nonnull userInfo) {
        SS
        [self showUserCardWithUserInfo:userInfo];
    };
    [self addViewController:coreVC];
    self.coreVC = coreVC;
}

// 初始化操作面板
- (void)setupOptFuncVC {
    XYLiveMultiPKOptFuncVC *optFuncVC = [[XYLiveMultiPKOptFuncVC alloc] initWithContainerVC:self.containerVC
                                                                            liveInfoService:self.depend.liveInfoService
                                                                           multiLinkService:self.depend.multiLinkService linkHostService:self.depend.linkHostService];
    WS
    optFuncVC.didTapUserHandler = ^(XYLiveUserInfo * _Nonnull userInfo) {
        SS
        [self showUserCardWithUserInfo:userInfo];
    };
    optFuncVC.didTapEndLinkHandler = ^{
        SS
        [self.coreVC stopLink];
    };
    optFuncVC.didTapRematchHandler = ^{
        SS
        [self.matchVC startReMatchWithExtraInfo:nil];
    };
    optFuncVC.didTapBizOptHandler = ^(XYLiveMultiLinkBizType bizType, XYLiveMultiLineBizOptType optType) {
        SS
        if (optType == XYLiveMultiLineBizOptTypeReOpen) {
            [self.coreVC startPKAgain];
        } else if (optType == XYLiveMultiLineBizOptTypeClose) {
            [self.coreVC stopPK];
        }
    };
    [self addViewController:optFuncVC];
    self.optFuncVC = optFuncVC;
}

#pragma mark - Private

// 注册监听
- (void)registerAllObservers {
    [self.inviteVC registerListener:self];
    [self.matchVC registerListener:self];
    [self.coreVC registerListener:self];
}

// 展示个人资料卡
- (void)showUserCardWithUserInfo:(XYLiveUserInfo *)userInfo {
    WS
    [self.depend.userCardService handleUserInfoFetchForUserCardWithUserID:userInfo.userID completion:^(XYLiveUserInfo * _Nullable user, NSError * _Nullable error) {
        SS
        if (error) {
            [XYAlert live_showTextItemWithError:error];
        } else {
            XYLiveOtherRole otherRole = XYLiveOtherRoleHostLiving;
            user.roomID = userInfo.roomID;
            // 查看观众信息卡，需要特殊处理
            if (userInfo.roomRole == XYLiveRoomRoleAudience) {
                otherRole = XYLiveOtherRoleNone;
                user.roomID = 0;
            }
            // 拉起个人资料卡
            [self.depend.userCardService handleUserCardPresentingWithUser:user otherRole:otherRole sourceType:XYLiveInvokeSourceTypeLink];
        }
    }];
}

// 关闭面板
- (void)hideAllPanel {
    [self.optFuncVC hideAllPanel];
}

// 更新评论区顶部高度
- (void)updateCommentTopYWithIsLinking:(BOOL)isLinking {
    [self.depend.adapterService.decorateView layoutLeftBottom];
}

// 更新焦距视图状态
- (void)updateCameraControlViewStatusWithIsLinking:(BOOL)isLinking {
    self.depend.adapterService.decorateView.cameraControlView.hidden = isLinking;
}

// 调整商卡状态
- (void)updateRightBottomCardStatusWithIsLinking:(BOOL)isLinking {
    [self.depend.adapterService.decorateView showRightBottomCardAtDefaultPosition];
}

// 更新氛围包框
- (void)updateFeelBorderViewHierarcyWithIsLinking:(BOOL)isLinking {
    if (isLinking) {
        [self.depend.adapterService.pushViewController.feelBorderDomain adjustViewHierarchyAboveView:self.depend.multiLinkService.view];
    } else {
        [self.depend.adapterService.pushViewController.feelBorderDomain adjustViewHierarchyAboveView:nil];
    }
}

#pragma mark - Lazy

- (NSHashTable<id<XYLivePushMultiPKListener>> *)listeners {
    if (_listeners == nil) {
        _listeners = [NSHashTable weakObjectsHashTable];
    }
    return _listeners;
}

@end
