//
//  PushActivityBannerDomain.swift
//  XYLiveKit
//
//  Created by gongyidemac on 2025/5/21.
//  Copyright © 2025 XingIn. All rights reserved.
//

import UIKit
import XYLiveServiceProtocol
import XYAlphaUtils

/*
 * 主播端活动Banner
 */
@objc public protocol PushActivityBannerDependProtocol {
    // 直播间控制器
    func pushAdapterService() -> XYLivePushAdapterServiceProtocol?
    /// 主播打散事件
    func disperseEventService() -> XYLivePushDisperseEventServiceProtocol?
    // 直播间基础信息
    func liveInfoService() -> XYLiveInfoServiceProtocol?
    /// banner 容器
    func bannerService() -> XYLiveCommonLoopBannerProtocol?
    /// 长链接订阅
    func imService() -> XYLiveIMDistributerServiceProtocol?
}

@objcMembers
class PushActivityBannerDomain: LiveBaseDomain<PushActivityBannerDependProtocol> {
    private var bannerVC: XYLivePushActivityBannerVC?
    
    override class func enableDomain() -> Bool {
        LiveConfigCenter.enableLoopBannerOpt()
    }
    
    override func doInjectService(provider: ServiceProvider) -> PushActivityBannerDependProtocol {
        @objcMembers
        class PushActivityBannerDepend: PushActivityBannerDependProtocol {
            let serviceProvider: ServiceProvider
            
            init(serviceProvider: ServiceProvider) {
                self.serviceProvider = serviceProvider
            }
            
            func pushAdapterService() -> (any XYLivePushAdapterServiceProtocol)? {
                return self.serviceProvider.getService(XYLivePushAdapterServiceProtocol.self)
            }
            
            func disperseEventService() -> (any XYLivePushDisperseEventServiceProtocol)? {
                return self.serviceProvider.getService(XYLivePushDisperseEventServiceProtocol.self)
            }
            
            func liveInfoService() -> (any XYLiveInfoServiceProtocol)? {
                return self.serviceProvider.getService(XYLiveInfoServiceProtocol.self)
            }
            
            func imService() -> XYLiveIMDistributerServiceProtocol? {
                return self.serviceProvider.getService(XYLiveIMDistributerServiceProtocol.self)
            }
            
            func bannerService() -> (any XYLiveCommonLoopBannerProtocol)? {
                return serviceProvider.getService(XYLiveCommonLoopBannerProtocol.self)
            }
        }
        
        return PushActivityBannerDepend(serviceProvider: provider)
    }
    
    override func didLoad() {
        super.didLoad()
        
        if let depend = self.depend {
            let vc = XYLivePushActivityBannerVC(depend: depend)
            add(vc)
            bannerVC = vc
        }
    }
}
