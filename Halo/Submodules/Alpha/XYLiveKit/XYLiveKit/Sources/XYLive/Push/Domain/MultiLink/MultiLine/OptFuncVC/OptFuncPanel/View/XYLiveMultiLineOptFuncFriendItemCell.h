//
//  XYLiveMultiLineOptFuncFriendItemCell.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/22.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <XYLiveKit/XYLiveMultiLineOptFuncGroupItem.h>
@class XYLiveLinkHostInviteeInfo;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiLineOptFuncFriendItemCell : UITableViewCell

// 点击添加
@property (nonatomic, copy) void(^didTapAddHandler)(void);
// 点击头像
@property (nonatomic, copy) void(^didTapUserHandler)(XYLiveLinkHostInviteeInfo *inviteeInfo);
// 点击取消
@property (nonatomic, copy) void(^didTapCancelHandler)(XYLiveLinkHostInviteeInfo *inviteeInfo);
// 更新添加按钮状态
@property (nonatomic, assign) BOOL disableAdd;

// 数据绑定
- (void)bindDataSource:(NSArray<XYLiveMultiLineOptFuncGroupItem *> *)dataSource indexPath:(NSIndexPath *)indexPath;

@end

NS_ASSUME_NONNULL_END
