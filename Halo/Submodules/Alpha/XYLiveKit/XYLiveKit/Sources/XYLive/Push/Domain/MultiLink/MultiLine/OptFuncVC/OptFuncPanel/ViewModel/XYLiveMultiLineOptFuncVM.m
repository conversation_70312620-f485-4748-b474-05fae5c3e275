//
//  XYLiveMultiLineOptFuncVM.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/3.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineOptFuncVM.h"
#import <XYLiveKit/XYLiveLinkHostInviteeInfo.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLiveMultiLineOptFuncGroupItem.h>

@interface XYLiveMultiLineOptFuncVM ()<XYLiveMultiLinkListener>

@property (nonatomic, weak) id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak) id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, copy) NSArray<XYLiveLinkHostInviteeInfo *> *inviteInfoList;
@property (nonatomic, copy) NSArray<XYLiveMultiLinkWindowInfo *> *roomInfoList;
@property (nonatomic, copy) NSArray<XYLiveMultiLineOptFuncGroupItem *> *dataSource;

@end

@implementation XYLiveMultiLineOptFuncVM

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService
                        inviteeInfoList:(NSArray<XYLiveLinkHostInviteeInfo *> *)inviteeInfoList {
    if (self = [super init]) {
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
        _inviteInfoList = inviteeInfoList;
        _roomInfoList = multiLinkService.roomInfo.windowInfoList;
        // 配置数据源
        [self configureDataSource];
        // 注册监听
        [multiLinkService registerListener:self];
    }
    return self;
}

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    // 过滤数据
    NSArray *inviteList = self.inviteInfoList;
    switch (state) {
        // 处理空闲状态
        case XYLiveMultiLinkInviteStateIdle:{
            // 过滤掉邀请中用户
            inviteList = [self.inviteInfoList xy_select:^BOOL(XYLiveLinkHostInviteeInfo * _Nonnull obj) {
                return ![obj.userId isEqualToString:inviteeInfo.userId];
            }];
        }
            break;
        // 处理邀请中状态
        case XYLiveMultiLinkInviteStateInviting: {
            // 判断当前用户是否已经再房
            if (![self.multiLinkService isInRtcRoomWithUserId:inviteeInfo.userId]) {
                NSMutableArray *listItemsM = self.inviteInfoList ? [self.inviteInfoList mutableCopy] : @[].mutableCopy;
                [listItemsM insertObject:inviteeInfo atIndex:0];
                inviteList = listItemsM.copy;
            }
        }
            break;
        default:
            break;
    }
    // 更新数据源
    [self updateListItemsWithInviteList:inviteList roomInfoList:self.roomInfoList];
}

- (NSInteger)linkMemberNum {
    return self.inviteInfoList.count + self.roomInfoList.count;
}

- (XYLiveMultiLinkBizType)bizType {
    return [self.multiLinkService bizType];
}

#pragma mark - XYLiveMultiLinkListener

- (void)onRtcUpdateRoomInfo:(XYLiveMultiLinkRoomInfo *)roomInfo {
    // 更新数据源
    [self updateListItemsWithInviteList:self.inviteInfoList roomInfoList:roomInfo.windowInfoList];
}

#pragma mark - Private

// 配置数据源
- (void)configureDataSource {
    // 连线好友
    XYLiveMultiLineOptFuncListFriendItem *item00 = [[XYLiveMultiLineOptFuncListFriendItem alloc] init];
    item00.list = [self buildListItemsWithInviteList:self.inviteInfoList roomInfoList:self.roomInfoList];
    XYLiveMultiLineOptFuncGroupItem *group0 = [[XYLiveMultiLineOptFuncGroupItem alloc] init];
    group0.items = @[item00];
    group0.itemH = 92;
    
    // 礼物PK
    XYLiveMultiLineOptFuncListBizItem *item10 = [[XYLiveMultiLineOptFuncListBizItem alloc] init];
    item10.iconURL = @"https://fe-platform.xhscdn.com/platform/104101l031itdep36h406crss5gt00000000000ie0o7pu";
    item10.title = @"礼物PK";
    item10.subTitle = @"比拼连线过程的礼物";
    item10.bizType = XYLiveMultiLinkBizTypePKGift;
    // 礼物PK中
    if (self.multiLinkService.bizType == XYLiveMultiLinkBizTypePKGift) {
        item10.isPlaying = YES;
        item10.optTypes = @[@(XYLiveMultiLineBizOptTypeReOpen), @(XYLiveMultiLineBizOptTypeClose)];
    } else {
        item10.isPlaying = NO;
        item10.optTypes = @[@(XYLiveMultiLineBizOptTypeOpen)];
    }
    
    // 人气PK
    XYLiveMultiLineOptFuncListBizItem *item11 = [[XYLiveMultiLineOptFuncListBizItem alloc] init];
    item11.iconURL = @"https://fe-platform.xhscdn.com/platform/104101l031itdep4d1e06crss5gt00000000000n8t3iba";
    item11.title = @"人气PK";
    item11.subTitle = @"比拼连线过程的人气";
    item11.bizType = XYLiveMultiLinkBizTypePKHeat;
    // 礼物PK中
    if (self.multiLinkService.bizType == XYLiveMultiLinkBizTypePKHeat) {
        item11.isPlaying = YES;
        item11.optTypes = @[@(XYLiveMultiLineBizOptTypeReOpen), @(XYLiveMultiLineBizOptTypeClose)];
    } else {
        item11.isPlaying = NO;
        item11.optTypes = @[@(XYLiveMultiLineBizOptTypeOpen)];

    }
    
    XYLiveMultiLineOptFuncGroupItem *group1 = [[XYLiveMultiLineOptFuncGroupItem alloc] init];
    group1.title = @"更多玩法";
    group1.items = @[item10, item11];
    group1.itemH = 80;
    
    // 更新数据源
    self.dataSource = @[group0, group1];
}

// 构建邀请信息
- (NSArray<XYLiveLinkHostInviteeInfo *> *)buildListItemsWithInviteList:(NSArray<XYLiveLinkHostInviteeInfo *> *)inviteList roomInfoList:(NSArray<XYLiveMultiLinkWindowInfo *> *)roomInfoList {
    // 优先插入邀请中
    NSMutableArray *listItemsM = [NSMutableArray array];
    // 优先插入邀请中列表
    [listItemsM addObjectsFromArray:inviteList];
    // 其次插入进房成功的
    [roomInfoList enumerateObjectsUsingBlock:^(XYLiveMultiLinkWindowInfo * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        // 合法性校验
        if (!obj.isEmptyMic) {
            // 用户信息
            XYLiveUserInfo *userInfo = obj.userInfo.userInfo;
            XYLiveLinkHostInviteeInfo *inviteeInfo = [[XYLiveLinkHostInviteeInfo alloc] init];
            inviteeInfo.userId = obj.userId;
            inviteeInfo.avatarUrl = userInfo.avatar;
            inviteeInfo.roomId = obj.userInfo.roomId;
            inviteeInfo.nickName = userInfo.nickname;
            [listItemsM addObject:inviteeInfo];
        }
    }];
    return listItemsM.copy;
}

// 更新数据源
- (void)updateListItemsWithInviteList:(NSArray<XYLiveLinkHostInviteeInfo *> *)inviteList roomInfoList:(NSArray<XYLiveMultiLinkWindowInfo *> *)roomInfoList {
    XYLiveMultiLineOptFuncGroupItem *group = self.dataSource.firstObject;
    XYLiveMultiLineOptFuncListFriendItem *listItem = XYSAFE_CAST(group.items.firstObject, XYLiveMultiLineOptFuncListFriendItem);
    // 过滤掉邀请中上麦成功的用户
    NSArray *linkingUserIds = [roomInfoList valueForKeyPath:@"userId"];
    NSArray *filterInviteList = [inviteList xy_filter:^BOOL(XYLiveLinkHostInviteeInfo * _Nonnull obj, NSUInteger index) {
        return ![linkingUserIds containsObject:obj.userId];
    }];
    listItem.list = [self buildListItemsWithInviteList:filterInviteList roomInfoList:roomInfoList];
    // 更新缓存
    self.inviteInfoList = filterInviteList;
    self.roomInfoList = roomInfoList;
    // 刷新列表
    self.onUpdateDataSourceHandler ? self.onUpdateDataSourceHandler() : nil;
}

@end
