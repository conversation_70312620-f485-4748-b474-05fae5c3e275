//
//  XYLiveMultiPKAvatarView.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/14.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <UIKit/UIKit.h>
@class XYLiveUserInfo;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiPKAvatarView : UIView

// 点击回调事件
@property (nonatomic, copy) void(^didTapHandler)(XYLiveUserInfo *userInfo);

@property (nonatomic, strong, readonly) UIButton *avatarBtn;
@property (nonatomic, strong, readonly) UILabel *numberLabel;

// 创建子视图
- (void)setupSubviews;

// 设置头像
- (void)setUserInfo:(XYLiveUserInfo * _Nullable)userInfo placeholder:(UIImage *)placeholder;

// 设置边框内容和颜色
- (void)setText:(NSString *)text borderColor:(UIColor *)borderColor;

@end

@interface XYLiveMultiPKLeftAvatarView : XYLiveMultiPKAvatarView

@end

@interface XYLiveMultiPKRightAvatarView : XYLiveMultiPKAvatarView

@end

NS_ASSUME_NONNULL_END
