//
//  XYLivePushPrepViewController+Domain.swift
//  XYLiveKit
//
//  Created by wenguoning on 2023/12/26.
//  Copyright © 2023 XingIn. All rights reserved.
//

import Foundation
import XYLiveCore

extension XYLivePushRoomViewController {
    @objc
    public func buildDomain() {
        /// 创建 domainBuilder
        self.createDomainBuilderIfNeed()
        /// 加载核心 Domain
        self.domainBuilder?.executeCoreDomain()
        if LiveConfigCenter.enableLivePushMemoryDomain() {
            buildMemoryControlDomain()
        }
        
        buidlGiftDomain()
        let enable = Experiment.shared.justOnceBool(forKey: "ios_enable_effect_domain_v2", defalut: false)
        if !enable {
            buildEffectDomain()
        }
        buildPerfStatusDomain()
        
        buildFaceSeiDomain()
        if LiveConfigCenter.pushFeelBorderDomainAdjust() == false {
            buildFeelBorderDomain()
        }
        /// 加载业务 Domain
        self.domainBuilder?.executeCommonDomain()
    }
    
    func buildFaceSeiDomain() {
        let domain = FaceSEIDomain(self, depend: self)
        addVCDomain(domain)
    }
    
    func buildMemoryControlDomain() {
        let domain = MemoryControlDomain(self, depend: self)
        addVCDomain(domain)
    }
    
    @objc
    public func buildEffectDomain() {
        let effectDomain = LiveEffectDomain(hostVC: self, liveEffectDepend: self.giftDomain?.giftEffectlVC)
        addVCDomain(effectDomain)
        self.effectDomain = effectDomain
    }
    
    func buidlGiftDomain() {
        var pushGiftService: XYLivePushGiftNodeServiceProtocol?
        if let containerVC = self.nodesLoader?.containerVC as? XYLiveNodeContainerProtocol,
           let pgs = containerVC.serviceManager?.getServiceWith(XYLivePushGiftNodeServiceProtocol.self) as? XYLivePushGiftNodeServiceProtocol {
            pushGiftService = pgs
        }
        let containerViewController: UIViewController = self
        let bizObservable = UIDomainExtraLifecycleObservable()
        let giftDomain = GiftDomain(self, observable: bizObservable, containerViewController: containerViewController,
                                    container:self.decorateView.animationHierarchy, giftDepend: self, isHost: true, playGiftService: nil, pushGiftService: pushGiftService)
        addVCDomain(giftDomain)
        self.giftDomain = giftDomain
    }
    
    func buildPerfStatusDomain() {
        let enable = ConfigCenter.shared.justOnceBool(forKey: "ios_enable_perf_status_v2", defalut: false)
        if enable {
            return
        }
        
        let perfStatusDomain = PerfStatusDomain(hostVC: self, perfStatusDepend: self)
        self.addVCDomain(perfStatusDomain)
    }
    
    @objc
    public func buildFeelBorderDomain() {
        if LiveConfigCenter.feelBorderDomain() {
            let bizObservable = UIDomainExtraLifecycleObservable()
            let multiLinkPushService = self.serviceManager?.getServiceWith(LiveMultiLinkPushServiceProtocol.self) as? LiveMultiLinkPushServiceProtocol

            // swiftlint:disable line_length
            self.feelBorderDomain = FeelBorderDomain(self,
                                                     observable: bizObservable,
                                                     containerView: multiLinkPushService?.linkView(),
                                                     feelBorderDepend: self,
                                                     topView: nil,
                                                     source: "host",
                                                     topUrl: "",
                                                     bottomUrl: "",
                                                     hierarchyService: nil)
            self.feelBorderDomain?.onFirstStageUILoad()
            if let domain = self.feelBorderDomain {
                addVCDomain(domain)
            }
        }
    }
}

extension XYLivePushRoomViewController: FeelBorderDepend {
    public func liveRNService() -> (any LiveRNServiceProtocol)? {
        serviceManager?.getServiceWith(LiveRNServiceProtocol.self) as? LiveRNServiceProtocol
    }
    
    public func liveIMDistributerService() -> (any XYLiveIMDistributerServiceProtocol)? {
        serviceManager?.getServiceWith(XYLiveIMDistributerServiceProtocol.self) as? XYLiveIMDistributerServiceProtocol
    }
}

// GiftDepend
extension XYLivePushRoomViewController: GiftDepend {
    public func getRouteManager() -> ActionRouteManager? {
        return XYLiveManager.shared().actionRouteManager
    }
    
    public func getCurrentLinkUsers() -> [LinkmicApplyUserInfoModel] {
        if let pushVC = self.nodesLoader?.containerVC as? XYLiveNodeContainerProtocol,
           let linkPushService = pushVC.serviceManager?.getServiceWith(LiveMultiLinkPushServiceProtocol.self) as? LiveMultiLinkPushServiceProtocol {
            if linkPushService.getContextType() == MultiLinkContextType.liveChat.rawValue || linkPushService.getContextType() == MultiLinkContextType.sameRoom.rawValue {
                return linkPushService.onMicUsers() ?? []
            } else {
                return []
            }
        }
        return []
    }
    
    public func commentAdapter() -> XYLiveIMCallbackAdapter? {
        return self.decorateVM.adapter
    }
    
    public func getEffectContainer() -> GiftEffectContainer? {
        return self.decorateView.getEffectContainer()
    }
    
    public func getGiftDockContainer() -> GiftDockContainer? {
        return self.decorateView.giftDockContainer
    }
    
    public func getLiveEffectService() -> (any XYLiveEffectServiceProtocol)? {
        if let service = self.smImpl.getService(XYLiveEffectServiceProtocol.self) {
            return service
        }
        return nil
    }
    
    public func getLiveEffectServiceV2() -> (any LiveEffectServiceProtocolV2)? {
        if let service = self.smImpl.getService(LiveEffectServiceProtocolV2.self) {
            return service
        }
        return nil
    }
    
    public func getBottomBarService() -> LiveBottomBarServiceProtocol? {
        return nil
    }
    
    public func hierarchyService() -> (any LiveHierarchyServiceProtocol)? {
        return nil
    }
    
    public func infoService() -> (any XYLiveInfoServiceProtocol)? {
        return self.serviceManager?.getServiceWith(XYLiveInfoServiceProtocol.self) as? XYLiveInfoServiceProtocol
    }
    
    public func groupLiveService() -> (any LiveGroupServiceProtocol)? {
        if let service = self.smImpl.getService(LiveGroupServiceProtocol.self) {
            return service
        }
        return nil
    }
    
    public func groupLivePKService() -> (any GroupLivePKServiceProtocol)? {
        if let service = self.smImpl.getService(GroupLivePKServiceProtocol.self) {
            return service
        }
        return nil
    }
    
    public func getEffectSwitchState() -> Bool {
        return !XYLiveManager.shared().roomInfo.giftAnimationToggle
    }
}

extension XYLivePushRoomViewController: PerfStatusDepend {
    public var pushDecorateView: XYLivePushDecorateView {
        return self.decorateView
    }
    
    public var isDegrate: Bool {
        return LivePushDegradeManager.shared.isDegrate
    }
    
    public var deviceMonitor: DeviceMonitorProtocol? {
        let commonMemMonitor = serviceManager?.getServiceWith(LiveCommonMonitorMemServiceProtocol.self) as? LiveCommonMonitorMemServiceProtocol
        return commonMemMonitor?.monitor
    }
    
    public func openLowEnergyConsumptionMode() {
        LivePushDegradeManager.shared.manualStart()
    }
    
    public func cancelLowEnergyConsumptionMode() {
        LivePushDegradeManager.shared.manualEnd()
    }
    
    public func imDistributerService() -> (any XYLiveIMDistributerServiceProtocol)? {
        return serviceManager?.getServiceWith(XYLiveIMDistributerServiceProtocol.self) as? XYLiveIMDistributerServiceProtocol
    }
    
    public func showDashboardPanel() {
        self.decorateVM.notifyVCToPresentDashboard = true
    }
    
    public func showCoverSettingPanel() {
        let changeCoverService = self.serviceManager?.getServiceWith(XYLivePushChangeCoverNodeServiceProtocol.self) as? XYLivePushChangeCoverNodeServiceProtocol
        changeCoverService?.showLivePushCoverChangeVC()
    }
    
    public func liveInfoService() -> (any XYLiveInfoServiceProtocol)? {
        return self.serviceManager?.getServiceWith(XYLiveInfoServiceProtocol.self) as? XYLiveInfoServiceProtocol
    }
    
    public func qualityTipService() -> (any XYLivePushCommonIMTipNodeServiceProtocol)? {
        return self.serviceManager?.getServiceWith(XYLivePushCommonIMTipNodeServiceProtocol.self) as? XYLivePushCommonIMTipNodeServiceProtocol
    }
}

extension XYLivePushRoomViewController: FaceSEIDependency {
    public func eventService() -> XYLivePlayRoomEventService? {
        serviceManager?.getServiceWith(LiveRoomEventServiceProtocol.self) as? XYLivePlayRoomEventService
    }
    public func rtcCorePushService() -> XYLiveRtcCoreServiceProtocol? {
        serviceManager?.getServiceWith(XYLiveRtcCoreServiceProtocol.self) as? XYLiveRtcCoreServiceProtocol
    }
}

extension XYLivePushRoomViewController: MemoryControlDependency {
    public func roomEventService() -> XYLivePlayRoomEventService? {
        serviceManager?.getServiceWith(LiveRoomEventServiceProtocol.self) as? XYLivePlayRoomEventService
    }
}
