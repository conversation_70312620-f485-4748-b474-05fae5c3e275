//
//  XYLivePushRoomViewController+HotSwitch.m
//  XYLiveKit
//
//  Created by 周博立 on 2021/3/15.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import "XYLivePushRoomViewController.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYIMComm.h"
#import "XYLiveRtcCoreService.h"
#import <XYLiveKit/XYLiveKit-Swift.h>

@import XYTracker;
@import XYConfigCenter;
@import XYLiveFoundation;
@import SDWebImage;
@import KVOController;
@import XYLivePusher;

static NSString *kMessageParamTypeUniversalPush = @"push_video_v2";
static NSString *kMessageParamTypeTRTCExp = @"trtc_exp";

@implementation XYLivePushRoomViewController (HotSwitch)

- (void)setupSwitchPusherIM {
    @weakify(self);
    [liveSharedImDistributer registerWithType:XYIM_CUSTOM_LIVE_PUSH_INFO_CHANGE impl:^(NSString * _Nonnull type, XYLiveCodableModel * _Nonnull info, NSDictionary<NSString *,id> * _Nonnull rawData) {
        @strongify(self);
        XYLivePusherChangeInfo *msgInfo = [XYLivePusherChangeInfo xy_modelWithDictionary:rawData];
        [self beginSwithPusherWithInfo:msgInfo];
    }];
    
    [liveSharedImDistributer registerWithType:XYMANUAL_LIVE_HOST_CHANGE_RTCPUSHER impl:^(NSString * _Nonnull type, XYLiveCodableModel * _Nonnull info, NSDictionary<NSString *,id> * _Nonnull rawData) {
        @strongify(self);
        [self switchPusherToRtcPusherIfNeeded];
    }];
    
    
}

// MARK: - 只在初始化使用 其它方式禁用
- (void)loadRtcCoreWithRTCSession:(RTCChannelSession *)session {
    [XYLogCollector xyLiveLogTag:@"pusher_start" content:@"startRtcCore enter"];
    self.rtcCoreService = [XYLiveRtcCoreService new];
    
    [XYLogCollector xyLiveLogTag:@"pusher_start" content:@"startRtcCore success"];
    
    // 加载rtc core
    RTCCoreChannelSession *coreSession = [self buildRtcChannelSession:session];
    
    if (XYLiveManagerSharedInstance.globalWindow.rtcCore) {
        if (XYConfigCenter().boolForKey(@"ios_live_host_floating_resume_opt", NO) && XYLiveManagerSharedInstance.globalWindow.rtcCore.rtcChannelSession) {
            coreSession = XYLiveManagerSharedInstance.globalWindow.rtcCore.rtcChannelSession;
        }
        XYLiveManagerSharedInstance.globalWindow.rtcCore = nil;
    }

    [self.rtcCoreService loadRtcCore:coreSession];
    
    if (XYConfigCenter().boolForKey(@"ios_open_kasa_core2_0", YES)) {
        return;
    }
    self.roomInfo.pushType = XYLivePushTypeTrtc;
}

- (void)switchPusherToRtcCoreIfNeeded:(RTCVendorType)type {
    if([XYAlphaSwitch enableLinkInterceptPC]) {
        if (self.roomInfo.bizStyle != XYLiveRoomBizPushStyleNormal) {
            [XYLogCollector xyLiveLogTag:@"pusher_switch" content:[NSString stringWithFormat:@"switchPusherToRtcCore not allowed biz style = %@",@(self.roomInfo.bizStyle)]];
            return;
        }
    }
    [XYLogCollector xyLiveLogTag:@"pusher_switch" content:@"switchPusherToRtcCore enter"];
    id<XYLiveRtcCoreServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveRtcCoreServiceProtocol)];
    
    NSLog(@"switchPusherToRtcCoreIfNeeded current: %d, dest: %ld", (long)service.rtcCore.vendorType, (long)type);
    if(XYConfigCenter().boolForKey(@"ios_open_kasa_core2_0", YES)) {
        if (service.rtcCore != nil && service.rtcCore.vendorType == type) {
            [XYLogCollector xyLiveLogTag:@"pusher_switch" content:@"switchPusherToRtcCore faliure core2.0 same vendorType"];
            return;
        }
    } else {
        if (service.isWorking) {
            [XYLogCollector xyLiveLogTag:@"pusher_switch" content:@"switchPusherToRtcCore faliure"];
            return;
        }
    }
    
    [XYLogCollector xyLiveLogTag:@"pusher_switch" content:@"switchPusherToRtcCore success"];
    
    RTCChannelSession *session = nil;
    if (self.rtcCore != nil) {
        XYLiveRoomInfo *roomInfo = XYLiveManagerSharedInstance.roomInfo;
        XYLivePassport *passport = XYLiveManagerSharedInstance.imConfigParam.passport;
        XYLiveUserInfo *userInfo = XYLiveManagerSharedInstance.userInfo;
        session = [RTCChannelSession sessionWithRoomInfo:roomInfo
                                                userInfo:userInfo
                                                passport:passport
                                                    role:RTCMemberRoleHost
                                audienceRtcLinkReference:XYLiveRtcLinkAudiencePushTypeTrtc];
    } else {
        session = self.rtcPusher.rtcChannelSession;
    }
    
    if (self.rtcCore != nil && XYConfigCenter().boolForKey(@"ios_live_kasa_hot_switch_use_session", YES)) {
        RTCVideoEncodeInfo *info = [[RTCVideoEncodeInfo alloc] init];
        RTCVideoEncodeInfo *coreInfo = self.rtcCore.rtcChannelSession.encode;
        info.videoFPS = coreInfo.videoFPS;
        info.codec = coreInfo.codec;
        info.videoBitrate = coreInfo.videoBitrate;
        info.minBitrateRatio = coreInfo.minBitrateRatio;
        info.gop = coreInfo.gop;
        info.resolution = coreInfo.resolution;
        info.qosPreference = coreInfo.qosPreference;
        info.denoise = coreInfo.denoise;
        
        [session reloadEncodeInfo: info];
    }
    // 帧发送器置空, 放在destory之前，否则有崩溃
    XYLiveManagerSharedInstance.coreManager.media.customVendor = nil;
    
    // 销毁rtcPusher
    [self.rtcPusher destroyRTCVendorWithDestoryTRTCCloud:NO];
    BOOL trtcDestoryed = self.rtcPusher.trtcAlreadyDestoryCloud;
    if (self.rtcPusher == nil) {
        trtcDestoryed = YES;
    }
    self.rtcPusher = nil;
    
    // 销毁rtmpPusher
    [self _destoryRtmpPusher];
    
    // 销毁kasaPusher
    [self _destoryKasaPusher];
    
    [XYLiveMediaControl reloadAudioSessionWithReceiver:self sceneType:XYLiveAudioSessionScenePush];
    
    // 加载rtc core
    // 目前只有多人连线会进 强行切到trtc
    RTCCoreChannelSession *coreSession = nil;
    if (XYConfigCenter().boolForKey(@"ios_hot_swap_new_build_session", NO) && self.rtcCore != nil) {
        coreSession = [self buildRtcChannelSession: self.rtcCore.encodeInfo vendorType: type];
    } else {
        session.vendorType = type;
        coreSession = [self buildRtcChannelSession:session];
    }
   
    coreSession.reJoinChannel = !trtcDestoryed;
    NSLog(@"multilink reloadRtcCore vendor>>>>>>>> %ld", (long)coreSession.baseInfo.vendorType);
    [service reloadRtcCore:coreSession];
    
    // 修改pushType，保证能恢复到rtcPusher
    self.roomInfo.pushType = XYLivePushTypeTrtc;
}

- (void)switchPusherToRtcCoreIfNeeded {
    if(XYConfigCenter().boolForKey(@"ios_live_link_filter_pc", NO)) {
        if (self.roomInfo.bizStyle != XYLiveRoomBizPushStyleNormal) {
            [XYLogCollector xyLiveLogTag:@"pusher_switch" content:[NSString stringWithFormat:@"switchPusherToRtcCore not allowed biz style = %@",@(self.roomInfo.bizStyle)]];
            return;
        }
    }
    [XYLogCollector xyLiveLogTag:@"pusher_switch" content:@"switchPusherToRtcCore enter"];
    id<XYLiveRtcCoreServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveRtcCoreServiceProtocol)];
    if (service.isWorking) {
        [XYLogCollector xyLiveLogTag:@"pusher_switch" content:@"switchPusherToRtcCore faliure"];
        return;
    }
    
    [XYLogCollector xyLiveLogTag:@"pusher_switch" content:@"switchPusherToRtcCore success"];

    RTCChannelSession *session = self.rtcPusher.rtcChannelSession;
    
    // 帧发送器置空, 放在destory之前，否则有崩溃
    XYLiveManagerSharedInstance.coreManager.media.customVendor = nil;
    
    // 销毁rtcPusher
    [self.rtcPusher destroyRTCVendorWithDestoryTRTCCloud:NO];
    BOOL trtcDestoryed = self.rtcPusher.trtcAlreadyDestoryCloud;
    self.rtcPusher = nil;
    
    // 销毁rtmpPusher
    [self _destoryRtmpPusher];
    
    // 销毁kasaPusher
    [self _destoryKasaPusher];
    
    [XYLiveMediaControl reloadAudioSessionWithReceiver:self sceneType:XYLiveAudioSessionScenePush];
    
    // 加载rtc core
    // 目前只有多人连线会进 强行切到trtc
    session.vendorType = RTCVendorTypeTRTC;
    // channelSession 赋值
    if (self.rtcCore != nil && XYConfigCenter().boolForKey(@"ios_live_kasa_hot_switch_use_session", YES)) {
        RTCVideoEncodeInfo *info = [[RTCVideoEncodeInfo alloc] init];
        RTCVideoEncodeInfo *coreInfo = self.rtcCore.rtcChannelSession.encode;
        info.videoFPS = coreInfo.videoFPS;
        info.codec = coreInfo.codec;
        info.videoBitrate = coreInfo.videoBitrate;
        info.minBitrateRatio = coreInfo.minBitrateRatio;
        info.gop = coreInfo.gop;
        info.resolution = coreInfo.resolution;
        info.qosPreference = coreInfo.qosPreference;
        info.denoise = coreInfo.denoise;
        [session reloadEncodeInfo: info];
    }
    RTCCoreChannelSession *coreSession = [self buildRtcChannelSession:session];
    coreSession.reJoinChannel = !trtcDestoryed;
    [service reloadRtcCore:coreSession];
    
    // 修改pushType，保证能恢复到rtcPusher
    
    self.roomInfo.pushType = XYLivePushTypeTrtc;
//    if (self.roomInfo.pushType == XYLivePushTypeKasa) {
//
//    } else {
//        self.roomInfo.pushType = XYLivePushTypeTrtc;
//    }

}

- (RTCCoreChannelSession *)buildRtcChannelSession: (RTCChannelSession *)session {
    RTCChannelSession *oldSession = session;
    if (oldSession == nil) {
        XYLiveRoomInfo *roomInfo = XYLiveManagerSharedInstance.roomInfo;
        XYLivePassport *passport = XYLiveManagerSharedInstance.imConfigParam.passport;
        XYLiveUserInfo *userInfo = XYLiveManagerSharedInstance.userInfo;
        oldSession = [RTCChannelSession sessionWithRoomInfo:roomInfo
                                                   userInfo:userInfo
                                                   passport:passport
                                                       role:RTCMemberRoleHost
                                   audienceRtcLinkReference:XYLiveRtcLinkAudiencePushTypeTrtc];
    }
    
    RTCCoreChannelSession *coreSession = [[RTCCoreChannelSession alloc] initWithChannelSession:oldSession];
    RTCChannelBizInfo *bizInfo = [RTCChannelBizInfo new];
    bizInfo.isVoiceLive = XYLiveManagerSharedInstance.roomInfo.isVoiceLive;
    bizInfo.isLiveChat = XYLiveManagerSharedInstance.roomInfo.isLiveChat;
    bizInfo.isScreenCap = XYLiveManagerSharedInstance.roomInfo.isScreenLive;
    bizInfo.isVideoLive = XYLiveManagerSharedInstance.roomInfo.isVideoLive;
    bizInfo.fansCount = XYLiveManagerSharedInstance.hostInfo.totalFansNum;
    if (XYConfigCenter().boolForKey(@"ios_live_push_landscape_fix", YES)) {
        bizInfo.isLandscape = XYLivePushPrepParamCache.pushLandscape && XYLiveManagerSharedInstance.roomInfo.isScreenLive;
    } else {
        bizInfo.isLandscape = XYLivePushPrepParamCache.pushLandscape;
    }
    
    bizInfo.isHost = YES;
    coreSession.baseInfo.bizInfo = bizInfo;
        // 代码开关保护，原始逻辑此处热切畅聊只能走trtc，开关打开使用pushInfo.talkEncode.pushType作为sesssion.vendor来决定畅聊推流器类型
    if (session.vendorType == RTCVendorTypeKasa && (
                XYLiveManagerSharedInstance.roomInfo.contentType == XYLiveRoomPushContentTypeVideo ||
                XYLiveManagerSharedInstance.roomInfo.contentType == XYLiveRoomPushContentTypeScreenCap ||
                XYLiveManagerSharedInstance.roomInfo.contentType == XYLiveRoomPushContentTypeRedHouse)) {
            coreSession.baseInfo.vendorType = RTCVendorTypeKasa;
    } else {
        coreSession.baseInfo.vendorType = RTCVendorTypeTRTC;
    }
    
        
    return coreSession;
}

- (RTCCoreChannelSession *)buildRtcChannelSession:(RTCVideoEncodeInfo *)encode vendorType:(RTCVendorType)type {
    NSString *channelId = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
    NSString *userId = XYLiveManagerSharedInstance.roomInfo.hostInfo.userID;
    NSString *sdkAppId = XYLiveManagerSharedInstance.passport.trtcSdkAppID;
    NSString *pushUrl = XYLiveManagerSharedInstance.roomInfo.streamURLInfo.livePushURL;
    NSString *userSign = XYLiveManagerSharedInstance.passport.trtcUserSign;
    
    RTCCoreChannelSession *coreSession = [[RTCCoreChannelSession alloc] initWithChannelID:channelId userId:userId pushUrl:pushUrl userSign:userSign];
    
    RTCChannelBizInfo *bizInfo = [RTCChannelBizInfo new];
    bizInfo.isVoiceLive = XYLiveManagerSharedInstance.roomInfo.isVoiceLive;
    bizInfo.isLiveChat = XYLiveManagerSharedInstance.roomInfo.isLiveChat;
    bizInfo.isScreenCap = XYLiveManagerSharedInstance.roomInfo.isScreenLive;
    bizInfo.isVideoLive = XYLiveManagerSharedInstance.roomInfo.isVideoLive;
    bizInfo.fansCount = XYLiveManagerSharedInstance.hostInfo.totalFansNum;

    if (XYConfigCenter().boolForKey(@"ios_live_push_landscape_fix", YES)) {
        bizInfo.isLandscape = XYLivePushPrepParamCache.pushLandscape && XYLiveManagerSharedInstance.roomInfo.isScreenLive;
    } else {
        bizInfo.isLandscape = XYLivePushPrepParamCache.pushLandscape;
    }
    
    bizInfo.isHost = YES;
    coreSession.baseInfo.bizInfo = bizInfo;
    
    
    if (type == RTCVendorTypeKasa && (XYLiveManagerSharedInstance.roomInfo.contentType == XYLiveRoomPushContentTypeVideo ||
        XYLiveManagerSharedInstance.roomInfo.contentType == XYLiveRoomPushContentTypeScreenCap ||
        XYLiveManagerSharedInstance.roomInfo.contentType == XYLiveRoomPushContentTypeRedHouse)) {
        coreSession.baseInfo.vendorType = RTCVendorTypeKasa;
    } else {
        coreSession.baseInfo.vendorType = RTCVendorTypeTRTC;
    }
    

    coreSession.encode.videoFPS = encode.videoFPS;
    coreSession.encode.codec = encode.codec;
    coreSession.encode.videoBitrate = encode.videoBitrate;
    coreSession.encode.minBitrateRatio = encode.minBitrateRatio;
    coreSession.encode.gop = encode.gop;
    coreSession.encode.resolution = encode.resolution;
    coreSession.encode.qosPreference = encode.qosPreference;
    coreSession.encode.denoise = encode.denoise;
    
    if ([XYAlphaSwitch enablePushMultiLevel]) {
        if (encode.multiLevel.count > 0) {
            coreSession.encode.multiLevel = encode.multiLevel;
        }
        
        if (encode.levelName.length > 0) {
            coreSession.encode.levelName = encode.levelName;
        }
    }
    
    return coreSession;
}

- (void)destroyRTCCore:(BOOL)destoryTRTCCloud {
    XYLiveManagerSharedInstance.coreManager.media.customVendor = nil;
    
    id<XYLiveRtcCoreServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveRtcCoreServiceProtocol)];
    [service destoryRtcCore:destoryTRTCCloud];
}

- (void)switchPusherToRtcPusherIfNeeded {
    [XYLogCollector xyLiveLogTag:@"pusher_switch" content:@"switchPusherToRtcPusher enter"];
    id<XYLiveMultiLinkPushServiceProtocol> linkService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveMultiLinkPushServiceProtocol)];
    if (linkService.isLinking) {
        [XYLogCollector xyLiveLogTag:@"pusher_switch" content:@"switchPusherToRtcPusher faliure because multilink is linking"];
        return;
    }
    
    if (self.rtcPusher != nil && self.rtcPusher.isRTCPushing) {
        [XYLogCollector xyLiveLogTag:@"pusher_switch" content:@"switchPusherToRtcPusher faliure"];
        return;
    }
    
    [XYLogCollector xyLiveLogTag:@"pusher_switch" content:@"switchPusherToRtcPusher success"];
    
    // PK场景这里需要硬切trtc
    self.roomInfo.pushType = XYLivePushTypeTrtc;
    
    // 创建rtcPusher
    BOOL trtcDestoryed = self.rtcCore.trtcAlreadyDestoryCloud;
    
    if (self.rtcCore.vendorType == RTCVendorTypeKasa) {
        [self _buildRtcPusherIfNeeded:trtcDestoryed];
    } else {
        [self _buildRtcPusherIfNeeded:NO];
    }
    XYLivePassport *passport = XYLiveManagerSharedInstance.passport;
    RTCVendorType destVendor = [RTCChannelSession rtcVendorTypeWithPushType:self.roomInfo.pushType];
    
    // 切换到指定推流器
    [self.rtcPusher hotSwitchWithTargetVendor:destVendor passport:passport];
    
    [self.rtcPusher reJoinRoomWithChannelJoined:!trtcDestoryed];
    // 开始推流
    if (self.shouldScreenPushing) {
        [self.rtcPusher startPushWithLandscape:XYLivePushPrepParamCache.pushLandscape];
    } else {
        [self.rtcPusher startPush];
    }
       
    [self handlePusherMirrorStats];
}

- (void)beginSwithPusherWithInfo:(XYLivePusherChangeInfo *)info {
    BOOL hotSwitch = XYConfigCenter().boolForKey(@"ios_live_hot_switch_pusher", YES);
    if (!hotSwitch) {
        return;
    }
    [XYLogCollector xyLiveLogTag:@"pusher_switch" content:[NSString stringWithFormat:@"receive hot pusher switch IM with info: %@", info]];
    if (![self.roomInfo.roomIDStr isEqualToString:info.roomID]) return;
    if (self.roomInfo.bizStyle != XYLiveRoomBizPushStyleNormal) return;
    if ([self isLinking]) return;
    if (info.pushType == XYLivePushTypeUndefine || info.pushType == XYLivePushTypeRtmp) { return; }
    if ([info.pushURL isEqualToString:self.roomInfo.streamURLInfo.livePushURL] && self.rtcPusher.pushType == info.pushType) {
        return;
    }
    
    if ((self.rtcPusher.pushType == XYLivePushTypeTrtc) &&
        (info.pushType == XYLivePushTypeTrtc)) {
        return;
    }
    
    XYLivePushType targetType = info.pushType;
    NSString *pushURL = info.pushURL ?: @"";
    if (pushURL.length > 0) {
        self.roomInfo.streamURLInfo.livePushURL = pushURL;
    }
    
    [XYLogCollector xyLiveLogTag:@"pusher_switch" content:[NSString stringWithFormat:@"prepare to switch pusher from: %@ to: %@",  @(self.roomInfo.pushType), @(targetType)]];
    
    [self.decorateVM.adapter appendDebugInfo:[NSString stringWithFormat:@"hot pusher switch from %@ to: %@", @(self.roomInfo.pushType), @(targetType)]];
    
    if ([XYLiveMultiLinkConfig getLocalConfig].enableHotSwitchDestoryRtcCore) {
        [XYLogCollector xyLiveLogTag:@"hot_switch" content:@"destory Rtc core"];
        id<XYLiveRtcCoreServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveRtcCoreServiceProtocol)];
        if (service.isWorking) {
            [service destoryRtcCore:YES];
        }
    }
    
    RTCVendorType destVendor = [RTCChannelSession rtcVendorTypeWithPushType:targetType];
    [self.rtcPusher destroyRTCVendorWithDestoryTRTCCloud:YES];
    [self _buildRtcPusherIfNeeded:YES];
   
    XYLivePassport *passport = XYLiveManagerSharedInstance.passport;
    [self.rtcPusher hotSwitchWithTargetVendor:destVendor passport:passport];
    self.roomInfo.pushType = targetType;
    [self.rtcPusher startPush];
    [self handlePusherMirrorStats];
    
}

- (void)deltaUpdatePusherInfo:(NSDictionary *)param {
    if (!param) { return; }
    if (self.roomInfo.bizStyle != XYLiveRoomBizPushStyleNormal) return;
    
    if ([self isLinking]) return;
    NSString *kMessageType = [XYLiveMessageWordTool messageType];
    NSString *kMessageParams = [XYLiveMessageWordTool messageParams];
    
    id type = [param valueForKey:kMessageType];
    if ([type isKindOfClass:[NSString class]] &&
        [(NSString *)type isEqualToString:kMessageParamTypeUniversalPush] &&
        [[param valueForKey:kMessageParams] isKindOfClass:[NSDictionary class]]) {
        // Detail
        NSDictionary *info = (NSDictionary *)param[kMessageParams];
        // Parse
        // Stream
        XYLivePushDispatchInfo *dispatch = [XYLivePushDispatchInfo new];
        dispatch.encode = [XYLivePushDispatchInfoEncode xy_modelWithDictionary:info];
        dispatch.defaultResolution = dispatch.encode.resolution;
        // Room
        XYLivePusherChangeInfo *msgInfo = [XYLivePusherChangeInfo xy_modelWithDictionary:info];
        
        XYLivePushType targetType = msgInfo.pushType;
        if (targetType == XYLivePushTypeUndefine) {
            [XYLogCollector xyLiveLogTag:@"pusher_switch" content: @"targetPushType error!!!!"];
            return;
        }
        NSString *pushURL = msgInfo.pushURL ?: @"";
        if (pushURL.length > 0) {
            self.roomInfo.streamURLInfo.livePushURL = pushURL;
        }
        
        RTCVideoEncodeInfo *encode = [self canApplyRTC] ? self.rtcPusher.referenceVideoEncoderParam : self.pusher.videoEncodeInfo;
        
        if ([self rtcCoreEnable]) {
            if (self.rtcCore.rtcChannelSession.encode != nil) {
                encode = self.rtcCore.rtcChannelSession.encode;
            }
        }
        
        if (![info valueForKey:@"codec"]) {
            dispatch.encode.codec = encode.codec;
        }
        
        if (![info valueForKey:@"qos"]) {
            dispatch.encode.qos = encode.qosPreference;
        }
        
        if (![info valueForKey:@"resolution"]) {
            dispatch.encode.resolution = encode.resolution;
        }
        
        if (![info valueForKey:@"fps"]) {
            dispatch.encode.fps = encode.videoFPS;
        }
        
        if (![info valueForKey:@"bitrate"]) {
            dispatch.encode.bitrate = encode.videoBitrate.rtcReference;
        }
        
        if (![info valueForKey:@"mbr"]) {
            NSInteger bitrate = encode.videoBitrate.rtcReference;
            NSInteger minBitrate = encode.videoBitrate.rtcMinReference;
            dispatch.encode.mbr = minBitrate / bitrate;
        }
        
        if (![info valueForKey:@"gop"]) {
            dispatch.encode.gop = encode.gop;
        }
        
        if (![info valueForKey:@"denoise"]) {
            dispatch.encode.denoise = encode.denoise;
        }

        if ([self canApplyRTC]) {
            [XYLogCollector xyLiveLogTag:@"pusher_switch" content: [NSString stringWithFormat:@"will hotSwitch from: %@ to: %@, codec: %@, res: %@, bitrate: %@, fps: %@",  @([self rtcCoreEnable] ? self.rtcCore.pushType : self.rtcPusher.pushType), @(targetType), @(dispatch.encode.codec), @(dispatch.encode.resolution), @(dispatch.encode.bitrate), @(dispatch.encode.fps)]];
        } else {
            [XYLogCollector xyLiveLogTag:@"pusher_switch" content: [NSString stringWithFormat:@"will hotSwitch from: %@ to: %@, codec: %@, res: %@, bitrate: %@, fps: %@",  @(XYLivePushTypeRtmp), @(targetType), @(dispatch.encode.codec), @(dispatch.encode.resolution), @(dispatch.encode.bitrate), @(dispatch.encode.fps)]];
        }
        
        // 统一后门热切推流器
        if ([XYAlphaSwitch enableFixDoorHotSwapToRtcCore]) {
            id<XYLiveRtcCoreServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveRtcCoreServiceProtocol)];
            RTCVendorType destVendor = [RTCChannelSession rtcVendorTypeWithPushType:targetType];
            [XYLogCollector xyLiveLogTag:@"pusher_switch" content: [NSString stringWithFormat:@"will hotSwitch from: %@ to: %@", @(service.rtcCore.vendorType), @(destVendor)]];
            RTCVideoEncodeInfo *encode = [RTCVideoEncodeInfo encodeInfoWithDispatchInfo:dispatch vendorType:destVendor];
            [XYLiveManagerSharedInstance.coreManager.media reloadCameraPreset:encode];
            
            if (XYConfigCenter().boolForKey(@"ios_live_hot_switch_multi_level_fix", YES) && [XYAlphaSwitch enablePushMultiLevel]) {
                if (self.rtcCore.encodeInfo.multiLevel.count > 0) {
                    encode.multiLevel = self.rtcCore.encodeInfo.multiLevel;
                }
                
                if (self.rtcCore.encodeInfo.levelName.length > 0) {
                    encode.levelName = self.rtcCore.encodeInfo.levelName;
                }
            }
            
            
            self.roomInfo.pushType = targetType;
            RTCCoreChannelSession *coreSession = [self buildRtcChannelSession:encode vendorType:destVendor];
            coreSession.reJoinChannel = !self.rtcCore.trtcAlreadyDestoryCloud;
            
            [service.rtcCore loadRTCCoreChannelSession: coreSession forceSwap:true];
            XYLiveManagerSharedInstance.coreManager.media.customVendor = service.rtcCore.videoTransmiter;
            [service.rtcCore startPush];
             
            [XYLogCollector xyLiveLogTag:@"pusher_switch" content:@"enableFixDoorHotSwapToRtcCore"];

            // 1、设置语音/视频背景图
            [self.presetImageUtil configBizInfoWithVoiceLive:coreSession.baseInfo.bizInfo.isVoiceLive screenLive:coreSession.baseInfo.bizInfo.isScreenCap chatLive:coreSession.baseInfo.bizInfo.isLiveChat];
            
            if (XYConfigCenter().boolForKey(@"ios_config_rtc_core_mute_image", NO)) {
                [self.rtcCore setVideoMuteImage:[UIImage xy_liveKitBundleImage:@"xyLiveBackground"]
                      appActiveEffective:YES];
            }
            [self.presetImageUtil configAppActiveImage];
            [self.presetImageUtil configAppInactiveImage];
            
            
            if ([XYLiveLinkConfig pausePushStopCameraEnable] && XYLiveManager.sharedManager.isHostPausePush && XYLiveManager.sharedManager.isHost) {
                [self.rtcCore pausePush];
            }

            return;
        }
        
                
        if (targetType == XYLivePushTypeRtmp) {
            if ([self canApplyRTC]) {
                [self.rtcPusher destroyRTCVendorWithDestoryTRTCCloud:YES];
                self.rtcPusher = nil;
                if ([self rtcCoreEnable]) {
                    [self.rtcCoreService destoryRtcCore:YES];
                }
            } else if ([self canApplyKasa]){
                [self.kasaPusher stopPush];
                [self.kasaPusher destory];
                self.kasaPusher = nil;
            } else {
                [self.pusher stopPush];
                [self.pusher destory];
                self.pusher = nil;
            }
            self.roomInfo.pushType = targetType;
            [self rtmpPusherInitialize];
            [self.pusher reloadVideoEncoderParam:[RTCVideoEncodeInfo encodeInfoWithDispatchInfo:dispatch vendorType:RTCVendorTypeNone]];
            
            NSString *rtmp = self.roomInfo.streamURLInfo.livePushURL;
            if (rtmp.length) {
                NSString *roomID = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
                NSString *userID = XYLiveManagerSharedInstance.userInfo.userID;
                NSDictionary *extra = @{
                    kLogHostID: userID ?: @"",
                    kLogRoomID: roomID ?: @"",
                    kRtmpPushResolution: @(self.roomInfo.encodedResolution)
                };
                [self.pusher startPush:rtmp extra:extra];
            }
        } else if (targetType == XYLivePushTypeKasa) {
            
            if (XYConfigCenter().boolForKey(@"ios_use_rtc_engine_kasa", YES)) {
                // 打开开关 使用rtcEngine
//                [self kasaHotSwitchToTrtc];
                [self kasaHotSwitchToType:targetType dispathchInfo:dispatch];
            } else {
                //热切到kasa
                if ([self canApplyRTC]) {
                    [self.rtcPusher destroyRTCVendorWithDestoryTRTCCloud:YES];
                    if ([self rtcCoreEnable]) {
                        [self.rtcCoreService destoryRtcCore:YES];
                    }
                } else if ([self canApplyKasa]){
                    [self.kasaPusher stopPush];
                    [self.kasaPusher destory];
                    self.kasaPusher = nil;
                } else {
                    [self.pusher stopPush];
                    [self.pusher destory];
                    self.pusher = nil;
                }
                self.roomInfo.pushType = targetType;
                [self kasaPusherInitialize];
                [self.kasaPusher reloadVideoEncoderParam:[RTCVideoEncodeInfo encodeInfoWithDispatchInfo:dispatch vendorType:RTCVendorTypeNone] userId:XYLiveManagerSharedInstance.userInfo.userID];
                
                NSString *rtmp = self.roomInfo.streamURLInfo.livePushURL;
                if (rtmp.length) {
                    NSString *roomID = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
                    NSString *userID = XYLiveManagerSharedInstance.userInfo.userID;
                    NSDictionary *extra = @{
                        kLogHostID: userID ?: @"",
                        kLogRoomID: roomID ?: @"",
                        kRtmpPushResolution: @(self.roomInfo.encodedResolution)
                    };
                    [self.kasaPusher startPush:rtmp roomId:roomID extra:extra];
                }
            }
        } else {
            RTCVendorType destVendor = [RTCChannelSession rtcVendorTypeWithPushType:targetType];
            [self.rtcPusher destroyRTCVendorWithDestoryTRTCCloud:YES];
            [self _buildRtcPusherIfNeeded:YES];
            RTCVideoEncodeInfo *encode = [RTCVideoEncodeInfo encodeInfoWithDispatchInfo:dispatch vendorType:destVendor];
            [self.rtcPusher reloadVideoEncoderParam:encode];
            [XYLiveManagerSharedInstance.coreManager.media reloadCameraPreset:encode];
            XYLivePassport *passport = XYLiveManagerSharedInstance.passport;
            [self.rtcPusher hotSwitchWithTargetVendor:destVendor passport:passport];
            self.roomInfo.pushType = targetType;
            [self configHostLeavingImage:self.roomInfo.isVoiceLive];
            [self.rtcPusher startPush];
        }
        [self handlePusherMirrorStats];
    }
}


- (void)updateFaceVInfo:(NSDictionary *)param {
    if (!param) { return; }
    if (self.roomInfo.bizStyle != XYLiveRoomBizPushStyleNormal) return;
    
    if ([self isLinking]) return;
    if (param[@"facev"] && [param[@"facev"] isKindOfClass:[NSNumber class]]) {
        NSNumber *faceV = param[@"facev"];
        [XYLogCollector xyLiveLogTag:@"updateFaceVInfo" content:[NSString stringWithFormat:@"%@", param]];
        [[XYLiveManager sharedManager].coreManager.media switchAifaceVersion:faceV.intValue];
    }
}

- (void)hotSwitchToRtcPusher {
    if ([self _buildRtcPusherIfNeeded:YES]) {
        [self.rtcPusher startPush];
        [self handlePusherMirrorStats];
    }
}

- (void)kasaHotSwitchToTrtc {
    [self kasaHotSwitchToType: XYLivePushTypeTrtc dispathchInfo:self.roomInfo.pushDispatchInfo];
}

- (void)kasaHotSwitchToType: (XYLivePushType)targetType dispathchInfo:(XYLivePushDispatchInfo *)dispatchInfo {
    RTCVendorType destVendor = [RTCChannelSession rtcVendorTypeWithPushType:targetType];
    [self.rtcPusher destroyRTCVendorWithDestoryTRTCCloud:YES];
    [self _buildRtcPusherIfNeeded:YES];
    RTCVideoEncodeInfo *encode = [RTCVideoEncodeInfo encodeInfoWithDispatchInfo:dispatchInfo vendorType:destVendor];
    [self.rtcPusher reloadVideoEncoderParam:encode];
    [XYLiveManagerSharedInstance.coreManager.media reloadCameraPreset:encode];
    XYLivePassport *passport = XYLiveManagerSharedInstance.passport;
    [self.rtcPusher hotSwitchWithTargetVendor:destVendor passport:passport];
    self.roomInfo.pushType = targetType;
    [self configHostLeavingImage:self.roomInfo.isVoiceLive];
    [self.rtcPusher startPush];
}

- (BOOL)_buildRtcPusherIfNeeded:(BOOL)rtcCoreDestoryTRTC {
    [self _destoryRtmpPusher];
    [self _destoryKasaPusher];
    if ([self rtcCoreEnable]) {
        [self destroyRTCCore:rtcCoreDestoryTRTC];
    }
    return [self rtcPusherInitialize];
}

- (void)_destoryRtmpPusher {
    if (self.pusher) {
        self.hotSwitchToRtc = YES;
        [self.pusher destory];
        self.pusher = nil;
    }
}

- (void)_destoryKasaPusher {
    if (self.kasaPusher) {
        self.hotSwitchToRtc = YES;
        [self.kasaPusher stopPush];
        [self.kasaPusher destory];
        self.kasaPusher = nil;
    }
}

/**
 Step 1. 组装Session
 Step 2. 装配VideoEncoder
 Step 3. 装配相机、美颜配置
 Step 4. 相机渲染
 */
- (BOOL)rtcPusherInitialize {
    if (!self.rtcPusher) {
        self.rtcPusher = [[RTCPusher alloc] init];
        // Step 1
        RTCChannelSession *rtcSession = [RTCChannelSession sessionWithRoomInfo:self.roomInfo userInfo:self.loginParam.userInfo passport:XYLiveManagerSharedInstance.imConfigParam.passport role:RTCMemberRoleHost audienceRtcLinkReference:XYLiveManagerSharedInstance.linkmicManager.audiencePushProtocolType];
        [self.rtcPusher loadRTChannelSession:rtcSession];
        [self _switchToTRTCIfNeeded];
        
        // 根据业务线获取不同的dispathInfo
        XYLiveBussinessType busType = XYLiveBussinessTypeVideo;
        // 录屏直播
        if (self.shouldScreenPushing) {
            busType = XYLiveBussinessTypeGame;
        }
        RTCVideoEncodeInfo *encodeInfo = nil;
        if (self.roomInfo.pushDispatchInfo.defaultResolution && XYAlphaSwitch.livePushDispatch) {
            if (XYConfigCenter().boolForKey(@"ios_live_enable_talk_encode", YES) == YES) {
                encodeInfo = [RTCVideoEncodeInfo encodeInfoWithDispatchInfo:self.roomInfo.pushDispatchInfo vendorType:rtcSession.vendorType contentType:self.roomInfo.contentType];
            } else {
                encodeInfo = [RTCVideoEncodeInfo encodeInfoWithDispatchInfo:self.roomInfo.pushDispatchInfo vendorType:rtcSession.vendorType bussType: busType];
            }
        } else {
            encodeInfo = [RTCVideoEncodeInfo encodeInfoWithResolution:self.roomInfo.encodedResolution vendorType:rtcSession.vendorType];
        }
        
        [self configHostLeavingImage:self.roomInfo.isVoiceLive];
        // Step 2
        [self.rtcPusher reloadVideoEncoderParam:encodeInfo];
        // Step 3
        [self setupPusher];
        // Step 4
        XYLiveManagerSharedInstance.coreManager.media.cameraType = XYLiveRTCVendorExpTypeTencent;
        // Step 5
        [self.rtcPusher setMicrophoneStatus:!XYLiveManagerSharedInstance.isMicrophoneOff];
        return YES;
    }
    return NO;
}

// 主播Rtmp开播，观众连线，强制切成trtc
- (void)_switchToTRTCIfNeeded {
//    BOOL enable = XYConfigCenter().boolForKey(@"ios_live_rtmp_linkmic_switch_trtc", NO);
    //    if (!enable) { return; }
    // rtmp开播 是否开启使用后端下发target
    BOOL enable = XYConfigCenter().boolForKey(@"all_pk_rtmp_use_target_push_type", NO);
    if (enable) {return;}

    XYLivePassport *passport = XYLiveManagerSharedInstance.passport;
    XYLiveRtcLinkType type = XYLiveManagerSharedInstance.linkmicManager.linkmicType;
    XYLiveUserInfo *receiver = nil;
    if (type == XYLiveRtcLinkTypeSubhostAudio || type == XYLiveRtcLinkTypeSubhostVideo) {
        if (receiver.currentPushType == XYLivePushTypeRtmp) {
            [self.rtcPusher hotSwitchWithTargetVendor:RTCVendorTypeTRTC passport:passport];
        }
    }
}

- (BOOL)canApplyRTC {
    // 手游开播篡改推流类型
//    if (XYLiveRoomPushContentTypeScreenCap == self.roomInfo.contentType) {
//        self.roomInfo.pushType = XYLivePushTypeKasa;
//        return YES;
//    }
    
    BOOL allowRTC = RTCSwitch;
    // 接口优先
    BOOL enable = XYConfigCenter().boolForKey(@"ios_live_pre_read_room_info_push_type", YES);
    if (enable) {
        if (XYConfigCenter().boolForKey(@"ios_use_rtc_engine_kasa", YES)) {
            allowRTC = self.roomInfo.pushType > XYLivePushTypeRtmp;
        } else {
            BOOL isKasa = [self canApplyKasa];
            allowRTC = (self.roomInfo.pushType > XYLivePushTypeRtmp && !isKasa);
        }
    }
    
    return (allowRTC || self.hotSwitchToRtc);
}

- (BOOL)canApplyKasa {
    return self.roomInfo.pushType == XYLivePushTypeKasa && !XYConfigCenter().boolForKey(@"ios_use_rtc_engine_kasa", YES);
}

- (BOOL)forceRTC {
    self.hotSwitchToRtc = YES;
    return [self canApplyRTC];
}

- (BOOL)rtmpPusherInitialize {
    if (!self.pusher) {
        self.pusher = [[XYLiveRtmpPusher alloc] init];
        RTCVideoEncodeInfo *encodeInfo = [RTCVideoEncodeInfo encodeInfoWithDispatchInfo:self.roomInfo.pushDispatchInfo vendorType:RTCVendorTypeNone];
        [self configHostLeavingImage:self.roomInfo.isVoiceLive];
        self.pusher.delegate = self;
        XYLiveManagerSharedInstance.coreManager.media.cameraType = XYLiveRTCVendorExpTypeRtmp;
        self.hotSwitchToRtc = NO;
        return YES;
    }
    return NO;
}

- (BOOL)kasaPusherInitialize {
    if (!self.kasaPusher) {
        self.kasaPusher = [[XYLiveKasaPusher alloc] init];
        RTCVideoEncodeInfo *encodeInfo = [RTCVideoEncodeInfo encodeInfoWithDispatchInfo:self.roomInfo.pushDispatchInfo vendorType:RTCVendorTypeNone];
        [self configHostLeavingImage:self.roomInfo.isVoiceLive];
        [self setupPusherDelegate];
        XYLiveManagerSharedInstance.coreManager.media.cameraType = XYLiveRTCVendorExpTypeRtmp;
        self.hotSwitchToRtc = NO;
        return YES;
    }
    return NO;
}

- (BOOL)rtcCoreEnable {
    return self.rtcCore != nil;
}

- (BOOL)isLinking {
    BOOL linking = [XYLiveManagerSharedInstance currentLinkType] != XYLiveLinkTypeNone;
    if (XYConfigCenter().boolForKey(@"ios_live_rtc_core_linking", NO)) {
        id<XYLiveMultiLinkPushServiceProtocol> linkService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveMultiLinkPushServiceProtocol)];
        linking = linking || linkService.isLinking;
    }
    
    return linking;
    
}

@end
