//
//  LivePushMultiChatDomain.swift
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

import XYAlphaFusion
import XYLiveServiceProtocol
import XYAlphaUtils
import XYLiveCore

@objcMembers
class LivePushMultiChatDomain: LiveBaseDomain<LivePushMultiChatDependProtocol>, LiveRoomInfoServiceProtocol {
    
    // 容器vc
    private weak var vc: XYLivePushMultiChatController?
    
    // 中间层，用于缓存数据和事件
    private lazy var serviceImpl: XYLivePushMultiChatService = {
        let service = XYLivePushMultiChatService()
        return service
    }()
    
    // 注册服务
    public override func doRegisterService(registery: ServiceRegistery) {
        registery.registerService(protocol: XYLivePushMultiChatServiceProtocol.self, service: self.serviceImpl)
    }
    
    // 声明依赖服务
    public override func doInjectService(provider: ServiceProvider) -> LivePushMultiChatDependProtocol {
        return LivePushMultiChatDepend(provider)
    }
    
    public override func didLoad() {
        super.didLoad()
        // 注册监听
        self.depend?.liveInfoService()?.registerRoomInfoListener(self)
    }
    
    public func onRoomInfoDidUpdate(_ oldRoomInfo: XYLiveRoomInfo?, newRoomInfo: XYLiveRoomInfo?) {
        // 畅聊直播间则直接跳出
        guard let depend = self.depend, let isLiveChat = newRoomInfo?.isLiveChat, !isLiveChat else { return }
        // 初始化容器vc
        let vc = XYLivePushMultiChatController(containerVC: self.hostViewController(), depend: depend)
        add(vc)
        self.vc = vc
        // 绑定服务
        serviceImpl.bindTarget(vc)
    }
}

