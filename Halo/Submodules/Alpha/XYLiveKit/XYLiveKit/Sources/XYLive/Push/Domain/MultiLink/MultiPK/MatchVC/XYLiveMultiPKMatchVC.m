//
//  XYLiveMultiPKMatchVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/28.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiPKMatchVC.h"
#import <XYLiveKit/XYLiveAlertViewController.h>
#import <XYLiveKit/XYLiveMultiPKMatchVM.h>
#import "XYLiveMultiPKMatchPanelVC.h"

@interface XYLiveMultiPKMatchVC()<XYLiveMultiPKMatchDelegate>

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, weak)   XYLiveMultiPKMatchPanelVC *panelVC;
@property (nonatomic, weak)   XYLiveAlertViewController *alertVC;
@property (nonatomic, strong) NSHashTable<id<XYLiveMultiPKMatchListener>> *listeners;
@property (nonatomic, strong) XYLiveMultiPKMatchVM *viewModel;

@end

@implementation XYLiveMultiPKMatchVC

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    // 注册监听
    [self.multiLinkService registerListener:self.viewModel];
}

// 发起匹配
- (void)startMatchWithSource:(XYLiveMultiPKMatchSource)source extraInfo:(NSString *_Nullable)extraInfo {
    // 判断是否处于匹配中
    if ([self isMatching]) {
        // 展示匹配中面板
        [self showMatchPanel];
        return;
    }
    // 发起匹配
    [self.viewModel startMatchWithSource:source extraInfo:extraInfo];
}

// 发起重新匹配
- (void)startReMatchWithExtraInfo:(NSString *_Nullable)extraInfo {
    WS
    XYLiveAlertViewController *alertVC = [[XYLiveAlertViewController alloc] initWithTitle:@"确认重新匹配" message:@"选择确定后，你将断开当前连线进行重新匹配" cancelButtonTitle:@"取消" confirmButtonTitle:@"确认" handler:^(NSInteger index) {
        SS
        if (index == 1) {
            [self.viewModel startReMatchWithExtraInfo:extraInfo];
        }
    }];
    [alertVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.alertVC = alertVC;
}

// 展示匹配面板
- (void)showMatchPanel {
    XYLiveMultiPKMatchPanelVC *panelVC = [[XYLiveMultiPKMatchPanelVC alloc] initWithContainerVC:self.containerVC liveInfoService:self.liveInfoService];
    WS
    panelVC.didTapCancelHandler = ^{
        SS
        [self cancelMatch];
    };
    [panelVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.panelVC = panelVC;
}

// 取消匹配
- (void)cancelMatch {
    [self.viewModel cancelMatch];
}

// 是否匹配中
- (BOOL)isMatching {
    return [self.viewModel isMatching];
}

// 注册监听
- (void)registerListener:(id<XYLiveMultiPKMatchListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners addObject:listener];
}

// 取消注册监听
- (void)unregisterListener:(id<XYLiveMultiPKMatchListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners removeObject:listener];
}

// 关闭弹窗
- (void)hidePanel {
    [self.alertVC dismissWithAnimated:YES complete:nil];
    [self.panelVC dismissWithAnimated:YES complete:nil];
}

#pragma mark - XYLiveMultiPKMatchDelegate

- (void)onUpdateMatchState:(XYLiveMultiLinkMatchState)state {
    // 事件转发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLiveMultiPKMatchListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onPKUpdateMatchState:)]) {
            [obj onPKUpdateMatchState:state];
        }
    }];
    // 关闭匹配面板
    if (state != XYLiveMultiLinkMatchStateMatching) {
        [self hidePanel];
    }
}

#pragma mark - Lazy

- (NSHashTable<id<XYLiveMultiPKMatchListener>> *)listeners {
    if (!_listeners) {
        _listeners = [NSHashTable weakObjectsHashTable];
    }
    return _listeners;
}

- (XYLiveMultiPKMatchVM *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[XYLiveMultiPKMatchVM alloc] initWithLiveInfoService:self.liveInfoService multiLinkService:self.multiLinkService];
        _viewModel.delegate = self;
    }
    return _viewModel;
}

@end
