//
//  BindCompleteBottomBar.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/5/8.
//  Copyright © 2025 XingIn. All rights reserved.
//

@objcMembers
class BindCompleteBottomBar: UIView, BindCardBottomBarProtocol {
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    convenience init(completion:(()->Void)?) {
        self.init()
        self.completionBtnCallback = completion
    }
    
    func setupUI() {
        self.addSubview(completeBtn)
        self.completeBtn.snp.makeConstraints { make in
            make.height.equalTo(40.0)
            make.top.equalToSuperview().offset(8.0)
            make.left.equalToSuperview().offset(16.0)
            make.right.equalToSuperview().offset(-16.0)
        }
    }
    
    var completionBtnCallback: (() -> Void)?
    var selectCardCountHandler: (() -> Int)?
    var isAllowSelectNone = false
    
    lazy var completeBtn = {
        let button = UIButton(type: .custom)
        button.layer.cornerRadius = 20.0
        button.backgroundColor = .ReDs.primary.light()
        button.setTitle(AdsLeadGenerationTools.getI18NLanguage(from: "完成"), for: .normal)
        button.setTitleColor(.ReDs.background.light(), for: .normal)
        button.titleLabel?.font = Theme.fontLarge
        button.addTarget(self, action: #selector(completionBtnDidPressed), for: .touchUpInside)
        button.backgroundColor = .ReDs.primary.withAlphaComponent(0.4)
        return button
    }()
    
    func completionBtnDidPressed() {
        guard !self.isAllowSelectNone else {
            // 直播前
            completionBtnCallback?()
            return
        }
        
        // 直播中
        if self.getSelectCardCount() > 0 { // 选择卡片数 大于 0
            completionBtnCallback?()
        } else {
            AdsLeadGenerationTools.showToast(message: "直播间线索工具至少需要挂载1张卡片")
        }
    }
    
    func showAllSubViews(isShow: Bool) {
        completeBtn.isHidden = !isShow
    }
    
    func update(shouldShowAllSelectBtn: Bool, shouldEnableCompleteBtn: Bool, shouldShowBottomBar:Bool) {
        self.showAllSubViews(isShow: shouldShowBottomBar)
        self.completeBtn.isEnabled = shouldEnableCompleteBtn
        if shouldEnableCompleteBtn {
            self.completeBtn.backgroundColor = .ReDs.primary.withAlphaComponent(1.0)
        } else {
            self.completeBtn.backgroundColor = .ReDs.primary.withAlphaComponent(0.4)
        }
        // 更新按钮状态
        self.updateCompleteBtnStatus()
    }
    
    func updateCompleteBtnStatus() {
        self.updateCompleteBtnFrame()
        self.updateCompleteBtnColor()
    }
    
    func updateCompleteBtnColor() {
        // 按钮不可点击，那就是透明红色
        if self.completeBtn.isEnabled == false {
            self.completeBtn.backgroundColor = .ReDs.primary.withAlphaComponent(0.4)
            return
        }
        
        // 直播前绑卡
        if isAllowSelectNone {
            self.completeBtn.backgroundColor = .ReDs.primary.withAlphaComponent(1.0)
        } else { // 直播中绑卡
            self.completeBtn.backgroundColor = self.getSelectCardCount() > 0 ? .ReDs.primary.withAlphaComponent(1.0) : .ReDs.primary.withAlphaComponent(0.4)
        }
    }
    
    func updateCompleteBtnFrame() {}
    
    func getSelectCardCount() -> Int {
        let count = self.selectCardCountHandler?() ?? 0
        return count
    }
    
    func update(selectAllCount: Int, selectedCount: Int){
    }
    
    func setCanSelectNone(isAllow: Bool) {
        self.isAllowSelectNone = isAllow
    }
    
    func setSelectCardCountHandler(handler: (() -> Int)?) {
        self.selectCardCountHandler = handler
    }
    // 最大可选卡片数
    func setLimitCardCountHandler(handler: (() -> Int)?) {}
}
