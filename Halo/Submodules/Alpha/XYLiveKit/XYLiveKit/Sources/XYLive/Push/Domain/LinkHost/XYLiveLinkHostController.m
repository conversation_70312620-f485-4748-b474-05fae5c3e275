//
//  XYLiveLinkHostController.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/27.
//  Copyright © 2025 XingIn. All rights reserved.
//
#import "XYLiveLinkHostController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveFoundation/XYLiveFoundation-Swift.h>
#import <XYLiveKit/XYLivePushDecorateView.h>
#import <XYLiveKit/XYLiveLinkHostInvitePanelVC.h>
#import <XYLiveKit/XYLiveLinkHostViewModel.h>
#import <XYLiveKit/XYLivePushUserCardService.h>
#import <XYLiveKit/XYLiveLinkHostSettingPanelVC.h>
#import <XYLiveKit/XYLiveLinkHostSearchPanelVC.h>
#import <XYLiveKit/XYLiveLinkHostInviteeInfo.h>
#import "XYLiveLinkHostBubbleVC.h"

@interface XYLiveLinkHostController()<XYLiveLinkHostDelegate, XYLivePushMultiPKListener, XYLivePushMultiLineListener>

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   id<XYLiveLinkHostDependProtocol> depend;
@property (nonatomic, weak)   XYLiveLinkHostInvitePanelVC *invitePanelVC;
@property (nonatomic, weak)   XYLiveLinkHostSearchPanelVC *searchPanelVC;
@property (nonatomic, weak)   XYLiveLinkHostSettingPanelVC *setttingPanelVC;
@property (nonatomic, weak)   XYLiveLinkHostBubbleVC *bubbleVC;
@property (nonatomic, strong) XYLiveLinkHostViewModel *viewModel;

@end

@implementation XYLiveLinkHostController

- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                             depend:(id<XYLiveLinkHostDependProtocol>)depend {
    if (self = [super init]) {
        _containerVC = containerVC;
        _depend = depend;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    // 初始化气泡vc
    [self setupBubbleVC];
    // 初始化底部栏vc
    [self setupBottomBarItemVC];

    // 注册监听
    [self.depend.multiLineService registerListener:self.viewModel];
    [self.depend.multiPKService registerListener:self.viewModel];
    
    // 注册ActionLink
    [self registerActionLink];
}

// 更新布局
- (void)updateLayoutType:(XYLiveMultiLinkLayoutType)layoutType completion:(void (^)(NSError * _Nonnull))completion {
    [self.viewModel updateLineSwitch:self.viewModel.configModel.lineSwitch layoutType:layoutType limitType:self.viewModel.configModel.lineLimitType completion:^(NSError * _Nonnull error) {
        if (error == nil) {
            [XYAlert live_showTextItemWithText:@"布局切换成功"];
        }
        completion ? completion(error) : nil;
    }];
}

// 展示设置面板
- (void)showLinkHostSettingPanel {
    XYLiveLinkHostSettingPanelVC *panelVC = [[XYLiveLinkHostSettingPanelVC alloc] initWithContainerVC:self.containerVC liveInfoService:self.depend.liveInfoService multiPKService:self.depend.multiPKService viewModel:self.viewModel];
    XYLiveNavigationChildController *nav = [[XYLiveNavigationChildController alloc] initWithRootChildController:panelVC];
    nav.portraitHeight = ^CGFloat{
        return kXYLiveLinkHostSettingPanelH;
    };
    nav.needReachBottom = YES;
    [nav showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.setttingPanelVC = panelVC;
}

// 注册deeplink
- (void)registerActionLink {
    WS
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"pk_panel" action:^(NSString * _Nonnull _, NSDictionary<NSString *,id> * _Nonnull param) {
        // 判断是否命中新框架
        if (![XYAlphaSwitch enableNewMultiLinkFramework]) { return; }
        // 展示邀请面板
        [self showInvitePanelIfNeedWithSource:XYLiveMultiLinkInviteSourceDeepLink extraInfo:nil];
    }];
    
    [XYLiveManagerSharedInstance.actionRouteManager registerWithPath:@"open_invite_host_panel" action:^(NSString * _Nonnull _, NSDictionary<NSString *,id> * _Nonnull param) {
        // 判断是否命中新框架
        if (![XYAlphaSwitch enableNewMultiLinkFramework]) { return; }
        // 展示邀请面板
        [self showInvitePanelIfNeedWithSource:XYLiveMultiLinkInviteSourceDeepLink extraInfo:nil];
    }];
}

#pragma mark - XYLiveLinkHostDelegate

- (void)onLinkUpdateState:(XYLiveLinkHostState)state {
    // 更新底部栏按钮状态
    [self refreshBottomItemStatusWithState:state];
    // 更新气泡信息
    [self.bubbleVC updateLinkState:state];
    // 非匹配中,其他情况都关闭面板
    if (state != XYLiveLinkHostStateMatch) {
        // 关闭面板
        [self hideAllPanel];
    }
}

- (void)onLinkUpdateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo isPK:(BOOL)isPK {
    // 同步邀请状态
    [self.invitePanelVC updateInviteState:state inviteeInfo:inviteeInfo isPK:isPK];
    [self.searchPanelVC updateInviteState:state inviteeInfo:inviteeInfo isPK:isPK];
}

#pragma mark - Event

// 点击连主播按钮
- (void)didTapLinkHost {
    // 判断是否处于连线中
    if (self.viewModel.isLinking) {
        // 展示功能操作面板
        [self.viewModel showOptFuncPanel];
        return;
    }
    // 判断是否处于匹配中
    if (self.viewModel.isMatching) {
        // 展示匹配面板
        [self.viewModel showMatchPanel];
        return;
    }
    // 展示邀请面板
    [self showInvitePanelVCWithSource:XYLiveMultiLinkInviteSourceBottomBar extraInfo:nil];
}

#pragma mark - Private

// 初始化气泡vc
- (void)setupBubbleVC {
    XYLiveLinkHostBubbleVC *bubbleVC = [[XYLiveLinkHostBubbleVC alloc] initWithContainerVC:self.containerVC liveInfoService:self.depend.liveInfoService adapterService:self.depend.adapterService];
    [self addViewController:bubbleVC];
    self.bubbleVC = bubbleVC;
}

// 初始化底部栏按钮vc
- (void)setupBottomBarItemVC {
    WS
    // 点击事件
    self.depend.adapterService.decorateView.bottomView.linkHostButtonPressedBlock = ^{
        SS
        [self didTapLinkHost];
    };
}

// 展示邀请面板
- (void)showInvitePanelVCWithSource:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString *_Nullable)extraInfo {
    [XYLogCollector xyLiveLogTag:kXYLiveLinkHostModuleName content:[NSString stringWithFormat:@"show invite panel vc<source:%@, extraInfo:%@>", @(source), extraInfo]];
    XYLiveLinkHostInvitePanelVC *panelVC = [[XYLiveLinkHostInvitePanelVC alloc] initWithContianerVC:self.containerVC liveInfoService:self.depend.liveInfoService multiLinkService:self.depend.multiLinkService];
    WS
    panelVC.didTapSearchHandler = ^{
        SS
        [self showSearchPanelVC];
    };
    panelVC.didTapSettingHandler = ^{
        SS
        [self showLinkHostSettingPanel];
    };
    panelVC.didTapMatchHandler = ^(BOOL isPK) {
        SS
        [self.viewModel startMatchWithIsPK:isPK];
    };
    panelVC.didTapInviteHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull inviteeInfo) {
        SS
        [self handleInviteWithInviteeInfo:inviteeInfo];
    };
    panelVC.didTapUserHandler = ^(XYLiveLinkHostInviteeInfo *inviteeInfo) {
        SS
        [self showUserCardWithInviteeInfo:inviteeInfo];
    };
    XYLiveNavigationChildController *nav = [[XYLiveNavigationChildController alloc] initWithRootChildController:panelVC];
    nav.portraitHeight = ^CGFloat{
        return kXYLiveLinkHostInvitePanelH;
    };
    nav.needReachBottom = YES;
    [nav showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.invitePanelVC = panelVC;
}

// 展示搜索面板
- (void)showSearchPanelVC {
    XYLiveLinkHostSearchPanelVC *panelVC = [[XYLiveLinkHostSearchPanelVC alloc] initWithContianerVC:self.containerVC liveInfoService:self.depend.liveInfoService multiLinkService:self.depend.multiLinkService];
    WS
    panelVC.didTapInviteHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull inviteeInfo) {
        SS
        [self handleInviteWithInviteeInfo:inviteeInfo];
    };
    panelVC.didTapUserHandler = ^(XYLiveLinkHostInviteeInfo *inviteeInfo) {
        SS
        [self showUserCardWithInviteeInfo:inviteeInfo];
    };
    [panelVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.searchPanelVC = panelVC;
}


// 展示用户信息卡
- (void)showUserCardWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    [self.depend.userCardService handleFetchingUserInfoAndPresentingWithUser:inviteeInfo.userInfo otherRole:XYLiveOtherRoleHostLiving sourceType:XYLiveInvokeSourceTypeDefault];
}

// 处理邀请/取消操作
- (void)handleInviteWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    if (inviteeInfo.isInvited) {
        [self.viewModel cancelInviteWithInviteeInfo:inviteeInfo];
    } else {
        [self.viewModel inviteWithInviteeInfo:inviteeInfo];
    }
}

// 关闭面板
- (void)hideAllPanel {
    [self.invitePanelVC.navigationChildController dismissWithAnimated:YES complete:nil];
    [self.searchPanelVC dismissWithAnimated:YES complete:nil];
    [self.setttingPanelVC.navigationChildController dismissWithAnimated:YES complete:nil];
}

// 刷新底部栏按钮状态
- (void)refreshBottomItemStatusWithState:(XYLiveLinkHostState)state {
    NSDictionary *configMap = @{
        @(XYLiveLinkHostStateMatch): self.viewModel.resource.matchingLottie,
        @(XYLiveLinkHostStateLine): self.viewModel.resource.liningImage,
        @(XYLiveLinkHostStatePK): self.viewModel.resource.pkingImage,
        @(XYLiveLinkHostStateLikePK): self.viewModel.resource.likePKingImage,
        @(XYLiveLinkHostStateIdle): self.viewModel.resource.normalImage,
    };
    NSDictionary *config = [configMap safeObjectForKey:@(state)];
    if (config == nil) {
        config = self.viewModel.resource.normalImage;
    }
    // 更新状态
    XYLivePushBottomButton *itemView = self.depend.adapterService.decorateView.bottomView.linkHostButton;
    [itemView setConfig:config];
}

// 展示邀请面板
- (void)showInvitePanelIfNeedWithSource:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString *_Nullable)extraInfo {
    // 判断是否连线中
    if (self.viewModel.isLinking) {
        [self.viewModel showOptFuncPanel];
    } else {
        [self showInvitePanelVCWithSource:source extraInfo:nil];
    }
}

#pragma mark - Lazy

- (XYLiveLinkHostViewModel *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[XYLiveLinkHostViewModel alloc] initWithLiveInfoService:self.depend.liveInfoService multiLineService:self.depend.multiLineService multiPKService:self.depend.multiPKService];
        _viewModel.delegate = self;
    }
    return _viewModel;
}

@end
