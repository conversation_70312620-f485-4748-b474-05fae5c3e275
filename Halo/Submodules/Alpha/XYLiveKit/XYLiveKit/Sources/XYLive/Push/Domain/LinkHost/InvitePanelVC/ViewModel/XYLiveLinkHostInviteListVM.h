//
//  XYLiveLinkHostInviteListVM.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/20.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkBizConsts.h>
@class XYLiveLinkHostInviteeInfo;

NS_ASSUME_NONNULL_BEGIN


@interface XYLiveLinkHostInviteListVM : NSObject

// 数据源
@property (nonatomic, copy, readonly)   NSArray *dataSource;
// 是否选中PK
@property (nonatomic, assign, readonly) BOOL isPK;

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

// 请求列表数据
- (void)requestListDataWithIsPK:(BOOL)isPK completion:(void(^)(NSError *error))completion;

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo isPK:(BOOL)isPK;

@end

NS_ASSUME_NONNULL_END
