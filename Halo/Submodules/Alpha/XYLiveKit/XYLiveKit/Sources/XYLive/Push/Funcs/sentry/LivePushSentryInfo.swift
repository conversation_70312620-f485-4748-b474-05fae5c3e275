//
//  LivePushSentryInfo.swift
//  XYLiveKit
//
//  Created by wangzhenxing on 2024/9/13.
//  Copyright © 2024 XingIn. All rights reserved.
//

import Foundation
import XYStorageCore

@objc(XYLivePushSentryInfo)
@objcMembers
public class LivePushSentryInfo: NSObject {
    private static var sentryInfo  = Dictionary<String, String>()
    public static var livepushOpenTime: Int = 0
    private static let infroKey = "ios_sentry_tag_livepush_info"
    private static let infoAccessQueue = DispatchQueue(label: "com.xylive.sentryInfo.queue")
    
    private static func infoQueue() -> DispatchQueue {
        if enableQueue() {
            return infoAccessQueue
        } else {
            return DispatchQueue.global()
        }
    }
    
    public static func sentryRecordLivePushInfo(_ roomId: String, liveType: String) {
        if enableUpdate() {
            infoQueue().async {
                var tagInfo = Dictionary<String, String>()
                tagInfo["livepush_roomId"] = roomId
                tagInfo["liveType"] = liveType
                sentryInfo = tagInfo
                XYStorageCenter.default().permanent().setDictionary(sentryInfo, forKey: infroKey)
            }
        } else {
            DispatchQueue.global().async {
                let tagInfo = NSMutableDictionary()
                tagInfo["livepush_roomId"] = roomId
                tagInfo["liveType"] = liveType
                UserDefaults.standard.setValue(tagInfo, forKey: infroKey)
            }
        }
    }
    
    public static func updateSentryRecordLivePushInfo(_ roomId: String, info: Dictionary<String, String>) {
        infoQueue().async {
            let tagInfo = sentryInfo
            if let livepush_roomId = tagInfo["livepush_roomId"] as String?, livepush_roomId == roomId {
                var newInfo = Dictionary<String, String>()
                for (key, value) in tagInfo {
                    newInfo[key] = value
                }
                for (key, value) in info {
                    newInfo[key] = value
                }
                sentryInfo = newInfo
                XYStorageCenter.default().permanent().setDictionary(sentryInfo, forKey: infroKey)
            } else {
                var tagInfo = Dictionary<String, String>()
                tagInfo["livepush_roomId"] = roomId
                for (key, value) in info {
                    tagInfo[key] = value
                }
                sentryInfo = tagInfo
                XYStorageCenter.default().permanent().setDictionary(sentryInfo, forKey: infroKey)
            }
        }
    }
    
    public static func cleanSentryRecordLivePushInfo() {
        if enableUpdate() {
            if enableQueue() {
                infoQueue().async {
                    sentryInfo = Dictionary<String, String>()
                    XYStorageCenter.default().permanent().removeValue(forKey: infroKey)
                }
            } else {
                sentryInfo = Dictionary<String, String>()
                XYStorageCenter.default().permanent().removeValue(forKey: infroKey)
            }
        } else {
            if ConfigCenter.shared.justOnceBool(forKey: "ios_livepush_sentry_record_enable_clean", defalut: true) {
                UserDefaults.standard.removeObject(forKey: infroKey)
            }
        }
    }
    
    public static func enable() -> Bool {
        return ConfigCenter.shared.justOnceBool(forKey: "ios_livepush_sentry_record_enable", defalut: true)
    }
    
    public static func enableUpdate() -> Bool {
        return ConfigCenter.shared.justOnceBool(forKey: "ios_livepush_sentry_record_enable_update", defalut: true)
    }
    
    private static func enableQueue() -> Bool {
        return ConfigCenter.shared.justOnceBool(forKey: "ios_livepush_sentry_record_enable_queue", defalut: false)
    }
}
