//
//  XYLiveMultiChatInviteListAudienceCell.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <UIKit/UIKit.h>
@class XYLiveMultiChatInviteListAudienceItem, XYTrackerEventContext;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiChatInviteListAudienceCell : UITableViewCell

// 点击邀请
@property (nonatomic, copy) void(^didTapInviteHandler)(XYLiveMultiChatInviteListAudienceItem *item, NSIndexPath *indexPath);

// 点击埋点
@property (nonatomic, copy) XYTrackerEventContext*(^didTapTrackerHandler)(XYLiveMultiChatInviteListAudienceItem *item, NSIndexPath *indexPath);

/// 数据绑定
- (void)bindListItems:(NSArray*)listItems indexPath:(NSIndexPath *)indexPath;

@end

NS_ASSUME_NONNULL_END
