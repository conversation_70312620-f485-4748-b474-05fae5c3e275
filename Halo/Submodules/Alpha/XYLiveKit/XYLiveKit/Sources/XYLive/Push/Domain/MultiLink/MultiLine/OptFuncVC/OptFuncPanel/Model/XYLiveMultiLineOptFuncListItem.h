//
//  XYLiveMultiLineOptFuncListItem.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/22.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveKit/XYLiveLinkHostInviteeInfo.h>
#import "XYLiveMultiLineOptFuncPanelConsts.h"

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiLineOptFuncListItem : NSObject

@end


@interface XYLiveMultiLineOptFuncListFriendItem : XYLiveMultiLineOptFuncListItem

// 好友列表
@property (nonatomic, copy) NSArray<XYLiveLinkHostInviteeInfo *> *list;

@end

@interface XYLiveMultiLineOptFuncListBizItem : XYLiveMultiLineOptFuncListItem

// 图标
@property (nonatomic, copy)   NSString *iconURL;
// 标题
@property (nonatomic, copy)   NSString *title;
// 副标题
@property (nonatomic, copy)   NSString *subTitle;
// 操作类型
@property (nonatomic, copy)   NSArray<NSNumber *> *optTypes;
// 是否进行中
@property (nonatomic, assign) BOOL isPlaying;
// 玩法类型
@property (nonatomic, assign) XYLiveMultiLinkBizType bizType;

@end

NS_ASSUME_NONNULL_END
