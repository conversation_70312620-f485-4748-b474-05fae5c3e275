//
//  XYLiveLinkHostInviteListSectionView.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/19.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostInviteListSectionView.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveKit/XYLiveLinkHostInviteListCheckBoxView.h>
#import <XYLiveKit/XYLiveLinkHostInvitePanelConsts.h>
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <Masonry/Masonry.h>

@interface XYLiveLinkHostInviteListSectionView()

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) XYLiveLinkHostInviteListCheckBoxView *checkBoxView;

@end

@implementation XYLiveLinkHostInviteListSectionView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

- (void)setChecked:(BOOL)checked {
    _checked = checked;
    self.checkBoxView.checked = checked;
}

// 更新顶部圆角
- (void)setTopRadius:(CGFloat)topRadius {
    if (_topRadius == topRadius) { return; }
     [self live_updateMaskWithRoundedRect:self.bounds topLeftRadius:topRadius topRightRadius:topRadius bottomLeftRadius:0 bottomRightRadius:0];
    _topRadius = topRadius;
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 设置背景色
    self.backgroundColor = [XYLiveTokenColor bg];
    // 创建标题
    [self setupTitleLabel];
    // 创建复选框控件
    [self setupCheckBoxView];
    
    // 布局
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(kXYLiveLinkHostPanelPadding);
        make.centerY.equalTo(self);
    }];
    
    [self.checkBoxView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-kXYLiveLinkHostPanelPadding);
        make.centerY.equalTo(self);
        make.height.mas_equalTo(30);
    }];
}

- (void)setupTitleLabel {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:16];
    titleLabel.textColor = [XYLiveTokenColor neutralBlack];
    titleLabel.text = @"选择要邀请的主播";
    [self addSubview:titleLabel];
    self.titleLabel = titleLabel;
}

- (void)setupCheckBoxView {
    XYLiveLinkHostInviteListCheckBoxView *checkBoxView = [[XYLiveLinkHostInviteListCheckBoxView alloc] init];
    checkBoxView.layer.cornerRadius = 15;
    checkBoxView.layer.masksToBounds = YES;
    WS
    checkBoxView.didTapHandler = ^(BOOL isChecked) {
        SS
        self.didTapHandler ? self.didTapHandler(isChecked) : nil;
    };
    [self addSubview:checkBoxView];
    self.checkBoxView = checkBoxView;
}

@end
