//
//  XYLiveMultiChatInviteListViewModel.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveKit/XYLiveMultiChatInviteListModel.h>
@class XYPMMessageShareToMessageLiveModel, XYPMMessageChatUserModel;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiChatInviteListViewModel : NSObject

// 列表数据
@property (nonatomic, copy, readonly) NSArray<XYLiveMultiChatInviteListGroupItem *> *listItems;
// 更新缓存状态
@property (nonatomic, copy) BOOL(^didFetchInviteStatusHandler)(XYLiveMultiChatInviteListAudienceItem * listItem);

// 请求列表数据
- (void)requestListDataWithRoomId:(NSString *)roomId
                       completion:(void(^)(NSArray<XYLiveMultiChatInviteListGroupItem *> *listItems, NSError *error))completion;

// 邀请好友
- (void)inviteFriendWithRoomId:(NSString *)roomId
                        userId:(NSString *)userId
                    completion:(void(^)(XYPMMessageShareToMessageLiveModel * _Nullable shareModel, XYPMMessageChatUserModel * _Nullable userInfo, NSError * _Nullable error))completion;

// 更新邀请状态
- (void)updateInviteStatusWithUserId:(NSString *)userId isInvited:(BOOL)isInvited;

@end

NS_ASSUME_NONNULL_END
