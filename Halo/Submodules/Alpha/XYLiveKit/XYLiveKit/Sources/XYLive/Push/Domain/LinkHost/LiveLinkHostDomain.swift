//
//  LiveLinkHostDomain.swift
//  XYLiveKit
//
//  Created by 大远 on 2025/5/27.
//  Copyright © 2025 XingIn. All rights reserved.
//

import Foundation
import XYAlphaFusion
import XYLiveServiceProtocol

@objcMembers
class LiveLinkHostDomain: LiveBaseDomain<LiveLinkHostDependProtocol>, LiveRoomInfoServiceProtocol {
    
    override class func enableDomain() -> Bool {
        return XYAlphaSwitch.enableNewMultiLinkFramework()
    }
    
    // 中间层，用于缓存数据和事件
    private lazy var serviceImpl: XYLiveLinkHostService = {
        let service = XYLiveLinkHostService()
        return service
    }()
    
    // 声明依赖服务
    override func doInjectService(provider: ServiceProvider) -> LiveLinkHostDependProtocol {
        return LiveLinkHostDepend(provider)
    }
    
    // 注册服务
    public override func doRegisterService(registery: ServiceRegistery) {
        registery.registerService(protocol: XYLiveLinkHostServiceProtocol.self, service: self.serviceImpl)
    }
    
    override func didLoad() {
        super.didLoad()
        // 注册监听
        self.depend?.liveInfoService()?.registerRoomInfoListener(self)
    }
    
    func onRoomInfoDidUpdate(_ oldRoomInfo: XYLiveRoomInfo?, newRoomInfo: XYLiveRoomInfo?) {
        // 合法性校验
        guard let depend = self.depend, let roomInfo = newRoomInfo else { return }
        // 游戏和畅聊直播间不处理
        if roomInfo.isScreenLive || roomInfo.isLiveChat { return }
        // 初始化容器vc
        let vc = XYLiveLinkHostController(containerVC: self.hostViewController(), depend: depend)
        add(vc)
        // 绑定服务
        serviceImpl.bindTarget(vc)
    }
    
}

