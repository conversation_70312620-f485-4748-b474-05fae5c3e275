//
//  XYLiveMultiPKInviteVC.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/26.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiPKInviteServiceProtocol.h>


NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiPKInviteVC : XYViewController<XYLiveMultiPKInviteServiceProtocol>

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

// 注册监听
- (void)registerListener:(id<XYLiveMultiPKInviteListener>)listener;

// 取消注册监听
- (void)unregisterListener:(id<XYLiveMultiPKInviteListener>)listener;

@end

NS_ASSUME_NONNULL_END
