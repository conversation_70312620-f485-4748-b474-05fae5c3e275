//
//  GlobalWindowManager+MultiLinkEvent.swift
//  XYLiveKit
//
//  Created by 王帅 on 2023/11/30.
//  Copyright © 2023 XingIn. All rights reserved.
//

import Foundation
import XYLiveCore
import XYLivePlayManager
import RedI18N
import I18N

extension GlobalWindowManager: MultiLinkEventProtocol {
    public func onImLinkOnMicUserChanged(_ info: IMLinkmicOnMicChanged) {
        if self.role == .host {
            if info.userID == XYLiveManager.shared().hostInfo?.userID {
                if info.isOnMic() == true {
                    self.linkUtil?.onHostStartLink(info: info)
                } else {
                    self.linkUtil?.onHostStopLink(info: info)
                }
            }
        } else {
            if info.userID == XYLiveManager.shared().userInfo.userID {
                if info.isOnMic() == false {
                    self.linkUtil?.onAudienceStopLink(info: info)
                }
            }
        }
    }
    
    public func onIMLinkmicLayoutInfo(_ info: LinkLayoutInfo, timeStamp: TimeInterval, source: LinkLayoutSource) {
        transferLinkState()
    }
    
    public func onIMLinkmicTerminateAllV2() {
        transferLinkState()
    }
    
    public func onIMLinkOnMicUserJoin(_ userId: String) {
        guard XYAlphaSwitch.globalWindowLinkApmFix() else { return }
        guard floating, isDismissVC else { return }
        if  XYSessionManager.shared().user?.userId ?? "" != userId, !userId.isEmpty, !userId.contains("null") {
            XYLiveManager.shared().coreManager.media.linkMic?.globalWindowFillLinkAPM(userID: userId)
        }
    }
    
    public func onIMLinkOnMicUserLeave(_ userId: String) {
        guard self.role == .audience else {
            return
        }
        
        if let session = self.rtcCore?.rtcChannelSession,
           session.isSelf(userId) {
            self.onMicAudienceReport(reason: "HOST_STOP")
            self.destoryLink()
            self.startPullPlay()
            XYAlert.live_showTextItem(withText: L.live.live_disconnect)
        }
    }
    
    public func onIMHostSwitchOtherMicrophone(_ info: IMLinkSwitchUserAudio) {
        guard let liveService = Router.destination(protocol: Routable<LiveInvokableService>()), liveService.enjoying() else {
            if info.userId == XYLiveManager.shared().userInfo.userID {
                self.onChangeAudio(user: info.userId, operatorId: info.operatorId, mute: info.enable == 0)
            }
            return
        }
    }
    
    private func onChangeAudio(user: String, operatorId: String?, mute: Bool) {
        MultiLinkmicCommService.switchAudio(operatorId: operatorId, enable: mute ? 0 : 1) { success, error in
            print("multilink switchaudio \(mute) \(success)")
            if error == nil {
                self.rtcCore?.disableAudioRecording(disable: mute)
                XYLiveManager.shared().isMicrophoneOff = mute
                self.collectLog(content: "audio status change success: \(!mute)")
            }
        }
    }
}
