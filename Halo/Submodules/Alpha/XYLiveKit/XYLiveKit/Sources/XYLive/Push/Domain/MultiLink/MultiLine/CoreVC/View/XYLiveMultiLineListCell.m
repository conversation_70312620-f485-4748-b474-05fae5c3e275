//
//  XYLiveMultiLineListCell.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/16.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineListCell.h"
#import <XYLiveKit/XYLiveMultiLinkUserInfoWidget.h>
#import <XYLiveKit/XYLiveMultiLinkSuppendWidget.h>
#import <XYLiveKit/XYLiveMultiLinkEmptyPHWidget.h>
#import <Masonry/Masonry.h>

@interface XYLiveMultiLineListCell()

@property (nonatomic, strong) XYLiveMultiLinkEmptyPHWidget *emptyPHWidget;
@property (nonatomic, strong) XYLiveMultiLinkUserInfoWidget *userInfoWidget;
@property (nonatomic, strong) XYLiveMultiLinkSuppendWidget *suppendWidget;

@end

@implementation XYLiveMultiLineListCell

- (void)bindDataWithWindowInfo:(XYLiveMultiLinkWindowInfo *)windowInfo mediaInfo:(XYLiveMultiLinkMediaInfo *)mediaInfo {
    [super bindDataWithWindowInfo:windowInfo mediaInfo:mediaInfo];
    // 更新高度
    [self.userInfoWidget.widgetView mas_updateConstraints:^(MASConstraintMaker *make) {
        CGFloat height = windowInfo.layoutType == XYLiveMultiLinkLayoutTypeFloat ? 30 : 36;
        make.height.mas_equalTo(height);
    }];
}

#pragma mark - Override

- (void)setupSubviews {
    [super setupSubviews];
    // 创建空麦位视图
    [self setupEmptyPHWidget];
    // 创建暂停组件
    [self setupSuppendWidget];
    // 创建用户信息组件
    [self setupUserInfoWidget];
    
    // 布局
    [self.emptyPHWidget.widgetView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    
    [self.suppendWidget.widgetView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    
    [self.userInfoWidget.widgetView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.bottom.equalTo(self);
        make.height.mas_equalTo(36);
    }];
}

#pragma mark - 创建子视图

// 创建空麦位组件
- (void)setupEmptyPHWidget {
    XYLiveMultiLinkEmptyPHWidget *emptyPHWidget = [[XYLiveMultiLinkEmptyPHWidget alloc] init];
    [self addWidget:emptyPHWidget];
    self.emptyPHWidget = emptyPHWidget;
}

// 创建暂停组件
- (void)setupSuppendWidget {
    XYLiveMultiLinkSuppendWidget *widget = [[XYLiveMultiLinkSuppendWidget alloc] init];
    [self addWidget:widget];
    self.suppendWidget = widget;
}

// 创建用户信息组件
- (void)setupUserInfoWidget {
    XYLiveMultiLinkUserInfoWidget *widget = [[XYLiveMultiLinkUserInfoWidget alloc] init];
    [self addWidget:widget];
    self.userInfoWidget = widget;
}

@end
