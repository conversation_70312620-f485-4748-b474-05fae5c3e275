//
//  GlobalWindowManager+Audience.swift
//  XYLiveKit
//
//  Created by 王帅 on 2023/5/24.
//  Copyright © 2023 XingIn. All rights reserved.
//

import Foundation
import XYLiveCore
import XYLivePlayManager
import XYConfigCenter

public extension GlobalWindowManager {
    func registerAudienceIM() {
        imDistributer?.register(type: kIMCUSTOM_LIVE_PUSH_SDK_TYPE_CHANGE) { [weak self] type, _, rawData in
            self?.collectLog(content: "receive im type: \(type)")
            if let content = try? XYLivePusherChangeInfo.xy_model(dictionary: rawData) {
                guard let self = self else { return }
                defer {
                    self.reportPushSwitchResult()
                }
                guard self.isLinking() else {
                    // 起推流器trtc & kasa后续
                    self.startLink()
                    return
                }
                
                guard self.rtcCore?.pushType != content.pushType else {
                    return
                }
                
                // 后续接kasa & trtc 互切
            }
        }
    }
    
    func onMicAudienceReport(reason: String) {
        if let info = self.linkUtil?.onMicInfo {
            info.stopReasonDesc = reason
            self.linkUtil?.onAudienceStopLink(info: info)
        }
    }
    
    func onMicAudienceClose(selfOp: Bool) {
        guard floating else {
            return
        }
        self.collectLog(content: "audience close by self \(selfOp)")
        self.onMicAudienceReport(reason: selfOp ? "USER_QUIT" : "ROOM_CLOSE")
        self.destoryLink()
        self.dismiss()
        
        if ConfigCenter.shared.bool(forKey: "ios_live_floating_manager_dealloc", defalut: false) == false {
            guard let liveService = Router.destination(protocol: Routable<LiveInvokableService>()), liveService.enjoying() else {
                XYLiveManager.shared().onDealloc()
                return
            }
        } else {
            XYLiveManager.shared().onDealloc()
        }
    }
    
    func startPullPlay() {
        guard floating else {
            return
        }
        collectLog(content: "start pull play")
        let playManager = XYLivePlayManager.sharedInstance()
        XYLiveMediaControl.reloadAudioSession(receiver: playManager,
                                              sceneType: .play)
        // 开始拉流
        let playUrl = XYLiveManager.shared().roomInfo.streamURLInfo.liveFLVPlayURL
        let builder = LivePlaybackStrategyBuilder().url(playUrl).delegate(self).containerView(self.floatingView).source("global_window")
        XYLivePlayManager.sharedInstance().startupPlayer(builder.buildV2())
        collectLog(content: "start pull play url \(builder.playingURL) \(String(describing: builder.playerDelegate)) \(String(describing: builder.superView) )")
        playManager.selfChooseFloatPreview = true
        let roomId = XYLiveManager.shared().roomInfo.roomIDStr
        let hostId = XYLiveManager.shared().roomInfo.hostInfo.userID ?? ""
        let sessionId = XYLiveAnalyticsDataSource.sharedInstance().sessionID ?? ""
        let asset = XYLivePlayerAsset(playURL: playUrl, roomID: roomId, hostUserID: hostId, sessionID: sessionId, dataSource: XYLiveAnalyticsDataSource.sharedInstance())
        playManager.initFloating(with: asset)
        playManager.startPreview()
        playManager.floatingPlayView?.center = self.floatingView.center
        dismiss()
        playManager.mute(false)
        
        guard let liveService = Router.destination(protocol: Routable<LiveInvokableService>()), liveService.enjoying() else {
            XYLiveManager.shared().onDealloc()
            return
        }
    }
}

extension GlobalWindowManager: XYLivePlayerEventDelegate {}
