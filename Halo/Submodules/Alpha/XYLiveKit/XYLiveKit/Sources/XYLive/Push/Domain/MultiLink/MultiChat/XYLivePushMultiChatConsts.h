//
//  XYLivePushMultiChatConsts.h
//  XYLiveKit
//
//  Created by 大远 on 2024/11/6.
//  Copyright © 2024 XingIn. All rights reserved.
//
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkConsts.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkBizConsts.h>
#import <XYLiveUIKit/XYLiveUIKit.h>

#ifndef XYLivePushMultiChatConsts_h
#define XYLivePushMultiChatConsts_h

// 主播端模块名称
#define kXYLivePushMultiChatModule            @"MultiLink-Push-Chat"

// 列表类型
typedef NS_ENUM(NSUInteger, XYLiveMultiChatInviteListType) {
    XYLiveMultiChatInviteListTypeFriend,   // 好友列表
    XYLiveMultiChatInviteListTypeAudience, // 观众列表
};

// 连麦限制条件
typedef NS_ENUM(NSUInteger, XYLiveMultiChatLimitType) {
    XYLiveMultiChatLimitTypeAll,    // 所有观众
    XYLiveMultiChatLimitTypeFollow, // 仅关注
    XYLiveMultiChatLimitTypeFans,   // 仅粉丝团
    XYLiveMultiChatLimitTypeGift    // 送礼
};

// 邀请面板高度
#define kXYLiveMultiChatInvitePanelHeight                 DeviceUtility.screenHeight * 0.75

// 空占位cell高度
#define kXYLiveMultiChatInviteListEmptyCellHeight         kXYLiveMultiChatInvitePanelHeight - 50 - 32 - 80 - 8 - [XYLiveStandardContentHeight applicationSafeBottom]

// 关闭连麦提示文案
#define kXYLiveMultiChatInviteSwitchCloseToast            @"已关闭观众连线"

// 接通提示文案
#define kXYLiveMultiChatInviteAcceptSuccessToast          @"申请已接受，等待接通中"

#endif /* XYLivePushMultiChatConsts_h */
