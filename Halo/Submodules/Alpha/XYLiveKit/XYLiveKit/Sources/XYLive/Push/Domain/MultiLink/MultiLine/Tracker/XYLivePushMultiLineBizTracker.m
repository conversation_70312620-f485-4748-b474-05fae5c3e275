//
//  XYLivePushMultiLineBizTracker.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/30.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushMultiLineBizTracker.h"
#import <XYSessionManager/XYSessionManager.h>

@implementation XYLivePushMultiLineBizTracker

/* 直播开播页/连线中-邀请更多/点击 */
+ (void)eventActionId80514WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80514)/* 直播开播页/连线中-邀请更多/点击 */._igoto(1)._mid(47938)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/连线中-结束邀请/点击 */
+ (void)eventActionId80515WithRoomId:(NSString *)roomId inviteeUserId:(NSString *)inviteeUserId hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80515)/* 直播开播页/连线中-结束邀请/点击 */._igoto(1)._mid(47939)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")._targetAnchorId(inviteeUserId)
     send];
}

/* 直播开播页/连线中-头像/点击 */
+ (void)eventActionId80516WithRoomId:(NSString *)roomId inviteeUserId:(NSString *)inviteeUserId hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80516)/* 直播开播页/连线中-头像/点击 */._igoto(1)._mid(47940)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")._targetAnchorId(inviteeUserId)
     send];
}

/* 直播开播页/连线中-结束/点击 */
+ (void)eventActionId80517WithRoomId:(NSString *)roomId channelTabName:(NSString *)channelTabName hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._channelTabName(channelTabName)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80517)/* 直播开播页/连线中-结束/点击 */._igoto(1)._mid(47941)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/连线中-玩法操作/点击 */
+ (void)eventActionId80518WithRoomId:(NSString *)roomId channelTabName:(NSString *)channelTabName channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._channelTabName(channelTabName)._channelTabType(channelTabType)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80518)/* 直播开播页/连线中-玩法操作/点击 */._igoto(1)._mid(47942)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/匹配中-放弃匹配/点击 */
+ (void)eventActionId80519WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._channelTabType(channelTabType)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80519)/* 直播开播页/匹配中-放弃匹配/点击 */._igoto(1)._mid(47943)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/邀请连线-拒绝/点击 */
+ (void)eventActionId80520WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._channelTabType(channelTabType)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80520)/* 直播开播页/邀请连线-拒绝/点击 */._igoto(1)._mid(47944)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/邀请连线-接受/点击 */
+ (void)eventActionId80521WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._channelTabType(channelTabType)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80521)/* 直播开播页/邀请连线-接受/点击 */._igoto(1)._mid(47945)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/邀请连线-多人连线邀请的更多/点击 */
+ (void)eventActionId80522WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80522)/* 直播开播页/邀请连线-多人连线邀请的更多/点击 */._igoto(1)._mid(47946)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/邀请连线面板/曝光 */
+ (void)eventActionId80529WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._channelTabType(channelTabType)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Impression)._pid(80529)/* 直播开播页/邀请连线面板/曝光 */._igoto(2)._mid(48097)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

@end
