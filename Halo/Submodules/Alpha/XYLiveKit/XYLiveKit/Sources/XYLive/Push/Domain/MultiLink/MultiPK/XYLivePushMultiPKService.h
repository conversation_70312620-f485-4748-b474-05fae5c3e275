//
//  XYLivePushMultiPKService.h
//  XYLiveKit
//
//  Created by 大远 on 2025/4/24.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveServiceProtocol/XYLivePushMultiPKServiceProtocol.h>
#import <XYLiveKit/XYLivePushMultiPKController.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiPKService : NSObject<XYLivePushMultiPKServiceProtocol>

// 绑定服务
- (void)bindTarget:(XYLivePushMultiPKController *)target;

@end

NS_ASSUME_NONNULL_END
