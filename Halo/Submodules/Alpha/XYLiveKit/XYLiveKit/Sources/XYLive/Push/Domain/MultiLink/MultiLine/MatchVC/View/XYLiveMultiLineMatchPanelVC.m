//
//  XYLiveMultiLineMatchPanelVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/4.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineMatchPanelVC.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveKit/XYLiveImageLottieView.h>
#import <XYLiveKit/XYLiveMultiLinkResourceManager.h>
#import <XYLiveKit/XYLivePushMultiLineBizTracker.h>
#import <Masonry/Masonry.h>

@interface XYLiveMultiLineMatchPanelVC()

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UILabel *subTitleLabel;
@property (nonatomic, strong) XYLiveImageLottieView *lottieView;
@property (nonatomic, strong) UIButton *cancelBtn;
@property (nonatomic, strong) XYLiveMultiLinkResourceManager *resource;

@end

@implementation XYLiveMultiLineMatchPanelVC

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _liveInfoService = liveInfoService;
    }
    return self;
}

- (void)setupSubViews {
    [super setupSubViews];
    self.containerView.backgroundColor = [XYLiveTokenColor bg];
    self.contentView.backgroundColor = [XYLiveTokenColor bg];
    // 创建标题
    [self setupTitleLabel];
    // 创建副标题
    [self setupSubTitleLabel];
    // 创建StackView
    [self setupLottieView];
    // 创建取消按钮
    [self setupCancelBtn];
    
    // 布局
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(24);
        make.centerX.equalTo(self.contentView);
    }];
    
    [self.subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(4);
        make.centerX.equalTo(self.contentView);
    }];
    
    [self.lottieView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.subTitleLabel.mas_bottom).offset(4);
        make.centerX.equalTo(self.contentView);
        make.height.mas_equalTo(100);
    }];
    
    [self.cancelBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(16);
        make.right.equalTo(self.contentView).offset(-16);
        make.height.mas_equalTo(40);
        make.bottom.equalTo(self.contentView.mas_safeBottom).offset(-8);
    }];
    
}

#pragma mark - 创建子视图

- (void)setupTitleLabel {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"匹配中...";
    titleLabel.font = [UIFont fontWithName:@"PingFangSC-Semibold" size:18];
    titleLabel.textColor = [XYLiveTokenColor title];
    [self.contentView addSubview:titleLabel];
    self.titleLabel = titleLabel;
}

- (void)setupSubTitleLabel {
    UILabel *subTitleLabel = [[UILabel alloc] init];
    subTitleLabel.text = @"正在寻找其他在线主播进行连麦";
    subTitleLabel.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
    subTitleLabel.textColor = [XYLiveTokenColor desc];
    [self.contentView addSubview:subTitleLabel];
    self.subTitleLabel = subTitleLabel;
}

- (void)setupLottieView {
    XYLiveImageLottieView *lottieView = [[XYLiveImageLottieView alloc] init];
    [lottieView setConfig:self.resource.lineResource.randomMatchLottie];
    lottieView.contentMode = UIViewContentModeScaleToFill;
    [self.contentView addSubview:lottieView];
    self.lottieView = lottieView;
}

- (void)setupCancelBtn {
    UIButton *cancelBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    cancelBtn.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:16];
    [cancelBtn setTitle:@"放弃匹配" forState:UIControlStateNormal];
    [cancelBtn setTitleColor:[XYLiveTokenColor title] forState:UIControlStateNormal];
    cancelBtn.layer.borderWidth = 0.5;
    cancelBtn.layer.borderColor = [XYLiveTokenColor separator2].CGColor;
    cancelBtn.layer.cornerRadius = 20;
    cancelBtn.layer.masksToBounds = YES;
    [cancelBtn addTarget:self action:@selector(didTapCancel:) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:cancelBtn];
    self.cancelBtn = cancelBtn;
}

#pragma mark - Override

- (CGFloat)heightWhenPortrait {
    return 280;
}

- (BOOL)needReachBottom {
    return YES;
}

#pragma mark - Event

- (void)didTapCancel:(UIButton *)sender {
    // 埋点上报
    [XYLivePushMultiLineBizTracker eventActionId80519WithRoomId:self.liveInfoService.roomId channelTabType:@"connect" hasGoods:self.liveInfoService.roomInfo.hasGoods];
    // 执行事件回调
    self.didTapCancelHandler ? self.didTapCancelHandler() : nil;
    // 关闭面板
    [self.containerVC dismissPopBottomVC:self dismissAnimated:YES dismissComplete:nil];
}

#pragma mark - TraitCollection

- (void)traitCollectionDidChange:(UITraitCollection *)previousTraitCollection {
    [super traitCollectionDidChange:previousTraitCollection];
    // 强刷
    if(@available(iOS 13, *)) {
        if ([self.traitCollection hasDifferentColorAppearanceComparedToTraitCollection:previousTraitCollection]) {
            self.cancelBtn.layer.borderColor = [XYLiveTokenColor separator2].CGColor;
        }
    }
}

#pragma mark - Lazy

- (XYLiveMultiLinkResourceManager *)resource {
    if (_resource == nil) {
        _resource = [[XYLiveMultiLinkResourceManager alloc] init];
    }
    return _resource;
}

@end
