//
//  XYLiveMultiLineSearchVM.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/3.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineSearchVM.h"
#import <XYAlphaNetwork/XYAlphaNetwork-Swift.h>
#import <XYAPIRoute/XYAPIRoute.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineConsts.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import "XYLiveMultiLineSearchListModel.h"

@interface XYLiveMultiLineSearchVM()

@property (nonatomic, weak) id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak) id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, copy) NSArray<XYLiveLinkHostInviteeInfo *> *dataSource;

@end

@implementation XYLiveMultiLineSearchVM

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkSevice {
    if (self = [super init]) {
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkSevice;
    }
    return self;
}

// 发起搜索请求
- (void)requestSearchResultWithKey:(NSString *)key completion:(void(^)(NSError *error))completion {
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/line/v1/search_invite_host_list" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMPOST;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"app_id"] = @(1);
    params[@"room_id"] = self.liveInfoService.roomId;
    params[@"query"] = key;
    [api addingQueryItems:params.copy];
    [XYLogCollector xyLiveLogTag:kXYLiveMultiLineModuleName content:[NSString stringWithFormat:@"start request search host list api,{key:%@, roomId:%@}", key, self.liveInfoService.roomId]];
    // 重置数据源
    self.dataSource = nil;
    WS
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        [XYLogCollector xyLiveLogTag:kXYLiveMultiLineModuleName content:[NSString stringWithFormat:@"request search host list api reposne,{error:%@}", response.error]];
        SS
        // 处理成功
        if (response.error == nil) {
            // 数据转模型
            XYLiveMultiLineSearchListModel *listModel = [XYLiveMultiLineSearchListModel xy_modelWithDictionary:response.result.value];
            // 构建数据源
            [self configureDataSourceWithListModel:listModel];
        }
        // 执行事件回调
        completion ? completion(response.error) : nil;
    }];
}

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    // 更新状态
    XYLiveLinkHostInviteeInfo *listItem = [self.dataSource xy_match:^BOOL(id  _Nonnull obj) {
        XYLiveLinkHostInviteeInfo *item = XYSAFE_CAST(obj, XYLiveLinkHostInviteeInfo);
        return [item.userId isEqualToString:inviteeInfo.userId];
    }];
    listItem.isInvited = (state != XYLiveMultiLinkInviteStateIdle);
}

#pragma mark - Private

// 配置数据源
- (void)configureDataSourceWithListModel:(XYLiveMultiLineSearchListModel *)listModel {
    NSMutableArray *dataSourceM = [NSMutableArray array];
    for (XYLiveLinkHostInviteeInfo *listItem in listModel.listItems) {
        // 更新邀请状态
        listItem.isInvited = [self.multiLinkService isInvitingWithBizType:XYLiveMultiLinkBizTypeLine targetUserId:listItem.userId];
        [dataSourceM addObject:listItem];
    }
    self.dataSource = dataSourceM.copy;
}

@end
