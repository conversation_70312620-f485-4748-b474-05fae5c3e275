//
//  GlobalWindowManager.swift
//  XYLiveKit
//
//  Created by lian<PERSON><PERSON><PERSON> on 2023/5/18.
//  Copyright © 2023 XingIn. All rights reserved.
//

import Foundation
import XYRouter
import XYAlertCenter
import XYLiveCore
import XYLiveFoundation
import XYLivePlayManager
import XYAlphaNetwork

@objcMembers
@objc(XYLiveGlobalWindowManager)
public final class GlobalWindowManager: NSObject {
    public static let routerSource: String = "floating"
    public var rtcPusher: RTCPusher?
    public var rtcCore: RTCCore?
    public weak var imDistributer: ImDistributer?
    public var msgParser: XYIMMessageParser? = XYIMMessageParser()
    public var viewModel = MultiLinkViewModel()
    public var groupDeletedEvent: ((_ groupId: String) -> Void)?
    // 主播vc是否销毁
    public var isDismissVC: Bool = true
    
    var viewStartUtil: LiveMultiLinkStatistics? = LiveMultiLinkStatistics()
    var linkUtil: LiveMultiLinkStatistics? = LiveMultiLinkStatistics()
    
    public private(set) var floating: Bool = false
    
    public private(set) var resumeCameraSetup: Bool = true
    
    var reportdSuspend: Bool = false
    
    public var audienceSelfChoose: Bool = true
    
    public var renderViewFrame: CGRect = .zero
    
    public var rtcAvailable: Bool {
        rtcPusher != nil || rtcCore != nil
    }
    
    enum Status {
        case deactive
        case active
    }
    
    var curStatus: Status = .active
    
    public var role: RoomRole = .audience
    
    private var renderView: UIView?
    
    private lazy var cameraContainerView: UIView = {
        UIView()
    }()
    
    public init(role: RoomRole) {
        super.init()
        self.role = role
        XYLiveMediaControl.reloadAudioSession(receiver: self, sceneType: .push)
    }
    
    deinit {
        collectLog(content: "deinit")
    }
    
    lazy var floatingView: XYLiveFloatingPlayView = {
        let fView = XYLiveFloatingPlayView()
        fView.decoratedView.floatingType = XYLiveFloatingPlayViewType.host
        fView.hostID = XYLiveManager.shared().hostInfo?.userID ?? ""
        fView.roomID = XYLiveManager.shared().roomInfo.roomIDStr
        fView.backgroundColor = Theme.color.black
        fView.showPreview()
        fView.update(CGSize(width: XYLivePlayConfig.liveFloatingViewWidth(), height: XYLivePlayConfig.liveFloatingViewHeight()))
        fView.decoratedView.setupCloseBtnHidden(true)
        fView.showBlock = {
            
        }
        fView.dismissBlock = { [weak self] in
            self?.floating = false
        }
        fView.setHiddenBlock = { _ in
            
        }
        fView.didTapOnFloatingView = { _ in
            var path = "xhsdiscover://live_audience?room_id=\(XYLiveManager.shared().roomInfo.roomIDStr)&animationType=default&source=\(GlobalWindowManager.routerSource)"
            let url = URL(string: path)
            JLRoutes.routeURL(url)
        }
        fView.didPressMuteBtnBlock = {[weak self] sender in
            let mute = !sender.isSelected
            self?.setMute(mute)
        }
        fView.didPressCloseBtnBlock = { [weak self] _ in
            self?.onMicAudienceClose(selfOp: true)
        }
        return fView
    }()
    
    public lazy var presetImageUtil: RTCPresetImageUtil = {
        let util = RTCPresetImageUtil()
        util.inactiveImageCompleted = { image, _ in
            if image != nil {
                self.rtcCore?.setVideoMuteImage(image, appActiveEffective: false)
            }
        }
        return util
    }()
    
    // MARK: - Core
    public func show() {
        guard !floating else {
            return
        }
        collectLog(content: "show")
        setupIfNeeded()
        floating = true
        floatingView.show()
        addRenderView(floatingView)
        addCamearPreviewIfNeeded()
        reportSuspend(true)
        viewStart()
    }
    
    public func dismiss() {
        guard floating else {
            return
        }
        collectLog(content: "dismiss")
        reportSuspend(false)
        renderView?.removeFromSuperview()
        floatingView.resetPreview()
        floating = false
        viewEnd()
    }
    
    public func setHidden(_ hide: Bool) {
        guard floating else { return }
        collectLog(content: "hidden: \(hide)")
        floatingView.isHidden = hide
    }
    
    public func setMute(_ mute: Bool) {
        collectLog(content: "mute: \(mute)")
        self.curStatus = mute ? .deactive : .active
        if !mute {  // inactive other audio
            
        }
        XYExecuteOnMainQueue {
            self.floatingView.setMute(mute, disabled: false)
        }
        
        self.rtcPusher?.muteAllRemoteAudio(mute)
        self.rtcCore?.muteAllRemoteAudio(mute)
    }
    
    public func recordCameraRunning(_ state: Bool) {
        resumeCameraSetup = state
        collectLog(content: "record camera setup: \(state)")
    }
    
    public func collectLog(content: String) {
        XYLogCollector.xyLiveLogTag("global_window", content: content)
    }
    
    public func isLinking() -> Bool {
        XYLiveManager.shared().isLiveChat || (rtcCore != nil && (rtcCore?.rtcChannelSession?.findAllMemberID().count ?? 0) > 1)
    }
    
    func destoryLink() {
        guard floating else {
            return
        }
        collectLog(content: "destory link")
        
        // 关闭camera
        XYLiveManager.shared().coreManager.media.customVendor = nil
        XYLiveManager.shared().coreManager.media.releaseCamera()
        
        // 关闭rtc
        self.rtcCore?.destory()
        self.rtcCore = nil
        
        self.rtcPusher = nil
    }
    
    public func bindLinkStatistics(_ statistics: LiveMultiLinkStatistics?) {
        linkUtil = statistics
    }
    
    func setupIfNeeded() {
        if msgParser == nil {
            msgParser = XYIMMessageParser()
        }
        
        if linkUtil == nil {
            linkUtil = LiveMultiLinkStatistics()
        }
        
        if viewStartUtil == nil {
            viewStartUtil = LiveMultiLinkStatistics()
        }
        
        setIMParserDelegate()
    }
    
    public func reset() {
        imDistributer = nil
        msgParser = nil
        floating = false
        reportdSuspend = false
        linkUtil = nil
        viewStartUtil = nil
        resumeCameraSetup = false
    }
    
    public static func reportUserSuspend(_ suspend: Bool, videoOn: Bool, audioOn: Bool, completion: ((Bool) -> Void)?) {
        if LiveConfigCenter.enableMultiNewReport() {
            // 小窗内部调用，走新上报逻辑
            MultiLinkmicCommService.reportMediaInfo(suspend: suspend, videoOn:videoOn , audioOn: audioOn) { isSuccess, error in
                completion?(isSuccess)
            }
            
            return
        }
        reportUserSuspendOld(suspend, completion: completion)
    }
    
    public static func reportUserSuspend(_ suspend: Bool, completion: ((Bool) -> Void)?) {
        if LiveConfigCenter.enableMultiNewReport() {
            // 非小窗内部调用，直接走外部上报，这里不做处理
            return
        }
        reportUserSuspendOld(suspend, completion: completion)
    }
    
    private static func reportUserSuspendOld(_ suspend: Bool, completion: ((Bool) -> Void)?) {
        let appId = 1
        let roomId = XYLiveManager.shared().roomInfo.roomIDStr
        let baseApi: XYAPI = APIRouter.buildGoURL()
        baseApi.method = .WSHTTPMPOST
        baseApi.path = "/api/sns/red/live/app/v1/line/center/report_suspend"
        baseApi.addingQueryItem("app_id", value: appId)
        baseApi.addingQueryItem("room_id", value: roomId)
        baseApi.addingQueryItem("suspend", value: suspend)
        baseApi.data().responseJson {(response: DataResponse<Any, Error>) in
            if case let .success(result) = response.result,
               let val = result as? [String: Any],
               let success: Bool = val["success"] as? Bool,
               response.error == nil {
                completion?(success)
            } else {
                completion?(false)
            }
        }
    }
}

// MARK: - EventListener
public extension GlobalWindowManager {
    func setIMParserDelegate() {
        self.msgParser?.setupImListener()
        self.msgParser?.delegate = self
    }

    func bindImDistributer(_ distributer: ImDistributer?) {
        imDistributer = distributer
        collectLog(content: "bind im distributer")
        // 注册IM
        guard role == .host else {
            return
        }
        registerHostIM()
    }
    
    func registerLiveCoreListener() {
        XYLiveManager.shared().coreManager.media.registerEventListener(listener: self)
    }
}

// MARK: - View
extension GlobalWindowManager {
    private func addCamearPreviewIfNeeded() {
        guard anchorPictureAndAudiencePictureExperiment() else {
            return
        }
        if resumeCameraSetup {
            if cameraContainerView.superview != floatingView {
                floatingView.insertSubview(cameraContainerView, belowSubview: floatingView.decoratedView)
            }
            transferLinkState()
            cameraContainerView.frame = floatingView.bounds
            XYLiveManager.shared().coreManager.media.startPreview(cameraContainerView)
        }
    }
    
    private func addRenderView(_ superView: UIView) {
        let view = UIImageView(frame: superView.bounds)
        view.sd_setImage(with: URL(string: LiveConfigCenter.chatLiveLeavingUrl()))
        
        let avatarImgView = UIImageView(frame: CGRect(x: 0, y: 0, width: 48, height: 48))
        avatarImgView.center = view.center
        avatarImgView.sd_setImage(with: URL(string: XYLiveManager.shared().userInfo.avatar ?? ""))
        avatarImgView.layer.cornerRadius = 24
        avatarImgView.clipsToBounds = true
        view.addSubview(avatarImgView)
        
        let shadeView = UIView()
        shadeView.backgroundColor = .ReDs.alwaysDarkParagraph
        shadeView.bounds = avatarImgView.bounds
        shadeView.center = avatarImgView.center
        shadeView.layer.cornerRadius = 24
        shadeView.clipsToBounds = true
        view.addSubview(shadeView)
        
        let outImgView = UIImageView(frame: CGRect(x: 12, y: 12, width: 24, height: 24))
        outImgView.sd_setImage(with: URL(string: LiveConfigCenter.anchorCoffeeImageUrl()))
        shadeView.addSubview(outImgView)
        
        let title = UILabel(frame: CGRect(x: avatarImgView.xy_xCenter - 48 / 2.0, y: avatarImgView.xy_bottom + 12, width: 48, height: 16))
        title.textColor = .ReDs.alwaysLightDescription
        title.font = UIFont.xyto_mediumFont(ofSize: 12.0)
        let titleText = anchorPictureAndAudiencePictureExperiment() ?  "连线中..." : "暂时离开"
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineHeightMultiple = 0.95
        title.textAlignment = .justified
        title.attributedText = NSMutableAttributedString(string: titleText, attributes: [NSAttributedString.Key.paragraphStyle: paragraphStyle])
        view.addSubview(title)
        
        superView.insertSubview(view, at: 0)
        renderView = view
    }
    
    public func transferLinkState() {
        guard anchorPictureAndAudiencePictureExperiment() else {
            reportSuspend(true)
            return
        }
        cameraContainerView.isHidden = isLinking()
    }
    
    internal func anchorPictureAndAudiencePictureExperiment() -> Bool {
        if role == .host {
            return true
        }
        return (role == .audience)
    }
}

// MARK: Suspend
private extension GlobalWindowManager {
    func reportSuspend(_ suspend: Bool) {
        guard !anchorPictureAndAudiencePictureExperiment() else {
            return
        }
        collectLog(content: "report suspend \(suspend) check \(reportdSuspend)")
        if suspend, reportdSuspend { return }
        let localUser = viewModel.localUser
        GlobalWindowManager.reportUserSuspend(suspend, videoOn: localUser?.isVideoOn ?? false, audioOn: localUser?.isAudioOn ?? false) { [weak self] success in
            self?.recordIfNeeded(suspend, result: success)
        }
    }
    
    func recordIfNeeded(_ suspend: Bool, result: Bool) {
        collectLog(content: "record suspend \(suspend) success \(result)")
        if suspend {
            self.reportdSuspend = result
        }
    }
}

// MARK: Track
extension GlobalWindowManager {
    func viewStart() {
        viewStartUtil?.viewStart()
        viewStartUtil?.event50049()
    }
    
    func viewEnd() {
        viewStartUtil?.viewEnd()
    }
}
