//
//  XYLiveMultiPKInviteVM.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/29.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiPKInviteServiceProtocol.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
@class XYLiveLinkHostInviteeInfo, XYLiveMultiLinkInviteInfo;

NS_ASSUME_NONNULL_BEGIN

@protocol XYLiveMultiPKInviteDelegate <NSObject>

// 邀请状态变化
- (void)onUpdateInviteState:(XYLiveMultiLinkInviteState)inviteState inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo;

// 收到连线邀请
- (void)onReceiveInviteWithBizType:(XYLiveMultiLinkBizType)bizType inviteInfo:(XYLiveMultiLinkInviteInfo *)inviteInfo;

// 倒计时回调
- (void)onTimerTrick:(NSInteger)remainInterval totalInterval:(NSInteger)totalInterval;

// 倒计时完成
- (void)onTimerFinish;

@end

@interface XYLiveMultiPKInviteVM : NSObject<XYLiveMultiPKInviteServiceProtocol, XYLiveMultiLinkListener>

// 代理
@property (nonatomic, weak) id<XYLiveMultiPKInviteDelegate> delegate;

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

// 接受邀请
- (void)acceptInviteWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId targetRoomId:(NSString *)targetRoomId;

// 拒绝
- (void)rejectInviteWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId targetRoomId:(NSString *)targetRoomId;

@end

NS_ASSUME_NONNULL_END
