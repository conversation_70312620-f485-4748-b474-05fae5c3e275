//
//  XYLivePushMultiChatController.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/30.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveServiceProtocol/XYLivePushMultiChatServiceProtocol.h>
@protocol XYLivePushMultiChatDependProtocol;

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiChatController : XYViewController<XYLivePushMultiChatServiceProtocol>

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containVC
                             depend:(id<XYLivePushMultiChatDependProtocol>)depend;

@end

NS_ASSUME_NONNULL_END
