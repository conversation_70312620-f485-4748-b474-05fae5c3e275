//
//  XYLiveMultiChatApplyListHeaderView.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatApplyListHeaderView.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYUITheme/XYThemeManager.h>
#import <XYFoundation/XYFoundation.h>
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYLiveKit/XYLivePushMultiLinkBizTracker.h>
#import <Masonry/Masonry.h>
#import <XYUIKitCore/XYUIKitCore-Swift.h>
#import "XYLiveMultiChatApplyListHeaderItemView.h"

@interface XYLiveMultiChatApplyListHeaderView()

@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, copy)   NSArray<XYLiveMultiChatApplyListSortItem *> *listItems;

@end

@implementation XYLiveMultiChatApplyListHeaderView

// 初始化
- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建UI
        [self setupSubviews];
    }
    return self;
}

#pragma mark - Public

// 数据绑定
- (void)bindListItems:(NSArray<XYLiveMultiChatApplyListSortItem *> *)listItems {
    if (self.containerView.subviews.count != listItems.count) {
        [self.containerView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
        // 构建视图
        [self buildItemViewWithItems:listItems];
    }
}

// 更新
- (void)update {
    [self.listItems enumerateObjectsUsingBlock:^(XYLiveMultiChatApplyListSortItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (self.containerView.subviews.count != self.listItems.count) {
            return;
        }
        XYLiveMultiChatApplyListHeaderItemView *itemView = self.containerView.subviews[idx];
        itemView.selected = obj.isSelected;
    }];
}

#pragma mark - Private

- (void)setupSubviews {
    self.backgroundColor = [XYLiveTokenColor groupedSecondaryBackground];
    
    self.containerView = [[UIView alloc] init];
    [self addSubview:self.containerView];
    
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16);
        make.centerY.equalTo(self);
        make.height.mas_equalTo(28);
    }];
}

- (void)didTapItem:(XYLiveMultiChatApplyListHeaderItemView *)sender {
    XYLiveMultiChatApplyListSortItem *item = self.listItems[sender.tag];
    if (item.isSelected) { return; }
    self.didTapListItemHandler ? self.didTapListItemHandler(item) : nil;
}

- (void)buildItemViewWithItems:(NSArray<XYLiveMultiChatApplyListSortItem *> *)lisItems {
    [lisItems enumerateObjectsUsingBlock:^(XYLiveMultiChatApplyListSortItem * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        XYLiveMultiChatApplyListHeaderItemView *itemView = [[XYLiveMultiChatApplyListHeaderItemView alloc] initWithTitle:obj.desc];
        itemView.layer.cornerRadius = 6;
        itemView.layer.masksToBounds = YES;
        itemView.tag = idx;
        itemView.selected = obj.isSelected;
        WS
        WEAK_OBJ_REF(itemView)
        [itemView xy_addTapGesture:^(UITapGestureRecognizer * _Nonnull tapGesture) {
            SS
            [self didTapItem:weak_itemView];
        }];
        [itemView xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
            return [XYLivePushMultiLinkBizTracker eventActionId69409WithTabName:obj.desc];
        }];
        [self.containerView addSubview:itemView];
    }];
    self.listItems = lisItems;
    
    [self.containerView.subviews mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.containerView);
    }];
    [self.containerView.subviews mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:6 leadSpacing:0 tailSpacing:0];
}

@end
