//
//  XYLiveMultiChatInviteListFriendView.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatInviteListFriendView.h"
#import <XYLiveKit/XYLiveMultiChatInviteListModel.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYWebImage/UIImageView+XYWebImage.h>
#import <XYUserAvatar/UIImageView+XYCornerRadius.h>
#import <XYLiveFoundation/UIImage+XYLiveKit.h>
#import <XYLiveKit/XYLivePushMultiLinkBizTracker.h>
#import <XYLiveFoundation/XYLiveFoundation-Swift.h>
#import <Masonry/Masonry.h>

// 重用标识符
static NSString *const kXYLiveMultiChatInviteListFriendItemCellIde = @"XYLiveMultiChatInviteListFriendItemCell";

@interface XYLiveMultiChatInviteListFriendItemMaskView : UIView

@property (nonatomic, strong) UIImageView *imageView;

@end

@implementation XYLiveMultiChatInviteListFriendItemMaskView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    // 创建图片
    [self setupImageView];
    
    // 布局
    [self.imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(16, 16));
    }];
}

#pragma mark - 创建UI

- (void)setupImageView {
    UIImageView *imageView = [[UIImageView alloc] init];
    imageView.image = [UIImage xy_liveKitBundleImage:@"link_invited"];
    imageView.contentMode = UIViewContentModeCenter;
    [self addSubview:imageView];
    self.imageView = imageView;
}

@end

@interface XYLiveMultiChatInviteListFriendItemCell: UICollectionViewCell

@property (nonatomic, strong) UIImageView *avatarImgView;
@property (nonatomic, strong) UILabel *nickNameLbl;
@property (nonatomic, strong) XYLiveMultiChatInviteListFriendItemMaskView *maskView;
@property (nonatomic, strong) XYLiveMultiChatInviteListFriendModel *listItem;
@property (nonatomic, strong) NSIndexPath *indexPath;

@end

@implementation XYLiveMultiChatInviteListFriendItemCell

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建UI
        [self setupSubviews];
    }
    return self;
}

/// 数据绑定
- (void)bindListItem:(XYLiveMultiChatInviteListFriendModel *)item indexPath:(NSIndexPath *)indexPath {
    // 更新头像
    if (item.userId) {
        [self.avatarImgView xy_setImageWithURL:[NSURL URLWithString:item.avatarUrl ?: @""]];
    } else {
        [self.avatarImgView xylive_setImageWithURLWithLight:@"https://picasso-static.xiaohongshu.com/fe-platform/a6a9ae5f14917e98b4556433bac9e3bc40389f60.png" dark:@"https://picasso-static.xiaohongshu.com/fe-platform/5526f66518f38ddc75d6cf67ddec53cac3535961.png"];
    }
    self.maskView.hidden = !item.isInvited;
    // 更新标题
    self.nickNameLbl.text = item.isInvited ? @"已邀请" : item.nickName;
    self.nickNameLbl.textColor = item.isInvited ? XYLiveTokenColor.desc : XYLiveTokenColor.title;
    self.listItem = item;
    self.indexPath = indexPath;
}

- (void)setupSubviews {
    // 创建头像
    [self setupAvatarImgView];
    // 创建昵称
    [self setupNickNameLbl];
    // 创建蒙层
    [self setupMaskView];
    
    // 布局
    [self.avatarImgView  mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(40, 40));
        make.top.mas_equalTo(8);
    }];
    
    [self.nickNameLbl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.contentView);
        make.top.equalTo(self.avatarImgView.mas_bottom).offset(8);
        make.left.equalTo(self.contentView).offset(2);
        make.right.equalTo(self.contentView).offset(-2);
    }];
    
    [self.maskView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.top.right.bottom.equalTo(self.avatarImgView);
    }];
}

#pragma mark - UI

- (void)setupAvatarImgView {
    UIImageView *avatarImgView = [[UIImageView alloc] init];
    avatarImgView.contentMode = UIViewContentModeScaleAspectFill;
    avatarImgView.userInteractionEnabled = YES;
    avatarImgView.layer.cornerRadius = 20;
    avatarImgView.layer.masksToBounds = YES;
    avatarImgView.layer.borderColor = [XYLiveTokenColor separator].CGColor;
    avatarImgView.layer.borderWidth = 0.5;
    [self.contentView addSubview:avatarImgView];
    self.avatarImgView = avatarImgView;
}

- (void)setupNickNameLbl {
    UILabel *nickNameLabel = [[UILabel alloc] init];
    nickNameLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:12];
    nickNameLabel.textColor = XYLiveTokenColor.title;
    nickNameLabel.textAlignment = NSTextAlignmentCenter;
    [self.contentView addSubview:nickNameLabel];
    self.nickNameLbl = nickNameLabel;
}

- (void)setupMaskView {
    XYLiveMultiChatInviteListFriendItemMaskView *maskView = [[XYLiveMultiChatInviteListFriendItemMaskView alloc] init];
    maskView.backgroundColor = [XYLiveTokenColor.black colorWithAlphaComponent:0.4];
    maskView.layer.cornerRadius = 20;
    maskView.layer.masksToBounds = YES;
    maskView.hidden = YES;
    [self.contentView addSubview:maskView];
    self.maskView = maskView;
}

@end

@interface XYLiveMultiChatInviteListFriendView()<UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout>

@property (nonatomic, strong) UICollectionView *listView;
@property (nonatomic, copy)   NSArray<XYLiveMultiChatInviteListFriendModel *> *listItems;

@end

@implementation XYLiveMultiChatInviteListFriendView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建UI
        [self setupSubviews];
    }
    return self;
}

/// 绑定数据源
- (void)bindListItems:(NSArray<XYLiveMultiChatInviteListFriendModel *> *)listItems indexPath:(NSIndexPath *)indexPath {
    self.listItems = listItems;
    [self.listView reloadData];
}

/// 刷新列表
- (void)reloadData {
    [self.listView reloadData];
}

#pragma mark - UI

- (void)setupSubviews {
    // 创建列表
    [self setupListView];
    
    // 布局
    [self.listView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
}

// 创建列表
- (void)setupListView {
    UICollectionViewFlowLayout *layout = [[UICollectionViewFlowLayout alloc] init];
    layout.scrollDirection = UICollectionViewScrollDirectionHorizontal;
    layout.itemSize = CGSizeMake(70, 80);
    layout.sectionInset = UIEdgeInsetsMake(0, 16, 0, 16);
    
    UICollectionView *collectionView = [[UICollectionView alloc] initWithFrame:CGRectZero collectionViewLayout:layout];
    collectionView.backgroundColor = [UIColor clearColor];
    collectionView.delegate = self;
    collectionView.dataSource = self;
    collectionView.showsHorizontalScrollIndicator = NO;
    // 注册cell
    [collectionView registerClass:XYLiveMultiChatInviteListFriendItemCell.class forCellWithReuseIdentifier:kXYLiveMultiChatInviteListFriendItemCellIde];
    WS
    [collectionView xyAutoTrack_registerDidSelectTrackerBlock:^XYTrackerEventContext * _Nullable(NSIndexPath * _Nonnull indexPath) {
        SS
        XYLiveMultiChatInviteListFriendModel *listItem = self.listItems[indexPath.item];
        return self.didTapTrackerHandler ? self.didTapTrackerHandler(listItem, indexPath) : nil;
    }];
    [self addSubview:collectionView];
    self.listView = collectionView;
}

#pragma mark - UICollectionViewDelegate / UICollectionViewDataSource

- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    return self.listItems.count;
}

- (__kindof UICollectionViewCell *)collectionView:(UICollectionView *)collectionView cellForItemAtIndexPath:(NSIndexPath *)indexPath {
    XYLiveMultiChatInviteListFriendItemCell *cell = [collectionView dequeueReusableCellWithReuseIdentifier:kXYLiveMultiChatInviteListFriendItemCellIde forIndexPath:indexPath];
    // 数据绑定
    [cell bindListItem:self.listItems[indexPath.item] indexPath:indexPath];
    return cell;
}

- (void)collectionView:(UICollectionView *)collectionView didSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    XYLiveMultiChatInviteListFriendModel *listItem = self.listItems[indexPath.item];
    if (listItem.isInvited) { return; }
    self.didTapHandler ? self.didTapHandler(listItem, indexPath) : nil;
}

- (void)collectionView:(UICollectionView *)collectionView willDisplayCell:(UICollectionViewCell *)cell forItemAtIndexPath:(NSIndexPath *)indexPath {
    XYLiveMultiChatInviteListFriendModel *listItem = self.listItems[indexPath.item];
    // 更多按钮曝光
    if (!listItem.hasShow && !listItem.userId.length) {
        listItem.hasShow = YES;
        // 埋点上报
        [XYLivePushMultiLinkBizTracker eventActionId69407];
    }
}

@end
