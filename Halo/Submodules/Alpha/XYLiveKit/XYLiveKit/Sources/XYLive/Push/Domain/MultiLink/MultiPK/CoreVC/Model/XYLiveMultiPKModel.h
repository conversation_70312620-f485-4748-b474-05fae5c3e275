//
//  XYLiveMultiPKModel.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/12.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYLiveFoundation/XYLiveFoundation.h>

NS_ASSUME_NONNULL_BEGIN

// 贡献榜用户信息
@interface XYLiveMultiPKContributorUserInfo : XYLiveCodableModel

// 分数
@property (nonatomic, copy)   NSString *score;
// 用户信息
@property (nonatomic, strong) XYLiveUserInfo *userInfo;

@end

// 战队信息
@interface XYLiveMultiPKTeamInfo : XYLiveCodableModel

// 分数
@property (nonatomic, assign) NSInteger score;
// 战队信息
@property (nonatomic, copy)   NSString *teamId;
// 房间id
@property (nonatomic, copy)   NSString *roomId;
// 会话id
@property (nonatomic, copy)   NSString *sessionId;
// 用户id
@property (nonatomic, copy)   NSString *userId;
// 用户信息
@property (nonatomic, strong) XYLiveUserInfo *userInfo;
// 贡献榜
@property (nonatomic, copy)   NSArray<XYLiveMultiPKContributorUserInfo *> *contributorList;

@end

@interface XYLiveMultiPKModel : XYLiveCodableModel

// pk标识
@property (nonatomic, copy)   NSString *pkId;
// pk状态-字符串类型
@property (nonatomic, copy)   NSString *stage;
// 服务端时间戳，单位：s
@property (nonatomic, assign) NSInteger currentTs;
// 开始时间,单位是s
@property (nonatomic, assign) NSInteger startTs;
// 结束时间,单位是s
@property (nonatomic, assign) NSInteger endTs;
// 战队信息
@property (nonatomic, copy)   NSArray<XYLiveMultiPKTeamInfo *> *teamList;
// 获胜队伍信息
@property (nonatomic, copy)   NSString *winnerTeamId;

@end

NS_ASSUME_NONNULL_END
