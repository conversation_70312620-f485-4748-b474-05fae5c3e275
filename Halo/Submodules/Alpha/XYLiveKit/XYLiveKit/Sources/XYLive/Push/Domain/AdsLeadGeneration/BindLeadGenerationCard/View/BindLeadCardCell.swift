//
//  BindLeadCardCell.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/4/29.
//  Copyright © 2025 XingIn. All rights reserved.
//

import UIKit
import XYUITheme


@objcMembers
class ExplainButton: UIButton {
    private var lastClickTime: TimeInterval = 0
    private let minimumClickInterval: TimeInterval = 0.5
    
    override func sendAction(_ action: Selector, to target: Any?, for event: UIEvent?) {
        let currentTime = Date().timeIntervalSince1970
        if currentTime - lastClickTime > minimumClickInterval {
            lastClickTime = currentTime
            super.sendAction(action, to: target, for: event)
        }
    }
    
    override var isSelected: Bool {
        didSet {
            // 根据选中状态更新边框颜色
            self.theme.borderColor = isSelected ? .ReDs.separator2.light() : .ReDs.primary.light()
        }
    }
}

@objc(XYBindLeadCardCellConstant)
@objcMembers
class BindLeadCardCellConstant: NSObject {
    static let bindLeadCardCellHeight: CGFloat = 124.0
    static let bindLeadCardCellReuseIdentifier = "bindLeadCardCellReuseIdentifier"
}

@objc(XYDisplayLeadCardCellConstant)
@objcMembers
class DisplayLeadCardCellConstant: NSObject {
    static let displayLeadCardCellHeight: CGFloat = 155.0
    static let displayLeadCardCellReuseIdentifier = "displayLeadCardCellReuseIdentifier"
}

@objcMembers
@objc(XYDisplayLeadCardCell)
public class DisplayLeadCardCell: BindLeadCardCell {
    
    private lazy var leadsBulletManager = PushLeadsBulletManager.shared
    
    lazy var leftTimeLabel = {
        let label = UILabel()
        label.text = AdsLeadGenerationTools.getI18NLanguage(from: "弹卡")
        label.font = Theme.fontLeSmall
        label.theme.borderColor = .ReDs.separator2.light()
        label.textColor = .ReDs.disabled.light()
        label.layer.borderWidth = 0.5
        label.layer.cornerRadius = 12.0
        label.isHidden = true
        label.textAlignment = .center
        
        return label
    }()
    
    lazy var bulletBtn = {
        let button = UIButton(type: .custom)
        button.setTitleColor(.ReDs.title.light(), for: .normal)
        button.setTitleColor(.ReDs.disabled.light(), for: .disabled)
        button.setTitle(AdsLeadGenerationTools.getI18NLanguage(from: "弹卡"), for: .normal)
        button.addTarget(self, action: #selector(didPressedBulletBtn(_:)), for: .touchUpInside)
        button.titleLabel?.font = Theme.fontLeSmall
        button.theme.borderColor = .ReDs.separator2.light()
        button.layer.borderWidth = 0.5
        button.layer.cornerRadius = 12.0
        return button
    }()
    
    lazy var explainBtn = {
        let button = ExplainButton(type: .custom)
        button.setTitleColor(.ReDs.primary.light(), for: .normal)
        button.setTitleColor(.ReDs.title.light(), for: .selected)
        button.setTitle(AdsLeadGenerationTools.getI18NLanguage(from: "讲解"), for: .normal)
        button.setTitle(AdsLeadGenerationTools.getI18NLanguage(from: "结束讲解"), for: .selected)
        button.addTarget(self, action: #selector(didPressedExplainBtn(_:)), for: .touchUpInside)
        button.titleLabel?.font = Theme.fontLeSmall
        button.theme.borderColor = .ReDs.primary.light()
        button.layer.borderWidth = 0.5
        button.layer.cornerRadius = 12.0
        return button
    }()
    
    override func setupUI() {
        // 父类方法里面没有任何函数调用才能省略，否则不能省略，最好全部不要省略，保持调用链完整
        super.setupUI()
        self.checkboxImageView.isHidden = true
        self.indexBgImageView.isHidden = false
        self.contentView.addSubview(self.bulletBtn)
        self.contentView.addSubview(self.explainBtn)
        self.contentView.addSubview(self.leftTimeLabel)
        self.coverImageView.snp.updateConstraints { make in
            make.left.equalToSuperview().offset(12)
        }
        self.explainBtn.snp.makeConstraints { make in
            make.width.equalTo(72.0)
            make.height.equalTo(24.0)
            make.right.equalToSuperview().offset(-12.0)
            make.top.equalTo(coverImageView.snp.bottom).offset(8.0)
        }
        self.bulletBtn.snp.makeConstraints { make in
            make.width.equalTo(48.0)
            make.height.equalTo(24.0)
            make.centerY.equalTo(self.explainBtn)
            make.right.equalTo(self.explainBtn.snp.left).offset(-8.0)
        }
        self.leftTimeLabel.snp.makeConstraints { make in
            make.width.equalTo(43.0)
            make.height.equalTo(24.0)
            make.centerY.equalTo(self.explainBtn)
            make.right.equalTo(self.explainBtn.snp.left).offset(-8.0)
        }
    }
    
    override func updateCell(with cellModel: AdsLeadBindCardModel) {
        super.updateCell(with: cellModel)
        
        if self.leadsBulletManager.currentCardItem?.cardId == cellModel.cardId { // 讲解中 or 弹卡中
            let leftTime = leadsBulletManager.currentCardItem?.leftTime ?? 0
            if leftTime <= 0 {
                self.leftTimeLabel.isHidden = true
                self.bulletBtn.isHidden = false
                self.leftTimeLabel.text = AdsLeadGenerationTools.getI18NLanguage(from: "弹卡")
                self.bulletBtn.isEnabled = true
            } else {
                self.leftTimeLabel.isHidden = false
                self.bulletBtn.isHidden = true
                self.leftTimeLabel.text = String(self.leadsBulletManager.currentCardItem?.leftTime ?? 0) + "s"
                self.bulletBtn.isEnabled = false
            }
        } else {
            self.bulletBtn.isEnabled = self.leadsBulletManager.currentCardItem?.explaining != true
            self.leftTimeLabel.isHidden = true
            self.bulletBtn.isHidden = false
        }
        // 更新讲解按钮UI
        self.explainBtn.isSelected = cellModel.explaining == true
    }
    
    func didPressedExplainBtn(_ sender: UIButton) {
        if self.cellModel?.explaining == true { // 当前为讲解中状态
            self.leadsBulletManager.stopExplainAdsLeads(leadsCardItem: self.cellModel)
            self.leadsBulletManager.cardOptionDidTrigerAction?(.stopExplain, self.cellModel)
        } else {
            self.leadsBulletManager.startExplainAdsLeads(leadsCardItem: self.cellModel)
            self.leadsBulletManager.cardOptionDidTrigerAction?(.startExplain, self.cellModel)
        }
    }
    
    func didPressedBulletBtn(_ sender: UIButton) {
        self.leadsBulletManager.startBulletAdsLeads(leadsCardItem: self.cellModel)
        self.leadsBulletManager.cardOptionDidTrigerAction?(.bulletCard, self.cellModel)
    }
}

@objcMembers
@objc(XYBindLeadCardCell)
public class BindLeadCardCell: UITableViewCell {
    
    var didPressShortTitleBlock: (() -> Void)?
    var trackFlag: GoodsSelectTrackFlag = .none
    var cellModel: AdsLeadBindCardModel?
    
    // 勾选框
    lazy var checkboxImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage.xy_liveKitBundleImage("xyLiveShopItemUnCheckIcon")
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 10
        imageView.clipsToBounds = true
        return imageView
    }()
    
    // 左侧大图
    lazy var coverImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.backgroundColor = UIColor.xy.gray25.light()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = 4.0
        imageView.clipsToBounds = true
        return imageView
    }()
    
    // 顶部数字图
    fileprivate lazy var indexBgImageView: UIImageView = {
        let imageView = UIImageView()
        if let image = UIImage.xy_liveKitBundleImage("xyLiveAdsLeadsItemNewOrderIndexIcon") {
            let x = (image.size.width - 1) / 2
            let y = (image.size.height - 1) / 2
            imageView.image = image.resizableImage(withCapInsets: UIEdgeInsets(top: y, left: x, bottom: y, right: x))
        }
        imageView.isHidden = true
        
        return imageView
    }()
    
    // 数字label
    private lazy var indexLabel: UILabel = {
        let label = UILabel()
        label.textColor = Theme.color.whitePatch1.light()
        label.textAlignment = .center
        label.font = Theme.fontXXSmall
        return label
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .ReDs.title.light()
        label.font = Theme.fontXMediumBold
        label.lineBreakMode = .byTruncatingTail
        return label
    }()
    
    private lazy var specificLabel: UILabel = {
        let label = UILabel()
        label.textColor = .ReDs.description.light()
        label.textAlignment = .center
        label.font = Theme.fontXXSmall
        label.theme.borderColor = .ReDs.description.light()
        label.layer.borderWidth = 0.5
        label.layer.cornerRadius = 2.0
        return label
    }()
    
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .ReDs.description.light()
        label.font = Theme.fontLeSmall
        label.lineBreakMode = .byTruncatingTail
        return label
    }()
    
    lazy var reservationInfoLabel: UILabel = {
        let label = UILabel()
        label.textColor = .ReDs.paragraph.light()
        label.font = Theme.fontXXSmall
        return label
    }()
    
    // MARK: Life Cycle
    
    public override func prepareForReuse() {
        super.prepareForReuse()
    }
    
    public required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    public override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        self.setupUI()
    }
}

// MARK: Public

extension BindLeadCardCell {
    
    func setupUI() {
        self.contentView.backgroundColor = Theme.color.white.light()
        self.selectionStyle = .none
        self.contentView.addSubview(checkboxImageView)
        self.contentView.addSubview(coverImageView)
        self.coverImageView.addSubview(indexBgImageView)
        self.indexBgImageView.addSubview(indexLabel)
        self.contentView.addSubview(titleLabel)
        self.contentView.addSubview(subtitleLabel)
        self.contentView.addSubview(specificLabel)
        self.contentView.addSubview(reservationInfoLabel)
        
        self.checkboxImageView.snp.makeConstraints { make in
            make.width.height.equalTo(20.0)
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().offset(12.0)
        }
        
        self.coverImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(44)
            make.top.equalToSuperview().offset(12.0)
            make.size.equalTo(kGoodsSelectCoverSize)
        }
        
        self.indexBgImageView.snp.makeConstraints { make in
            make.top.left.equalToSuperview()
            make.size.equalTo(CGSize(width: 20.0, height: 16.0))
        }
        
        self.indexLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        self.titleLabel.snp.makeConstraints { make in
            make.height.equalTo(20.0)
            make.top.equalTo(coverImageView).offset(2)
            make.left.equalTo(coverImageView.snp.right).offset(8)
            make.right.lessThanOrEqualTo(contentView).offset(-12)
        }
        
        self.subtitleLabel.snp.makeConstraints { make in
            make.height.equalTo(18.0)
            make.left.equalTo(self.titleLabel)
            make.top.equalTo(self.titleLabel.snp.bottom).offset(4.0)
            make.right.lessThanOrEqualTo(contentView).offset(-12)
        }
        
        self.specificLabel.snp.makeConstraints { make in
            make.height.equalTo(14.0)
            make.width.equalTo(40.0)
            make.left.equalTo(self.titleLabel)
            make.top.equalTo(self.subtitleLabel.snp.bottom).offset(6.0)
        }
        
        self.reservationInfoLabel.snp.makeConstraints { make in
            make.height.equalTo(14.0)
            make.left.equalTo(self.titleLabel)
            make.right.equalToSuperview().offset(-12.0)
            make.bottom.equalTo(self.coverImageView).offset(-2.0)
        }
    }
    
    func updateCell(with cellModel: AdsLeadBindCardModel) {
        self.cellModel = cellModel
        self.updateCheckboxStatus(cellModel)
        self.coverImageView.sd_setImage(with: URL(string: cellModel.cover ?? ""), completed: nil)
        self.indexBgImageView.snp.updateConstraints { make in
            make.size.equalTo(CGSize(width: self.cellModel?.orderIndex ?? 1 >= 100 ? 25 : 20, height: 16))
        }
        
        self.titleLabel.text = cellModel.title
        self.subtitleLabel.text = cellModel.subTitle
        self.indexLabel.text = String(cellModel.orderIndex)
        self.reservationInfoLabel.text = cellModel.effectText
        
        self.specificLabel.text = cellModel.cardTag
        self.specificLabel.sizeToFit()
        self.specificLabel.snp.updateConstraints { make in
            make.width.equalTo(self.specificLabel.bounds.size.width + 4.0)
        }
    }
    
    func updateCheckboxStatus(_ cellModel: AdsLeadBindCardModel) {
        if cellModel.selected {
            checkboxImageView.image = UIImage.xy_liveKitBundleImage("xyLiveShopItemCheckIcon")
        } else {
            checkboxImageView.image = UIImage.xy_liveKitBundleImage("xyLiveShopItemUnCheckIcon")
        }
    }
}
