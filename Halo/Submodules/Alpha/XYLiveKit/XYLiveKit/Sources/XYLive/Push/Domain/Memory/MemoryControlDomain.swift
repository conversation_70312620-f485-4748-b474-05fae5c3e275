//
//  MemoryControlDomain.swift
//  XYLiveKit
//
//  Created by wangzhenxing on 2024/9/24.
//  Copyright © 2024 XingIn. All rights reserved.
//

import Foundation
import XYEyeSauron

@objc public enum LiveMemoryLevel: Int {
    case nomal = 0
    case low = 1
    case warning = 2
    case critical = 3
    case systemwarning = 4
}

@objc(XYLiveMemoryConrolService)
public protocol MemoryControlService: AnyObject {
    func liveMemoryLevel() -> LiveMemoryLevel
}

@objc(XYLiveMemoryControlDependency)
public protocol MemoryControlDependency: AnyObject {
    func roomEventService() -> XYLivePlayRoomEventService?
}

@objc(XYLiveMemoryControlDomain)
@objcMembers

public class MemoryControlDomain: ViewControllerDomain {
    
    private weak var depend: MemoryControlDependency?
    private var memoryLevel: LiveMemoryLevel
    
    required public init(_ hostVC: VCHostUIViewController, depend: MemoryControlDependency) {
        self.depend = depend
        if let memoryLevel = LiveMemoryLevel(rawValue: XYEyeSauronRAMSession.session .ramLevel.rawValue) {
            self.memoryLevel = memoryLevel
        } else {
            self.memoryLevel = .nomal
        }
        super.init(hostVC)
        XYEyeSauronRAMSession.session.subscribe(self)
    }
    
    deinit {
        XYEyeSauronRAMSession.session.unsubscribe(self)
    }
    
    public override func doRegisterService() {
        super.doRegisterService()
        registerService(protocol: MemoryControlService.self, service: self)
    }
    
    private func memLevelEvent() {
        if let eventService = self.depend?.roomEventService() {
            eventService.asXYLivePlayRoomEventServiceProtocol().eventMemEvent?(self.memoryLevel)
        }
    }
}

extension MemoryControlDomain: MemoryControlService {
    public func liveMemoryLevel() -> LiveMemoryLevel {
        return memoryLevel
    }
}

extension MemoryControlDomain: XYEyeSauronRAMSessionProtocol {
    public func ramSession(_ session: XYEyeSauronRAMSession, ramUsed: XYEyeSauronRAMUsed) {
        
    }
    
    public func ramSession(_ session: XYEyeSauronRAMSession, ramUsed: XYEyeSauronRAMUsed, ramLevel: XYEyeSauronRAMLevel) {
        DispatchQueue.main.async {
            if let memoryLevel = LiveMemoryLevel(rawValue: ramLevel.rawValue) {
                self.memoryLevel = memoryLevel
                self.memLevelEvent()
            }
        }
    }
}
