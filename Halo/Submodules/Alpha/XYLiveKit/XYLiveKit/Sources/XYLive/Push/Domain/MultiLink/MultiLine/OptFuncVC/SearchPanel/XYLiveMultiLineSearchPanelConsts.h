//
//  XYLiveMultiLineSearchPanelConsts.h
//  XYLiveKit
//
//  Created by 大远 on 2025/6/7.
//  Copyright © 2025 XingIn. All rights reserved.
//
#import <XYLiveUIKit/XYLiveUIKit.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineConsts.h>


#ifndef XYLiveMultiLineSearchPanelConsts_h
#define XYLiveMultiLineSearchPanelConsts_h

// 面板高度
#define kXYLiveMultiLineSearchPanelH           DeviceUtility.screenHeight * 0.75

// cell高度
static CGFloat const kXYLiveMultiLineSearchListItemCellH = 68;

// 导航条高度
static CGFloat const kXYLiveMultiLineSearchListNavBarH = 52;

// section高度
static CGFloat const kXYLiveMultiLineSearchListSectionH = 34;

// 重用标识符
static NSString * const kXYLiveMultiLineSearchListItemCelllIde = @"XYLiveLinkHostInviteListItemCell";

#endif /* XYLiveMultiLineSearchPanelConsts_h */
