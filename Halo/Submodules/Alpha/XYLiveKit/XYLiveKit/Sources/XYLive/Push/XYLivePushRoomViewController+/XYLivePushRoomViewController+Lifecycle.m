//
//  XYLivePushRoomViewController+Lifecycle.m
//  XYLiveKit
//
//  Created by 周博立 on 2023/1/18.
//  Copyright © 2023 XingIn. All rights reserved.
//

#import "XYLivePushRoomViewController.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushDecorateView.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLiveWalletManager.h"
#import "XYLivePushHierarchyManager.h"
#import "UIViewController+XYLive.h"
#import "XYLiveCommonShareNode.h"

@import XYConfigCenter;
@import KVOController;
@import XYAlphaFusion;

@implementation XYLivePushRoomViewController (Lifecycle)

/// !!! P0 警告：禁止执行任何直接或间接调用 weak self 的代码
- (void)lifecycle_dealloc {
    [self clearPreviewLogger];
    [self notification_unregistry];
    
    if (!XYLiveManagerSharedInstance.globalWindow.floating) {
        BOOL nonStop = XYConfigCenter().boolForKey(@"ios_live_manager_dealloc_non_stop", NO);
        if (nonStop) {
            [XYLiveManagerSharedInstance onDealloc];
        } else {
            [XYLiveManagerSharedInstance destroyWithNotifyServer:!self.decorateVM.dismissWithoutCallServer onPrepRoom:NO];
        }
    }
    
    [XYLiveWalletManager.sharedManager destroy];
    if (!XYLiveManagerSharedInstance.globalWindow.floating) {
        [XYLiveManagerSharedInstance.coreManager.media releaseCamera];
    }
    [XYLiveAlfredInterfaceCenter destroy];
    [XYLiveGuidanceMutexManager destroy];
    if ([XYAlphaSwitch vpnDetectValid]) {
        [[VPNNetService sharedInstance] unregisterListener:self];
    }
    
    [XYLiveDebugToast showWithToast:@"push room vc dealloc"];
    [XYLogCollector xyLiveLogTag:@"push" content:@"push room vc dealloc"];
}

- (void)lifecycle_viewDidLoad {
    [self lifecycle_viewDidLoad_beforeNode];
    [self lifecycle_viewDidLoad_nodeRegistry];
    [self lifecycle_viewDidLoad_afterNode];
    // 接口请求
    [self fetchAggregateBusinessInfo];
    [self fetchHostRoomConfig];
    if ([XYAlphaSwitch vpnDetectValid]) {
        BOOL isValidBiz = XYLiveManagerSharedInstance.roomInfo.isVideoLive || XYLiveManagerSharedInstance.roomInfo.isVoiceLive || XYLiveManagerSharedInstance.roomInfo.isScreenLive;
        if (isValidBiz) {
            [[VPNNetService sharedInstance] registerListener:self];
        }
    }
}

- (void)fetchAggregateBusinessInfo {
    __weak typeof(self) weakSelf = self;
    [XYLiveManagerSharedInstance fetchAggregateBusinessInfoWithCompletion:^(XYLiveAggregateBusinessInfo * _Nullable info) {
        if (info) {
            [weakSelf aggregateBusinessInfoDidLoadWithInfo:info];
        }
    }];
}

- (void)fetchHostRoomConfig {
    __weak typeof(self) weakSelf = self;
    [XYLiveManagerSharedInstance fetchHostRoomConfigWithCompletion:^(XYLiveHostEnterRoomConfig * _Nullable info) {
        if (info) {
            [weakSelf hostRoomConfigInfoDidLoadWithInfo:info];
        }
    }];
}

- (void)lifecycle_viewDidLoad_afterNode {
    [self kvoRegistry_setup];
    [self setupActionLinkRegister];
    [self moduleRouter_registry];
    [self notification_registry];
    
    [self setupSwitchPusherIM];
    
    self.definesPresentationContext = YES;
    
    BOOL open = XYConfigCenter().boolForKey(@"ios_live_pushvc_hook_callstack", false);
    if (open) {
        [UIViewController xyLive_swizzleDismissViewController];
    }
    
    [UIViewController xyLive_swizzle];
    if (XYConfigCenter().boolForKey(@"ios_live_liveviewswizzle_open", true)) {
        [UIView hook];
    }
    
    [self setup_analyticsDataSource];
    
    // apm
    xyLiveSharedAPMManager.startApmInfo.enterPushTs = [[NSDate date] timeIntervalSince1970];
    
    // 注入im
    id<XYLiveMultiLinkPushServiceProtocol> service =
    [self.serviceManager getServiceWithProtocol:@protocol(XYLiveMultiLinkPushServiceProtocol)];
    [service bindImDistributer: XYLiveManagerSharedInstance.liveImDistributer];
    
    // start frame count monitor
    if (XYLiveManagerSharedInstance.roomInfo.isVideoLive) {
        [XYLiveManagerSharedInstance.coreManager.media startFrameCountMonitor];
    }
    
    [XYLiveClassPropertiesUtil logProperties: self.class];
}

- (void)lifecycle_viewDidLoad_nodeRegistry {
    // 视图层级
    XYLivePushHierarchyManager *hierarchyManager = (XYLivePushHierarchyManager *)self.hierarchyManager;
    [hierarchyManager mapHierarchyView:self.view
                            renderView:self.renderContainerView
                         bussinessView:self.decorateView
                             cleanView:self.decorateView.businessContainerView
                    alertContainerView:self.decorateView.alertContainerView
                    panelContainerView:self.decorateView.panelContainerView];
    
    // 加载业务nodes
    [self.nodesLoader loadBizNodes];
    
    [self buildDomain];
    // 生命周期
    [self.nodesLoader viewDidLoad];
    
    [hierarchyManager setupHierarchyAdapters:self.nodesLoader.nodes];
    [hierarchyManager triggerSetupUI];
    if ([XYLiveConfigCenter pushFeelBorderDomainAdjust]) {
        [self buildFeelBorderDomain];
    }
}

- (void)lifecycle_viewDidLoad_beforeNode {
    [XYLiveManagerSharedInstance.consumeService memoryGenerate:XYLiveConsumeMemOptionStartMem];
    [XYLiveManagerSharedInstance setupTraces];
    self.viewDidLoadTime = CFAbsoluteTimeGetCurrent();
    // Log
    [XYLogCollector xyLiveLogTag:@"rtc" content:[NSString stringWithFormat:@"push_room_vc vdl rtc_switch: %@", @(self.roomInfo.pushType)]];
    [XYLiveEffectViewController logEffectConfigForScene:@"scene_start_push"];
    
    ///  基础服务初始化
    [self setupBaseService];
    
    if ([self canApplyRTC]) {
        [xyLiveSharedAPMManager updateRTCPushResolution:XYLiveManagerSharedInstance.roomInfo.encodedResolution];
    }
    
    [self setup_resetParameters];
    [self setupPusher];
    [self setupUI];
    [self sdk_setupListener];
    [self sdk_setCameraCapture];
    // 电脑推流时立即Start
    if (self.shouldStopPush) {
        [self sdk_startLive];
    }
    [[XYSCKVMigrateTool defaultUserDefaultTool_v2] setInt:self.roomInfo.contentType forKey:kXYLiveLastLiveMode];
}

- (void)lifecycle_viewDidAppear {
    [XYLogCollector xyLiveLogTag:@"push_room" content:[NSString stringWithFormat:@"view did appear: %@", @(self.roomInfo.pushType)]];
    [XYLiveMediaControl reloadAudioSessionWithReceiver:self sceneType:XYLiveAudioSessionScenePush];

    if (self.needInitLivePush) {
        self.needInitLivePush = NO;
        [XYLogCollector xyLiveLogTag:@"push_room" content:[NSString stringWithFormat:@"before start push: %@", @(self.roomInfo.pushType)]];
        
        [self startPush:self.roomInfo loginParam:self.loginParam];
        
        [XYLogCollector xyLiveLogTag:@"push_room" content:[NSString stringWithFormat:@"end start push: %@", @(self.roomInfo.pushType)]];
        if (self.screenshotView) {
            [UIView animateWithDuration:0.1 animations:^{
                self.screenshotView.alpha = 0;
            } completion:^(BOOL finished) {
                [self.screenshotView removeFromSuperview];
                self.screenshotView = nil;
            }];
        }
    }
    // 校验麦上用户开启视频采集恢复preView
    [self checkLinkCameraPreview];
    
    /// 截屏监听
    id<XYLiveCommonShareServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveCommonShareServiceProtocol)];
    [service resumeScreenShotMonitor];
}

- (void)aggregateBusinessInfoDidLoadWithInfo:(XYLiveAggregateBusinessInfo *)businessInfo {
    id<XYLivePushDisperseEventServiceProtocol> eventService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushDisperseEventServiceProtocol)];
    [eventService aggregateBusinessInfoDidLoad:businessInfo];
}

- (void)hostRoomConfigInfoDidLoadWithInfo:(XYLiveHostEnterRoomConfig *)info {
    id<XYLivePushDisperseEventServiceProtocol> eventService = [self.serviceManager getServiceWithProtocol:@protocol(XYLivePushDisperseEventServiceProtocol)];
    [eventService hostEnterRoomInfoDidLoad:info];
}

- (void)lifecycle_viewWillAppear {
    [XYLogCollector xyLiveLogTag:@"push_room" content:[NSString stringWithFormat:@"view will appear: %@", @(self.roomInfo.pushType)]];
    // 关闭小窗
    if (XYLiveManagerSharedInstance.globalWindow) {
        [XYLiveManagerSharedInstance.globalWindow dismiss];
        [XYLiveManagerSharedInstance.globalWindow reset];
        id<XYLiveMultiLinkPushServiceProtocol> multiLinkService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveMultiLinkPushServiceProtocol)];
        if (multiLinkService.isLinking) {
            [XYLiveManagerSharedInstance.liveImDistributer trigger:@"linkmic_host_resume_audio" info:nil raw:@{
                @"mute" :@(NO)
            }];
        }
    }
    // 恢复camera preview布局
    BOOL flag = (XYConfigCenter().boolForKey(@"ios_live_chat_camera_leak", NO) == NO) ? (!XYLiveManagerSharedInstance.coreManager.media.camera.willStopRuning && [XYLiveManagerSharedInstance.coreManager.media.camera running]) : YES;
    if (flag && [XYLiveManagerSharedInstance.coreManager.media.camera cameraView].superview != self.cameraRenderingContainerView) {
        [XYLiveManagerSharedInstance.coreManager.media.camera startPreview:self.cameraRenderingContainerView];
    }

    if (!XYConfigCenter().boolForKey(@"ios_live_floating_window_group_leave", NO)) {
        if (!XYLiveManagerSharedInstance.isLiveFinished) {
            [self.decorateVM startListeningParser];
        } else if (XYConfigCenter().justOnceBoolForKey(@"ios_live_fix_live_end_has_im", NO)) {
            [self.decorateVM startListeningParser];
        }
    }
}

- (void)lifecycle_viewWillDisappear {
    [XYLogCollector xyLiveLogTag:@"push_room" content:[NSString stringWithFormat:@"view will disappear"]];
    [[XYLiveAlfredInterfaceCenter center] dismissEntrance];
    id<XYLiveCommonShareServiceProtocol> service = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveCommonShareServiceProtocol)];
    [service stopScreenShotMonitor];
}

- (void)lifecycle_viewDidDisappear {
    [XYLogCollector xyLiveLogTag:@"push_room" content:@"view did disappear"];
    if ([self canApplyRTC]) {
        self.rtcPusher.visible = NO;
    } else if ([self canApplyKasa]) {
        self.kasaPusher.isVisible = NO;
    } else {
        self.pusher.isVisible = NO;
    }
}

@end
