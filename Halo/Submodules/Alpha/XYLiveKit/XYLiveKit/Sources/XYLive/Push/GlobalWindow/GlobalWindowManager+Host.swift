//
//  GlobalWindowManager+Host.swift
//  XYLiveKit
//
//  Created by 王帅 on 2023/5/24.
//  Copyright © 2023 XingIn. All rights reserved.
//

import Foundation
import XYLiveCore
import XYLivePusher
import XYConfigCenter

public extension GlobalWindowManager {
    func registerHostIM() {
        imDistributer?.register(type: kIMCUSTOM_LIVE_PUSH_SDK_TYPE_CHANGE) { [weak self] type, _, rawData in
            self?.collectLog(content: "receive im type: \(type)")
            if let content = try? XYLivePusherChangeInfo.xy_model(dictionary: rawData) {
                guard let self = self else { return }
                defer {
                    self.reportPushSwitchResult()
                }
                guard self.isLinking() else {
                    // 起推流器trtc & kasa后续
                    self.startLink()
                    return
                }
                
                guard self.rtcCore?.pushType != content.pushType else {
                    return
                }
                
                // 后续接kasa & trtc 互切
            }
        }
    }
    
    func reportPushSwitchResult() {
        guard let core = self.rtcCore else {
            return
        }
        MultiLinkmicCommService.reportPusherSwitch(type: core.pushTypeDesc) {[weak self] success, error in
            self?.collectLog(content: "success = \(success) error = \(String(describing: error))")
        }
    }
    
    func startLink(vendorType: RTCVendorType = .TRTC) {
        self.collectLog(content: "switchPusherToRtcCore enter")
        if self.rtcCore != nil {
            if self.rtcCore?.vendorType != vendorType {
                // 热切
                let coreSession: RTCCoreChannelSession = self.buildRtcChannelSession(nil)
                if ConfigCenter.shared.bool(forKey: "ios_live_push_multi_level_global_window_fix", defalut: true), XYAlphaSwitch.enablePushMultiLevel() {
                    if let levels = self.rtcCore?.encodeInfo?.multiLevel, !levels.isEmpty {
                        coreSession.encode.multiLevel = levels
                    }
                    if let levelName = self.rtcCore?.encodeInfo?.levelName, !levelName.isEmpty {
                        coreSession.encode.levelName = levelName
                    }
                }
                self.rtcCore?.loadRTCCoreChannelSession(coreSession)
                self.presetImageUtil.configBizInfo(voiceLive: coreSession.baseInfo?.bizInfo.isVoiceLive ?? false, screenLive: coreSession.baseInfo?.bizInfo.isScreenCap ?? false, chatLive: coreSession.baseInfo?.bizInfo.isLiveChat ?? false)
                self.presetImageUtil.configAppInactiveImage()
                self.rtcCore?.startPush()
                XYLiveManager.shared().roomInfo.pushType = .trtc
            } else {
                self.collectLog(content: "switchPusherToRtcCore faliure")
                return
            }
        }
        
        self.collectLog(content: "switchPusherToRtcCore success")
        let session = self.rtcPusher?.rtcChannelSession
        if let rtcPuhser: RTCPusher = self.rtcPusher {
            // 销毁rtcPusher
            rtcPuhser.destroyRTCVendor(destoryTRTCCloud: false)
            let trtcDestoryed = rtcPuhser.trtcAlreadyDestoryCloud
            self.rtcPusher = nil
            // 加载rtc core
            let coreSession: RTCCoreChannelSession = self.buildRtcChannelSession(session)

            coreSession.reJoinChannel = !trtcDestoryed
            
            if self.rtcCore == nil {
                self.rtcCore = RTCCore()
            }
            
            self.rtcCore?.loadRTCCoreChannelSession(coreSession)
            self.presetImageUtil.configBizInfo(voiceLive: coreSession.baseInfo?.bizInfo.isVoiceLive ?? false, screenLive: coreSession.baseInfo?.bizInfo.isScreenCap ?? false, chatLive: coreSession.baseInfo?.bizInfo.isLiveChat ?? false)
            self.presetImageUtil.configAppInactiveImage()
            
            self.rtcCore?.startPush()
            
            XYLiveManager.shared().roomInfo.pushType = .trtc
        }
    }
    
    func buildRtcChannelSession(_ session: RTCChannelSession?) -> RTCCoreChannelSession {
        let availableSession: RTCChannelSession = session ?? buildSession()
        
        func buildSession() -> RTCChannelSession {
            let roomInfo = XYLiveManager.shared().roomInfo
            let passport = XYLiveManager.shared().imConfigParam?.passport ?? XYLivePassport()
            let userInfo = XYLiveManager.shared().userInfo
            return RTCChannelSession.session(roomInfo: roomInfo, userInfo: userInfo, passport: passport, role: .host, audienceRtcLinkReference: .trtc)
        }
        
        let coreSession = RTCCoreChannelSession(channelSession: availableSession)
        let bizInfo = RTCChannelBizInfo()
        bizInfo.isVoiceLive = XYLiveManager.shared().roomInfo.isVoiceLive
        bizInfo.isLiveChat = XYLiveManager.shared().roomInfo.isLiveChat
        bizInfo.isScreenCap = XYLiveManager.shared().roomInfo.isScreenLive
        bizInfo.isVideoLive = XYLiveManager.shared().roomInfo.isVideoLive
        bizInfo.fansCount = XYLiveManager.shared().hostInfo?.totalFansNum ?? 0

        if ConfigCenter.shared.bool(forKey: "ios_live_push_landscape_fix", defalut: true) {
            bizInfo.isLandscape = PushPrepParamCache.pushLandscape && XYLiveManager.shared().roomInfo.isScreenLive
        } else {
            bizInfo.isLandscape = PushPrepParamCache.pushLandscape
        }
        
        bizInfo.isHost = true
        coreSession.baseInfo?.bizInfo = bizInfo
        coreSession.baseInfo?.vendorType = .TRTC
        return coreSession
    }
    
    func hostClose() {
        guard floating else {
            return
        }
        
        let enable = ConfigCenter.shared.justOnceBool(forKey: "ios_live_enable_push_global_window_op", defalut: true)
        if enable {
            guard self.isDismissVC else {
                collectLog(content: "host close vc exist")
                self.dismiss()
                self.reset()
                return
            }
        }
        
        collectLog(content: "host close")
        if let info = self.linkUtil?.onMicInfo {
            info.stopReasonDesc = "ROOM_CLOSE"
            self.linkUtil?.onHostStopLink(info: info)
        }
        self.destoryLink()
        self.dismiss()
        XYLiveManager.shared().onDealloc()
    }
}

