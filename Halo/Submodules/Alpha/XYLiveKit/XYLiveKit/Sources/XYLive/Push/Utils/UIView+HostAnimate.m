//
//  UIView+HostAnimate.h
//  XYLiveKit
//
//  Created by 张恒 on 2024/5/28.
//  Copyright © 2024 小红书. All rights reserved.
//

#import "UIView+HostAnimate.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
@import XYAlphaUtils;
#if RELEASE
#else
@import XYLiveFoundation;
#endif

// swiftlint:disable line_length
@implementation UIView (HostAnimate)

+ (BOOL)hostAnimateDegrade {
    if (!XYAlphaSwitch.enablePushCustomAnimationFps) {
        return false;
    }
    BOOL needDegrade = XYLivePushAnimationDegradeControl.canDegrade;
#if RELEASE
    return needDegrade;
#else
    return [XYLiveOdysseyConfig hostAnimateDegrade] || needDegrade;
#endif
}

+ (void)apa_hostAnimateWithDuration:(NSTimeInterval)duration animations:(void (^)(void))animations {
    if (self.hostAnimateDegrade) {
        [UIView animateWithDuration:duration delay:0 options:UIViewAnimationOptionPreferredFramesPerSecond30 animations:animations completion:nil];
    } else {
        [UIView animateWithDuration:duration animations:animations];
    }
}

+ (void)apa_hostAnimateWithDuration:(NSTimeInterval)duration animations:(void (^)(void))animations completion:(void (^)(BOOL))completion {
    if (self.hostAnimateDegrade) {
        [UIView animateWithDuration:duration delay:0 options:UIViewAnimationOptionPreferredFramesPerSecond30 animations:animations completion:completion];
    } else {
        [UIView animateWithDuration:duration animations:animations completion:completion];
    }
}

+ (void)apa_hostAnimateWithDuration:(NSTimeInterval)duration delay:(NSTimeInterval)delay options:(UIViewAnimationOptions)options animations:(void (^)(void))animations completion:(void (^)(BOOL))completion {
    if (self.hostAnimateDegrade) {
        [UIView animateWithDuration:duration delay:delay options:options | UIViewAnimationOptionPreferredFramesPerSecond30 animations:animations completion:completion];
    } else {
        [UIView animateWithDuration:duration delay:delay options:options animations:animations completion:completion];
    }
}

+ (void)apa_hostAnimateWithDuration:(NSTimeInterval)duration delay:(NSTimeInterval)delay usingSpringWithDamping:(CGFloat)dampingRatio initialSpringVelocity:(CGFloat)velocity options:(UIViewAnimationOptions)options animations:(void (^)(void))animations completion:(void (^ __nullable)(BOOL finished))completion {
    if (self.hostAnimateDegrade) {
        [UIView animateWithDuration:duration delay:delay usingSpringWithDamping:dampingRatio initialSpringVelocity:velocity options:options | UIViewAnimationOptionPreferredFramesPerSecond30 animations:animations completion:completion];
    } else {
        [UIView animateWithDuration:duration delay:delay usingSpringWithDamping:dampingRatio initialSpringVelocity:velocity options:options animations:animations completion:completion];
    }
}

@end

