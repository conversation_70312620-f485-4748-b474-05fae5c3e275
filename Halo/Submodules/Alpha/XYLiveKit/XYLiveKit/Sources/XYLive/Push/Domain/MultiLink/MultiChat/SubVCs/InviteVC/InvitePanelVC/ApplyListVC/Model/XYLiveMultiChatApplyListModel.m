//
//  XYLiveMultiChatApplyListModel.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatApplyListModel.h"

@implementation XYLiveMultiChatApplyListItem

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"userId": @"user_id",
        @"nickName": @"nickname",
        @"avatarURL": @"avatar",
        @"followStatus": @"follow_status",
        @"mediaType": @"media_type",
        @"sendCoins": @"send_coins",
        @"applyTime": @"apply_time",
        @"applyReason": @"apply_reason",
        @"hasTargetSeat": @"has_target_seat",
        @"seatNumber": @"seat_number",
        @"seatName": @"seat_name",
        @"sessionId": @"session_id",
        @"fansClubInfo": @"fans_group"
    };
}

- (XYLiveUserInfo *)userInfo {
    XYLiveUserInfo *user = [[XYLiveUserInfo alloc] init];
    user.userID = self.userId;
    user.avatar = self.avatarURL;
    user.nickname = self.nickName;
    user.redCoins = self.sendCoins;
    user.mtype = self.mediaType == XYLiveMultiLinkMediaTypeAudio ? XYLiveIMLinkmicTypeAudio : XYLiveIMLinkmicTypeVideo;
    return user;
}

@end

@implementation XYLiveMultiChatApplyListSortItem

@end

@implementation XYLiveMultiChatApplyListModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"applyList": @"apply_list",
    };
}

+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"applyList": XYLiveMultiChatApplyListItem.class,
    };
}

@end
