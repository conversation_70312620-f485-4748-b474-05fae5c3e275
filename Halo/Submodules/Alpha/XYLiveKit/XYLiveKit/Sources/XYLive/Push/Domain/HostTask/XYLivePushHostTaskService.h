//
//  XYLivePushHostTaskService.h
//  XYLiveKit
//
//  Created by gongyidemac on 2025/6/13.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>

@import XYLiveServiceProtocol;

NS_ASSUME_NONNULL_BEGIN

@protocol XYLiveHostTaskServiceDelegate <XYLivePushHostTaskServiceProtocol>

@end

@interface XYLivePushHostTaskService : NSObject <XYLivePushHostTaskServiceProtocol>

@property (nonatomic, weak, nullable) id<XYLiveHostTaskServiceDelegate> delegate;

@end

NS_ASSUME_NONNULL_END
