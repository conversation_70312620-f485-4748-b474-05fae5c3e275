//
//  XYLiveMultiPKInviteVM.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/29.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiPKInviteVM.h"
#import <XYLiveKit/XYLiveLinkHostInviteeInfo.h>
#import <XYLiveServiceProtocol/XYLiveMultiPKConsts.h>
#import <XYFoundation/XYFoundation.h>

@interface XYLiveMultiPKInviteVM()

@property (nonatomic, weak) id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak) id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, copy) NSArray<XYLiveLinkHostInviteeInfo *> *inviteeInfoList;

@end

@implementation XYLiveMultiPKInviteVM

/// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
        _inviteeInfoList = @[];
    }
    return self;
}

// 发起邀请
- (void)inviteWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo source:(XYLiveMultiPKInviteSource)source extraInfo:(NSString *_Nullable)extraInfo {
    // 更新缓存
    [self addItemWithInviteeInfo:inviteeInfo];
    // 更新状态
    [self updateInviteState:XYLiveMultiLinkInviteStateInviting userId:inviteeInfo.userId];
    // 发起请求
    [self.multiLinkService inviteWithBizType:XYLiveMultiLinkBizTypePKGift targetUserId:inviteeInfo.userId targetRoomId:inviteeInfo.roomId mediaType:XYLiveMultiLinkMediaTypeVideo bizExtraInfo:nil];
}

// 取消邀请
- (void)cancelInviteWithUserId:(NSString *)targetUserId {
    [self.multiLinkService cancelInviteWithBizType:XYLiveMultiLinkBizTypePKGift targetUserId:targetUserId];
}

// 是否处于邀请中
- (BOOL)isInvitingWithUserId:(NSString *)userId {
    return [self.multiLinkService isInvitingWithBizType:XYLiveMultiLinkBizTypePKGift targetUserId:userId];
}

// 接受邀请
- (void)acceptInviteWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId targetRoomId:(NSString *)targetRoomId {
    [self.multiLinkService acceptInviteWithBizType:bizType targetUserId:targetUserId targetRoomId:targetRoomId bizExtraInfo:nil];
}

// 拒绝邀请
- (void)rejectInviteWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId targetRoomId:(NSString *)targetRoomId {
    [self.multiLinkService rejectInviteWithBizType:bizType targetUserId:targetUserId targetRoomId:targetRoomId];
}


#pragma mark - XYLiveLinkHostInviteeInfo

- (void)onInviteResponseWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId error:(NSError *)error {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypePKGift && bizType != XYLiveMultiLinkBizTypePKHeat) { return; }
    // 处理邀请成功相关逻辑
    if (error == nil) {
        // Toast提示
        [XYAlert live_showTextItemWithText:@"已发送邀请"];
    } else {
        // 更新状态
        [self updateInviteState:XYLiveMultiLinkInviteStateIdle userId:targetUserId];
        // 更新缓存
        [self deleteItemWithUserId:targetUserId];
        // Toast提示
        [XYAlert live_showTextItemWithError:error];
    }
}

- (void)onCancelInviteResponseWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId error:(NSError *)error {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypePKGift && bizType != XYLiveMultiLinkBizTypePKHeat) { return; }
    // 处理取消成功
    if (error == nil) {
        // 更新状态
        [self updateInviteState:XYLiveMultiLinkInviteStateIdle userId:targetUserId];
        // 更新缓存
        [self deleteItemWithUserId:targetUserId];
        // Toast提示
        [XYAlert live_showTextItemWithText:@"已撤销邀请"];
    } else {
        // 移除提示
        [[XYAlert sharedInstance] hideLoadingAlertItems];
        // Toast提示
        [XYAlert live_showTextItemWithError:error];
    }
}

- (void)onInviteSuccessWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypePKGift && bizType != XYLiveMultiLinkBizTypePKHeat) { return; }
    // 更新状态
    [self updateInviteState:XYLiveMultiLinkInviteStateInvited userId:targetUserId];
    // 更新缓存
    [self deleteItemWithUserId:targetUserId];
}

- (void)onInviteFailWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId failReason:(XYLiveMultiLinkInviteFailReason)failReason {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypePKGift && bizType != XYLiveMultiLinkBizTypePKHeat) { return; }
    // 更新状态
    [self updateInviteState:XYLiveMultiLinkInviteStateIdle userId:targetUserId];
    // 更新缓存
    [self deleteItemWithUserId:targetUserId];
    // Toast提示
    [XYAlert live_showTextItemWithText:@"对方已拒绝"];
}

- (void)onReceiveInviteMessageWithBizType:(XYLiveMultiLinkBizType)bizType inviteInfo:(XYLiveMultiLinkInviteInfo *)inviteInfo {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypePKGift && bizType != XYLiveMultiLinkBizTypePKHeat) { return; }
    // 事件分发
    if ([self.delegate respondsToSelector:@selector(onReceiveInviteWithBizType:inviteInfo:)]) {
        [self.delegate onReceiveInviteWithBizType:bizType inviteInfo:inviteInfo];
    }
}

- (void)onAcceptInviteResponseWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId error:(NSError *)error {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypePKGift && bizType != XYLiveMultiLinkBizTypePKHeat) { return; }
    // 处理失败
    if (error) {
        // Toast提示
        [XYAlert live_showTextItemWithError:error];
    }
}

- (void)onRejectInviteResponseWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId error:(NSError *)error {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypePKGift && bizType != XYLiveMultiLinkBizTypePKHeat) { return; }
    // 处理失败
    if (error) {
        // Toast提示
        [XYAlert live_showTextItemWithError:error];
    }
}

- (void)onInviteCountDownTrickWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId remainTime:(NSInteger)remainTime totalTime:(NSInteger)totalTime {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypePKGift && bizType != XYLiveMultiLinkBizTypePKHeat) { return; }
    // 更新倒计时
    if ([self.delegate respondsToSelector:@selector(onTimerTrick:totalInterval:)]) {
        [self.delegate onTimerTrick:remainTime totalInterval:totalTime];
    }
}

- (void)onInviteCountDownFinishWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypePKGift && bizType != XYLiveMultiLinkBizTypePKHeat) { return; }
    // 倒计时完成
    if ([self.delegate respondsToSelector:@selector(onTimerFinish)]) {
        [self.delegate onTimerFinish];
    }
}

#pragma mark - Private

// 添加邀请
- (void)addItemWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    NSMutableArray *listItemsM = self.inviteeInfoList.mutableCopy;
    [listItemsM addObject:inviteeInfo];
    self.inviteeInfoList = listItemsM.copy;
}

// 删除邀请
- (void)deleteItemWithUserId:(NSString *)userId {
    NSMutableArray *listItemsM = [NSMutableArray array];
    [self.inviteeInfoList enumerateObjectsUsingBlock:^(XYLiveLinkHostInviteeInfo * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (![obj.userId isEqualToString:userId]) {
            [listItemsM addObject:obj];
        }
    }];
    self.inviteeInfoList = listItemsM.copy;
}

// 更新某人邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)inviteState userId:(NSString *)userId {
    // 过滤用户信息
    XYLiveLinkHostInviteeInfo *inviteeInfo = [self.inviteeInfoList xy_match:^BOOL(XYLiveLinkHostInviteeInfo * _Nonnull obj) {
        return [obj.userId isEqualToString:userId];
    }];
    // 合法性校验
    if (inviteeInfo == nil) { return; }
    [XYLogCollector xyLiveLogTag:kXYLiveMultiPKModuleName content:[NSString stringWithFormat:@"update invite state:{state:%@, userid:%@}", @(inviteState), userId]];
    // 事件分发
    if ([self.delegate respondsToSelector:@selector(onUpdateInviteState:inviteeInfo:)]) {
        [self.delegate onUpdateInviteState:inviteState inviteeInfo:inviteeInfo];
    }
}

@end
