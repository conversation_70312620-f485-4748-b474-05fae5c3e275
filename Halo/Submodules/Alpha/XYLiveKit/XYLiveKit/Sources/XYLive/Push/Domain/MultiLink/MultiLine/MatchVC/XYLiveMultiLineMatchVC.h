//
//  XYLiveMultiLineMatchVC.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/28.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineMatchServiceProtocol.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiLineMatchVC : XYViewController<XYLiveMultiLineMatchServiceProtocol>

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

// 发起重新匹配
- (void)startReMatchWithExtraInfo:(NSString * _Nullable)extraInfo;

// 关闭弹窗
- (void)hideAllPanel;

// 注册监听
- (void)registerListener:(id<XYLiveMultiLineMatchListener>)listener;

// 取消注册监听
- (void)unregisterListener:(id<XYLiveMultiLineMatchListener>)listener;

@end

NS_ASSUME_NONNULL_END
