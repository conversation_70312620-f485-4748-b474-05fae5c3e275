//
//  XYLiveMultiChatInviteListHeaderView.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/23.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatInviteListHeaderView.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <Masonry/Masonry.h>

@interface XYLiveMultiChatInviteListFriendHeaderView()

@property (nonatomic, strong) UILabel *titleLbl;

@end

@implementation XYLiveMultiChatInviteListFriendHeaderView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.backgroundColor = [XYLiveTokenColor groupedSecondaryBackground];
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    // 创建标题
    [self setupTitleLbl];
    
    // 布局
    [self.titleLbl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16);
        make.top.mas_equalTo(12);
    }];
}

#pragma mark - setter

- (void)setTitle:(NSString *)title {
    self.titleLbl.text = title;
}

#pragma mark - UI

- (void)setupTitleLbl {
    UILabel *titleLbl = [[UILabel alloc] init];
    titleLbl.textColor = XYLiveTokenColor.desc;
    titleLbl.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
    [self addSubview:titleLbl];
    self.titleLbl = titleLbl;
}

@end


@interface XYLiveMultiChatInviteListHeaderView()

@property (nonatomic, strong) UILabel *titleLbl;

@end

@implementation XYLiveMultiChatInviteListHeaderView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        self.backgroundColor = [XYLiveTokenColor groupedSecondaryBackground];
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    // 创建标题
    [self setupTitleLbl];
    
    // 布局
    [self.titleLbl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16);
        make.centerY.equalTo(self);
    }];
}

#pragma mark - setter

- (void)setTitle:(NSString *)title {
    self.titleLbl.text = title;
}

#pragma mark - UI

- (void)setupTitleLbl {
    UILabel *titleLbl = [[UILabel alloc] init];
    titleLbl.textColor = XYLiveTokenColor.desc;
    titleLbl.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
    [self addSubview:titleLbl];
    self.titleLbl = titleLbl;
}

@end
