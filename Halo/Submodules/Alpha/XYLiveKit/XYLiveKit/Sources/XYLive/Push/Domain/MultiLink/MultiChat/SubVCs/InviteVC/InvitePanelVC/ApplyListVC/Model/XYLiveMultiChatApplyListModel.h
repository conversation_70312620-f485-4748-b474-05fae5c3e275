//
//  XYLiveMultiChatApplyListModel.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <XYLiveFoundation/XYLiveFoundation.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkConsts.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkBizConsts.h>
#import <XYLiveFoundation/XYLiveFoundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiChatApplyListItem : XYLiveCodableModel

// 用户id
@property (nonatomic, copy)   NSString *userId;
// 昵称
@property (nonatomic, copy)   NSString *nickName;
// 头像
@property (nonatomic, copy)   NSString *avatarURL;
// 关注关系
@property (nonatomic, assign) XYLiveFollowStatus followStatus;
// 媒体类型
@property (nonatomic, assign) XYLiveMultiLinkMediaType mediaType;
// 送金币
@property (nonatomic, assign) NSInteger sendCoins;
// 申请时间
@property (nonatomic, copy)   NSString *applyTime;
// 描述文案
@property (nonatomic, copy)   NSArray *labels;
// 申请原因
@property (nonatomic, copy)   NSString *applyReason;
// 是否有麦位
@property (nonatomic, assign) BOOL hasTargetSeat;
// 麦位索引
@property (nonatomic, copy)   NSString *seatNumber;
// 麦位名称
@property (nonatomic, copy)   NSString *seatName;
// 会话id
@property (nonatomic, copy)   NSString *sessionId;
// 粉丝团
@property (nonatomic, strong) XYLiveUserFansClubInfo *fansClubInfo;

// 用户信息
@property (nonatomic, strong, readonly) XYLiveUserInfo *userInfo;
// 是否曝光过
@property (nonatomic, assign) BOOL hasShow;
// 是否可交互
@property (nonatomic, assign) BOOL disable;

@end

@interface XYLiveMultiChatApplyListSortItem : NSObject

// 排序类型
@property (nonatomic, copy) NSString *type;
// 排序文案
@property (nonatomic, copy) NSString *desc;
// 是否被选中
@property (nonatomic, assign, getter = isSelected) BOOL selected;

@end

@interface XYLiveMultiChatApplyListModel : XYLiveCodableModel

/// 申请列表
@property (nonatomic, copy) NSArray<XYLiveMultiChatApplyListItem *> *applyList;

@end

NS_ASSUME_NONNULL_END
