//
//  XYLiveMultiLineSearchVM.h
//  XYLiveKit
//
//  Created by 大远 on 2025/6/3.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineInviteServiceProtocol.h>
@class XYLiveLinkHostInviteeInfo;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiLineSearchVM : NSObject

// 数据源
@property (nonatomic, copy, readonly) NSArray<XYLiveLinkHostInviteeInfo *> *dataSource;

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkSevice;

// 发起搜索请求
- (void)requestSearchResultWithKey:(NSString *)key completion:(void(^)(NSError *error))completion;

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo;

@end

NS_ASSUME_NONNULL_END
