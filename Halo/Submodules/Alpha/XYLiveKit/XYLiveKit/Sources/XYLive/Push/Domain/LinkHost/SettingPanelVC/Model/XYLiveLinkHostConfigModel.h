//
//  XYLiveLinkHostConfigModel.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/27.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYLiveFoundation/XYLiveFoundation.h>
#import <XYLiveKit/XYLiveLinkHostConsts.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveLinkHostConfigModel : XYLiveCodableModel

// 连线布局方式
@property (nonatomic, assign) XYLiveMultiLinkLayoutType layoutType;
// 连线开关
@property (nonatomic, assign) BOOL lineSwitch;
// 连线限制范围
@property (nonatomic, assign) XYLiveMultiLineLimitType lineLimitType;
// PK开关
@property (nonatomic, assign) BOOL pkSwitch;
// PK限制范围
@property (nonatomic, assign) XYLiveMultiPKLimitType pkLimitType;

@end

NS_ASSUME_NONNULL_END
