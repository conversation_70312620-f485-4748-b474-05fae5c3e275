//
//  XYLivePushMultiChatOperateVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/2/20.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatOperateVC.h"
#import <XYLiveCore/XYLiveCore-Swift.h>
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYSessionManager/XYSessionManager.h>
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLiveKit/XYLiveActionSheetViewController.h>
#import <XYLiveKit/XYLivePushMultiLinkBizTracker.h>
#import <XYLiveKit/XYLiveMultiLinkAlertVC.h>
#import <XYLiveKit/XYLiveMultiLinkSwitchConfig.h>
@import I18N;
@import RedI18N;

@interface XYLivePushMultiChatOperateVC()

@property (nonatomic, weak) UIViewController *containerVC;
@property (nonatomic, weak) XYLivePushMultiChatCoreModel *coreModel;
@property (nonatomic, weak) id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak) id<XYLiveMultiLinkPushServiceProtocol> linkService;

@end

@implementation XYLivePushMultiChatOperateVC

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                          coreModel:(XYLivePushMultiChatCoreModel *)coreModel
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                        linkService:(id<XYLiveMultiLinkPushServiceProtocol>)linkService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _coreModel = coreModel;
        _liveInfoService = liveInfoService;
        _linkService = linkService;
    }
    return self;
}

/// 展示操作面板
- (void)showOperatePanelWithUserInfo:(XYLiveOnmicUserModel *)userInfo {
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"--showOperatePanel--{userId:%@, isAudio:%d, isVideo:%d, isLinking:%d, isHost:%d, isMultiLayout:%d}", userInfo.userId, userInfo.isAudioOn, userInfo.isVideoOn, self.linkService.isLinking, userInfo.isHost, self.linkService.isMultiLayout]];

    /**
     游戏直播间 (支持断开连麦、开闭麦克风、查看资料)
     1、操作自己：断开连麦、开闭麦克风
     2、操作其他人：断开连麦、开闭麦克风、查看资料
     
     视频直播间（支持断开连麦、开闭麦克风、开闭摄像头、翻转摄像头、切换布局、切换大小画面、查看资料）
     1、操作自己：断开连麦、开闭麦克风、开闭摄像头、翻转摄像头
     2、操作其他人：断开全部连麦、开闭麦克风、切换大小画面
     */
    
    WS
    // 断开连麦
    NSString *stopItemTitle = userInfo.isLocalUser ? @"断开全部连线"  : NSLocalizedString(L.live.live_stop_link, @"断开连线");
    XYLivePopupItem *stopItem = XYLivePopupItemMake(stopItemTitle, XYLivePopupItemTypeHighlight, ^(NSInteger index) {
        SS
        if (!userInfo.isLocalUser) {
            [self.linkService leaveMicWithUserInfo:userInfo];
        } else {
            NSString *title = userInfo.isLocalUser ? @"断开全部连线" : @"确认断开连线";
            NSString *subTitle = userInfo.isLocalUser ? @"断开后所有观众将会下麦，连线结束" : @"断开后该观众将被移出连线";
            XYLiveMultiLinkAlertVC *alertVC = [[XYLiveMultiLinkAlertVC alloc] initWithTitle:title msg:subTitle];
            alertVC.didTapConfirmHandler = ^{
                SS
                [self.linkService leaveMicWithUserInfo:userInfo];
            };
            alertVC.didTapConfirmItemAutoTrackHandler = ^XYTrackerEventContext * _Nonnull{
                if (userInfo.isLocalUser) {
                    return [XYLivePushMultiLinkBizTracker eventActionId32870];
                }
                return nil;
            };
            [alertVC showWithAnimated:YES onVC:self.containerVC complete:nil];
        }
    });
    
    // 打开/关闭麦克风
    NSString *micItemTitle = userInfo.isAudioOn ? NSLocalizedString(L.live.live_close_mic, @"关闭麦克风") : NSLocalizedString(L.live.live_open_mic, @"打开麦克风");
    XYLivePopupItem *micItem = XYLivePopupItemMake(micItemTitle, XYLivePopupItemTypeNormal, ^(NSInteger index) {
        SS
        [self.linkService updateMicStatusWithUserInfo:userInfo isOpened:!userInfo.isAudioOn];
    });
    
    // 合法性校验
    BOOL isGrid = self.linkService.isMultiLayout;
    if ([XYLiveMultiLinkSwitchConfig enableRTCFixLayoutTypeException]) {
        if (isGrid && self.coreModel.config.layoutType != XYLiveMultiLinkLayoutTypeGrid) {
            isGrid = NO;
        }
    }
    NSString *layoutItemTitle = isGrid ? @"切换小窗布局" : @"切换宫格布局";
    XYLivePopupItem *layoutItem = XYLivePopupItemMake(layoutItemTitle, XYLivePopupItemTypeNormal, ^(NSInteger index) {
        SS
        XYLiveMultiLinkLayoutType layoutType = isGrid ? XYLiveMultiLinkLayoutTypeFloat : XYLiveMultiLinkLayoutTypeGrid;
        [self.coreModel updateLayoutType:layoutType completion:^(NSError * _Nonnull error) {
            if (error == nil) {
                [XYAlertCenter live_showTextItemWithText:@"布局切换成功"];
            } else {
                [XYAlertCenter live_showTextItemWithError:error];
            }
        }];
    });
    
    // 翻转摄像头
    XYLivePopupItem *flipCameraItem = XYLivePopupItemMake(NSLocalizedString(L.live.live_flip_camera, @"翻转摄像头"), XYLivePopupItemTypeNormal, ^(NSInteger index) {
        SS
        [self.linkService switchCameraFlipWithUserInfo:userInfo];
    });
    
    // 放大/缩小画面
    NSString *scaleItemTitle = userInfo.zoomIn ? NSLocalizedString(L.live.live_zoom_out_pic, @"缩小画面") : NSLocalizedString(L.live.live_zoom_in_pic, @"放大画面");
    XYLivePopupItem *scaleItem = XYLivePopupItemMake(scaleItemTitle, XYLivePopupItemTypeNormal, ^(NSInteger index) {
        SS
        [self.linkService updateScaleStatusWithUserInfo:userInfo isZoomIn:!userInfo.zoomIn];
    });
    
    // 查看资料
    XYLivePopupItem *openUserCardItem = XYLivePopupItemMake(NSLocalizedString(L.live.live_open_profile, @"查看资料"), XYLivePopupItemTypeNormal, ^(NSInteger index) {
        SS
        [self.linkService showUserCardWithUserInfo:userInfo];
    });
    
    NSMutableArray *itemM = [NSMutableArray array];
    // 游戏直播间
    if (XYLiveManager.sharedManager.isScreenLive) {
        itemM = @[stopItem, micItem].mutableCopy;
        // 操作其他人,则展示个人资料
        if (!userInfo.isLocalUser) {
            [itemM addObject:openUserCardItem];
        }
    } else {
        // 视频直播间
        // 操作自己，则展示断开连麦、开闭麦克风、开闭摄像头、翻转摄像头
        if (userInfo.isLocalUser) {
            // 处于大小窗模式下 & 大小窗布局
            if (userInfo.isZoomOutHost && !self.linkService.isMultiLayout) {
                itemM = @[stopItem, layoutItem, scaleItem, micItem].mutableCopy;
            } else {
                itemM = @[stopItem, layoutItem, micItem].mutableCopy;
            }
            // 判断是否开启摄像头，开启则支持翻转摄像头
            if (userInfo.isVideoOn) {
                [itemM addObject:flipCameraItem];
            }
        } else {
            // 操作其他人，则展示断开全部连麦、切换大小画面、开闭麦克风、个人资料
            if (!userInfo.isVideoOn || self.linkService.isMultiLayout) {
                itemM = @[stopItem, micItem, openUserCardItem].mutableCopy;
            } else {
                itemM = @[stopItem, scaleItem, micItem, openUserCardItem].mutableCopy;
            }
        }
    }
    XYLiveActionSheetViewController *sheetVC = [[XYLiveActionSheetViewController alloc] initWithTitle:nil items:itemM.copy];
    sheetVC.didTapItemAutoTrackHandler = ^XYTrackerEventContext * _Nonnull(XYLivePopupItem * _Nonnull item, NSUInteger index) {
        // 主播自己操作自己
        if (userInfo.isLocalUser) {
            return [XYLivePushMultiLinkBizTracker eventActionId32864WithCtName:item.title];
        }
        return [XYLivePushMultiLinkBizTracker eventActionId32949WithCtName:item.title];
    };
    [self.containerVC showPopBottomVC:sheetVC];
    // 面板曝光埋点上报
    if  (userInfo.isLocalUser) {
        [XYLivePushMultiLinkBizTracker eventActionId32861];
    } else {
        [XYLivePushMultiLinkBizTracker eventActionId32856];
    }
}

@end
