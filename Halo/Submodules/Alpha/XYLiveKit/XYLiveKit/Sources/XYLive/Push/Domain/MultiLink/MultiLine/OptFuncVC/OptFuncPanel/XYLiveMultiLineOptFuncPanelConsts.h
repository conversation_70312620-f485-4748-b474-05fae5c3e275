//
//  XYLiveMultiLineOptFuncPanelConsts.h
//  XYLiveKit
//
//  Created by 大远 on 2025/6/7.
//  Copyright © 2025 XingIn. All rights reserved.
//
#import <XYLiveUIKit/XYLiveUIKit.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineConsts.h>

#ifndef XYLiveMultiLineOptFuncPanelConsts_h
#define XYLiveMultiLineOptFuncPanelConsts_h

// 操作面板高度
#define kXYLiveMultiLineOptFuncPanelH           DeviceUtility.screenHeight * 0.5

// 玩法操作类型
typedef NS_ENUM(NSUInteger, XYLiveMultiLineBizOptType) {
    XYLiveMultiLineBizOptTypeOpen,   // 开启
    XYLiveMultiLineBizOptTypeClose,  // 关闭
    XYLiveMultiLineBizOptTypeReOpen, // 重开
};

#endif /* XYLiveMultiLineOptFuncPanelConsts_h */
