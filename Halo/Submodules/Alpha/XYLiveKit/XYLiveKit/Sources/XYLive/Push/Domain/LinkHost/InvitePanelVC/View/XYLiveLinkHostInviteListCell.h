//
//  XYLiveLinkHostInviteListCell.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/27.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <XYLiveKit/XYLiveLinkHostInviteListModel.h>
#import <XYLiveKit/XYLiveMutiLinkEmptyView.h>
#import <XYLiveUIKit/XYLivePaddingLabel.h>
#import <XYLiveKit/XYLiveLinkHostInviteListSubTitleView.h>

NS_ASSUME_NONNULL_BEGIN

// 基类
@interface XYLiveLinkHostInviteListCell : UITableViewCell

// 列表项
@property (nonatomic, strong) id listItem;
// 索引
@property (nonatomic, strong) NSIndexPath *indexPath;

// 创建子视图
- (void)setupSubviews;

/// 更新数据
- (void)bindListItems:(NSArray *)listItems indexPath:(NSIndexPath *)indexPath;

@end

// 纯文本
@interface XYLiveLinkHostInviteListTextCell : XYLiveLinkHostInviteListCell

// 标题
@property (nonatomic, strong) UILabel *titleLabel;

@end

// 空占位视图
@interface XYLiveLinkHostInviteListEmptyCell : XYLiveLinkHostInviteListCell

@property (nonatomic, strong) XYLiveMutiLinkEmptyView *emptyView; // 空视图

@end

// 成员
@interface XYLiveLinkHostInviteListItemCell : XYLiveLinkHostInviteListCell

// 头像
@property (nonatomic, strong) UIImageView *avatarImgView;
// 昵称
@property (nonatomic, strong) UILabel *nickNameLabel;
// 标签
@property (nonatomic, strong) XYLivePaddingLabel *relationLabel;
// 副标题
@property (nonatomic, strong) XYLiveLinkHostInviteListSubTitleView *subTitleView;
// 邀请
@property (nonatomic, strong) UIButton *inviteBtn;

// 点击邀请事件回调
@property (nonatomic, copy) void(^didTapInviteHandler)(XYLiveLinkHostInviteeInfo *inviteeInfo, NSIndexPath *indexPath);

@end

NS_ASSUME_NONNULL_END
