//
//  XYLiveLinkHostInviteListCheckBoxView.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/28.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostInviteListCheckBoxView.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveFoundation/UIImage+XYLiveKit.h>
#import <XYLiveFoundation/XYLiveFoundation-Swift.h>
#import <XYWebImage/UIImageView+XYWebImage.h>
#import <XYUITheme/XYThemeManager.h>
#import <Masonry/Masonry.h>

@interface XYLiveLinkHostInviteListCheckBoxView()

// 复选框视图
@property (nonatomic, strong) UIImageView *radioImgView;
// 标题
@property (nonatomic, strong) UILabel *titleLabel;
// 玩法icon
@property (nonatomic, strong) UIImageView *iconImgView;

@end

@implementation XYLiveLinkHostInviteListCheckBoxView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
        // 添加点击手势
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(didTap:)];
        [self addGestureRecognizer:tapGesture];
    }
    return self;
}

- (void)setChecked:(BOOL)checked {
    _checked = checked;
    if (checked) {
        [self.radioImgView xylive_setImageWithURLWithLight:@"https://fe-platform.xhscdn.com/platform/104101l031jd0n9272u4mcrss5gt00000000000lc72t0m" dark:@"https://fe-platform.xhscdn.com/platform/104101l031jd0n9143206crss5gt00000000000h32ic8k"];
        self.backgroundColor = [XYLiveTokenColor primary2];
        self.titleLabel.textColor = [XYLiveTokenColor primary];
    } else {
        [self.radioImgView xylive_setImageWithURLWithLight:@"https://fe-platform.xhscdn.com/platform/104101l031jd0n94f2u06crss5gt00000000000hrkv5gk" dark:@"https://fe-platform.xhscdn.com/platform/104101l031jd0n93biu06crss5gt00000000000ge76k5s"];
        self.backgroundColor = [XYLiveTokenColor fill1];
        self.titleLabel.textColor = [XYLiveTokenColor title];
    }
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 创建复选框视图
    [self setupRadioImgView];
    // 创建标题
    [self setupTitleLabel];
    // 创建玩法icon
    [self setupIconImgView];
    
    // 布局
    [self.radioImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(10);
        make.centerY.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(14, 14));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.radioImgView.mas_right).offset(4);
        make.centerY.equalTo(self);
    }];
    
    [self.iconImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel.mas_right).offset(4);
        make.right.mas_equalTo(-12);
        make.size.mas_equalTo(CGSizeMake(23, 23));
        make.centerY.equalTo(self);
    }];
}

- (void)setupRadioImgView {
    UIImageView *radioImgView = [[UIImageView alloc] init];
    radioImgView.userInteractionEnabled = YES;
    [self addSubview:radioImgView];
    self.radioImgView = radioImgView;
}

- (void)setupTitleLabel {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:12];
    titleLabel.textColor = [XYLiveTokenColor title];
    titleLabel.text = @"和TA进行PK";
    titleLabel.userInteractionEnabled = YES;
    [self addSubview:titleLabel];
    self.titleLabel = titleLabel;
}

- (void)setupIconImgView {
    UIImageView *iconImgView = [[UIImageView alloc] init];
    iconImgView.contentMode = UIViewContentModeScaleAspectFit;
    iconImgView.userInteractionEnabled = YES;
    [iconImgView xy_setImageWithURL:[NSURL URLWithString:@"https://fe-platform.xhscdn.com/platform/104101l031ith9l4a1g06crss5gt00000000000lon23dq"]];
    [self addSubview:iconImgView];
    self.iconImgView = iconImgView;
}

#pragma mark - Event

- (void)didTap:(UITapGestureRecognizer *)tapGesture {
    self.checked = !self.isChecked;
    if (self.didTapHandler) {
        self.didTapHandler(self.isChecked);
    }
}
@end
