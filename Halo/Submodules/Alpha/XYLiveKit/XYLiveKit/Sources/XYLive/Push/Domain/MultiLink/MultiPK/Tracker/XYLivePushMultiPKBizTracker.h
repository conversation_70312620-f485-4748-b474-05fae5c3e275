//
//  XYLivePushMultiPKBizTracker.h
//  XYLiveKit
//
//  Created by 大远 on 2025/6/30.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYAnalytics/XYAnalytics.h>
@class XYTrackerEventContext;


NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiPKBizTracker : NSObject

/* 直播开播页/连线中-结束/点击 */
+ (void)eventActionId80517WithRoomId:(NSString *)roomId channelTabName:(NSString *)channelTabName hasGoods:(BOOL)hasGoods;

/* 直播开播页/匹配中-放弃匹配/点击 */
+ (void)eventActionId80519WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods;

/* 直播开播页/邀请连线-拒绝/点击 */
+ (void)eventActionId80520WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods;

/* 直播开播页/邀请连线-接受/点击 */
+ (void)eventActionId80521WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods;

/* 直播开播页/邀请连线面板/曝光 */
+ (void)eventActionId80529WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods;

@end

NS_ASSUME_NONNULL_END
