//
//  XYLiveMultiPKContributorView.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/14.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiPKContributorView.h"
#import <XYLiveKit/XYLiveMultiPKAvatarView.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYLiveFoundation/UIImage+XYLiveKit.h>
#import <Masonry/Masonry.h>

// 列表数量
static NSInteger kXYLiveMultiPKContributorListNum = 3;
// 头像宽高
static CGFloat kXYLiveMultiPKContributorListItemWH = 32;

@interface XYLiveMultiPKContributorView()

@property (nonatomic, strong) UIView *leftView;
@property (nonatomic, strong) UIView *rightView;
@property (nonatomic, strong) XYLiveMultiPKTeamInfo *leftTeamInfo;
@property (nonatomic, strong) XYLiveMultiPKTeamInfo *rightTeamInfo;

@end

@implementation XYLiveMultiPKContributorView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

// 更新列表数据
- (void)updateLeftTeamInfo:(XYLiveMultiPKTeamInfo *)leftTeamInfo rightTeamInfo:(XYLiveMultiPKTeamInfo *)rightTeamInfo {
    // 更新左侧战队信息
    [self updateListItemsWithTeamInfo:leftTeamInfo isLeft:YES];
    // 更新右侧战队信息
    [self updateListItemsWithTeamInfo:rightTeamInfo isLeft:NO];
    // 更新缓存
    self.leftTeamInfo = leftTeamInfo;
    self.rightTeamInfo = rightTeamInfo;
}

// 更新状态
- (void)updateViewStatusWithResult:(XYLiveMultiPKResult)result {
    XYLiveMultiPKAvatarView *avatarView = nil;
    // 我方 / 对方获胜
    if (result == XYLiveMultiPKResultWin || result == XYLiveMultiPKResultLose) {
        avatarView = result == XYLiveMultiPKResultWin ? XYSAFE_CAST(self.leftView.subviews.lastObject, XYLiveMultiPKAvatarView) : XYSAFE_CAST(self.rightView.subviews.firstObject, XYLiveMultiPKAvatarView);
        [avatarView setText:@"MVP" borderColor:[UIColor colorWithRed:249.0 / 255.0 green:180.0 / 255.0 blue:39.0 / 255.0 alpha:1.0]];
    }
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 创建左侧容器视图
    [self setupLeftView];
    // 创建右侧容器视图
    [self setupRightView];
    
    // 布局
    [self.leftView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.left.equalTo(self);
    }];
    
    [self.rightView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.right.equalTo(self);
    }];
}

- (void)setupLeftView {
    UIView *leftView = [[UIView alloc] init];
    [self addSubview:leftView];
    self.leftView = leftView;
    // 创建子视图
    [self setupItemViewsWithCTView:leftView isLeft:YES];
}

- (void)setupRightView {
    UIView *rightView = [[UIView alloc] init];
    [self addSubview:rightView];
    self.rightView = rightView;
    // 创建子视图
    [self setupItemViewsWithCTView:rightView isLeft:NO];
}

#pragma mark - Private

- (void)setupItemViewsWithCTView:(UIView *)ctView isLeft:(BOOL)isLeft {
    for (int i = 0; i < kXYLiveMultiPKContributorListNum; i++) {
        XYLiveMultiPKAvatarView *avatarView = isLeft ? [XYLiveMultiPKLeftAvatarView new] : [XYLiveMultiPKRightAvatarView new];
        [avatarView setUserInfo:nil placeholder:[UIImage xy_liveKitBundleImage:@"xyLivePKAvatarPlaceHolder"]];
        NSInteger index = i;
        if (isLeft) {
            index = kXYLiveMultiPKContributorListNum - i - 1;
        }
        UIColor *borderColor = isLeft ? [UIColor colorWithRed:255.0 / 255.0 green:36.0 / 255.0 blue:66.0 / 255.0 alpha:1.0] : [UIColor colorWithRed:80.0 / 255.0 green:125.0 / 255.0 blue:236.0 / 255.0 alpha:1.0];
        [avatarView setText:[NSString stringWithFormat:@"%@",@(index + 1)] borderColor:borderColor];
        WS
        avatarView.didTapHandler = ^(XYLiveUserInfo * _Nonnull userInfo) {
            SS
            self.didTapUserHandler ? self.didTapUserHandler(userInfo) : nil;
        };
        [ctView addSubview:avatarView];
    }
    
    // 布局
    [ctView.subviews mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self.leftView);
        make.size.mas_equalTo(CGSizeMake(kXYLiveMultiPKContributorListItemWH, kXYLiveMultiPKContributorListItemWH));
    }];
    [ctView.subviews mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:8 leadSpacing:0 tailSpacing:0];
}

- (void)updateListItemsWithTeamInfo:(XYLiveMultiPKTeamInfo *)teamInfo isLeft:(BOOL)isLeft {
    // 合法性校验
    [teamInfo.contributorList enumerateObjectsUsingBlock:^(XYLiveMultiPKContributorUserInfo * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        // 合法性校验
        if (idx < kXYLiveMultiPKContributorListNum) {
            NSInteger index = isLeft ? kXYLiveMultiPKContributorListNum - idx - 1 : idx;
            XYLiveMultiPKAvatarView *itemView = isLeft ? self.leftView.subviews[index] : self.rightView.subviews[index];
            [itemView setUserInfo:obj.userInfo placeholder:[UIImage xy_liveKitBundleImage:@"xyLivePKAvatarPlaceHolder"]];
        }
    }];
}

@end
