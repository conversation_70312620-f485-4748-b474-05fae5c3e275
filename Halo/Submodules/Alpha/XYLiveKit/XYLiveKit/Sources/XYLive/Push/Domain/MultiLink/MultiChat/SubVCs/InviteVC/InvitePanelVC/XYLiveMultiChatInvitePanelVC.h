//
//  XYLiveMultiChatInvitePanelVC.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/22.
//  Copyright © 2024 XingIn. All rights reserved.
//
#import <XYLiveKit/XYLiveNavigationChildController.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveKit/XYLivePushMultiChatConsts.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiChatInvitePanelVC : XYLiveBaseChildViewController<XYLiveNavigationChildControllerProtocol>

// 点击设置
@property (nonatomic, copy) void(^didTapSettingHandler)(void);
// 调整索引位置
@property (nonatomic, copy) NSInteger(^moveToIndexHandler)(void);
// 展示个人资料卡
@property (nonatomic, copy) void(^showUserCardHandler)(XYLiveUserInfo *userInfo);
// 是否挂车
@property (nonatomic, copy) BOOL(^hasGoodsHandler)(void);
// 直播间基础信息
@property (nonatomic, copy) id<XYLiveInfoServiceProtocol>(^liveInfoServiceHandler)(void);
// 多人互动框架服务
@property (nonatomic, copy) id<XYLiveMultiLinkServiceProtocol>(^multiLinkServiceHandler)(void);


// 初始化
- (instancetype)initWithBizType:(XYLiveMultiLinkBizType)bizType
                         source:(XYLiveMultiLinkInviteSource)source
                      extraInfo:(NSString * _Nullable)extraInfo;

/// 切换至指定索引位置
- (void)moveToIndex:(NSInteger)index animated:(BOOL)animated;

@end

NS_ASSUME_NONNULL_END
