//
//  XYLiveMultiPKInviteVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/26.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiPKInviteVC.h"
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLiveMultiLinkOnInvitePanelVC.h>
#import <XYLiveServiceProtocol/XYLiveMultiPKConsts.h>
#import <XYLiveCore/XYLiveCore-Swift.h>
#import <XYLiveKit/XYLiveLinkHostInviteeInfo.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYLiveKit/XYLiveMultiPKInviteVM.h>
#import <XYLiveKit/XYLivePushMultiPKBizTracker.h>

@interface XYLiveMultiPKInviteVC()<XYLiveMultiPKInviteDelegate>

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, weak)   XYLiveMultiLinkOnInvitePanelVC *onInviteVC;
@property (nonatomic, strong) NSHashTable<id<XYLiveMultiPKInviteListener>> *listeners;
@property (nonatomic, strong) XYLiveMultiPKInviteVM *viewModel;

@end

@implementation XYLiveMultiPKInviteVC

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    // 注册监听
    [self.multiLinkService registerListener:self.viewModel];
}

// 发起邀请
- (void)inviteWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo source:(XYLiveMultiPKInviteSource)source extraInfo:(NSString *_Nullable)extraInfo {
    [self.viewModel inviteWithInviteeInfo:inviteeInfo source:source extraInfo:extraInfo];
}

// 取消邀请
- (void)cancelInviteWithUserId:(NSString *)userId {
    [self.viewModel cancelInviteWithUserId:userId];
}

// 是否处于邀请中
- (BOOL)isInvitingWithUserId:(NSString *)userId {
    return [self.viewModel isInvitingWithUserId:userId];
}

// 注册监听
- (void)registerListener:(id<XYLiveMultiPKInviteListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners addObject:listener];
}

// 取消注册监听
- (void)unregisterListener:(id<XYLiveMultiPKInviteListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners removeObject:listener];
}

#pragma mark - XYLiveMultiPKInviteDelegate

- (void)onUpdateInviteState:(XYLiveMultiLinkInviteState)inviteState inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    // 事件分发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLiveMultiPKInviteListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onPKUpdateInviteState:inviteeInfo:)]) {
            [obj onPKUpdateInviteState:inviteState inviteeInfo:inviteeInfo];
        }
    }];
}

- (void)onReceiveInviteWithBizType:(XYLiveMultiLinkBizType)bizType inviteInfo:(XYLiveMultiLinkInviteInfo *)inviteInfo {
    // 展示受邀弹窗
    XYLiveMultiLinkOnInvitePanelVC *panelVC = [[XYLiveMultiLinkOnInvitePanelVC alloc] initWithBizType:bizType inviteInfo:inviteInfo];
    [panelVC updateRejectItemTitle:[NSString stringWithFormat:@"拒绝（%@s）", @(inviteInfo.interval)]];
    NSString *targetUserId = inviteInfo.fromUserInfo.userID;
    NSString *targetRoomId = [NSString stringWithFormat:@"%@", @(inviteInfo.fromUserInfo.roomID)];
    WS
    panelVC.didTapRejectHandler = ^{
        SS
        // 埋点上报
        [XYLivePushMultiPKBizTracker eventActionId80520WithRoomId:self.liveInfoService.roomId channelTabType:@"2" hasGoods:self.liveInfoService.roomInfo.hasGoods];
        // 发起请求
        [self.viewModel rejectInviteWithBizType:bizType targetUserId:targetUserId targetRoomId:targetRoomId];
    };
    panelVC.didTapAcceptHandler = ^{
        SS
        // 埋点上报
        [XYLivePushMultiPKBizTracker eventActionId80521WithRoomId:self.liveInfoService.roomId channelTabType:@"2" hasGoods:self.liveInfoService.roomInfo.hasGoods];
        // 发起请求
        [self.viewModel acceptInviteWithBizType:bizType targetUserId:targetUserId targetRoomId:targetRoomId];
    };
    [panelVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.onInviteVC = panelVC;
    // 埋点上报
    [XYLivePushMultiPKBizTracker eventActionId80529WithRoomId:self.liveInfoService.roomId channelTabType:@"2" hasGoods:self.liveInfoService.roomInfo.hasGoods];
}

- (void)onTimerTrick:(NSInteger)remainInterval totalInterval:(NSInteger)totalInterval {
    // 更新倒计时
    [self.onInviteVC updateRejectItemTitle:[NSString stringWithFormat:@"拒绝（%@s）", @(remainInterval)]];
}

- (void)onTimerFinish {
    // 关闭面板
    [self.onInviteVC dismissWithAnimated:YES complete:nil];
}

#pragma mark - Lazy

- (NSHashTable<id<XYLiveMultiPKInviteListener>> *)listeners {
    if (_listeners == nil) {
        _listeners = [NSHashTable weakObjectsHashTable];
    }
    return _listeners;
}

- (XYLiveMultiPKInviteVM *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[XYLiveMultiPKInviteVM alloc] initWithLiveInfoService:self.liveInfoService multiLinkService:self.multiLinkService];
        _viewModel.delegate = self;
    }
    return _viewModel;
}

@end
