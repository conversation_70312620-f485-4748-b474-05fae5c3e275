//
//  XYLivePushPlanAViewController.h
//  XYPostKit
//
//  Created by <PERSON><PERSON><PERSON> on 2019/9/9.
//

@import XYLivePusher;
// @import XYDynamicUI;

#import <XYLiveKit/XYIMComm.h>
#import <XYLiveKit/XYLiveManager.h>
#import <XYLiveKit/XYLiveAuthorityConfig.h>
#import <XYLiveKit/XYIMModel.h>
#import <XYLiveKit/XYLiveLinkmicModel.h>
#import <XYLiveKit/XYLivePKModel.h>
#import <XYLiveKit/XYLivePushDecorateViewModel.h>
#import <XYLiveKit/XYLiveBreakOffAlertView.h>
#import <XYLiveKit/XYLivePushDecorateView.h>
#import <XYLiveKit/XYLiveWebViewController.h>
#import <XYLiveFoundation/XYLiveUserViewController.h>

extern NSInteger pushAudioCheckInterval;

NS_ASSUME_NONNULL_BEGIN

@class XYLiveRoomInfo;
@class XYLiveLoginParam;
@class XYLivePusher;
@class RTCPusher;
@class XYLiveEffectView;
@class XYLiveCustomAlertView;
@class LiveStreamInfo;
@class IMRandomLinkmicResponse;
@class XYLiveFrameExtractionIndexModel;
@class XYLiveGiftDomain;
@class MultiLinkmicCommService;
@class XYLiveFeelBorderDomain;
@class XYLiveEffectDomain;
@class XYLiveDomainBuilder;
@class XYLiveTaskRunLoopScheduler;

@interface XYLivePushRoomViewController : XYLiveUserViewController

/**
 路由跳转方法
 */
- (instancetype)initWithRoomInfo:(XYLiveRoomInfo * _Nullable)roomInfo
                      loginParam:(XYLiveLoginParam * _Nullable)loginParam
                          pusher:(XYLiveRtmpPusher * _Nullable)pusher
                       bizPushStyle:(XYLiveRoomBizPushStyle)bizPushStyle
                      screenshot:(UIImageView * _Nullable)screenshotView;

- (instancetype)initWithRoomInfo:(XYLiveRoomInfo * _Nullable)roomInfo
                      loginParam:(XYLiveLoginParam * _Nullable)loginParam
                          kasaPusher:(XYLiveKasaPusher * _Nullable)kasaPusher
                       bizPushStyle:(XYLiveRoomBizPushStyle)bizPushStyle
                      screenshot:(UIImageView * _Nullable)screenshotView;

- (instancetype)initWithRoomInfo:(XYLiveRoomInfo * _Nullable)roomInfo
                      loginParam:(XYLiveLoginParam * _Nullable)loginParam
                       session:(RTCChannelSession * _Nullable)rtcSession
                       bizPushStyle:(XYLiveRoomBizPushStyle)bizPushStyle
                      screenshot:(UIImageView * _Nullable)screenshotView;

- (instancetype)initWithRoomInfo:(XYLiveRoomInfo * _Nullable)roomInfo
                      loginParam:(XYLiveLoginParam * _Nullable)loginParam
                       liveSession:(RTCChannelSession * _Nullable)rtcSession
                       bizPushStyle:(XYLiveRoomBizPushStyle)bizPushStyle
                      screenshot:(UIImageView * _Nullable)screenshotView;

///used for +PK
@property (nonatomic, readonly, nonnull) XYLivePushDecorateViewModel *decorateVM;
@property (nonatomic, readonly, nonnull) XYLivePushDecorateView *decorateView;
@property (nonatomic, readonly, nonnull) UIView *renderContainerView;

@property (nonatomic, readonly, nullable) XYLiveCustomAlertView *screenCaptureView;
@property (nonatomic, strong, nullable) XYLiveFrameExtractionIndexModel *frameModel;
///used for +Banner
@property (nonatomic, readonly, nullable) XYLiveRoomInfo *roomInfo;
@property (nonatomic, assign) BOOL hotSwitchToRtc;
@property (nonatomic, assign) XYLiveIMLinkmicType linkType;
/// used for obs & paid course
@property (nonatomic, assign, readonly) BOOL shouldStopPush;
@property (nonatomic, assign, readonly) BOOL shouldScreenPushing;
@property (nonatomic, assign) BOOL resumeCameraSetup;
@property (nonatomic, assign) BOOL hostFloatingEnter;

/// channelSession
@property (nonatomic, readonly, nullable) RTCChannelSession *channelSession;

@property (nonatomic, readonly, nullable) XYLiveGiftDomain *giftDomain;

@property (nonatomic, readonly, nullable) XYLiveEffectDomain *effectDomain;

@property (nonatomic, readonly, nullable) XYLiveFeelBorderDomain *feelBorderDomain;

@property (nonatomic, strong, readonly, nullable) XYLiveDomainBuilder *domainBuilder;

- (void)setupPusherDelegate;
- (BOOL)isVisible;

- (void)dismissPresentingCardView;

/// 创建新版 domainBuilder
- (void)createDomainBuilderIfNeed;

@end

@interface XYLivePushRoomViewController (Battle)

- (void)battle_setupKVO;

@end

@interface XYLivePushRoomViewController (Linkmic)

- (void)link_reset;
- (void)link_setupKvo;
- (void)adjustToShowRightBottomCard;
- (void)adjustToShowRightBottomCardForMultiLink:(BOOL)hasLink;
// 校验麦上用户开启视频需要恢复采集preView
- (void)checkLinkCameraPreview;

@end

// MARK: 不要新增方法
@interface XYLivePushRoomViewController (PK)

- (void)setupPKVCBlock;
- (void)pk_handleRandomResponse:(IMRandomLinkmicResponse *)response;

@end

@interface XYLivePushRoomViewController (BottomSection)

- (void)setupKVOForBottomView;
- (void)showMorePanelWithGuide:(NSString * _Nullable)guide;
- (void)showInteractPanelWithGuide:(NSString * _Nullable)guide;
- (void)showRoomSettingPanelWithGuide:(NSString * _Nullable)guide;

@end

@interface XYLivePushRoomViewController (Panel)

- (void)setupMorePanelBlock;
- (void)setupSettingPanelBlock;

@end

@interface XYLivePushRoomViewController (PlayerDelegate)

- (void)livePlayer_startHostPlayWithStreamInfo:(LiveStreamInfo * _Nullable)streamInfo;
- (void)livePlayer_stopHostPlay;
- (BOOL)livePlayer_judgeShouldSupportPlayer;

@end

@interface XYLivePushRoomViewController (Pusher)

- (void)setupPusher;
- (void)handlePusherMirrorStats;
- (void)startPush:(XYLiveRoomInfo *)roomInfo loginParam:(XYLiveLoginParam *)loginParam;
- (void)checkToClearRTCMixTranscodingWithRoomInfo:(XYLiveRoomInfo *)roomInfo;
- (void)updateApmRtmpParam;
- (void)updateApmKasaParam;
- (void)clearPreviewLogger;
- (void)pausePush;
- (void)resumePush;
- (BOOL)shouldStopPush;
- (BOOL)shouldScreenPushing;
- (BOOL)checkPushUrlRoomID;
- (void)sendSubmitPushType:(bool)onlyCodec;
- (XYLivePushType)fetchPushType;
- (NSString *)fetchSubmitPushInfo:(bool)onlyCodec;
- (NSString *)getSubmitPushInfo:(XYLivePushType)currentPushType onlyCodec:(bool)onlyCodec;

@end

@interface XYLivePushRoomViewController (PusherDelegate)

@end

@interface XYLivePushRoomViewController (RTCPusher)

- (void)updateApmV2BasicParams;

- (void)rtcPusher_setupBlock;

- (void)rtcPusher_onEnterBackground;
- (void)rtcPusher_onBackForeground;

- (void)rtcPusher_setupKvo;

- (void)rtcPusher_addRtcCoreService;

@end

@interface XYLivePushRoomViewController (HotSwitch)

- (void)setupSwitchPusherIM;

- (void)deltaUpdatePusherInfo:(NSDictionary *)param;
- (void)updateFaceVInfo:(NSDictionary *)param;

- (void)hotSwitchToRtcPusher;
- (void)kasaHotSwitchToTrtc;
- (BOOL)canApplyRTC;
- (BOOL)canApplyKasa;
- (BOOL)forceRTC;

// 畅聊直接加载RTCCore
- (void)loadRtcCoreWithRTCSession:(RTCChannelSession *)session;

// 切换到rtcCore
- (void)switchPusherToRtcCoreIfNeeded;

// 切换到指定推流器的rtcCore
- (void)switchPusherToRtcCoreIfNeeded:(RTCVendorType)type;

// 切换到rtcPusher
- (void)switchPusherToRtcPusherIfNeeded;

// 创建coreSession
- (RTCCoreChannelSession *)buildRtcChannelSession: (RTCChannelSession *)session;

// 利用encodeInfo创建coreSession
- (RTCCoreChannelSession *)buildRtcChannelSession: (RTCVideoEncodeInfo *)encode vendorType:(RTCVendorType)type;

@end

@interface XYLivePushRoomViewController (RtmpLink)

- (void)restoreSingleHostPusherAndResetPlayer;

@end

@interface XYLivePushRoomViewController (ModuleRouter)

- (void)moduleRouter_registry;
- (void)moduleRouter_triggerIntoPushRoom;

@end

@interface XYLivePushRoomViewController (Notification)

- (void)notification_registry;
- (void)notification_unregistry;

- (void)handleWebviewBroadcastNotification:(NSNotification *)notification;

@end

@interface XYLivePushRoomViewController (KvoRegistry)

- (void)kvoRegistry_setup;

@end

@interface XYLivePushRoomViewController (ActionLink)

- (void)setupActionLinkRegister;
- (void)actionlink_setupKVO;

@end

@interface XYLivePushRoomViewController (StreamImage)
- (void)configHostLeavingImage:(BOOL)isVoiceLive;
- (void)configInactiveImage:(UIImage * _Nullable)image;
@end

@interface XYLivePushRoomViewController (Lifecycle)
- (void)lifecycle_dealloc;
- (void)lifecycle_viewDidLoad;
- (void)lifecycle_viewDidAppear;
- (void)lifecycle_viewWillAppear;
- (void)lifecycle_viewWillDisappear;
- (void)lifecycle_viewDidDisappear;
@end

// MARK: 不要新增方法


@interface XYLivePushRoomViewController (Dismiss)
- (void)notifyAllDismissCardView;
- (void)dismissWithoutStopServer;
- (void)mutiLandDismissWithoutStopServer;
@end

@interface XYLivePushRoomViewController (Setup)
- (void)setupOnInitWithRoomInfo:(XYLiveRoomInfo * _Nullable)roomInfo loginParam:(XYLiveLoginParam * _Nullable)loginParam rtcSession:(RTCChannelSession * _Nullable)rtcSession;
- (void)refreshHostSetupInfo;
- (void)setupBaseService;
- (void)setupUI;
- (void)setup_BindViewAndVM;
- (void)setup_resetParameters;
- (void)setup_initialNetworking;
- (void)setup_analyticsDataSource;
- (void)showCountdownView:(void(^ _Nullable)(void))completion;
@end

NS_ASSUME_NONNULL_END

