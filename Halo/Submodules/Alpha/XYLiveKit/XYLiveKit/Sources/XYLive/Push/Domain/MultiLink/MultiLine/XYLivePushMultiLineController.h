//
//  XYLivePushMultiLineController.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/30.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveKit/XYLiveMultiLineInviteVC.h>
#import <XYLiveKit/XYLiveMultiLineMatchVC.h>
#import <XYLiveKit/XYLiveMultiLineCoreVC.h>
#import <XYLiveServiceProtocol/XYLivePushMultiLineServiceProtocol.h>
@protocol XYLivePushMultiLineDependProtocol;

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiLineController : XYViewController

// 邀请模块
@property (nonatomic, weak) XYLiveMultiLineInviteVC *inviteVC;
// 匹配模块
@property (nonatomic, weak) XYLiveMultiLineMatchVC *matchVC;
// 核心模块
@property (nonatomic, weak) XYLiveMultiLineCoreVC *coreVC;

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                             depend:(id<XYLivePushMultiLineDependProtocol>)depend;

// 展示操作面板
- (void)showOptFuncPanelWithSource:(XYLiveMultiLineOptFuncPanelSource)source;

// 注册监听
- (void)registerListener:(id<XYLivePushMultiLineListener>)listener;

// 取消注册监听
- (void)unregisterListener:(id<XYLivePushMultiLineListener>)listener;

@end

NS_ASSUME_NONNULL_END
