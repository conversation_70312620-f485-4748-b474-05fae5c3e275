//
//  XYLiveMultiLineOptFuncPanelVC.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/21.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveNavigationChildController.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineInviteServiceProtocol.h>
#import <XYLiveKit/XYLiveMultiLineOptFuncPanelConsts.h>
@class XYLiveLinkHostInviteeInfo;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiLineOptFuncPanelVC : XYLiveBaseChildViewController<XYLiveNavigationChildControllerProtocol>

// 点击添加
@property (nonatomic, copy) void(^didTapAddHandler)(void);
// 点击关闭
@property (nonatomic, copy) void(^didTapCloseHandler)(void);
// 点击用户
@property (nonatomic, copy) void(^didTapUserHandler)(XYLiveLinkHostInviteeInfo *inviteeInfo);
// 点击撤销
@property (nonatomic, copy) void(^didTapCancelHandler)(XYLiveLinkHostInviteeInfo *inviteeInfo);
// 点击操作按钮
@property (nonatomic, copy) void(^didTapOptHandler)(XYLiveMultiLinkBizType bizType, XYLiveMultiLineBizOptType optType);
// 点击设置
@property (nonatomic, copy) void(^didTapSettingHandler)(void);

// 初始化
- (instancetype)initWithContianerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService
                    inviteeInfoList:(NSArray<XYLiveLinkHostInviteeInfo *> * _Nullable)inviteeInfoList;

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo;

@end

NS_ASSUME_NONNULL_END
