//
//  XYLivePushRoomViewController+ModuleRouter.m
//  XYLiveKit
//
//  Created by 周博立 on 2021/4/9.
//  Copyright © 2021 XingIn. All rights reserved.
//

#import "XYLivePushRoomViewController.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLivePushRoomVC+CoreInternal.h"

@import XYSocketKit;
@import XYLiveFoundation;
@import XYDevice;
@import XYConfigCenter;

@implementation XYLivePushRoomViewController (ModuleRouter)

- (NSDictionary *)seiInfoOnHeartbeat {
    NSMutableDictionary *ext = @{}.mutableCopy;
    
    int64_t ts = [LiveLongLink getServerTime];
    int type = 1;
    NSString *ptype = @"trtc";
    if ([self canApplyRTC]) {
    } else if ([self canApplyKasa]) {
        ptype = @"kasa";
        type = 3;
    } else {
        ptype = @"rtmp";
        type = 0;
    }
    NSDictionary *baseInfo = @{
        @"push_ts": @(ts),
        @"push_type": @(type),
        @"room_id": self.roomInfo.roomIDStr ?: @"",
        @"pusher_platform": @(1),
        @"pusher_device_model": XYDevice.machineModelName ?: @"",
        @"pusher_os_version": XYDevice.systemVersionString ?: @"",
        @"pusher_app_version": XYApp.buildString ?: @""
    };
    [ext addEntriesFromDictionary:baseInfo];
    
    
    return ext.copy;
}

- (void)sendSeiInfo {
    if (XYConfigCenter().justOnceBoolForKey(@"ios_livepush_fix_heartbeat_crash", YES)) {
        NSDictionary *seiInfo = [self seiInfoOnHeartbeat];
        NSData *data = seiInfo.xy_modelToJSONData;
        if ([self canApplyRTC]) {
            [self.rtcPusher sendSeiData:data];
            [self.rtcCore sendSei:data];
        } else if ([self canApplyKasa]) {
            [self.kasaPusher sendSeiData:data];
        } else {
            [self.pusher sendSeiData:data];
        }
    } else {
        __weak typeof(self) wself = self;
        NSDictionary *seiInfo = [wself seiInfoOnHeartbeat];
        NSData *data = seiInfo.xy_modelToJSONData;
        if ([wself canApplyRTC]) {
            [wself.rtcPusher sendSeiData:data];
            [wself.rtcCore sendSei:data];
        } else if ([wself canApplyKasa]) {
            [wself.kasaPusher sendSeiData:data];
        } else {
            [wself.pusher sendSeiData:data];
        }
    }
  
}

- (void)moduleRouter_registry {
    __weak typeof(self) wself = self;
    [XYLiveManagerSharedInstance.moduleRouter registerWithType:XyLiveModuleRouteMsgTypeHostHeartbeat impl:^(NSString * _Nonnull type, XYLiveCodableModel * _Nullable model, NSDictionary<NSString *,id> * _Nullable custom) {
        NSInteger res = [custom[kXyLiveModuleRouteMsgResult] integerValue];
        NSInteger errCode = [custom[kXyLiveModuleRouteMsgErrCode] integerValue];
        NSString *content = [NSString stringWithFormat:@"heart %@ %@", @(res), @(errCode)];
        NSString *msg = custom[kXyLiveModuleRouteMsgErrMsg];
        if (msg.length) {
            content = [NSString stringWithFormat:@"%@ msg: %@", content, msg];
        }
        [wself.decorateVM.adapter appendDebugInfoWithoutLog:content];
        
        BOOL disableCheck = XYConfigCenter().boolForKey(@"ios_live_host_idle_disable", NO);
        if (disableCheck) {
            if (UIApplication.sharedApplication.isIdleTimerDisabled == NO) {
                UIApplication.sharedApplication.idleTimerDisabled = YES;
            }
        }
    }];

    [XYLiveManagerSharedInstance.moduleRouter registerWithType:XyLiveModuleRouteMsgTypeSeiHeartbeat impl:^(NSString * _Nonnull type, XYLiveCodableModel * _Nullable model, NSDictionary<NSString *,id> * _Nullable custom) {
        if (XYLiveLinkConfig.seiDelaySendInterval > 0) {
            [wself sendSeiInfo];
        }
    }];
    
    [XYLiveManagerSharedInstance.moduleRouter registerWithType:XyLiveModuleRouteMsgTypeChatboardDebugInfo impl:^(NSString * _Nonnull type, XYLiveCodableModel * _Nullable model, NSDictionary<NSString *,id> * _Nullable custom) {
        NSString *content = custom[kXyLiveModuleRouteMsgContent];
        BOOL collect = [custom[kXyLiveModuleRouteMsgTypeUseLogCollector] boolValue];
        [wself.decorateVM.adapter appendDebugInfo:content collectLog:collect];
    }];
    
    [self moduleRouter_triggerIntoPushRoom];
}

-(void)moduleRouter_triggerIntoPushRoom {
    [xyLiveSharedModuleRouter trigger:XyLiveModuleRouteMsgTypeFirstIntoPushRoom raw:@{
        kXyLiveModuleRouteMsgRoomInfo: self.roomInfo ?: @"",
    }];
}

@end

