//
//  XYLiveMultiChatInviteListVC.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveRefreshBaseViewController.h>
#import <XYLiveKit/XYLivePushMultiChatConsts.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiChatInviteListVC : XYLiveRefreshBaseViewController

// 展示个人资料卡
@property (nonatomic, copy) void(^showUserCardHandler)(XYLiveUserInfo *userInfo);
// 是否挂车
@property (nonatomic, copy) BOOL(^hasGoodsHandler)(void);
// 直播间基础信息
@property (nonatomic, copy) id<XYLiveInfoServiceProtocol>(^liveInfoServiceHandler)(void);
// 多人互动框架服务
@property (nonatomic, copy) id<XYLiveMultiLinkServiceProtocol>(^multiLinkServiceHandler)(void);

/// 初始化
- (instancetype)initWithBizType:(XYLiveMultiLinkBizType)bizType
                         source:(XYLiveMultiLinkInviteSource)source
                      extraInfo:(NSString * _Nullable)extraInfo;

@end

NS_ASSUME_NONNULL_END
