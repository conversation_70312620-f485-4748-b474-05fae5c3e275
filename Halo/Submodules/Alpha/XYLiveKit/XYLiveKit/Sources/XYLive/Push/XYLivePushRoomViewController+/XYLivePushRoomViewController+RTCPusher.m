//
//  XYLivePushRoomViewController+RTCPusher.m
//  XYLiveKit
//
//  Created by 洛萨 on 2020/7/7.
//  Copyright © 2020 XingIn. All rights reserved.
//

#import "XYLivePushRoomViewController.h"

#import "XYLivePushDecorateViewModel.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLiveManager.h"
#import "XYLiveLinkmicModel.h"
#import "XYLiveAlertView.h"
#import <XYStorageCore/XYStorageCenter.h>

#import <XYLiveKit/XYLiveKit-Swift.h>
@import XYMacroConfig;
@import XYProtoBuffer;
@import XYTracker;
@import XYLiveFoundation;
@import XYConfigCenter;
@import KVOController;
@import XYLiveFoundation;
@import XYLiveCore;

static NSString *const kAPMExtra = @"extra";

static NSString *const kPushRTCMuteVadToastLocalCacheKey = @"kPushRTCMuteVadToastLocalCacheKey";
static NSString *const kPushRTCMuteVadToastRoomIdKey = @"roomId";
static NSString *const kPushRTCMuteVadToastCountKey = @"count";
static NSString *const kPushRTCMuteVadToastLastDateKey = @"lastDate";

@implementation XYLivePushRoomViewController (RTCPusher)

- (void)rtcPusher_addRtcCoreService {
    if (self.rtcCoreService == nil) {
        self.rtcCoreService = [XYLiveRtcCoreService new];
    }
    [self.serviceManager addServiceWithProtocol:@protocol(XYLiveRtcCoreServiceProtocol) service: self.rtcCoreService];
    self.rtcCoreService = nil;
}

- (void)rtcPusher_setupKvo {
    __weak typeof(self) wself = self;
    [self.KVOController observe:self.decorateVM keyPath:NSStringFromSelector(@selector(kvoMessageParamUpdate)) options:NSKeyValueObservingOptionNew block:^(id  _Nullable observer, id  _Nonnull object, NSDictionary<NSString *,id> * _Nonnull change) {
        [wself rtcPusher_updateParam:wself.decorateVM.kvoMessageParamUpdate];
        [wself deltaUpdatePusherInfo:wself.decorateVM.kvoMessageParamUpdate];
    }];
}

- (void)rtcPusher_updateParam:(NSDictionary *)param {
    if (!param) { return; }
    [self.rtcPusher updateParams:param];
}

- (void)rtcPusher_setupBlock {
    __weak typeof(self) wself = self;
    self.onRemoteJoinedSuccBlock = ^{
        
    };
    
    self.onRemoteVideoAvailableBlock = ^(NSString *remoteUserID, BOOL available) {
        
    };
    
    self.onSentLocalFirstVideoFrameBlock = ^{
        
    };
}

- (void)rtcPusher_onEnterBackground {}

- (void)rtcPusher_onBackForeground {}

- (void)onNetworkQualityWithLocal:(NSInteger)local remotes:(NSArray<NSNumber *> *)remotes {
    [xyLiveSharedAPMManager reportPushRTCNetworkQualityWithLocal:local remotes:remotes];
}

- (void)onConnectionLost {
    [self.decorateVM.adapter appendDebugInfo:@"onConnectionLost"];
}

- (void)onConnectionRecovery {
    [self.decorateVM.adapter appendDebugInfo:@"onConnectionRecovery"];
}

- (void)onTryToReconnect {
    [self.decorateVM.adapter appendDebugInfo:@"onTryToReconnect"];
}

- (void)onErrorWithErrCode:(NSInteger)errCode errMsg:(NSString * _Nullable)errMsg extraInfo:(NSDictionary * _Nonnull)extraInfo isHotSwap:(BOOL)isHotSwap {
    NSString *info = [NSString stringWithFormat:@"onErrorWithErrCode: %@, msg: %@, extra: %@", @(errCode), errMsg, extraInfo];
    [self.decorateVM.adapter appendDebugInfo:info];
    
    [self updateApmV2BasicParams];
    [xyLiveSharedAPMManager reportLivePushError:(int32_t)errCode msg:errMsg extInfo:extraInfo?[extraInfo xy_modelToJSONString]:@""];
    
    //for new push_event
    [xyLiveSharedAPMManager reportLivePushEventError:XYLivePushEventError recovery:self.roomInfo.isRecovery encodeInfo:self.rtcPusher.referenceVideoEncoderParam errorCode:(int32_t)errCode errorMsg:errMsg];
   
    
    void(^reportStartPipelinePushError)(void) = ^{
        xyLiveSharedAPMManager.startApmInfo.pushRespTs = [NSDate date].timeIntervalSince1970;
        xyLiveSharedAPMManager.startApmInfo.pushRespCode = errCode;
        xyLiveSharedAPMManager.startApmInfo.pushRespMsg = errMsg ?: @"";
        [xyLiveSharedAPMManager reportStartPipelineTracker];
    };
    
    BOOL protect = XYConfigCenter().boolForKey(@"ios_live_start_apm_push_error_protect", NO);
    if (protect) {
        if (!isHotSwap) {// 命中优化后，只在非热切时进行推流失败上报
            reportStartPipelinePushError();
        }
    } else {
        reportStartPipelinePushError();
    }
        
    [self forceStopWhenFatalErr:errCode];
    
    if (XYConfigCenter().boolForKey(@"ios_live_kasa_downgrade", NO)) {
        // kasa core 2.0
        id<XYLiveRtcCoreServiceProtocol> coreService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveRtcCoreServiceProtocol)];
        if (coreService != nil && [coreService isWorking] && [coreService rtcCore].vendorType == RTCVendorTypeKasa && errCode < 0 && isHotSwap == YES) {
            [XYLogCollector xyLiveLogTag:@"push_room" content:@"kasa core2.0 recv cutStreamHotSwitch callback!!!"];
            dispatch_async(dispatch_get_main_queue(), ^{
                self.channelSession.vendorType = RTCVendorTypeTRTC;
                RTCCoreChannelSession *coreSession = [self buildRtcChannelSession: self.channelSession];
                coreSession.encode = [coreService rtcCore].encodeInfo;
                [coreService reloadRtcCore: coreSession];
                [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventPushRtmpHotSwitchRtc recovery:self.roomInfo.isRecovery encodeInfo: [coreService rtcCore].encodeInfo];
            });
        }
    }
    
    // kasa 这里降级
    if (self.rtcPusher != nil && self.rtcPusher.vendorType == RTCVendorTypeKasa && errCode < 0 && isHotSwap == YES) {
        [XYLogCollector xyLiveLogTag:@"push_room" content:@"kasa recv cutStreamHotSwitch callback"];
        //主线程执行
        dispatch_async(dispatch_get_main_queue(), ^{
            // 会涉及View操作 切换到主线程执行
            [self kasaHotSwitchToTrtc];
            [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventPushRtmpHotSwitchRtc recovery:self.roomInfo.isRecovery encodeInfo: self.rtcPusher.referenceVideoEncoderParam];
        });
    }
}

- (void)forceStopWhenFatalErr:(NSInteger)errCode {
    NSArray *errs = XYConfigCenter().arrayForKey(@"ios_live_push_err_probe", @[]);
    if (self.rtcPusher.pushType != XYLivePushTypeTrtc) { return; }
    NSString *errCodeStr = [NSString stringWithFormat:@"%ld",(long)errCode];
    if (errs.count && errCodeStr.length) {
        __typeof(self) __weak wself = self;
        if (!self.fatalAlerted) {
            self.fatalAlerted = YES;
            dispatch_async(dispatch_get_main_queue(), ^{
                if ([errs containsObject:errCodeStr]) {
                    XYLiveAlertView *alert = [XYLiveAlertView alertViewWithTitle:NSLocalizedString(@"网络错误,请切换网络后重新开播", nil) subTitle:@""];
                    alert.isSingleButtonStyle = YES;
                    alert.rightBtnTitle = NSLocalizedString(@"确定", nil);
                    alert.rightBtnBlock = ^(UIButton * _Nonnull sender, BOOL timeout) {
                        [wself.decorateView terminateGiftRendering];
                        [wself.decorateView.viewModel notifyLinkingHostQuit];
                        [wself.decorateVM notifyManagerToLeaveRoomWithReason:@"rtc_pusher_error_confirm"];
                    };
                    [alert showInView:wself.decorateView animated:YES];
                }
            });
        }
    }
}

- (void)onFirstAudioFrameWithUserID:(NSString * _Nonnull)userID {
    NSString *info = [NSString stringWithFormat:@"onFirstAudioFrameWithUserID: %@", userID];
    [self.decorateVM.adapter appendDebugInfo:info];
}

- (void)onFirstVideoFrameWithUserID:(NSString * _Nonnull)userID streamType:(NSInteger)streamType width:(NSInteger)width height:(NSInteger)height {
    NSString *info = [NSString stringWithFormat:@"onFirstVideoFrameWithUserID: %@, stream: %@, width: %@, height: %@", userID, @(streamType), @(width), @(height)];
    [self.decorateVM.adapter appendDebugInfo:info];
}

- (void)onJoinChannelWithResult:(NSInteger)result {
    if (XYMacroDefine.debugORAdhoc) {
        if (result <= 0) {
            NSString *res = [NSString stringWithFormat:@"加入RTC房间出错: %@（请截图保存供开发分析）", @(result)];
            [[XYAlertCenter createAlertItemWithMessage:res] show];
        }
    }
    
    if (result > 0 && [self enableScreenCaptureBiz]) {
        // 成功加入频道 弹屏幕分享提示窗
        XYExecuteOnMainQueue(^{
            [self.screenCaptureView showInView:self.decorateView animated:YES];
            [self.decorateView insertSubview:self.screenCaptureView belowSubview:self.decorateView.closeButton];
        });
    }
    
    NSString *ptypeDesc = self.rtcPusher.pushTypeDesc;
    NSString *roomID = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
    NSString *name = XYLiveManagerSharedInstance.userInfo.nickname;
    
    [XYTracker track:^(XYAPMContext *context) {
        context.iosLiveRtcJoinChnlRes
        .sampleRate(1.0)
        .roomId(roomID)
        .pushType(ptypeDesc)
        .joinRes((int64_t)result)
        .hostName(name);
    }];
    
    XYLivePushType ptype = [self fetchPushType];
    [self.decorateVM.adapter appendDebugInfo:[NSString stringWithFormat:@"submit push type: %@", @(ptype)]];
    
    [self updateApmV2BasicParams];
    [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventPushStart recovery:self.roomInfo.isRecovery encodeInfo:self.rtcPusher.referenceVideoEncoderParam];
    
    RTCVendorType vtype = self.rtcPusher.vendorType;
    
    NSString *info = [NSString stringWithFormat:@"onJoinChannelWithResult: %@, type: %@", @(result), @(vtype)];
    [self.decorateVM.adapter appendDebugInfo:info];
    
    if ([self.decorateVM streamQualityCheckDisable]) {
        return;
    }
    [self.decorateVM.streamQualityChecker start];
    [self.decorateVM.streamQualityChecker reloadTargetWithVideoBitrate:(uint32_t)self.rtcPusher.referenceVideoEncoderParam.videoBitrate.rtcReference];
}

- (void)onLeaveChannelWithReason:(NSInteger)reason {
    NSString *info = [NSString stringWithFormat:@"onLeaveChannelWithReason: %@", @(reason)];
    [self.decorateVM.adapter appendDebugInfo:info];
    [self.decorateVM.streamQualityChecker stop];
}

- (void)onRemoteUserJoinChannelWithUserID:(NSString * _Nonnull)userID {
    NSString *info = [NSString stringWithFormat:@"onRemoteUserJoinChannelWithUserID: %@", userID];
    [self.decorateVM.adapter appendDebugInfo:info];
    
    [self updateApmV2BasicParams];
    [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventLinkSuccess recovery:0 encodeInfo:self.rtcPusher.referenceVideoEncoderParam];
    
    if (self.onRemoteJoinedSuccBlock) {
        self.onRemoteJoinedSuccBlock();
    }
}

- (void)onRemoteUserLeaveChannelWithUserID:(NSString * _Nonnull)userID reason:(NSInteger)reason {
    XYLiveLinkType type = XYLiveManagerSharedInstance.currentLinkType;
    NSString *info = [NSString stringWithFormat:@"onRemoteUserLeaveChannelWithUserID: %@, reason: %@, ltype: %@", userID, @(reason), @(type)];
    [self.decorateVM.adapter appendDebugInfo:info];
    
    switch (type) {
        case XYLiveLinkTypePK:
            break;
        case XYLiveLinkTypeBattle:
            break;
        case XYLiveLinkTypeLinkmicVideo:
            break;
        case XYLiveLinkTypeLinkmicAudio:
            break;
        default:
            break;
    }
    
    [self updateApmV2BasicParams];
    [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventLinkStop recovery:0 encodeInfo:self.rtcPusher.referenceVideoEncoderParam];
}

- (void)onSendFirstLocalAudioFrame {
    [self.decorateVM.adapter appendDebugInfo:@"onSendFirstLocalAudioFrame"];
    //收到首帧回调 触发push_success 推流真正成功
    [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventPushSuccess recovery:self.roomInfo.isRecovery encodeInfo:self.rtcPusher.referenceVideoEncoderParam];
}

- (void)onSendFirstLocalVideoFrameWithStreamType:(NSInteger)streamType {
    [self sdk_startLive];// 开播推流成功后触发start
    
    NSString *info = [NSString stringWithFormat:@"onSendFirstLocalVideoFrameWithStreamType: %@", @(streamType)];
    [self.decorateVM.adapter appendDebugInfo:info];
    
    [XYLiveManagerSharedInstance updatePushStats:YES];
    
    if (self.onSentLocalFirstVideoFrameBlock) {
        self.onSentLocalFirstVideoFrameBlock();
    }
    //这里暂不发送codec, codec上报有可能延迟
    [self sdk_submitPushInfo:NO];
    
    if ([self enableScreenCaptureBiz]) {
        // 收起屏幕分享弹窗
        XYExecuteOnMainQueue(^{
            [self.screenCaptureView dismissWithAnimated:YES];
            [self.screenCaptureView.internalView reset];
        });
    }
    
    [XYLiveManagerSharedInstance.consumeService memoryGenerate:XYLiveConsumeMemOptionPushMem];
    [XYLiveManagerSharedInstance.consumeService setContentType:XYLiveManagerSharedInstance.roomInfo.contentType];
    [XYLiveManagerSharedInstance.consumeService reportStartPush];
}

- (void)onWillMixTranscoding {
    
}

- (void)onSetMixTranscodingWithCode:(NSInteger)code msg:(NSString * _Nonnull)msg finished:(BOOL)finished stopForced:(BOOL)stopForced {
    XYLiveLinkType type = XYLiveManagerSharedInstance.currentLinkType;

    NSString *info = [NSString stringWithFormat:@"onSetMixTranscodingWithCode code: %@, msg: %@, finished: %@, force: %@, ltype: %@", @(code), msg, @(finished), @(stopForced), @(type)];
    [self.decorateVM.adapter appendDebugInfo:info];
    
    if (stopForced) { ///强制取消混流，不执行上报和UI逻辑，避免陷入递归
        return;
    }
    
    ///混流异常，终结跨房连线并上报结果
    if (code != 0) {
        switch (type) {
            case XYLiveLinkTypePK: {
                [self.rtcPusher disconnectOtherChannel];
            }
                break;
            case XYLiveLinkTypeBattle: {
                [self.rtcPusher disconnectOtherChannel];
            }
                break;
            case XYLiveLinkTypeLinkmicVideo:
                break;
            case XYLiveLinkTypeLinkmicAudio:
                break;
            default:
                break;
        }
    }
}

- (void)onSwitchRoleWithErrCode:(NSInteger)errCode errMsg:(NSString * _Nullable)errMsg {
    NSString *info = [NSString stringWithFormat:@"onSwitchRoleWithErrCode code: %@, msg: %@", @(errCode), errMsg];
    [self.decorateVM.adapter appendDebugInfo:info];
}

- (void)onUserAudioAvailableWithUserID:(NSString * _Nonnull)userID available:(BOOL)available {
    XYLiveLinkType type = XYLiveManagerSharedInstance.currentLinkType;

    NSString *info = [NSString stringWithFormat:@"onUserAudioAvailableWithUserID userID: %@, available: %@, ltype: %@", userID, @(available), @(type)];
    [self.decorateVM.adapter appendDebugInfo:info];
}

- (void)onUserVideoAvailableWithUserID:(NSString * _Nonnull)userID available:(BOOL)available {
    XYLiveLinkType type = XYLiveManagerSharedInstance.currentLinkType;
    NSString *info = [NSString stringWithFormat:@"onUserVideoAvailableWithUserID userID: %@, available: %@, ltype: %@", userID, @(available), @(type)];
    [self.decorateVM.adapter appendDebugInfo:info];

    if (self.onRemoteVideoAvailableBlock) {
        self.onRemoteVideoAvailableBlock(userID, available);
    }
}

- (void)onWarningWithWarningCode:(NSInteger)warningCode warningMsg:(NSString * _Nullable)warningMsg extraInfo:(NSDictionary * _Nonnull)extraInfo {
    NSString *info = [NSString stringWithFormat:@"onWarning code: %@, msg: %@, extra: %@", @(warningCode), warningMsg, extraInfo];
    [self.decorateVM.adapter appendDebugInfo:info];
        
    ///根据错误码处理错误
    ///https://cloud.tencent.com/document/product/647/32257
    switch (warningCode) {
        case 2105: {///WARNING_VIDEO_PLAY_LAG 当前视频播放出现卡顿
            NSString *regex = @"\\d+";
            NSString *result = [NSString processWithString:warningMsg regularExpression:regex];
            if (result.length) {
                CFAbsoluteTime lag = result.doubleValue;
                [xyLiveSharedAPMManager accumulateRTCPusherTotalLagTime:lag];
                CFAbsoluteTime curTotalPushTime = CFAbsoluteTimeGetCurrent() - self.viewDidLoadTime;
                [xyLiveSharedAPMManager updateRTCPusherTotalPushTime:curTotalPushTime];
            }
        }
            break;
        case 1104: {
            /// WARNING_CURRENT_ENCODE_TYPE_CHANGED
            int currentType = [[extraInfo valueForKey:@"type"] intValue];
            [xyLiveSharedAPMManager reportLivePushCodecChange:currentType recovery:self.roomInfo.isRecovery encodeInfo:self.rtcPusher.referenceVideoEncoderParam];
            return;
        }
            break;
        default:
            break;
    }
    
    [self updateApmV2BasicParams];
    [xyLiveSharedAPMManager reportLivePushWarn:(int32_t)warningCode msg:warningMsg extInfo:extraInfo?[extraInfo xy_modelToJSONString]:@""];
    
    //for new push_event
    [xyLiveSharedAPMManager reportLivePushEventWarn:XYLivePushEventWarn recovery:self.roomInfo.isRecovery encodeInfo:self.rtcPusher.referenceVideoEncoderParam warnCode:(int32_t)warningCode warnMsg:warningMsg];
}

- (void)onStatistics:(RTCApmStatistics * _Nonnull)statistics {
    id<XYLiveCommonMonitorMemServiceProtocol> monitorMemService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveCommonMonitorMemServiceProtocol)];
    [monitorMemService updateRTCStatistics:statistics];

    [self updateApmV2BasicParams];
    [xyLiveSharedAPMManager reportLivePushLoop:statistics cameraRate:XYLiveManagerSharedInstance.coreManager.media.cameraRate previewRate:XYLiveManagerSharedInstance.coreManager.media.previewRate cameraOrientation:XYLiveManagerSharedInstance.coreManager.media.isCameraFront ? 0 : 1];
    
    if ([self.decorateVM streamQualityCheckDisable]) {
        return;
    }
    
    NSInteger videoBitrate = self.rtcPusher.referenceVideoEncoderParam.videoBitrate.rtcReference;
    if (self.rtcCore != nil) {
        videoBitrate = self.rtcCore.encodeInfo.videoBitrate.rtcReference;
    }
    [self.decorateVM.streamQualityChecker reloadTargetWithVideoBitrate:(uint32_t)videoBitrate];
    
    [self.decorateVM.streamQualityChecker slideInOnStatistics:statistics];
}

- (void)onNetworkQualityReportWithData:(RTCNetworkQualityInfo *_Nonnull)qualityInfo {
    if (XYConfigCenter().boolForKey(@"ios_live_network_detect_filter_biz", NO)) {
        if ([self.decorateVM streamQualityCheckDisable]) {
            self.decorateView.viewModel.networkQualityScore = qualityInfo.aggregateQualityScore;
        }
    } else {
        self.decorateView.viewModel.networkQualityScore = qualityInfo.aggregateQualityScore;
    }
}

- (void)onConnectOtherChannelWithUserID:(NSString * _Nonnull)userID code:(NSInteger)code msg:(NSString * _Nullable)msg {
    XYLiveLinkType type = XYLiveManagerSharedInstance.currentLinkType;

    NSString *info = [NSString stringWithFormat:@"conn other: %@, code: %@, msg: %@, ltype: %@", userID, @(code), msg, @(type)];
    [self.decorateVM.adapter appendDebugInfo:info];
    
    BOOL ignore = XYLiveLinkConfig.battleIgnoreRtcConnErr;
        
    ///加入频道异常，终结跨房连线并上报结果
    switch (type) {
        case XYLiveLinkTypePK:
            break;
        case XYLiveLinkTypeBattle: {
            if (code != 0) {
                if (ignore) {
                    return;
                }
            }
        }
            break;
        case XYLiveLinkTypeLinkmicVideo:
        case XYLiveLinkTypeLinkmicAudio:
            break;
        default:
            break;
    }
}

- (void)onDisconnectOtherChannelWithCode:(NSInteger)code msg:(NSString * _Nullable)msg {
    XYLiveLinkType type = XYLiveManagerSharedInstance.currentLinkType;

    NSString *info = [NSString stringWithFormat:@"disconn other, code: %@, msg: %@, ltype: %@", @(code), msg, @(type)];
    [self.decorateVM.adapter appendDebugInfo:info];
    
    
    switch (type) {
        case XYLiveLinkTypePK:
            break;
        case XYLiveLinkTypeBattle:
            break;
        case XYLiveLinkTypeLinkmicVideo:
            break;
        case XYLiveLinkTypeLinkmicAudio:
            break;
        default:
            break;
    }
}

#pragma mark - Audio音量监测
- (void)onUserVoiceVolumeWithLocal:(RTCUserVolumeInfo *)local remote:(RTCUserVolumeInfo *)remote {
    NSLog(@"onUserVoiceVolume local = %@ remote = %@", @(local.volume), @(remote.volume));
}

#pragma mark - videoCodec变化
- (void)onLocalVideoCodecChangedWithCodec:(XYLivePushCodecType)codec {
    // 上报codec,bitrate,fps 统一收口在这里,codec获取依然使用原来流程
    [self sdk_submitPushInfo:YES];
}

- (void)onScreenCaptureStarted {
    [XYLogCollector xyLiveLogTag:@"screen_capture" content:@"screen capture started"];
    // apm
    xyLiveSharedAPMManager.startApmInfo.screenCapTs = [NSDate date].timeIntervalSince1970;
    xyLiveSharedAPMManager.startApmInfo.screenCapRespCode = 0;
    // 录屏成功状态上报
    xyLiveSharedAPMManager.startApmInfo.screenServiceResult = 0;
    xyLiveSharedAPMManager.startApmInfo.screenServiceTs = [NSDate date].timeIntervalSince1970;
    if ([self enableScreenCaptureBiz]) {
        // 屏幕分享更改UI
        XYExecuteOnMainQueue(^{
            [self.screenCaptureView.internalView changeStateToOpening];
        });
    }
}

- (void)onScreenCaptureStopedWithReason:(int32_t)reason {
    // 主播手动结束录屏，触发关播逻辑
    NSString *roomID = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
    NSString *hostID = XYLiveManagerSharedInstance.hostInfo.userID;
    [XYTracker track:^(XYAPMContext *context) {
        context.snsSnsLiveGameDisconnectApm
            .roomId(roomID)
            .hostId(hostID)
            .pushType((int32_t) self.roomInfo.pushType)
            .errorCode(reason);
    }];
    [XYLogCollector xyLiveLogTag:@"screen_capture" content:@"screen capture stopped"];
    if ((reason == 0) && self.shouldScreenPushing) {
        [XYLogCollector xyLiveLogTag:@"screen_capture" content:@"screen capture stopped & close live"];
        [self.decorateVM notifyManagerToLeaveRoomWithReason:@"stop_screen_capture"];
    }
}

- (void)onScreenPusherErrorWithErrorCode:(XYLiveScreenPusherErrorCode)errorCode errorMsg:(NSString * _Nullable)errorMsg {
    NSString *info = [NSString stringWithFormat:@"onScreenPusherError errorCode: %@, errorMsg: %@", @(errorCode), errorMsg];
    [XYLogCollector xyLiveLogTag:@"screen_capture" content: info];
    // 录屏异常上报
    if (errorCode == XYLiveScreenPusherLivingError) {
        xyLiveSharedAPMManager.startApmInfo.screenLivingErrCode = errorCode;
        xyLiveSharedAPMManager.startApmInfo.screenLivingErrMsg = errorMsg;
        [xyLiveSharedAPMManager.startApmInfo reportLivingScreenError];
    } else if (errorCode == XYLiveScreenPusherStartError) {
        xyLiveSharedAPMManager.startApmInfo.screenServiceResult = errorCode;
        xyLiveSharedAPMManager.startApmInfo.screenServiceTs = [NSDate date].timeIntervalSince1970;
    }
}

- (void)onPerformanceStatusWithStatus:(NSInteger)status {
    NSString *info = [NSString stringWithFormat:@"onPerformanceStatus: %@", @(status)];
    [XYLogCollector xyLiveLogTag:@"screen_capture" content: info];
    NSString *str_status = [@(status) stringValue];
    NSString *roomID = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
    NSString *hostID = XYLiveManagerSharedInstance.hostInfo.userID;
    NSString *cpuUsage = @"";
    NSString *memUsage = @"";
    id<XYLiveCommonMonitorMemServiceProtocol> monitorMemService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveCommonMonitorMemServiceProtocol)];
    XYLiveDeviceMonitor *monitor = monitorMemService.monitor;
    if (monitor != nil) {
        DeviceStatistics *stats = [monitor deviceStatistics];
        if (stats != nil) {
            cpuUsage = [@(stats.sysCPU) stringValue];
            memUsage = [@(stats.memUsage) stringValue];
        }
    }

    [xyLiveSharedAPMManager reportLivePerformanceStatus:str_status roomId:roomID hostId: hostID cpuUsage:cpuUsage memUsage:memUsage];
}

- (void)onUpgrade:(NSString * _Nonnull)gradeLevel reason:(NSString * _Nonnull)reason {
    if (!XYAlphaSwitch.enablePushMultiLevel) { return; }
    id<XYLiveMultiLinkPushServiceProtocol> linkService = [self.serviceManager getServiceWithProtocol:@protocol(XYLiveMultiLinkPushServiceProtocol)];
    if ([linkService isLinking]) { return; }
    NSString *info = [NSString stringWithFormat:@"onUpgrade: %@, reason: %@", gradeLevel, reason];
    [XYLogCollector xyLiveLogTag:@"grade" content: info];
    BOOL video = XYLiveManagerSharedInstance.isVideoLive;
    BOOL game = XYLiveManagerSharedInstance.isScreenLive;
    if (video) {
        if ([XYAlphaSwitch enablePushMultiLevelV2]) {
            [XYLiveManagerSharedInstance.coreManager.media updateResolution:gradeLevel];
            return;
        } else {
            if ([self.roomInfo.pushDispatchInfo.encode.levelName isEqualToString: gradeLevel]) {
                return;
            }
        }
    } else if (game) {
        // 手游不触发被动降档
        return;
    }
    
    __weak typeof(self) wself = self;
    XYDefinitionTipViewController *tipVc =  [XYDefinitionTipViewController new];
    tipVc.levelName = gradeLevel;
    tipVc.reason = reason;
    tipVc.agreeBlock = ^{
        [[NSNotificationCenter defaultCenter] postNotificationName: XYDefinitionTipViewController.pushMultiLevelUpgrade
                                                            object:nil
                                                          userInfo:nil];
//        本地存储对应的编码参数
        if (video) {
            [wself.roomInfo.pushDispatchInfo restoreSavedLevelForVideo:gradeLevel];
        } else if (game) {
            [wself.roomInfo.pushDispatchInfo restoreSavedLevelForGame:gradeLevel];
        }
        [XYLiveManagerSharedInstance.coreManager.media updateGrade:gradeLevel];
        [XYAlertCenter live_showTextItemWithText:[NSString stringWithFormat:@"切换「%@」成功",gradeLevel]];
    };
    [tipVc showWithVc: self];
}

- (void)onResolutionAndEncodeChanged {
    NSString *info = [NSString stringWithFormat:@"onResolutionAndEncodeChanged res: %@X%@", @(self.rtcPusher.usedEncodeInfo.width), @(self.rtcPusher.usedEncodeInfo.height)];
    [XYLogCollector xyLiveLogTag:@"submit" content: info];
    // 上报width/height
    [self sdk_submitPushInfo:NO];
}

- (void)onMuteVadNotice:(BOOL)isMute {
    XYExecuteOnMainQueue(^{
        BOOL funcionSwitch = [XYLiveConfigCenter enableMuteVadToastFeature];
        if (funcionSwitch) {
            if (isMute) {
                if (self.muteVadMark) {
                    NSDate *curDate = [NSDate date];
                    NSTimeInterval timeInterval = [curDate timeIntervalSinceDate:self.muteVadMark];
                    if (timeInterval > 5) {
                        if ([self enableMuteVadToast]) {
                            [XYAlert live_showTextItemWithText:@"当前麦克风静音，请打开"];
                            [self saveMuteVadToastFrequency];
                        }
                    }
                } else {
                    self.muteVadMark = [NSDate date];
                }
            } else {
                self.muteVadMark = nil;
            }
        }
        
        NSString *info = [NSString stringWithFormat:@"onMuteVadNotice state: %d switch:%@", isMute, @(funcionSwitch)];
        [XYLogCollector xyLiveLogTag:@"submit" content: info];
    });
}

- (BOOL)enableMuteVadToast {
    NSDictionary *dataCache = [[[XYStorageCenter defaultCenter] permanent] getDictionaryForKey:kPushRTCMuteVadToastLocalCacheKey defaultValue:nil];
    // 本次没有cache，认为一次触发，可以弹出
    if (!dataCache) {
        return YES;
    }
    NSString *localRoomId = [dataCache safeObjectForKey:kPushRTCMuteVadToastRoomIdKey];
    if (![localRoomId isEqualToString:XYLiveManagerSharedInstance.roomInfo.roomIDStr]) {
        // 存储的直播间不相等，意味着主播重新开播，重新计数，可以弹出
        return YES;
    }
    NSDate *currentDate = [NSDate date];
    NSDate *lastDate = [dataCache safeObjectForKey:kPushRTCMuteVadToastLastDateKey];
    BOOL isOverMinute = NO;
    if (lastDate) {
        NSTimeInterval timeInterval = [currentDate timeIntervalSinceDate:lastDate];
        NSTimeInterval toastInterval = [XYLiveConfigCenter muteVadToastInterval];
        if (toastInterval <= 0) {
            toastInterval = 60;
        }
        if (timeInterval > toastInterval) {
            isOverMinute = YES;
        }
    } else {
        // 上一次时间不存在，意味着超过1分钟，可以弹出
        isOverMinute = YES;
    }
        
    int showCount = [[dataCache safeObjectForKey:kPushRTCMuteVadToastCountKey] intValue];
    // toast展示次数 < 2 && toast 超过1分钟，可以继续弹出
    if (showCount < 2 && isOverMinute) {
        return YES;
    }
    return NO;
}

- (void)saveMuteVadToastFrequency {
    NSDictionary *existCache = [[[XYStorageCenter defaultCenter] permanent] getDictionaryForKey:kPushRTCMuteVadToastLocalCacheKey defaultValue:nil];
    NSString *localRoomId = [existCache safeObjectForKey:kPushRTCMuteVadToastRoomIdKey];
    
    NSMutableDictionary *dataCache = [NSMutableDictionary dictionary];
    NSDate *currentDate = [NSDate date];
    NSString *roomID = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
    
    dataCache[kPushRTCMuteVadToastRoomIdKey] = roomID;
    dataCache[kPushRTCMuteVadToastLastDateKey] = currentDate;
    int showCount = 1;
    if (localRoomId && [roomID isEqualToString:localRoomId]) {
        showCount = [[existCache safeObjectForKey:kPushRTCMuteVadToastCountKey] intValue];
        showCount = showCount + 1;
    }
    dataCache[kPushRTCMuteVadToastCountKey] = @(showCount);
    [XYLogCollector xyLiveLogTag:@"push_room" content: [NSString stringWithFormat:@"muteVadToast roomid %@ count %@", roomID, @(showCount)]];
    [[[XYStorageCenter defaultCenter] permanent] setDictionary:[dataCache copy] forKey:kPushRTCMuteVadToastLocalCacheKey];
}

#pragma mark - gop变化
-(void)onGopChangedWithGop:(int)gop {
    NSString *info = [NSString stringWithFormat:@"onGopChanged gop: %@", @(gop)];
    [self.decorateVM.adapter appendDebugInfo:info];
    if (gop < 0) {
        return;
    }
    [self updateApmV2BasicParams];
    [xyLiveSharedAPMManager reportLivePushGopEvent:gop recovery:self.roomInfo.isRecovery encodeInfo:self.rtcPusher.referenceVideoEncoderParam];
}

-(void)onEncodeInfoUpdate {
    NSString *info = [NSString stringWithFormat:@"onEncodeInfoUpdate res: %@X%@", @(self.rtcPusher.usedEncodeInfo.width), @(self.rtcPusher.usedEncodeInfo.height)];
    [XYLogCollector xyLiveLogTag:@"submit" content: info];
    // 上报width/height
    [self sdk_submitPushInfo:YES];
}

- (BOOL)enableScreenCaptureBiz {
    return self.decorateVM.currentRoomInfo.isScreenLive && XYConfigCenter().boolForKey(@"ios_live_screen_capture_optimize_enable", NO);
}

#pragma mark - update apm basic params
- (void)updateApmV2BasicParams {
    NSString *pusherName = @"";
    NSString *pusherVer = @"";
    
    if (self.rtcPusher) {
        if (self.rtcPusher.vendorType == RTCVendorTypeTRTC) {
            pusherName = @"trtc";
        } else {
            pusherName = @"unKnow";
        }
        pusherVer = self.rtcPusher.vendorVersion;
    } else if (self.rtcCore) {
        if (self.rtcCore.pushType == RTCVendorTypeTRTC) {
            pusherName = @"trtc";
        } else {
            pusherName = @"kasa";
        }
        pusherVer = self.rtcCore.vendorVersion;
    }
    
    NSString *pushURL = XYLiveManagerSharedInstance.roomInfo.streamURLInfo.livePushURL ?: @"";
    NSString *linkID = @"";
    int32_t ptype = linkID.length ? (int32_t)xyLiveSharedLinkmicManager.linkmicType: 0;
    int32_t role = 0;
    
    [xyLiveSharedAPMManager updatePusherName:pusherName pusherVer:pusherVer pushUrl:pushURL pushType:ptype localRole:role linkId:linkID];
}

@end
