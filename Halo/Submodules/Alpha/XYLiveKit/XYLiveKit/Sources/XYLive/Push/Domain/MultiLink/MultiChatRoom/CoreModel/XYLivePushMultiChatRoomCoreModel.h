//
//  XYLivePushMultiChatRoomCoreModel.h
//  XYLiveKit
//
//  Created by 大远 on 2025/2/14.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveIMDistributerServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveKit/XYLivePushMultiChatRoomConsts.h>
#import <XYLiveKit/XYLivePushMultiChatRoomCoreModelListener.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiChatRoomCoreModel : NSObject

// 申请人数
@property (nonatomic, assign, readonly) NSInteger applyNum;
// 直播间id
@property (nonatomic, copy, readonly)   NSString *roomId;

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                          socketService:(id<XYLiveIMDistributerServiceProtocol>)socketService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

/// 邀请
/// - Parameters:
///   - userId: 用户id
///   - source: 来源
///   - extraInfo: 业务扩展参数
- (void)inviteWithUserId:(NSString *)userId source:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString * _Nullable)extraInfo;

/// 注册监听
- (void)registerListener:(id<XYLivePushMultiChatRoomCoreModelListener>)listener;

/// 取消注册监听
- (void)unregisterListener:(id<XYLivePushMultiChatRoomCoreModelListener>)listener;

@end

NS_ASSUME_NONNULL_END
