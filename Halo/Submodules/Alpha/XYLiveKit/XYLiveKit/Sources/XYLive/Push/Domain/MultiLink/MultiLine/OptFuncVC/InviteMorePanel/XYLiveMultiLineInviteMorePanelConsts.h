//
//  XYLiveMultiLineInviteMorePanelConsts.h
//  XYLiveKit
//
//  Created by 大远 on 2025/6/7.
//  Copyright © 2025 XingIn. All rights reserved.
//
#import <XYLiveUIKit/XYLiveUIKit.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineConsts.h>

#ifndef XYLiveMultiLineInviteMorePanelConsts_h
#define XYLiveMultiLineInviteMorePanelConsts_h

// 操作面板高度
#define kXYLiveMultiLineInviteMorePanelH           DeviceUtility.screenHeight * 0.75

// 重用标识符
static NSString *const kXYLiveMultiLineInviteMoreListTextCellIde = @"XYLiveMultiLineInviteMoreListTextCell";
static NSString *const kXYLiveMultiLineInviteMoreListItemCellIde = @"XYLiveMultiLineInviteMoreListItemCell";
static NSString *const kXYLiveMultiLineInviteMoreListEmptyCellIde = @"XYLiveMultiLineInviteMoreListEmptyCell";

// 导航条高度
static CGFloat const kXYLiveMultiLineInviteMorePanelNavBarH = 44;

// cell高度
static CGFloat const kXYLiveMultiLineInviteMoreListItemCellH = 68;
static CGFloat const kXYLiveMultiLineInviteMoreListTextCellH = 26;



#endif /* XYLiveMultiLineInviteMorePanelConsts_h */
