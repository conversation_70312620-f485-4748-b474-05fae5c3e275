//
//  LeadTools.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/4/29.
//  Copyright © 2025 XingIn. All rights reserved.
//

import XYAlertCenter
import XYAlphaUtils

@objcMembers
class AdsLeadGenerationTools: NSObject {
    class func getI18NLanguage(from text: String, withKey key: String = "") -> String {
        return text
    }
    
    class func showToast(message: String, withKey key: String = "") {
        if key.isEmpty {
            XYAlert.live_createTextItem(withText: message).show()
        } else {
            XYAlert.live_createTextItem(withText: Self.getI18NLanguage(from: message, withKey: key)).show()
        }
    }
}
