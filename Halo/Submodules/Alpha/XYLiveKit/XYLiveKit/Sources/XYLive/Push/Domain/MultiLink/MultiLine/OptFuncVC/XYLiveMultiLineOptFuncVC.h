//
//  XYLiveMultiLineOptFuncVC.h
//  XYLiveKit
//
//  Created by 大远 on 2025/6/3.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineInviteServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveLinkHostServiceProtocol.h>
#import <XYLiveKit/XYLiveMultiLineOptFuncPanelConsts.h>
@class XYLiveLinkHostInviteeInfo;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiLineOptFuncVC : XYViewController<XYLiveMultiLineInviteListener>

// 点击邀请
@property (nonatomic, copy) void(^didTapInviteHandler)(XYLiveLinkHostInviteeInfo *inviteeInfo);
// 点击撤销
@property (nonatomic, copy) void(^didTapCancelHandler)(XYLiveLinkHostInviteeInfo *inviteeInfo);
// 点击用户
@property (nonatomic, copy) void(^didTapUserHandler)(XYLiveLinkHostInviteeInfo *inviteeInfo);
// 点击结束连线
@property (nonatomic, copy) void(^didTapEndLinkHandler)(void);
// 点击重新匹配
@property (nonatomic, copy) void(^didTapRematchHandler)(void);

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService
                    linkHostService:(id<XYLiveLinkHostServiceProtocol>)linkHostService;

// 展示操作面板
- (void)showOptFuncPanelWithInviteeInfoList:(NSArray<XYLiveLinkHostInviteeInfo *> *)inviteeInfoList
                                     source:(XYLiveMultiLineOptFuncPanelSource)source;

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo;

// 关闭面板
- (void)hideAllPanel;

@end

NS_ASSUME_NONNULL_END
