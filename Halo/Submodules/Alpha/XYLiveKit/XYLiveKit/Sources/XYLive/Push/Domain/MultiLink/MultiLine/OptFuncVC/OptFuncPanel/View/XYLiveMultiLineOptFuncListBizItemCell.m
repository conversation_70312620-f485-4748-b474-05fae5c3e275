//
//  XYLiveMultiLineOptFuncListBizItemCell.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/22.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineOptFuncListBizItemCell.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveUIKit/XYLivePaddingLabel.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYWebImage/UIImageView+XYWebImage.h>
#import <Masonry/Masonry.h>

@interface XYLiveMultiLineOptFuncListBizItemCell()

@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, strong) UIImageView *iconImgView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) XYLivePaddingLabel *tipsLabel;
@property (nonatomic, strong) UILabel *subTitleLabel;
@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, strong) XYLiveMultiLineOptFuncListBizItem *listItem;

@end

@implementation XYLiveMultiLineOptFuncListBizItemCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

// 数据绑定
- (void)bindDataSource:(NSArray<XYLiveMultiLineOptFuncGroupItem *> *)dataSource indexPath:(NSIndexPath *)indexPath {
    XYLiveMultiLineOptFuncGroupItem *group = dataSource[indexPath.section];
    XYLiveMultiLineOptFuncListBizItem *listItem = XYSAFE_CAST(group.items[indexPath.row], XYLiveMultiLineOptFuncListBizItem);
    // 更新图标
    [self.iconImgView xy_setImageWithURL:[NSURL URLWithString:listItem.iconURL ?: @""]];
    // 更新标题
    self.titleLabel.text = listItem.title;
    // 更新副标题
    self.subTitleLabel.text = listItem.subTitle;
    // 更新提示
    self.tipsLabel.hidden = !listItem.isPlaying;
    // 动态创建操作按钮
    [self setupOperateItemViewWithItem:listItem];
    // 更新缓存
    self.listItem = listItem;
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 创建容器
    [self setupContainerView];
    // 创建图标
    [self setupIconImgView];
    // 创建标题
    [self setupTitleLabel];
    // 创建提示标题
    [self setupTipsLabel];
    // 创建副标题
    [self setupSubTitleLabel];
    // 创建操作容器
    [self setupStackView];
    
    // 布局
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsMake(4, 16, 4, 16));
    }];
    
    [self.iconImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(12);
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(40, 40));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.iconImgView);
        make.left.equalTo(self.iconImgView.mas_right).offset(16);
    }];
    
    [self.tipsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.titleLabel);
        make.left.equalTo(self.titleLabel.mas_right).offset(4);
        make.right.lessThanOrEqualTo(self.stackView.mas_left).offset(-10);
    }];
    
    [self.subTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(4);
        make.left.equalTo(self.titleLabel);
        make.right.lessThanOrEqualTo(self.stackView.mas_left).offset(-10);
    }];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.containerView);
        make.right.mas_equalTo(-16);
    }];
}


- (void)setupContainerView {
    UIView *containerView = [[UIView alloc] init];
    containerView.backgroundColor = [XYLiveTokenColor bg1];
    containerView.layer.cornerRadius = 12;
    containerView.layer.masksToBounds = YES;
    [self.contentView addSubview:containerView];
    self.containerView = containerView;
}

- (void)setupIconImgView {
    UIImageView *iconImgView = [[UIImageView alloc] init];
    iconImgView.contentMode = UIViewContentModeScaleAspectFit;
    [self.containerView addSubview:iconImgView];
    self.iconImgView = iconImgView;
}

- (void)setupTitleLabel {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.font = [UIFont fontWithName:@"PingFangSC-Semibold" size:14];
    titleLabel.textColor = [XYLiveTokenColor neutralBlack];
    [titleLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    [self.containerView addSubview:titleLabel];
    self.titleLabel = titleLabel;
}

- (void)setupTipsLabel {
    XYLivePaddingLabel *tipsLabel = [[XYLivePaddingLabel alloc] init];
    tipsLabel.font = [UIFont fontWithName:@"PingFangSC-Regular" size:10];
    tipsLabel.textColor = [XYLiveTokenColor primary];
    tipsLabel.text = @"进行中";
    tipsLabel.textInsets = UIEdgeInsetsMake(2, 4, 2, 4);
    tipsLabel.layer.cornerRadius = 4;
    tipsLabel.layer.masksToBounds = YES;
    tipsLabel.backgroundColor = [XYLiveTokenColor primary2];
    [tipsLabel setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.containerView addSubview:tipsLabel];
    self.tipsLabel = tipsLabel;
}

- (void)setupSubTitleLabel {
    UILabel *subTitleLabel = [[UILabel alloc] init];
    subTitleLabel.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
    subTitleLabel.textColor = [XYLiveTokenColor paragraph];
    [self.containerView addSubview:subTitleLabel];
    self.subTitleLabel = subTitleLabel;
}

- (void)setupStackView {
    UIStackView *stackView = [[UIStackView alloc] init];
    stackView.axis = UILayoutConstraintAxisHorizontal;
    stackView.spacing = 8;
    stackView.alignment = UIStackViewAlignmentCenter;
    [stackView setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [stackView setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.containerView addSubview:stackView];
    self.stackView = stackView;
}

- (void)setupOperateItemViewWithItem:(XYLiveMultiLineOptFuncListBizItem *)item {
    // 合法性校验
    if (self.stackView.subviews.count == item.optTypes.count) { return; }
    [self.stackView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];
    // 创建操作按钮
    [item.optTypes enumerateObjectsUsingBlock:^(NSNumber * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        UIButton *optItemView = [UIButton buttonWithType:UIButtonTypeCustom];
        optItemView.layer.borderWidth = 0.5;
        optItemView.layer.borderColor = [self convertToBorderColorWithType:obj.integerValue].CGColor;
        optItemView.layer.cornerRadius = 12;
        optItemView.layer.masksToBounds = YES;
        optItemView.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:12];
        [optItemView setTitle:[self convertToTitleWithType:obj.integerValue] forState:UIControlStateNormal];
        [optItemView setTitleColor:[self convertToTitleColorWithType:obj.integerValue] forState:UIControlStateNormal];
        optItemView.xy_responseRectInset = UIEdgeInsetsMake(-5, -5, -5, -5);
        optItemView.tag = obj.integerValue;
        [optItemView addTarget:self action:@selector(didTapOperate:) forControlEvents:UIControlEventTouchUpInside];
        [self.stackView addArrangedSubview:optItemView];
        
        [optItemView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.size.mas_equalTo(CGSizeMake(52, 24));
        }];
    }];
}

// 转换边框颜色
- (UIColor *)convertToBorderColorWithType:(NSInteger)type {
    return type == XYLiveMultiLineBizOptTypeClose ? [XYLiveTokenColor primary] : [XYLiveTokenColor separator2];
}

// 转换文本颜色
- (UIColor *)convertToTitleColorWithType:(NSInteger)type {
    return type == XYLiveMultiLineBizOptTypeClose ? [XYLiveTokenColor primary] : [XYLiveTokenColor title];
}

// 转换标题
- (NSString *)convertToTitleWithType:(NSInteger)type {
    if (type == XYLiveMultiLineBizOptTypeClose) {
        return @"结束";
    } else if (type == XYLiveMultiLineBizOptTypeReOpen) {
        return @"重开";
    }
    return @"发起";
}

#pragma mark - Event

- (void)didTapOperate:(UIButton *)sender {
    self.didTapOptHandler ? self.didTapOptHandler(self.listItem.bizType, sender.tag) : nil;
}

#pragma mark - TraitCollection

- (void)traitCollectionDidChange:(UITraitCollection *)previousTraitCollection {
    [super traitCollectionDidChange:previousTraitCollection];
    // 强刷
    if(@available(iOS 13, *)) {
        if ([self.traitCollection hasDifferentColorAppearanceComparedToTraitCollection:previousTraitCollection]) {
            [self.stackView.subviews enumerateObjectsUsingBlock:^(__kindof UIView * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
                if ([obj isKindOfClass:UIButton.class]) {
                    UIButton *button = (UIButton *)obj;
                    button.layer.borderColor = [self convertToBorderColorWithType:button.tag].CGColor;
                }
            }];
        }
    }
}

@end
