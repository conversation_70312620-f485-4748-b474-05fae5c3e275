//
//  XYLiveMultiChatInviteListAudienceCell.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatInviteListAudienceCell.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <Masonry/Masonry.h>
#import <XYUserAvatar/UIImageView+XYCornerRadius.h>
#import <XYWebImage/UIImageView+XYWebImage.h>
#import <XYLiveKit/XYLiveMultiChatInviteListModel.h>
#import <XYLiveFoundation/UIImage+XYLiveKit.h>
#import <XYLiveKit/XYLiveMultiChatInviteListTagView.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYAnalytics/XYAnalytics.h>

@interface XYLiveMultiChatInviteListAudienceCell()

@property (nonatomic, strong) UILabel *numLbl; // 序号
@property (nonatomic, strong) UIImageView *avatarImgView; // 头像
@property (nonatomic, strong) UILabel *nickNameLbl; // 昵称
@property (nonatomic, strong) UILabel *descLbl; // 副标题
@property (nonatomic, strong) XYLiveMultiChatInviteListTagView *tagView; // 标签
@property (nonatomic, strong) UIButton *inviteBtn; // 邀请按钮
@property (nonatomic, strong) UIView *separatorLiveView; // 分割线

@property (nonatomic, strong) XYLiveMultiChatInviteListAudienceItem *listItem;
@property (nonatomic, strong) NSIndexPath *indexPath;

 
@end

@implementation XYLiveMultiChatInviteListAudienceCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

/// 数据绑定
- (void)bindListItems:(NSArray *)listItems indexPath:(NSIndexPath *)indexPath {
    XYLiveMultiChatInviteListAudienceItem *listItem = XYSAFE_CAST(listItems[indexPath.row], XYLiveMultiChatInviteListAudienceItem);
    self.numLbl.text = [NSString stringWithFormat:@"%@", @(indexPath.row + 1)];
    // 设置头像
    [self.avatarImgView xy_setImageWithURL:[NSURL URLWithString:listItem.avatarURL ?: @""]];
    // 设置昵称
    self.nickNameLbl.text = listItem.nickName;
    // 设置副标题
    self.descLbl.text = [NSString stringWithFormat:@"本场消费%@薯币", @(listItem.sendCoins)];
    // 更新标签
    [self.tagView updateItem:listItem];
    // 更新按钮状态
    [self updateInviteBtnStatusWithEnable:!listItem.isInvited];
    // 分割线
    self.separatorLiveView.hidden = (listItems.count - 1 == indexPath.row);
    self.listItem = listItem;
    self.indexPath = indexPath;
}

- (void)setupSubviews {
    // 创建接通按钮
    [self setupInviteBtn];
    // 创建排队序号
    [self setupNumLbl];
    // 创建头像
    [self setupAvatarImgView];
    // 创建标题
    [self setupNickNameLbl];
    // 创建副标题
    [self setupDescLbl];
    // 创建标签视图
    [self setupTagView];
    // 分割线
    [self setupSeparatorLineView];
    
    // 布局
    [self.numLbl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.contentView).offset(8);
        make.width.mas_greaterThanOrEqualTo(24);
    }];
    
    [self.avatarImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(42);
        make.size.mas_equalTo(CGSizeMake(44, 44));
        make.centerY.equalTo(self.contentView);
    }];
    
    [self.nickNameLbl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarImgView.mas_right).offset(8);
        make.top.equalTo(self.avatarImgView.mas_top).offset(3);
    }];
    
    [self.descLbl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nickNameLbl);
        make.top.equalTo(self.nickNameLbl.mas_bottom).offset(4);
        make.right.equalTo(self.inviteBtn.mas_left).offset(-4);
    }];
    
    [self.tagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nickNameLbl.mas_right).offset(4);
        make.centerY.equalTo(self.nickNameLbl);
        make.right.lessThanOrEqualTo(self.inviteBtn.mas_left).offset(-24);
    }];
    
    [self.inviteBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.contentView).offset(-16);
        make.size.mas_equalTo(CGSizeMake(63, 28));
    }];
    
    [self.separatorLiveView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarImgView);
        make.bottom.right.equalTo(self.contentView);
        make.height.mas_equalTo(0.5);
    }];
}

#pragma mark - UI

// 创建序号视图
- (void)setupNumLbl {
    UILabel *numLbl = [[UILabel alloc] init];
    numLbl.font = [UIFont fontWithName:@"REDNumber-Bold" size:12];
    numLbl.textColor = XYLiveTokenColor.desc;
    numLbl.textAlignment = NSTextAlignmentCenter;
    [numLbl setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.contentView addSubview:numLbl];
    self.numLbl = numLbl;
}

// 创建头像视图
- (void)setupAvatarImgView {
    UIImageView *avatarImgView = [[UIImageView alloc] init];
    avatarImgView.layer.cornerRadius = 22;
    avatarImgView.layer.masksToBounds = YES;
    avatarImgView.layer.borderColor = [XYLiveTokenColor separator].CGColor;
    avatarImgView.layer.borderWidth = 0.5;
    avatarImgView.userInteractionEnabled = YES;
    avatarImgView.contentMode = UIViewContentModeScaleAspectFill;
    [self.contentView addSubview:avatarImgView];
    self.avatarImgView = avatarImgView;
}

// 创建昵称视图
- (void)setupNickNameLbl {
    UILabel *nickNameLbl = [[UILabel alloc] init];
    nickNameLbl.textColor = XYLiveTokenColor.title;
    nickNameLbl.font = [UIFont fontWithName:@"PingFangSC-Medium" size:14];
    [nickNameLbl setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [nickNameLbl setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    [self.contentView addSubview:nickNameLbl];
    self.nickNameLbl = nickNameLbl;
}

// 创建副标题视图
- (void)setupDescLbl {
    UILabel *descLbl = [[UILabel alloc] init];
    descLbl.textColor = XYLiveTokenColor.desc;
    descLbl.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
    [self.contentView addSubview:descLbl];
    self.descLbl = descLbl;
}

// 创建标签视图
- (void)setupTagView {
    XYLiveMultiChatInviteListTagView *tagView = [[XYLiveMultiChatInviteListTagView alloc] init];
    [self.contentView addSubview:tagView];
    self.tagView = tagView;
}

// 创建邀请按钮
- (void)setupInviteBtn {
    UIButton *inviteBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    inviteBtn.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:13];
    inviteBtn.xy_responseRectInset = UIEdgeInsetsMake(-5, -5, -5, -5);
    [inviteBtn setTitle:@"邀请" forState:UIControlStateNormal];
    [inviteBtn setTitle:@"已邀请" forState:UIControlStateDisabled];
    [inviteBtn setTitleColor:XYLiveTokenColor.primary forState:UIControlStateNormal];
    [inviteBtn setTitleColor:XYLiveTokenColor.disabled forState:UIControlStateDisabled];
    [inviteBtn addTarget:self action:@selector(didTapInvite:) forControlEvents:UIControlEventTouchUpInside];
    inviteBtn.layer.borderWidth = 0.5;
    inviteBtn.layer.borderColor = XYLiveTokenColor.primary.CGColor;
    inviteBtn.layer.cornerRadius = 14;
    inviteBtn.layer.masksToBounds = YES;
    WS
    [inviteBtn xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
        SS
        return self.didTapTrackerHandler ? self.didTapTrackerHandler(self.listItem, self.indexPath) : nil;
    }];
    [self.contentView addSubview:inviteBtn];
    self.inviteBtn = inviteBtn;
}

/// 创建分割线
- (void)setupSeparatorLineView {
    UIView *separatorLineView = [[UIView alloc] init];
    separatorLineView.backgroundColor = XYLiveTokenColor.separator;
    [self.contentView addSubview:separatorLineView];
    self.separatorLiveView = separatorLineView;
}

#pragma mark - Event

/// 接通按钮点击事件
- (void)didTapInvite:(UIButton *)sender {
    // 更新按钮状态
    [self updateInviteBtnStatusWithEnable:NO];
    // 触发事件
    if (self.didTapInviteHandler) {
        self.didTapInviteHandler(self.listItem, self.indexPath);
    }
}

#pragma mark - Private

- (void)updateInviteBtnStatusWithEnable:(BOOL)enable {
    self.inviteBtn.enabled = enable;
    if (enable) {
        self.inviteBtn.layer.borderColor = [XYLiveTokenColor primary].CGColor;
    } else {
        self.inviteBtn.layer.borderColor = [XYLiveTokenColor separator2].CGColor;
    }
}

@end
