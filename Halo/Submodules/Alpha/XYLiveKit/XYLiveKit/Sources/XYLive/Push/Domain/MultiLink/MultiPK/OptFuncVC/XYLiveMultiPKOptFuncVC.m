//
//  XYLiveMultiPKOptFuncVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/6.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiPKOptFuncVC.h"
#import <XYLiveKit/XYLiveMultiLineOptFuncPanelVC.h>
#import <XYLiveKit/XYLiveActionSheetViewController.h>
#import <XYLiveKit/XYLiveLinkHostInviteeInfo.h>
#import <XYLiveKit/XYLiveAlertViewController.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLivePushMultiPKBizTracker.h>

@interface XYLiveMultiPKOptFuncVC()

@property (nonatomic, weak) UIViewController *containerVC;
@property (nonatomic, weak) id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak) id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, weak) id<XYLiveLinkHostServiceProtocol> linkHostService;
@property (nonatomic, weak) XYLiveMultiLineOptFuncPanelVC *optFuncPanelVC;
@property (nonatomic, weak) XYLiveActionSheetViewController *sheetVC;
@property (nonatomic, weak) XYLiveAlertViewController *alertVC;

@end

@implementation XYLiveMultiPKOptFuncVC

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService
                    linkHostService:(id<XYLiveLinkHostServiceProtocol>)linkHostService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
        _linkHostService = linkHostService;
    }
    return self;
}

// 展示操作面板
- (void)showOptFuncPanelWithSource:(XYLiveMultiPKOptFuncPanelSource)source {
    [XYLogCollector xyLiveLogTag:kXYLiveMultiPKModuleName content:[NSString stringWithFormat:@"show pk optFunc panel,{source:%@}", @(source)]];
    XYLiveMultiLineOptFuncPanelVC *panelVC = [[XYLiveMultiLineOptFuncPanelVC alloc] initWithContianerVC:self.containerVC liveInfoService:self.liveInfoService multiLinkService:self.multiLinkService inviteeInfoList:nil];
    WS
    panelVC.didTapAddHandler = ^{
        SS
        [XYAlert live_showTextItemWithText:@"当前正在PK，暂不支持邀请更多"];
    };
    panelVC.didTapSettingHandler = ^{
        SS
        [self.linkHostService showLinkHostSettingPanel];
    };
    panelVC.didTapUserHandler = ^(XYLiveLinkHostInviteeInfo *inviteeInfo) {
        SS
        self.didTapUserHandler ? self.didTapUserHandler(inviteeInfo.userInfo) : nil;
    };
    panelVC.didTapCloseHandler = ^{
        SS
        [self showCloseSheetPanel];
    };
    panelVC.didTapOptHandler = ^(XYLiveMultiLinkBizType bizType, XYLiveMultiLineBizOptType optType) {
        SS
        // 玩法切换
        if (optType == XYLiveMultiLineBizOptTypeOpen) {
            [XYAlert live_showTextItemWithText:@"先结束当前玩法后再发起"];
            return;
        }
        self.didTapBizOptHandler ? self.didTapBizOptHandler(bizType, optType) : nil;
    };
    XYLiveNavigationChildController *nav = [[XYLiveNavigationChildController alloc] initWithRootChildController:panelVC];
    nav.portraitHeight = ^CGFloat{
        return kXYLiveMultiLineOptFuncPanelH;
    };
    [nav showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.optFuncPanelVC = panelVC;
}

// 隐藏面板
- (void)hideAllPanel {
    [self.optFuncPanelVC.navigationChildController dismissWithAnimated:YES complete:nil];
    [self.sheetVC dismissWithAnimated:YES complete:nil];
}

#pragma mark - Private

// 展示关闭操作面板
- (void)showCloseSheetPanel {
    WS
    XYLivePopupItem *stopItem = XYLivePopupItemMake(@"结束连线", XYLivePopupItemTypeHighlight, ^(NSInteger index) {
        SS
        // 埋点上报
        [XYLivePushMultiPKBizTracker eventActionId80517WithRoomId:self.liveInfoService.roomId channelTabName:@"close" hasGoods:self.liveInfoService.roomInfo.hasGoods];
        // 事件回调
        self.didTapEndLinkHandler ? self.didTapEndLinkHandler() : nil;
    });
    XYLivePopupItem *rematchItem = XYLivePopupItemMake(@"重新匹配", XYLivePopupItemTypeNormal, ^(NSInteger index) {
        SS
        self.didTapRematchHandler ? self.didTapRematchHandler() : nil;
    });
    XYLiveActionSheetViewController *sheetVC = [[XYLiveActionSheetViewController alloc] initWithTitle:nil items:@[stopItem, rematchItem]];
    [sheetVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.sheetVC = sheetVC;
}

// 展示玩法切换确认弹窗
- (void)showSwitchBizAlertWithBizType:(XYLiveMultiLinkBizType)bizType {
    NSString *title = bizType == XYLiveMultiLinkBizTypePKGift ? @"确认要发起礼物PK吗" : @"确认要发起人气PK吗";
//    NSString *msg = bizType == XYLiveMultiLinkBizTypePKGift ? @"发起后，对方将受到邀请提醒，对方同意后将开启礼物PK" : @"发起后，对方将受到邀请提醒，对方同意后将开启人气PK";
    WS
    XYLiveAlertViewController *alertVC = [[XYLiveAlertViewController alloc] initWithTitle:title message:nil cancelButtonTitle:@"取消" confirmButtonTitle:@"确认" handler:^(NSInteger index) {
        SS
        if (index == 1) {
            [self switchBizWithBizType:bizType extraInfo:nil completion:nil];
        }
    }];
    [alertVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.alertVC = alertVC;
}

// 玩法切换
- (void)switchBizWithBizType:(XYLiveMultiLinkBizType)bizType
                   extraInfo:(NSString *_Nullable)extraInfo
                  completion:(void(^_Nullable)(NSError *error))completion {
    WS
    [self.multiLinkService swithchBizWithTargetBizType:bizType bizExtraInfo:extraInfo completion:^(NSError * _Nonnull error) {
        SS
        // Toast提示
        error ? [XYAlert live_showTextItemWithError:error] : nil;
        // 执行回调
        completion ? completion(error) : nil;
    }];
}

@end
