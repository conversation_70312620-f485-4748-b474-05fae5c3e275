//
//  XYLivePushMultiChatService.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/22.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatService.h"

@interface XYLivePushMultiChatService()

@property (nonatomic, weak) id<XYLivePushMultiChatServiceProtocol> target;

@end

@implementation XYLivePushMultiChatService

// 绑定服务
- (void)bindTarget:(id<XYLivePushMultiChatServiceProtocol>)target {
    self.target = target;
}

- (void)showInvitePanelWithSource:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString *)extraInfo {
    [self.target showInvitePanelWithSource:source extraInfo:extraInfo];
}

- (void)inviteWithUserId:(NSString *)userId source:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString *)extraInfo {
    [self.target inviteWithUserId:userId source:source extraInfo:extraInfo];
}

- (void)showOperatePanelWithUserInfo:(XYLiveOnmicUserModel *)userInfo {
    [self.target showOperatePanelWithUserInfo:userInfo];
}

@end
