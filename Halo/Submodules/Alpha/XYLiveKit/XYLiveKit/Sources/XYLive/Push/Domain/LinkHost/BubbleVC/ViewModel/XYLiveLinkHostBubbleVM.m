//
//  XYLiveLinkHostBubbleVM.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/5.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostBubbleVM.h"
#import <XYStorageCore/XYStorageCenter.h>
#import <XYLiveKit/XYLiveLinkHostConsts.h>

// 缓存标识
static NSString * const kXYLiveLinkHostPKBubbleKey             = @"link-host-pk-bubble";
static NSString * const kXYLiveLinkHostSwitchBubbleKey         = @"link-host-switch-biz-bubble";

@interface XYLiveLinkHostBubbleVM()

@property (nonatomic, copy) NSDictionary <NSNumber *, XYLiveLinkHostBubbleModel *> *bubbleModelMap;

@end

@implementation XYLiveLinkHostBubbleVM

- (instancetype)init {
    if (self = [super init]) {
        // 初始化气泡配置
        [self setupBubbleConfig];
    }
    return self;
}

// 气泡配置
- (XYLiveLinkHostBubbleModel *)bubbleModelWithType:(XYLiveLinkHostBubbleType)type {
    return self.bubbleModelMap[@(type)];
}

// 是否可用
- (BOOL)enableBubbleWithType:(XYLiveLinkHostBubbleType)type {
    if (type == XYLiveLinkHostBubbleTypePK) {
        return [XYStorageCenter.defaultCenter.permanent getBoolForKey:kXYLiveLinkHostPKBubbleKey defaultValue:YES];
    }
    return [XYStorageCenter.defaultCenter.permanent getBoolForKey:kXYLiveLinkHostSwitchBubbleKey defaultValue:YES];
    
}

// 气泡不可用
- (void)disableBubbleWithType:(XYLiveLinkHostBubbleType)type {
    if (type == XYLiveLinkHostBubbleTypePK) {
        [XYStorageCenter.defaultCenter.permanent setBool:NO forKey:kXYLiveLinkHostPKBubbleKey];
    } else {
        [XYStorageCenter.defaultCenter.permanent setBool:NO forKey:kXYLiveLinkHostSwitchBubbleKey];
    }
}

#pragma mark - Private

- (void)setupBubbleConfig {
    // PK功能迁移
    XYLiveLinkHostBubbleModel *pkBubble = [[XYLiveLinkHostBubbleModel alloc] init];
    pkBubble.text = @"PK功能移到这里啦～";
    pkBubble.duration = 3.0;
    
    // 玩法切换
    XYLiveLinkHostBubbleModel *switchBizBubble = [[XYLiveLinkHostBubbleModel alloc] init];
    switchBizBubble.text = @"在这里切换连线玩法";
    switchBizBubble.duration = 3.0;
    
    self.bubbleModelMap = @{
        @(XYLiveLinkHostBubbleTypePK): pkBubble,
        @(XYLiveLinkHostBubbleTypeSwitchBiz): switchBizBubble
    };
                                                                                   
}

@end
