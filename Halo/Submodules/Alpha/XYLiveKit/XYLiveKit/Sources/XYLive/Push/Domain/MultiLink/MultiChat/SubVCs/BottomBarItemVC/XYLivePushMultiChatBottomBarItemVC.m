//
//  XYLivePushMultiChatBottomBarItemVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/2/14.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatBottomBarItemVC.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveKit/XYLivePushMultiChatConsts.h>

@interface XYLivePushMultiChatBottomBarItemVC()<XYLivePushMultiChatCoreModelListener>

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   XYLivePushMultiChatCoreModel *coreModel;
@property (nonatomic, weak)   id<XYLivePushAdapterServiceProtocol> adapterService;

@end

@implementation XYLivePushMultiChatBottomBarItemVC

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                          coreModel:(XYLivePushMultiChatCoreModel *)coreModel
                     adapterService:(id<XYLivePushAdapterServiceProtocol>)adapterService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _coreModel = coreModel;
        _adapterService = adapterService;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    // 注册监听
    [self registerAllObservers];
}

// 注册监听
- (void)registerAllObservers {
    // 注册coreModel监听
    [self.coreModel registerListener:self];
}

#pragma mark - XYLivePushMultiChatCoreModelListener

- (void)onUpdateSwitchStatus:(BOOL)isOpened {
    // 更新底部栏按钮入口状态
    [self.adapterService.decorateView updateBottomLinkMicButtonEntrance:isOpened];
    if (!isOpened) {
        // 清除底部栏角标
        self.adapterService.decorateViewModel.applyLinkmicCount = 0;
    }
}

- (void)onUpdateApplyNum:(NSInteger)applyNum {
    if (!self.coreModel.isOpened) { return; }
    // 更新底部栏按钮角标
    self.adapterService.decorateViewModel.applyLinkmicCount = applyNum;
}

@end
