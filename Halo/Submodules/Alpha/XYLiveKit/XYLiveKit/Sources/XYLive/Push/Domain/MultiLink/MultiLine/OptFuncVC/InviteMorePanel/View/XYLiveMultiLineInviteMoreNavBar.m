//
//  XYLiveMultiLineInviteMoreNavBar.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/3.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineInviteMoreNavBar.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYUITheme/XYThemeManager.h>
#import <XYUIKitCore/UIView+XYUIKCPointInside.h>
#import <Masonry/Masonry.h>

@interface XYLiveMultiLineInviteMoreNavBar()

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIButton *searchBtn;

@end

@implementation XYLiveMultiLineInviteMoreNavBar

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 初始化标题
    [self setupTitleLabel];
    // 初始化搜索
    [self setupSearchBtn];
    
    // 布局
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.left.equalTo(self).offset(16);
    }];

    [self.searchBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-16);
        make.centerY.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(24, 24));
    }];
}

- (void)setupTitleLabel {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.text = @"选择要邀请的主播";
    titleLabel.textColor = [XYLiveTokenColor title];
    titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:16];
    [self addSubview:titleLabel];
    self.titleLabel = titleLabel;
}

- (void)setupSearchBtn {
    UIButton *searchBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    [searchBtn setImage:Theme.icon.search.size(24).color(XYLiveTokenColor.title).image forState:UIControlStateNormal];
    searchBtn.contentMode = UIViewContentModeRight;
    searchBtn.xy_responseRectInset = UIEdgeInsetsMake(-5, -5, -5, -5);
    [searchBtn addTarget:self action:@selector(didTapSearch:) forControlEvents:UIControlEventTouchUpInside];
    [self addSubview:searchBtn];
    self.searchBtn = searchBtn;
}

#pragma mark - Event

- (void)didTapSearch:(UIButton *)sender {
    self.didTapSearchHandler ? self.didTapSearchHandler() : nil;
}

@end
