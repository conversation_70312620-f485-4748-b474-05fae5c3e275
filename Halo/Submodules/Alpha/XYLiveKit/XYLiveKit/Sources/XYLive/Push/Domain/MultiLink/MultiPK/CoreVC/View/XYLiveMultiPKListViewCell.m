//
//  XYLiveMultiPKListViewCell.m
//  XYLiveKit
//
//  Created by 大远 on 2025/4/24.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiPKListViewCell.h"
#import <XYLiveKit/XYLiveMultiLinkMuteWidget.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <Masonry/Masonry.h>

@interface XYLiveMultiPKListViewCell()

@property (nonatomic, strong)   XYLiveMultiLinkMuteWidget *muteWidget;

@end

@implementation XYLiveMultiPKListViewCell

#pragma mark - Override

- (void)setupSubviews {
    [super setupSubviews];
    // 创建静音按钮
    [self setupMuteWidget];
    
    // 布局
    [self.muteWidget.widgetView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.mas_equalTo(-6);
        make.bottom.mas_equalTo(-6);
        make.size.mas_equalTo(CGSizeMake(24, 24));
    }];
}

#pragma mark - 创建子视图

- (void)setupMuteWidget {
    XYLiveMultiLinkMuteWidget *widget = [[XYLiveMultiLinkMuteWidget alloc] init];
    WS
    widget.didTapHandler = ^(NSString * _Nonnull userId, BOOL isMute) {
        SS
        self.didTapMuteHandler ? self.didTapMuteHandler(userId, isMute) : nil;
    };
    [self addWidget:widget];
    self.muteWidget = widget;
}

@end
