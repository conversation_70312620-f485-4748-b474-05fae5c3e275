// MARK: - 底部栏类型枚举
enum BindLeadCardBottomBarType {
    case allSelect   // 全选底部栏
    case complete   // 完成底部栏
}

// MARK: - 底部栏工厂类
@objcMembers
class BindLeadCardBottomBarFactory {
    static func createBottomBar(type: BindLeadCardBottomBarType, completion: (() -> Void)?, allSelect: ((Bool) -> Void)?) -> UIView & BindCardBottomBarProtocol {
        switch type {
        case .allSelect: // 带全选
            return BindAllSelectBottomBar(completion: completion, allSelect: allSelect)
        case .complete: // 完成
            return BindCompleteBottomBar(completion: completion)
        }
    }
}

// MARK: - 底部栏管理类
@objcMembers
class BindLeadCardBottomBarManager: NSObject {
    private var bottomBars: [BindLeadCardBottomBarType: UIView & BindCardBottomBarProtocol] = [:]
    private weak var containerView: UIView?
    var selectedCountHandler: (() -> Int)?
    var limitedCountHandler: (() -> Int)?
    
    // 类方法：初始化并添加所有底部栏
    class func setupAllBottomBars(in container: BindLeadCardBottomView,
                                  isAllowSelectNone: Bool,
                                  completion: (() -> Void)?,
                                  allSelect: ((Bool) -> Void)?,
                                  selectedCountHandler: (() -> Int)?,
                                  limitedCountHandler: (() -> Int)?) -> BindLeadCardBottomBarManager {
        let manager = BindLeadCardBottomBarManager(container: container)
        manager.selectedCountHandler = selectedCountHandler
        manager.limitedCountHandler = limitedCountHandler
        manager.setupAllBottomBars(isAllowSelectNone: isAllowSelectNone, completion:completion, allSelect:allSelect)
        return manager
    }
    
    override init() {
        super.init()
    }
    
    private convenience init(container: UIView) {
        self.init()
        self.containerView = container
    }
    
    private func setupAllBottomBars(isAllowSelectNone: Bool, completion: (() -> Void)?, allSelect: ((Bool) -> Void)?) {
        guard let containerView = containerView else { return }
        
        // 创建并添加所有类型的底部栏
        [BindLeadCardBottomBarType.allSelect, .complete].forEach { type in
            let bottomBar = BindLeadCardBottomBarFactory.createBottomBar(type: type, completion:completion, allSelect:allSelect)
            bottomBar.setCanSelectNone(isAllow: isAllowSelectNone)
            bottomBar.setSelectCardCountHandler(handler: selectedCountHandler)
            bottomBar.setLimitCardCountHandler(handler: limitedCountHandler)
            
            containerView.addSubview(bottomBar)
            bottomBars[type] = bottomBar
            
            // 设置约束
            bottomBar.snp.makeConstraints { make in
                make.left.top.right.equalToSuperview()
                make.height.equalTo(56.0)
            }
            
            // 默认隐藏
            bottomBar.isHidden = true
        }
    }
    
    // 显示指定类型的底部栏，隐藏其他
    func showBottomBar(shouldShowAllSelectBtn: Bool, shouldEnableCompleteBtn: Bool, shouldShowBottomBar: Bool, cardType: BindLeadCardBottomBarType) {
        bottomBars.forEach { (barType, bar) in
            if barType != cardType {
                bar.isHidden = true
            } else {
                // 更新当前底部栏的状态
                bar.isHidden = false
                bar.update(shouldShowAllSelectBtn: shouldShowAllSelectBtn, shouldEnableCompleteBtn: shouldEnableCompleteBtn, shouldShowBottomBar:shouldShowBottomBar)
            }
        }
    }
    
    func updateBottomBar(limitCount: Int, selectedCount: Int, cardType: LeadCardType) {
        bottomBars.forEach { (barType, bar) in
            if cardType == .formMsg {
                bar.update(selectAllCount: limitCount, selectedCount: selectedCount)
            }
            // 更新底部栏目
            bar.updateCompleteBtnStatus()
        }
    }
    
    // 获取指定类型的底部栏
    func bottomBar(for type: BindLeadCardBottomBarType) -> UIView? {
        return bottomBars[type]
    }
}
