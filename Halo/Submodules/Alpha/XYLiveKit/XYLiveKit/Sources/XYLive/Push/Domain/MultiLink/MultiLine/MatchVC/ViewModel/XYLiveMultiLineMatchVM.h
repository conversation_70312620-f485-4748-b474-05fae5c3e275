//
//  XYLiveMultiLineMatchVM.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/30.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineMatchServiceProtocol.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>

NS_ASSUME_NONNULL_BEGIN

@protocol XYLiveMultiLineMatchDelegate <NSObject>

// 匹配状态变化
- (void)onUpdateMatchState:(XYLiveMultiLinkMatchState)state;

@end

@interface XYLiveMultiLineMatchVM : NSObject<XYLiveMultiLinkListener>

// 代理
@property (nonatomic, weak) id<XYLiveMultiLineMatchDelegate> delegate;
// 是否匹配中
@property (nonatomic, assign, readonly) BOOL isMatching;

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

// 发起匹配
- (void)startMatchWithSource:(XYLiveMultiLineMatchSource)source extraInfo:(NSString *)extraInfo;

// 取消匹配
- (void)cancelMatch;

// 发起重新匹配
- (void)startReMatchWithExtraInfo:(NSString *)extraInfo;

@end

NS_ASSUME_NONNULL_END
