//
//  XYLiveLinkHostInviteListModel.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/20.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYLiveFoundation/XYLiveFoundation.h>
#import <XYLiveKit/XYLiveLinkHostInviteeInfo.h>

NS_ASSUME_NONNULL_BEGIN

// 纯文本
@interface XYLiveLinkHostInviteListTextItem : NSObject

@property (nonatomic, copy) NSString *title;

@end

// 空视图
@interface XYLiveLinkHostInviteListEmptyItem : NSObject

// 展示空视图
@property (nonatomic, assign) BOOL showErrorView;

@end

@interface XYLiveLinkHostInviteListModel : XYLiveCodableModel

// 好友列表
@property (nonatomic, copy) NSArray<XYLiveLinkHostInviteeInfo *> *friendListItems;
// 推荐列表
@property (nonatomic, copy) NSArray<XYLiveLinkHostInviteeInfo *> *recommendListItems;

@end

NS_ASSUME_NONNULL_END
