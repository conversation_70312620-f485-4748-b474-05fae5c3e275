//
//  XYLivePushMultiChatNavigationBar.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/22.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatNavigationBar.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveFoundation/UIImage+XYLiveKit.h>
#import <XYUIKitCore/XYUIKitCore.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYLiveKit/XYLivePushMultiLinkBizTracker.h>
#import <Masonry/Masonry.h>

@interface XYLivePushMultiChatNavigationBar()

@property (nonatomic, strong) UIButton *shareBarItemView; // 分享
@property (nonatomic, strong) UIButton *settingBarItemView; // 设置

@end

@implementation XYLivePushMultiChatNavigationBar

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    // 创建分享按钮
    [self setupShareBarItemView];
    // 创建设置按钮
    [self setupSettingBarItemView];
    
    [self.settingBarItemView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(36, 36));
        make.right.mas_equalTo(-10);
        make.centerY.equalTo(self);
    }];
    
    [self.shareBarItemView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.size.mas_equalTo(CGSizeMake(36, 36));
        make.right.equalTo(self.settingBarItemView.mas_left).offset(-8);
        make.centerY.equalTo(self);
    }];
}

#pragma mark - UI

- (void)setupShareBarItemView {
    UIButton *shareBarItemView = [UIButton buttonWithType:UIButtonTypeCustom];
    [shareBarItemView setImage:[UIImage xy_liveKitBundleImage:@"link_share"] forState:UIControlStateNormal];
    shareBarItemView.xy_responseRectInset = UIEdgeInsetsMake(-5, -5, -5, -5);
    [shareBarItemView addTarget:self action:@selector(didTapShare:) forControlEvents:UIControlEventTouchUpInside];
    WS
    [shareBarItemView xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
        SS
        return self.didTapShareTrackerHandler ? self.didTapShareTrackerHandler() : nil;
    }];
    [self addSubview:shareBarItemView];
    self.shareBarItemView = shareBarItemView;
    
}

- (void)setupSettingBarItemView {
    UIButton *settingBarItemView = [UIButton buttonWithType:UIButtonTypeCustom];
    [settingBarItemView setImage:[UIImage xy_liveKitBundleImage:@"link_setting"] forState:UIControlStateNormal];
    settingBarItemView.xy_responseRectInset = UIEdgeInsetsMake(-5, -5, -5, -5);
    [settingBarItemView addTarget:self action:@selector(didTapSetting:) forControlEvents:UIControlEventTouchUpInside];
    WS
    [settingBarItemView xyAutoTrack_registerEventTrackerBlock:^XYTrackerEventContext * _Nullable{
        SS
        return self.didTapSettingTrackerHandler ? self.didTapSettingTrackerHandler() : nil;;
    }];
    [self addSubview:settingBarItemView];
    self.settingBarItemView = settingBarItemView;
}

#pragma mark - Event

- (void)didTapShare:(UIButton *)sender {
    if (self.didTapShareHandler) {
        self.didTapShareHandler();
    }
}

- (void)didTapSetting:(UIButton *)sender {
    if (self.didTapSettingHandler) {
        self.didTapSettingHandler();
    }
}

@end
