//
//  XYLiveMultiLineSearchSectionView.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/3.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineSearchSectionView.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <Masonry/Masonry.h>

@interface XYLiveMultiLineSearchSectionView()

@property (nonatomic, strong) UILabel *titleLabel;

@end

@implementation XYLiveMultiLineSearchSectionView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 设置背景色
    self.backgroundColor = [XYLiveTokenColor bg];
    // 创建标题
    [self setupTitleLabel];
    
    // 布局
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(16);
        make.top.mas_equalTo(8);
    }];
}

- (void)setupTitleLabel {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.textColor = [XYLiveTokenColor desc];
    titleLabel.text = @"搜索结果";
    titleLabel.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
    [self addSubview:titleLabel];
    self.titleLabel = titleLabel;
}

@end
