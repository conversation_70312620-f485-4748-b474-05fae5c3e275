//
//  XYLiveMultiChatApplyListCell.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatApplyListCell.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYUserAvatar/UIImageView+XYCornerRadius.h>
#import <XYWebImage/UIImageView+XYWebImage.h>
#import <XYLiveKit/XYLiveMultiChatApplyListTagView.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <Masonry/Masonry.h>

@interface XYLiveMultiChatApplyListCell()

@property (nonatomic, strong) UILabel *numLbl; // 序号
@property (nonatomic, strong) UIImageView *avatarImgView; // 头像
@property (nonatomic, strong) UILabel *nickNameLbl; // 昵称
@property (nonatomic, strong) UILabel *descLbl; // 副标题
@property (nonatomic, strong) XYLiveMultiChatApplyListTagView *tagView; // 标签
@property (nonatomic, strong) UIButton *acceptBtn; // 接通按钮
@property (nonatomic, strong) UIView *separatorLiveView; // 分割线

@property (nonatomic, strong) NSIndexPath *indexPath;
@property (nonatomic, strong) XYLiveMultiChatApplyListItem *listItem;

@end

@implementation XYLiveMultiChatApplyListCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        // 创建UI
        [self setupSubviews];
    }
    return self;
}

/// 数据绑定
- (void)bindListItems:(NSArray<XYLiveMultiChatApplyListItem *> *)listItems indexPath:(NSIndexPath *)indexPath {
    XYLiveMultiChatApplyListItem *listItem = listItems[indexPath.row];
    // 序列号
    self.numLbl.text = [NSString stringWithFormat:@"%@", @(indexPath.row + 1)];
    // 更新头像
    [self.avatarImgView xy_setImageWithURL:[NSURL URLWithString:listItem.avatarURL ?: @""]];
    // 更新昵称
    self.nickNameLbl.text = listItem.nickName;
    // 更新副标题
    self.descLbl.text = listItem.labels.firstObject;
    // 更新标签
    [self.tagView updateItem:listItem];
    // 隐藏分割线
    self.separatorLiveView.hidden = (listItems.count - 1 == indexPath.row);
    // 接通按钮状态更新
    [self updateBtnStatusWithDisable:listItem.disable];
    self.listItem = listItem;
    self.indexPath = indexPath;
}

- (void)setupSubviews {
    // 创建接通按钮
    [self setupAcceptBtn];
    // 创建排队序号
    [self setupNumLbl];
    // 创建头像
    [self setupAvatarImgView];
    // 创建标题
    [self setupNickNameLbl];
    // 创建副标题
    [self setupDescLbl];
    // 创建标签
    [self setupTagView];
    // 分割线
    [self setupSeparatorLineView];
    
    // 布局
    [self.numLbl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.left.equalTo(self.contentView).offset(8);
        make.width.greaterThanOrEqualTo(@(24));
    }];
    
    [self.avatarImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(42);
        make.size.mas_equalTo(CGSizeMake(44, 44));
        make.centerY.equalTo(self.contentView);
    }];
    
    [self.nickNameLbl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarImgView.mas_right).offset(8);
        make.top.equalTo(self.avatarImgView.mas_top).offset(3);
    }];
    
    [self.tagView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nickNameLbl.mas_right).offset(4);
        make.centerY.equalTo(self.nickNameLbl);
        make.right.lessThanOrEqualTo(self.acceptBtn.mas_left).offset(-24);
    }];
    
    [self.descLbl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nickNameLbl);
        make.top.equalTo(self.nickNameLbl.mas_bottom).offset(4);
        make.right.equalTo(self.acceptBtn.mas_left).offset(-4);
    }];
    
    [self.acceptBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.right.equalTo(self.contentView).offset(-16);
        make.size.mas_equalTo(CGSizeMake(63, 28));
    }];
    
    [self.separatorLiveView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarImgView);
        make.bottom.right.equalTo(self.contentView);
        make.height.mas_equalTo(0.5);
    }];
}

#pragma mark - UI

// 创建序号视图
- (void)setupNumLbl {
    UILabel *numLbl = [[UILabel alloc] init];
    numLbl.font = [UIFont fontWithName:@"REDNumber-Bold" size:12];
    numLbl.textColor = XYLiveTokenColor.desc;
    numLbl.textAlignment = NSTextAlignmentCenter;
    [numLbl setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.contentView addSubview:numLbl];
    self.numLbl = numLbl;
}

// 创建头像视图
- (void)setupAvatarImgView {
    UIImageView *avatarImgView = [[UIImageView alloc] init];
    avatarImgView.layer.cornerRadius = 22;
    avatarImgView.layer.masksToBounds = YES;
    avatarImgView.layer.borderColor = [XYLiveTokenColor separator].CGColor;
    avatarImgView.layer.borderWidth = 0.5;
    avatarImgView.userInteractionEnabled = YES;
    avatarImgView.contentMode = UIViewContentModeScaleAspectFill;
    [self.contentView addSubview:avatarImgView];
    self.avatarImgView = avatarImgView;
}

// 创建昵称视图
- (void)setupNickNameLbl {
    UILabel *nickNameLbl = [[UILabel alloc] init];
    nickNameLbl.textColor = XYLiveTokenColor.title;
    nickNameLbl.font = [UIFont fontWithName:@"PingFangSC-Medium" size:14];
    [nickNameLbl setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [nickNameLbl setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    [self.contentView addSubview:nickNameLbl];
    self.nickNameLbl = nickNameLbl;
}

// 创建副标题视图
- (void)setupDescLbl {
    UILabel *descLbl = [[UILabel alloc] init];
    descLbl.textColor = XYLiveTokenColor.desc;
    descLbl.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
    [self.contentView addSubview:descLbl];
    self.descLbl = descLbl;
}

// 创建标签列表
- (void)setupTagView {
    XYLiveMultiChatApplyListTagView *tagView = [[XYLiveMultiChatApplyListTagView alloc] init];
    [self.contentView addSubview:tagView];
    self.tagView = tagView;
}

// 创建接通按钮
- (void)setupAcceptBtn {
    UIButton *acceptBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    acceptBtn.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:13];
    acceptBtn.xy_responseRectInset = UIEdgeInsetsMake(-5, -5, -5, -5);
    [acceptBtn setTitle:@"接通" forState:UIControlStateNormal];
    [acceptBtn setTitle:@"接通中" forState:UIControlStateDisabled];
    [acceptBtn setTitleColor:XYLiveTokenColor.primary forState:UIControlStateNormal];
    [acceptBtn setTitleColor:[XYLiveTokenColor.primary colorWithAlphaComponent:0.2] forState:UIControlStateDisabled];
    [acceptBtn addTarget:self action:@selector(didTapAccept:) forControlEvents:UIControlEventTouchUpInside];
    acceptBtn.layer.borderWidth = 0.5;
    acceptBtn.layer.borderColor = XYLiveTokenColor.primary.CGColor;
    acceptBtn.layer.cornerRadius = 14;
    acceptBtn.layer.masksToBounds = YES;
    [self.contentView addSubview:acceptBtn];
    self.acceptBtn = acceptBtn;
}

/// 创建分割线
- (void)setupSeparatorLineView {
    UIView *separatorLineView = [[UIView alloc] init];
    separatorLineView.backgroundColor = XYLiveTokenColor.separator;
    [self.contentView addSubview:separatorLineView];
    self.separatorLiveView = separatorLineView;
}

#pragma mark - Event

/// 接通按钮点击事件
- (void)didTapAccept:(UIButton *)sender {
    // 更新按钮状态
    [self updateBtnStatusWithDisable:YES];
    // 事件回调
    if (self.didTapAcceptHandler) {
        self.didTapAcceptHandler(self.listItem, self.indexPath);
    }
}

#pragma mark - Private

- (void)updateBtnStatusWithDisable:(BOOL)disable {
    self.acceptBtn.enabled = !disable;
    if (disable) {
        self.acceptBtn.layer.borderColor = [XYLiveTokenColor.primary colorWithAlphaComponent:0.2].CGColor;
    } else {
        self.acceptBtn.layer.borderColor = XYLiveTokenColor.primary.CGColor;
    }
}

@end
