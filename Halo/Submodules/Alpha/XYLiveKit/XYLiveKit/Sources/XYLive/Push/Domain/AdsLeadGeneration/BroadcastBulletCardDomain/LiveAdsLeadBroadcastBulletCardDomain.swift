//
//  LiveAdsLeadBroadcastBulletCardDomain.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/4/24.
//  Copyright © 2025 XingIn. All rights reserved.
//

import Foundation
import XYAlphaFusion
import XYLiveServiceProtocol
import XYCanvas
import XYAdsBase
import XYCommentUtils

public protocol LiveAdsLeadBroadcastBulletCardDependProtocol {
    // 用于获取直播间控制器
    func pushAdapterService() -> XYLivePushAdapterServiceProtocol?
    func imDistributerService() -> XYLiveIMDistributerServiceProtocol?
    func broadcastListService() -> LiveAdsBroadcastListPageProtocol?
}

@objcMembers
@objc(XYLiveAdsLeadBroadcastBulletCardDomain)
public class LiveAdsLeadBroadcastBulletCardDomain: LiveBaseDomain <LiveAdsLeadBroadcastBulletCardDependProtocol>, LiveAdsBroadcastBulletCardProtocol, PushLeadsBulletManagerProtocol {
    public func isDSLContainerViewShown() -> Bool {
        return self.dslContainer.isHidden == false
    }
    private var cardModel: AdsLeadBindCardModel?
    private lazy var countUpProcessor = LiveAdsLeadBroadcastBulletCardCountUpProcessor()
    private lazy var countDownProcessor = LiveAdsLeadBroadcastBulletCardCountDownProcessor() { [weak self] in
        guard var dataSource = self?.cardModel?.xy_modelToJSONObject() as? [String: Any] else {
            return
        }
        dataSource["popUping"] = false
        self?.viewNode?.reloadDslComponent(dataSource)
        self?.viewNode?.layoutIfNeed()
    }
    var dataSource: [String: Any]?
    var dslEngine: XYCDslRenderEngine?
    public var viewNode: XYCViewNode?
    public var dslView: UIView?
    private var explainTime: Int64?
    
    private lazy var dslContainer = {
        let dslContainView = UIView()
        dslContainView.isHidden = true
        return dslContainView
    }()
    
    
    private lazy var decorateView = self.depend?.pushAdapterService()?.decorateView()
    private lazy var bulletManager = PushLeadsBulletManager.shared
    private lazy var serviceModel = AdsFetchLeadCardInfoServiceModel()
    
    // 注册服务
    public override func doRegisterService(registery: any ServiceRegistery) {
        registery.registerService(protocol: LiveAdsBroadcastBulletCardProtocol.self, service: self)
    }
    
    // 声明依赖服务
    public override func doInjectService(provider: any ServiceProvider) -> any LiveAdsLeadBroadcastBulletCardDependProtocol {
        @objcMembers
        class LiveAdsLeadBroadcastPageDependProtocolDepend: NSObject, LiveAdsLeadBroadcastBulletCardDependProtocol {
            
            weak var serviceProvider: ServiceProvider?
            
            init(serviceProvider: ServiceProvider) {
                self.serviceProvider = serviceProvider
            }
            
            func pushAdapterService() -> XYLivePushAdapterServiceProtocol? {
                return self.serviceProvider?.getService(XYLivePushAdapterServiceProtocol.self)
            }
            
            func imDistributerService() -> XYLiveIMDistributerServiceProtocol? {
                return self.serviceProvider?.getService(XYLiveIMDistributerServiceProtocol.self)
            }
            
            func broadcastListService() -> LiveAdsBroadcastListPageProtocol? {
                return self.serviceProvider?.getService(LiveAdsBroadcastListPageProtocol.self)
            }
        }
        
        return LiveAdsLeadBroadcastPageDependProtocolDepend(serviceProvider: provider)
    }
    
    public override class func enableDomain() -> Bool {
        return !ConfigCenter.shared.bool(forKey: "ios_live_leads_ads_bulletcard_domain_forbidden", defalut: false)
    }
    
    public override func didLoad() {
        super.didLoad()
        // 初始化的时候没有正在弹卡或者正在讲解中的卡片
        clearCardItemInBulletManager()
        initDSL()
        registerIM()
        registerBulletListener()
        fetchCardInfo()
    }
    
    // 退出直播间，重置 manager
    public override func didDestory() {
        super.didDestory()
        // 消失的时候要清空卡片
        clearCardItemInBulletManager()
    }
    
    private func clearCardItemInBulletManager() {
        self.bulletManager.currentCardItem = nil
    }
    
    private func fetchCardInfo() -> Void {
        
        guard !ConfigCenter.shared.bool(forKey: "ios_live_leads_ads_fetch_forbidden", defalut: false) else {
            return
        }
        
        self.serviceModel.requestAdsLeadCardInfo(roomId: Int64(XYLiveManager.shared().roomInfo.roomID)) { isSuccess, cardInfo in
            guard isSuccess, let cardInfo = cardInfo else {
                return
            }
            self.cardModel = AdsLeadBindCardModel.xy_model(with: cardInfo)
            self.bulletManager.currentCardItem = self.cardModel
            // 拉取到的一定是讲解中的卡片，且倒计时 15s
            self.bulletManager.currentCardItem?.popUping = true
            self.bulletManager.currentCardItem?.explaining = true
            self.showCard(cardInfo, nil)
        }
    }
    
    private func registerBulletListener() {
        guard !ConfigCenter.shared.bool(forKey: "ios_live_leads_ads_register_forbidden", defalut: false) else {
            return
        }
        self.bulletManager.registerListenerIfNeeded(listener: self)
    }
    
    private func registerIM() {
        
        guard !ConfigCenter.shared.bool(forKey: "ios_live_leads_ads_im_forbidden", defalut: false) else {
            return
        }
        
        self.depend?.imDistributerService()?.im_register(withType: XYIM_CUSTOM_LIVE_LEADS_CARD, key: NSStringFromClass(type(of: self)), completionHandler: {[weak self] type, info, rawData in
            guard let self = self else {
                return
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self.dealWithBulletCardEvent(type: type, rawData: rawData)
            }
        })
    }
    
    private func dealWithBulletCardEvent(type: String, rawData: [String: Any]) {
        
        guard type == "ads_leads_card" else {
            return
        }
        
        guard let cardInfoMap = rawData["leads_card"] as? [String: Any], let newCardModel = AdsLeadBindCardModel.xy_model(with: cardInfoMap) else {
            return
        }
        
        XYExecuteOnMainQueue { [weak self] in
            self?.bulletCardProcess(cardInfoMap, withNew: newCardModel)
        }
    }
    
    private func bulletCardProcess(_ cardInfoMap: [String: Any], withNew newCardModel : AdsLeadBindCardModel) {
        // 没有弹卡就直接弹出
        guard let currentCardModel = self.cardModel else {
            self.cardModel = newCardModel
            self.showCard(cardInfoMap) {}
            return
        }
        
        // 同一张卡片，弹卡更新讲解中的卡片倒计时状态
        if currentCardModel.cardId == newCardModel.cardId,
           currentCardModel.explaining == true {
            // 重新渲染视图
            var cardInfoMapTmp = cardInfoMap
            cardInfoMapTmp["popUping"] = true
            self.cardModel?.popUping = true
            self.viewNode?.reloadDslComponent(cardInfoMapTmp)
            self.viewNode?.layoutIfNeed()
            self.countDownProcessor.start(node: self.viewNode)
            return
        }
        
        // 弹卡顶弹卡或者讲解顶弹卡
        if newCardModel.priority <= currentCardModel.priority {
            self.dismissCard { [weak self] in
                self?.cardModel = newCardModel
                self?.showCard(cardInfoMap) { }
            }
            return
        }
    }
    
    // MARK: - Animation
    public func showCard(_ datamMap: [String: Any], _ completion: (() -> Void)?) {
        // 弹卡状态一定为 ture
        var dataSource = datamMap
        dataSource["popUping"] = true
        // 重置 dsl 画布
        self.dslContainer.layer.removeAllAnimations()
        self.dslContainer.removeAllSubviews()
        self.dslContainer.removeFromSuperview()
        buildDSLAdsLeadCard(dataSource)
        
        guard let containerView = self.decorateView?.businessContainer(),
              let leadButtom = self.decorateView?.bottomView.getAdsLeadsBtn(),
              let dslView = self.dslView else {
            return
        }
        // 添加 dsl 容器视图
        containerView.addSubview(dslContainer)
        dslContainer.addSubview(dslView)
        
        let vcWidth = containerView.xy_width
        let cardWidth = dslView.xy_width
        let cardHeight = dslView.xy_height
        let fromPoint = containerView.convert(leadButtom.center, from: leadButtom.superview)
        let toPoint = CGPoint(x: vcWidth - 12 - cardWidth / 2.0, y: bottomRightCardMaxY() - cardHeight / 2.0)
        let animation = showAnimation(from: fromPoint, toPoint: toPoint)
        dslContainer.isHidden = false
        dslContainer.frame = CGRect(x: vcWidth - 12 - cardWidth, y: bottomRightCardMaxY() - cardHeight, width: cardWidth, height: cardHeight)
        beginAnimationBlock(animation) {
            completion?()
        }
        // 评论区文本框布局
        self.decorateView?.layoutLeftBottom()
        self.track79455(self.cardModel?.cardId ?? "")
        // 开始计时
        self.countUpProcessor.start(node: self.viewNode, explainSeconds: Int64(self.cardModel?.explainSeconds ?? 0))
        self.countDownProcessor.start(node: self.viewNode)
    }
    
    func beginAnimationBlock(_ animation: CAAnimation, completion: (() -> Void)?) {
        CATransaction.begin()
        CATransaction.setCompletionBlock(completion)
        dslContainer.layer.add(animation, forKey: nil)
        CATransaction.commit()
    }
    
    func bottomRightCardMaxY() -> CGFloat {
        let screenHeight = self.hostViewController().view.bounds.size.height
        let bottomY = self.decorateView?.bottomRightCardMaxY() ?? (screenHeight - XYLiveBottomGradientLayerHeight - CommentUtils.safeAreaBottom - 8.0)
        
        return bottomY
    }
    
    func showAnimation(from: CGPoint, toPoint: CGPoint) -> CAAnimationGroup {
        let duration = 0.25
        let positionAnimation = CABasicAnimation(keyPath: "position")
        positionAnimation.fromValue = NSValue(cgPoint: from)
        positionAnimation.toValue = NSValue(cgPoint: toPoint)
        positionAnimation.duration = duration
        positionAnimation.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        positionAnimation.isRemovedOnCompletion = false
        positionAnimation.fillMode = .forwards
        
        let scale = CABasicAnimation(keyPath: "transform.scale")
        scale.fromValue = 0.01
        scale.toValue = 1.0
        scale.duration = duration
        scale.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        scale.isRemovedOnCompletion = false
        scale.fillMode = .forwards
        
        let opacity = CABasicAnimation(keyPath: "opacity")
        opacity.fromValue = 0.0
        opacity.toValue = 1.0
        opacity.duration = duration
        opacity.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        opacity.isRemovedOnCompletion = false
        opacity.fillMode = .forwards
        
        let groupAnimation = CAAnimationGroup()
        groupAnimation.animations = [positionAnimation, scale, opacity]
        groupAnimation.duration = duration
        groupAnimation.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        groupAnimation.isRemovedOnCompletion = false
        groupAnimation.fillMode = .forwards
        return groupAnimation
    }
    
    
    func initDSL() {
        guard !ConfigCenter.shared.bool(forKey: "ios_live_leads_ads_dsl_forbidden", defalut: false) else {
            return
        }
        let config = XYCEngineConfig().enableRenderOpt2456(true)
        dslEngine = XYCDslRenderEngine(bizName: "advert", config: config)
        registeCardCustomAction()
    }
    
    func registeCardCustomAction() {
        
        // 重新弹卡
        let cardBulletAction: XYCViewNode.actionClosureNew = { [weak self] viewNode, gesture, userInfo in
            guard let self = self else {
                return
            }
            
            guard var cardInfoMap = self.cardModel?.xy_modelToJSONObject() as? [String: Any] else {
                return
            }
            
            cardInfoMap["popUping"] = true
            
            self.viewNode?.reloadDslComponent(cardInfoMap)
            self.viewNode?.layoutIfNeed()
            
            self.bulletManager.startExplainAdsLeads(leadsCardItem: self.cardModel)
            self.countDownProcessor.start(node: self.viewNode)
            self.track79456(self.cardModel?.cardId ?? "", channelName: "2")
        }
        
        // 结束讲解
        let cardStopExplainAction: XYCViewNode.actionClosureNew = { [weak self] viewNode, gesture, userInfo in
            guard let self = self else {
                return
            }
            
            bulletManager.stopExplainAdsLeads(leadsCardItem: self.cardModel)
            self.track79456(self.cardModel?.cardId ?? "", channelName: "1")
        }
        
        dslEngine?.registerActionPool(["card_send" : cardBulletAction,
                                       "explaining_finish": cardStopExplainAction])
    }
    
    func dismissCard(_ completion: (() -> Void)?) {
        guard let containerView = self.decorateView?.businessContainer(),
              let leadButtom = self.decorateView?.bottomView.getAdsLeadsBtn(),
              let _ = self.dslView else {
            return
        }
        
        let toPoint = containerView.convert(leadButtom.center, from: leadButtom.superview)
        let fromPoint = dslContainer.center
        
        let animation = dismissAnimation(from: fromPoint, toPoint: toPoint)
        
        beginAnimationBlock(animation) { [weak self] in
            self?.dslContainer.layer.removeAllAnimations()
            self?.dslContainer.removeAllSubviews()
            self?.dslContainer.removeFromSuperview()
            self?.dslContainer.isHidden = true
            self?.decorateView?.layoutLeftBottom()
            completion?()
        }
    }
    
    func dismissAnimation(from: CGPoint, toPoint: CGPoint) -> CAAnimationGroup {
        let duration = 0.25
        let positionAnimation = CABasicAnimation(keyPath: "position")
        positionAnimation.fromValue = NSValue(cgPoint: from)
        positionAnimation.toValue = NSValue(cgPoint: toPoint)
        positionAnimation.duration = duration
        positionAnimation.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        positionAnimation.isRemovedOnCompletion = false
        positionAnimation.fillMode = .forwards
        
        let scale = CABasicAnimation(keyPath: "transform.scale")
        scale.fromValue = 1.0
        scale.toValue = 0.0
        scale.duration = duration
        scale.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        scale.isRemovedOnCompletion = false
        scale.fillMode = .forwards
        
        let opacity = CABasicAnimation(keyPath: "opacity")
        opacity.fromValue = 1.0
        opacity.toValue = 0.0
        opacity.duration = duration
        opacity.isRemovedOnCompletion = false
        opacity.fillMode = .forwards
        
        let groupAnimation = CAAnimationGroup()
        groupAnimation.animations = [positionAnimation, scale, opacity]
        groupAnimation.duration = duration
        groupAnimation.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        groupAnimation.isRemovedOnCompletion = false
        groupAnimation.fillMode = .forwards
        return groupAnimation
    }
    
    // MARK: - DSL
    public func buildDSLAdsLeadCard(_ dataSource: [String: Any]) {
        guard let dslEngine = dslEngine else {
            return
        }
        self.dataSource = dataSource
        let templateName = "ads_live_room_explanation_card"
        //        let localPath = AdsDSLManager.localTemplateZipPath(templateName) ?? ""
        let localPath = ""
        let templateInfo = DslTemplateInfo(name: templateName,
                                           presetFilePath: localPath,
                                           downgradeStrategy: .presetWithZip,
                                           preloadStrategy: .none)
        
        guard let template = XYCTemplateManager.shared.loadTemplate(templateInfo),
              let path = template.templatePath() else {
            return
        }
        
        let buildWidth = DeviceUtility.screenWidth
        let renderSpec = DslRenderSpec(widthSpec: DslMakeMeasureSpec(pt: CGFloat(buildWidth), DslMeasureSpec.EXACTLY),
                                       heightSpec: DslMakeMeasureSpec(pt: 0, DslMeasureSpec.UNSPECIFIED))
        let dslRootView = dslEngine.renderViewViaKITT(templateName: templateName,
                                                      templateVersion: nil,
                                                      templatePath: path,
                                                      dataDict: dataSource,
                                                      dataIdentifier: nil,
                                                      view: nil,
                                                      renderSpec: renderSpec,
                                                      service: self)
        
        viewNode = dslRootView?.viewNode
        dslView = dslRootView
    }
    
    func onFinishTime(model: AdsLeadBindCardModel, shouldReload: Bool) {
        self.dismissCard(nil)
        self.cardModel = nil
    }
    
    func onUpdateTime(model: AdsLeadBindCardModel, shouldReload: Bool) {}
    
    func onReloadDataTime() {}
}

extension LiveAdsLeadBroadcastBulletCardDomain: XYCViewNodeService, XYCTrackerProtocol { // 协议方法
    // XYCViewNodeService
    public func trackService() -> (any XYCanvas.XYCTrackerProtocol)? {
        self
    }
    
    public func track(viewNode: XYCViewNode?, tracker: XYAnalyticsOrganizer, extra: String?) {
        tracker.track(withExtension: extra)
    }
    
    public func dslContextTrack() -> XYAnalyticsOrganizer? {
        guard let org = self.dslView?.xy_viewController as? XYAnalyticsOrganizerDataSource else {
            return nil
        }
        return XYAnalyticsOrganizer.__(org)
    }
}

extension LiveAdsLeadBroadcastBulletCardDomain {
    // Track
    func baseTrackContext() -> XYTrackerEventContext {
        let noteID = XYLiveAnalyticsDataSource.sharedInstance().sourceNoteID
        let noteTrackID = XYLiveAnalyticsDataSource.sharedInstance().noteTrackID
        let preSource = XYLiveAnalyticsDataSource.sharedInstance().preSource
        let liveType = "Interaction"
        
        return XYLiveTrackBasePage.sharedInstance().hostAnalyticsOrganizer()
            .note
            .noteId(noteID)
            .trackId(noteTrackID)
            .page
            .pageInstance(.liveBroadcastPage)
            .live
            .liveType(liveType)
            .preSource(preSource)
            .event
    }
    
    func track79455(_ cardId: String) {
        baseTrackContext()
            .event
            .action(.impression)
            .pointId(79455 /* 直播开播页/B-商卡曝光/曝光 */)
            .isGoto(2)
            .moduleId(47084)
            .ads
            .cardId(cardId).send()
    }
    
    func track79456(_ cardId: String, channelName: String) {
        baseTrackContext()
            .index
            .channelTabName(channelName)
            .event
            .action(.click)
            .pointId(79456 /* 直播开播页/B-商卡点击/点击 */)
            .isGoto(1)
            .moduleId(47085)
            .ads
            .cardId(cardId).send()
    }
}


