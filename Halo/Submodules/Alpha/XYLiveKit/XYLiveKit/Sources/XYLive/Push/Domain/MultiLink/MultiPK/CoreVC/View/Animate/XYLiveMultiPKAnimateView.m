//
//  XYLiveMultiPKAnimateView.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/14.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiPKAnimateView.h"
#import <XYLiveFoundation/UIImage+XYLiveKit.h>
#import <Masonry/Masonry.h>

// 动画标识
static NSString * const kXYLiveMultiPKAnimateViewIdentifier = @"XYLiveMultiPKAnimateViewIdentifier";
// 大模式下高
static CGFloat const kXYLiveMultiPKAnimationViewBH = 60;
// 小模式下高
static CGFloat const kXYLiveMultiPKAnimationViewSH = 30;

@interface XYLiveMultiPKAnimateView()

@property (nonatomic, strong) UIImageView *leftImgView;
@property (nonatomic, strong) UIImageView *rightImgView;
@property (nonatomic, strong) UIImageView *middleImgView;

@end

@implementation XYLiveMultiPKAnimateView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

- (void)dealloc {
    [_leftImgView.layer removeAllAnimations];
    [_rightImgView.layer removeAllAnimations];
    [_middleImgView.layer removeAllAnimations];
}

// 更新视图状态
- (void)updateViewStatusWithResult:(XYLiveMultiPKResult)result {
    // 合法性校验
    if (result == XYLiveMultiPKResultUnknown) { return; }
    
    // 最终动画视图宽度
    CGFloat animationViewW = kXYLiveMultiPKAnimationViewSH * 2;
    CGFloat animationViewH = kXYLiveMultiPKAnimationViewSH;
    CGFloat topY = 61;
    
    // 更新图标
    self.leftImgView.image = result == XYLiveMultiPKResultWin ? [UIImage xy_liveKitBundleImage:@"xyLivePKVictory"] : [UIImage xy_liveKitBundleImage:@"xyLivePKFail"];
    self.rightImgView.image = result == XYLiveMultiPKResultWin ? [UIImage xy_liveKitBundleImage:@"xyLivePKFail"] : [UIImage xy_liveKitBundleImage:@"xyLivePKVictory"];
    // 更新状态
    self.leftImgView.hidden = result == XYLiveMultiPKResultDraw;
    self.rightImgView.hidden = result == XYLiveMultiPKResultDraw;
    self.middleImgView.hidden = result != XYLiveMultiPKResultDraw;
    
    // 强刷布局
    [self layoutIfNeeded];
    [CATransaction begin];
    // 执行平局动画
    if (result == XYLiveMultiPKResultDraw) {
        CGPoint offset = CGPointMake(CGRectGetWidth(self.bounds) * 0.5, topY + animationViewH * 0.5);
        [self startAnimationWithView:self.middleImgView offset:offset];
    } else {
        // 执行左侧动画
        CGPoint leftOffset = CGPointMake(4 + animationViewW * 0.5 , topY + animationViewH * 0.5);
        [self startAnimationWithView:self.leftImgView offset:leftOffset];
        // 执行右侧动画
        CGPoint rightOffset = CGPointMake(CGRectGetWidth(self.bounds) - animationViewW * 0.5 - 4, topY + animationViewH * 0.5);
        [self startAnimationWithView:self.rightImgView offset:rightOffset];
    }
    [CATransaction commit];
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 创建左侧视图
    [self setupLeftImgView];
    // 创建右侧视图
    [self setupRightImgView];
    // 创建中间视图
    [self setupMiddleImgView];
    
    // 布局
    [self.leftImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self).multipliedBy(0.5);
        make.size.mas_equalTo(CGSizeMake(kXYLiveMultiPKAnimationViewBH * 2, kXYLiveMultiPKAnimationViewBH));
        make.bottom.equalTo(self).offset(-67);
    }];
    
    [self.rightImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self).multipliedBy(1.5);
        make.size.mas_equalTo(CGSizeMake(kXYLiveMultiPKAnimationViewBH * 2, kXYLiveMultiPKAnimationViewBH));
        make.bottom.equalTo(self).offset(-67);
    }];
    
    [self.middleImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self);
        make.size.mas_equalTo(CGSizeMake(kXYLiveMultiPKAnimationViewBH * 2, kXYLiveMultiPKAnimationViewBH));
        make.bottom.equalTo(self).offset(-67);
    }];
}

- (void)setupLeftImgView {
    UIImageView *leftImgView = [[UIImageView alloc] init];
    leftImgView.image = [UIImage xy_liveKitBundleImage:@"xyLivePKVictory"];
    leftImgView.contentMode = UIViewContentModeScaleAspectFit;
    leftImgView.hidden = YES;
    [self addSubview:leftImgView];
    self.leftImgView = leftImgView;
}

- (void)setupRightImgView {
    UIImageView *rightImgView = [[UIImageView alloc] init];
    rightImgView.image = [UIImage xy_liveKitBundleImage:@"xyLivePKFail"];
    rightImgView.contentMode = UIViewContentModeScaleAspectFit;
    rightImgView.hidden = YES;
    [self addSubview:rightImgView];
    self.rightImgView = rightImgView;
}

- (void)setupMiddleImgView {
    UIImageView *middleView = [[UIImageView alloc] init];
    middleView.image = [UIImage xy_liveKitBundleImage:@"xyLivePKDraw"];
    middleView.contentMode = UIViewContentModeScaleAspectFit;
    middleView.hidden = YES;
    [self addSubview:middleView];
    self.middleImgView = middleView;
}

#pragma mark - Private

- (void)startAnimationWithView:(UIView *)view offset:(CGPoint)offset {
    // 创建缩放动画
    CAKeyframeAnimation *scaleAnimation = [CAKeyframeAnimation animationWithKeyPath:@"transform.scale"];
    CGFloat duration = 3.5;
    scaleAnimation.duration = duration;
    scaleAnimation.values = @[@(0), @(1.2), @(1), @(1), @(0), @(0), @(0.6), @(0.5)];
    scaleAnimation.keyTimes = @[@(0), @(0.4 / duration), @(0.6 / duration), @(2.6 / duration), @(2.8 / duration), @(2.9 / duration), @(3.3 / duration), @(1)];
    scaleAnimation.fillMode = kCAFillModeForwards;
    scaleAnimation.removedOnCompletion = NO;
    scaleAnimation.timingFunctions = @[[CAMediaTimingFunction functionWithControlPoints:1 :0 :0.5 :1],
                                       [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseOut],
                                       [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear],
                                       [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear],
                                       [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear],
                                       [CAMediaTimingFunction functionWithControlPoints:1 :0 :0.5 :1],
                                       [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseOut]];
    
    // 创建透明度动画
    CABasicAnimation *opacityAnimation = [CABasicAnimation animationWithKeyPath:@"opacity"];
    opacityAnimation.beginTime = 2.6;
    opacityAnimation.fromValue = @1;
    opacityAnimation.toValue = @0;
    opacityAnimation.duration = 0.2;
    opacityAnimation.removedOnCompletion = YES;
    opacityAnimation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut];
    
    // 创建位移动画
    CABasicAnimation *positionAnimation = [CABasicAnimation animationWithKeyPath:@"position"];
    positionAnimation.beginTime = 2.8;
    positionAnimation.duration = 0.1;
    positionAnimation.removedOnCompletion = NO;
    positionAnimation.fillMode = kCAFillModeForwards;
    positionAnimation.toValue = [NSValue valueWithCGPoint:offset];
    
    // 创建动画组
    CAAnimationGroup *group = [CAAnimationGroup animation];
    group.duration = 3.5;
    group.removedOnCompletion = NO;
    group.fillMode = kCAFillModeForwards;
    group.animations = @[scaleAnimation, opacityAnimation, positionAnimation];
    [view.layer addAnimation:group forKey:kXYLiveMultiPKAnimateViewIdentifier];
}

@end
