//
//  XYLiveLinkHostInviteListHeaderView.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/19.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostInviteListHeaderView.h"
#import <XYWebImage/UIImageView+XYWebImage.h>
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveFoundation/UIImage+XYLiveKit.h>
#import <XYLiveKit/XYLiveLinkHostInvitePanelConsts.h>
#import <Masonry/Masonry.h>

@implementation XYLiveLinkHostInviteListRandomItemView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
        // 添加手势
        UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(didTap:)];
        [self addGestureRecognizer:tapGesture];
    }
    return self;
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 创建背景图
    [self setupBgImgView];
    // 创建标题
    [self setupTitleLabel];
    // 创建图标
    [self setupIconImgView];
}

- (void)setupBgImgView {
    UIImageView *bgImgView = [[UIImageView alloc] init];
    bgImgView.userInteractionEnabled = YES;
    [self addSubview:bgImgView];
    self.bgImgView = bgImgView;
}

- (void)setupTitleLabel {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:14];
    titleLabel.textColor = [XYLiveTokenColor neutralBlack];
    titleLabel.userInteractionEnabled = YES;
    [self addSubview:titleLabel];
    self.titleLabel = titleLabel;
}

- (void)setupIconImgView {
    UIImageView *iconImgView = [[UIImageView alloc] init];
    iconImgView.contentMode = UIViewContentModeScaleAspectFit;
    iconImgView.userInteractionEnabled = YES;
    [self addSubview:iconImgView];
    self.iconImgView = iconImgView;
}

#pragma mark - Event

- (void)didTap:(UITapGestureRecognizer *)tapGesture {
    self.didTapHandler ? self.didTapHandler() : nil;
}

@end

@implementation XYLiveLinkHostInviteListLineRandomItemView

- (void)setupSubviews {
    [super setupSubviews];
    // 配置图片资源
    UIImage *bgImg = [UIImage xy_liveKitBundleImage:@"link_random_line_bg"];
    self.bgImgView.image = [bgImg resizableImageWithCapInsets:UIEdgeInsetsMake(bgImg.size.height * 0.5, bgImg.size.width * 0.5, bgImg.size.height * 0.5, bgImg.size.width * 0.5)];
    [self.iconImgView xy_setImageWithURL:[NSURL URLWithString:@"https://fe-platform.xhscdn.com/platform/104101l031itc60sv1a06crss5gt00000000000i37iapg"]];
    self.titleLabel.text = @"随机连线";
    
    // 布局
    [self.bgImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(14);
        make.bottom.mas_equalTo(-14);
        make.left.right.equalTo(self);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.left.mas_equalTo(20);
    }];
    
    [self.iconImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self).offset(-14);
        make.right.equalTo(self).offset(-12);
        make.size.mas_equalTo(CGSizeMake(66, 66));
    }];
}

@end

@implementation XYLiveLinkHostInviteListPKRandomItemView

- (void)setupSubviews {
    [super setupSubviews];
    // 配置图片资源
    UIImage *bgImg = [UIImage xy_liveKitBundleImage:@"link_random_pk_bg"];
    self.bgImgView.image = [bgImg resizableImageWithCapInsets:UIEdgeInsetsMake(bgImg.size.height * 0.5, bgImg.size.width * 0.5, bgImg.size.height * 0.5, bgImg.size.width * 0.5)];
    [self.iconImgView xy_setImageWithURL:[NSURL URLWithString:@"https://fe-platform.xhscdn.com/platform/104101l031itcbf7dhe06crss5gt00000000000h31fgg8"]];
    self.titleLabel.text = @"随机PK";
    
    // 布局
    [self.bgImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.mas_equalTo(14);
        make.bottom.mas_equalTo(-14);
        make.left.right.equalTo(self);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self);
        make.left.mas_equalTo(28);
    }];
    
    [self.iconImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self).offset(-14);
        make.right.equalTo(self).offset(-2);
        make.size.mas_equalTo(CGSizeMake(66, 66));
    }];
}

@end

@interface XYLiveLinkHostInviteListHeaderView()

@property (nonatomic, strong) XYLiveLinkHostInviteListLineRandomItemView *lineItemView;
@property (nonatomic, strong) XYLiveLinkHostInviteListPKRandomItemView *pkItemView;

@end

@implementation XYLiveLinkHostInviteListHeaderView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    // 创建左侧视图
    [self setupLineItemView];
    // 创建右侧视图
    [self setupPKItemView];
    
    // 布局
    [@[self.lineItemView, self.pkItemView] mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.bottom.equalTo(self);
    }];
    
    [@[self.lineItemView, self.pkItemView] mas_distributeViewsAlongAxis:MASAxisTypeHorizontal withFixedSpacing:-5 leadSpacing:kXYLiveLinkHostPanelPadding tailSpacing:kXYLiveLinkHostPanelPadding];
}

- (void)setupLineItemView {
    XYLiveLinkHostInviteListLineRandomItemView *lineItemView = [[XYLiveLinkHostInviteListLineRandomItemView alloc] init];
    WS
    lineItemView.didTapHandler = ^{
        SS
        [self didTapMatch:NO];
    };
    [self addSubview:lineItemView];
    self.lineItemView = lineItemView;
}

- (void)setupPKItemView {
    XYLiveLinkHostInviteListPKRandomItemView *pkItemView = [[XYLiveLinkHostInviteListPKRandomItemView alloc] init];
    WS
    pkItemView.didTapHandler = ^{
        SS
        [self didTapMatch:YES];
    };
    [self addSubview:pkItemView];
    self.pkItemView = pkItemView;
}

#pragma mark - Event

- (void)didTapMatch:(BOOL)isPK {
    self.didTapMatchHandler ? self.didTapMatchHandler(isPK) : nil;
}

@end
