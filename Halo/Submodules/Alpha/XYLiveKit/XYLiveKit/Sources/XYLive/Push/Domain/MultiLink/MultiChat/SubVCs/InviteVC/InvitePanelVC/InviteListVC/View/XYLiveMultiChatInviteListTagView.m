//
//  XYLiveMultiChatInviteListTagView.m
//  XYLiveKit
//
//  Created by 大远 on 2024/11/3.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatInviteListTagView.h"
#import <XYLiveKit/XYLiveMultiLinkBizUtil.h>
#import <XYUIKit/NSString+LineSpacingFix.h>
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <Masonry/Masonry.h>
#import <XYLiveFoundation/UIImage+XYLiveKit.h>
@import XYUIKit;
@import XYUITheme;

@interface XYLiveMultiChatInviteListTagView()

@property (nonatomic, strong) UILabel *relationLabel; // 关系标签

@property (nonatomic, strong) UIImageView *fansgroupIcon; // 粉丝团标签

@end

@implementation XYLiveMultiChatInviteListTagView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

- (void)setupSubviews {
    [self setupFansGroupIcon];
    // 创建关注关系视图
    [self setupRelationLabel];
    

    // 布局
    [self.fansgroupIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(0);
        make.top.bottom.equalTo(self);
        make.width.mas_equalTo(0);
        make.height.mas_equalTo(18);
    }];
    
    // 布局
    [self.relationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.fansgroupIcon);
        make.top.bottom.equalTo(self);
        make.width.mas_equalTo(0);
        make.right.mas_equalTo(0);
        make.height.mas_equalTo(18);
    }];
}

/// 更新数据源
- (void)updateItem:(XYLiveMultiChatInviteListAudienceItem *)item {
    self.fansgroupIcon.hidden = YES;
    self.relationLabel.hidden = YES;
    
    // 更新布局
    CGFloat relationTextWidth = 0;
    CGFloat fansGroupImgWidth = 0;
    if (item.fansClubInfo.activeFans && item.fansClubInfo.level > 0) {
        NSInteger level = MIN(item.fansClubInfo.level, 30);
        NSString *levelIcon = [NSString stringWithFormat:@"xyLiveFansNameplateLevel%ld", level];
        if (item.fansClubInfo.isShoppingClub) {
            level = MIN(level, 20);
            levelIcon = [NSString stringWithFormat:@"xyLiveShoppingFansNameplateLevel%ld", level];
        }
        self.fansgroupIcon.image = [UIImage xy_liveKitBundleImage:levelIcon];
        fansGroupImgWidth = 23;
        self.fansgroupIcon.hidden = NO;
    } else {
        // 关注关系
        NSString *relationText = [XYLiveUserInfo relationTextForHostWithFollowStatus:item.followStatus];
        self.relationLabel.text = relationText;
        
        if (relationText.length) {
            relationTextWidth = [relationText boundingRectWithSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX) font:self.relationLabel.font lineSpacing:0].width + 12;
        }
        self.relationLabel.hidden = NO;
    }
    
    [self.fansgroupIcon mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(fansGroupImgWidth);
    }];
    
    [self.relationLabel mas_updateConstraints:^(MASConstraintMaker *make) {
        make.width.mas_equalTo(relationTextWidth);
    }];
}

#pragma mark - UI

// 粉丝团icon
- (void)setupFansGroupIcon {
    UIImageView *fansGroupImgView = [[UIImageView alloc] init];
    [self addSubview:fansGroupImgView];
    self.fansgroupIcon = fansGroupImgView;
}

- (void)setupRelationLabel {
    UILabel *relationLabel = [[UILabel alloc] init];
    relationLabel.font = [UIFont fontWithName:@"PingFangSC-Regular" size:10];
    relationLabel.textColor = [[UIColor xy_hexWithString:@"#3E3E3E"] colorByAddingDarkColor:XYUIColor.alwaysWhite];
    relationLabel.textAlignment = NSTextAlignmentCenter;
    relationLabel.backgroundColor = [[UIColor xy_hexWithString:@"#F5F5F5"] colorByAddingDarkColor:[[UIColor xy_hexWithString:@"#FFFFFF"] colorWithAlphaComponent:0.3]];
    relationLabel.layer.cornerRadius = 9;
    relationLabel.layer.masksToBounds = YES;
    [self addSubview:relationLabel];
    self.relationLabel = relationLabel;
}

@end
