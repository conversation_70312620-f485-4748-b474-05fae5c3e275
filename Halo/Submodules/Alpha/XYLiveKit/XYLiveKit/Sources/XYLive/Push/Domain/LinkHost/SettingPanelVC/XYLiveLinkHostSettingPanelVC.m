//
//  XYLiveLinkHostSettingPanelVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/20.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostSettingPanelVC.h"
#import <XYLiveKit/XYLiveAlertViewController.h>
#import <XYLiveKit/XYLiveLinkHostBizTracker.h>

@interface XYLiveLinkHostSettingPanelVC()

@property (nonatomic, weak) id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak) id<XYLivePushMultiPKServiceProtocol> multiPKService;
@property (nonatomic, weak) XYLiveLinkHostViewModel *viewModel;
@property (nonatomic, weak) XYLiveAlertViewController *alertVC;

@end

@implementation XYLiveLinkHostSettingPanelVC

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                     multiPKService:(id<XYLivePushMultiPKServiceProtocol>)multiPKService
                          viewModel:(XYLiveLinkHostViewModel *)viewModel {
    if (self = [super initWithContainerVC:containerVC]) {
        _liveInfoService = liveInfoService;
        _multiPKService = multiPKService;
        _viewModel = viewModel;
    }
    return self;
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    // 关闭弹窗
    [self.alertVC dismissWithAnimated:YES complete:nil];
}

#pragma mark - Override

- (NSString *)XYLiveNav_title {
    return @"主播连线设置";
}

- (CGFloat)heightWhenPortrait {
    return kXYLiveLinkHostSettingPanelH;
}

- (BOOL)needReachBottom {
    return YES;
}

- (void)refreshWithType:(XYLiveRefreshDataType)type completion:(void (^)(NSError * _Nonnull))completion {
    WS
    [self.viewModel loadInitConfigWithCompletion:^(XYLiveLinkHostConfigModel * _Nonnull configModel, NSError * _Nonnull error) {
        SS
        if (error == nil) {
            // 配置数据源
            [self configDataSource];
            // 刷新列表
            [self.tableView reloadData];
        }
        completion ? completion(error) : nil;
    }];
}

#pragma mark - Private

// 配置数据源
- (void)configDataSource {
    // 获取基础配置
    XYLiveLinkHostConfigModel *configModel = self.viewModel.configModel;
    // 连线布局
    XYLiveMultiLinkListLayoutItemV2 *item0 = [[XYLiveMultiLinkListLayoutItemV2 alloc] init];
    item0.title = @"布局";
    item0.layoutType = configModel.layoutType;
    WS
    item0.didTapHanler = ^(XYLiveMultiLinkLayoutType layoutType, NSIndexPath * _Nonnull indexPath) {
        SS
        // 埋点上报
        [XYLiveLinkHostBizTracker eventActionId80525WithRoomId:self.liveInfoService.roomId channelTabName:layoutType == XYLiveMultiLinkLayoutTypeGrid ? @"2" : @"1" hasGoods:self.liveInfoService.roomInfo.hasGoods];
        // 发起请求
        [self updateLineLayoutType:layoutType];
    };
    XYLiveMultiLinkListGroupItem *group0 = [[XYLiveMultiLinkListGroupItem alloc] init];
    group0.title = @"连线权限设置";
    group0.items = @[item0];
    
    // 连线开关
    XYLiveMultiLinkListSwitchItem *item1 = [[XYLiveMultiLinkListSwitchItem alloc] init];
    item1.title = @"接受连线邀请";
    item1.isOn = configModel.lineSwitch;
    item1.didTapSwitchHandler = ^(BOOL isOn, NSIndexPath * _Nonnull indexPath, UISwitch * _Nonnull sender) {
        SS
        // 埋点上报
        [XYLiveLinkHostBizTracker eventActionId80526WithRoomId:self.liveInfoService.roomId status:isOn ? @"on" : @"off" hasGoods:self.liveInfoService.roomInfo.hasGoods];
        // 发起请求
        if (isOn) {
            [self updateLineConfigWithLayoutType:configModel.layoutType isOpened:isOn limitType:configModel.lineLimitType completion:nil];
        } else {
            // 展示二次确认弹窗
            [self showUpdateSwtichPopupWithIsPK:NO indexPath:indexPath];
        }
    };
    
    // 连线权限范围
    XYLiveMultiLinkListLimitTypeItem *item2 = [[XYLiveMultiLinkListLimitTypeItem alloc] init];
    item2.title = @"连线范围";
    item2.limitType = configModel.lineLimitType;
    item2.didTapHandler = ^(NSInteger limitType, NSIndexPath * _Nonnull indexPath) {
        SS
        // 埋点上报
        [XYLiveLinkHostBizTracker eventActionId80532WithRoomId:self.liveInfoService.roomId channelTabName:@(limitType).stringValue channelTabType:@"1" hasGoods:self.liveInfoService.roomInfo.hasGoods];
        // 发起请求
        [self updateLineConfigWithLayoutType:configModel.layoutType isOpened:configModel.lineSwitch limitType:limitType completion:nil];
    };
    
    XYLiveMultiLinkListLimitTypeModel *allLineModel = [[XYLiveMultiLinkListLimitTypeModel alloc] init];
    allLineModel.limitType = XYLiveMultiLineLimitTypeAll;
    allLineModel.limitTypeText = @"所有主播";
    
    XYLiveMultiLinkListLimitTypeModel *friendLineModel = [[XYLiveMultiLinkListLimitTypeModel alloc] init];
    friendLineModel.limitType = XYLiveMultiLineLimitTypeFriend;
    friendLineModel.limitTypeText = @"互关好友";
    item2.limitTypeListItems = @[allLineModel, friendLineModel];
    
    XYLiveMultiLinkListGroupItem *group1 = [[XYLiveMultiLinkListGroupItem alloc] init];
    group1.items = configModel.lineSwitch ? @[item1, item2] : @[item1];
    
    // PK开关
    XYLiveMultiLinkListSwitchItem *item3 = [[XYLiveMultiLinkListSwitchItem alloc] init];
    item3.title = @"接受PK邀请";
    item3.isOn = configModel.pkSwitch;
    item3.didTapSwitchHandler = ^(BOOL isOn, NSIndexPath * _Nonnull indexPath, UISwitch * _Nonnull sender) {
        SS
        // 埋点上报
        [XYLiveLinkHostBizTracker eventActionId80527WithRoomId:self.liveInfoService.roomId status:isOn ? @"on" : @"off" hasGoods:self.liveInfoService.roomInfo.hasGoods];
        // 发起请求
        if (isOn) {
            [self updatePKConfigWithIsOpend:isOn limitType:configModel.pkLimitType];
        } else {
            // 展示二次确认弹窗
            [self showUpdateSwtichPopupWithIsPK:YES indexPath:indexPath];
        }
    };
    
    // PK权限
    XYLiveMultiLinkListLimitTypeItem *item4 = [[XYLiveMultiLinkListLimitTypeItem alloc] init];
    item4.title = @"PK范围";
    item4.limitType = configModel.pkLimitType;
    item4.didTapHandler = ^(NSInteger limitType, NSIndexPath * _Nonnull indexPath) {
        SS
        // 埋点上报
        [XYLiveLinkHostBizTracker eventActionId80532WithRoomId:self.liveInfoService.roomId channelTabName:@(limitType).stringValue channelTabType:@"2" hasGoods:self.liveInfoService.roomInfo.hasGoods];
        // 发起请求
        [self updatePKConfigWithIsOpend:configModel.pkSwitch limitType:limitType];
    };
    
    XYLiveMultiLinkListLimitTypeModel *allPKModel = [[XYLiveMultiLinkListLimitTypeModel alloc] init];
    allPKModel.limitType = XYLiveMultiPKLimitTypeAll;
    allPKModel.limitTypeText = @"所有主播";
    
    XYLiveMultiLinkListLimitTypeModel *friendPKModel = [[XYLiveMultiLinkListLimitTypeModel alloc] init];
    friendPKModel.limitType = XYLiveMultiPKLimitTypeFriend;
    friendPKModel.limitTypeText = @"互关好友";
    item4.limitTypeListItems = @[allPKModel, friendPKModel];
    
    XYLiveMultiLinkListGroupItem *group2 = [[XYLiveMultiLinkListGroupItem alloc] init];
    group2.items = configModel.pkSwitch ? @[item3, item4] : @[item3];
    group2.title = @"PK权限设置";
    
    self.dataSource = @[group0, group1, group2];
}

// 更新连线布局
- (void)updateLineLayoutType:(XYLiveMultiLinkLayoutType)layoutType {
    WS
    [self updateLineConfigWithLayoutType:layoutType isOpened:self.viewModel.configModel.lineSwitch limitType:self.viewModel.configModel.lineLimitType completion:^(NSError *error) {
        SS
        if (error == nil) {
            // 非连线下，则提示特殊提示
            if (self.multiPKService.isPKing) {
                [XYAlert live_showTextItemWithText:@"切换布局在连线下会生效"];
            } else {
                [XYAlert live_showTextItemWithText:@"布局切换成功"];

            }
        }
    }];
}

// 更新连线配置
- (void)updateLineConfigWithLayoutType:(XYLiveMultiLinkLayoutType)layoutType isOpened:(BOOL)isOpened limitType:(XYLiveMultiLineLimitType)limitType completion:(void(^_Nullable)(NSError *error))completion {
    // 展示Loading提示
    [self showLoading];
    // 限制类型
    XYLiveMultiLineLimitType ltType = !isOpened ? XYLiveMultiLineLimitTypeAll : limitType;
    WS
    // 发起请求
    [self.viewModel updateLineSwitch:isOpened layoutType:layoutType limitType:ltType completion:^(NSError * _Nonnull error) {
        SS
        // 移除Loading
        [self hideLoading];
        // 刷新数据
        [self configDataSource];
        // 刷新表格
        [self.tableView reloadData];
        // 执行回调
        completion ? completion(error) : nil;
    }];
}

// 更新PK配置
- (void)updatePKConfigWithIsOpend:(BOOL)isOpened limitType:(XYLiveMultiPKLimitType)limitType {
    // 展示Loading提示
    [self showLoading];
    // 限制类型
    XYLiveMultiPKLimitType ltType = !isOpened ? XYLiveMultiPKLimitTypeAll : limitType;
    WS
    // 发起请求
    [self.viewModel updatePKSwtich:isOpened limitType:ltType completion:^(NSError * _Nonnull error) {
        SS
        // 移除Loading
        [self hideLoading];
        // 刷新数据
        [self configDataSource];
        // 刷新表格
        [self.tableView reloadData];
    }];
}

#pragma mark - 二次确认弹窗

// 展示更新连线开关弹窗
- (void)showUpdateSwtichPopupWithIsPK:(BOOL)isPK indexPath:(NSIndexPath *)indexPath {
    WS
    NSString *title = @"关闭邀请权限";
    NSString *message = isPK ? @"确认不接受PK邀请吗？关闭权限后主播将无法向你发起PK邀请" : @"确认不接受连线邀请吗？关闭权限后主播将无法向你发起连线邀请";
    // 基础配置
    XYLiveLinkHostConfigModel *configModel = self.viewModel.configModel;
    // 弹窗提示
    XYLiveAlertViewController *alertVC = [[XYLiveAlertViewController alloc] initWithTitle:title message:message cancelButtonTitle:@"取消" confirmButtonTitle:@"确认" handler:^(NSInteger index) {
        SS
        if (index == 1) {
            if (isPK) {
                [self updatePKConfigWithIsOpend:NO limitType:configModel.pkLimitType];
            } else {
                [self updateLineConfigWithLayoutType:configModel.layoutType isOpened:NO limitType:configModel.lineLimitType completion:nil];
            }
        } else {
            XYLiveMultiLinkListGroupItem *group = self.dataSource[indexPath.section];
            XYLiveMultiLinkListSwitchItem *item = XYSAFE_CAST(group.items[indexPath.row], XYLiveMultiLinkListSwitchItem);
            item.isOn = YES;
            [self.tableView reloadData];
        }
    }];
    [alertVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.alertVC = alertVC;
}


@end
