//
//  LiveAdsLeadAddittionCardPage.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/5/23.
//  Copyright © 2025 XingIn. All rights reserved.
//

import XYDeepLink

@objcMembers
class LiveAdsLeadAddittionCardPage: UIView {
    
    var cardType: LeadCardType?
    var bindCompletionAction: (() -> Void)?
    var formMsgCardListCallback: (() -> [AdsLeadBindCardModel])?
    var consultCardListCallback: (() -> [AdsLeadBindCardModel])?
    
    private lazy var titleLabel = {
        let label = UILabel()
        label.text = AdsLeadGenerationTools.getI18NLanguage(from: "暂无表单留资卡，快去添加吧")
        label.textColor = .ReDs.description.light()
        label.font = Theme.fontXMedium
        label.textAlignment = .center
        label.numberOfLines = 0
        
        return label
    }()
    
    private lazy var addCardBtn = {
        let button = UIButton(type: .custom)
        button.setTitle(AdsLeadGenerationTools.getI18NLanguage(from: "添加卡片"), for: .normal)
        button.setTitleColor(.ReDs.primary.light(), for: .normal)
        button.titleLabel?.font = Theme.fontXMedium
        button.titleLabel?.theme.borderColor = .ReDs.primary.light()
        button.layer.borderWidth = 0.5
        button.theme.borderColor = .ReDs.primary.light()
        button.layer.cornerRadius = 18.0
        
        button.addTarget(self, action: #selector(addCardBtnDidPressed), for: .touchUpInside)

        return button
    }()

    func addCardBtnDidPressed() {
        var param: [String: Any]? = [:]
        
        var selectedTab = 0
        if self.cardType == .formMsg {
            selectedTab = 1
        }
        param?[kBindCardSelectedTabKey] = selectedTab
        
        let roomInfo = XYLiveManager.shared().roomInfo
        param?[kBindCardRoomIdKey] = String(roomInfo.roomID)
        param?[kBindCardPageTitleKey] = AdsLeadGenerationTools.getI18NLanguage(from: "添加卡片")
        param?[kBindCardPageShowAtLivingTimeKey] = true
        param?[kBindCardPageAllowSelectNoneKey] = false
        param?[kFormMsgCardListDatasourceKey] = self.formMsgCardListCallback?() ?? [AdsLeadBindCardModel]()
        param?[kConsultCardListDatasourceKey] = self.consultCardListCallback?() ?? [AdsLeadBindCardModel]()
        
        let bindCompletionCallBack: (Int64?) -> Void = { [weak self] cardCount in // 仅记录成功的点位
            guard let self = self else {
                return
            }
            self.bindCompletionAction?()
        }
        
        param?[kBindCardCompletionHandlerKey] = bindCompletionCallBack
        
        JLRoutes.routeURL(URL(string: "xhsdiscover://live_bind_lead_generate_card?height_ratio=75"), withParameters: param)
    }
    
    convenience init(type: LeadCardType) {
        self.init()
        self.cardType = type
        self.setupUI()
        self.setupData()
    }
    
    func setupData() {
        var title = AdsLeadGenerationTools.getI18NLanguage(from: "暂无表单留资卡，快去添加吧")
        if self.cardType == .consult {
            title = AdsLeadGenerationTools.getI18NLanguage(from: "暂无私信咨询卡，快去添加吧")
        }
        
        self.titleLabel.text = title
    }
    
    func setupUI() {
        self.backgroundColor = .ReDs.background.light()
        self.addSubview(self.titleLabel)
        self.addSubview(self.addCardBtn)
        
        self.titleLabel.snp.makeConstraints { make in
            make.height.equalTo(22.0)
            make.top.equalToSuperview().offset(200.0)
            make.left.equalToSuperview().offset(48.0)
            make.right.equalToSuperview().offset(-48.0)
        }
        
        self.addCardBtn.snp.makeConstraints { make in
            make.width.equalTo(96.0)
            make.height.equalTo(36.0)
            make.centerX.equalTo(self.titleLabel)
            make.top.equalTo(self.titleLabel.snp.bottom).offset(24.0)
        }
    }
}
