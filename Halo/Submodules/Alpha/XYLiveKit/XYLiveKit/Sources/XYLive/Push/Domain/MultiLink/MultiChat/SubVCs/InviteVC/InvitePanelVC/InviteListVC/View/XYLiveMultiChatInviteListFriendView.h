//
//  XYLiveMultiChatInviteListFriendView.h
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <UIKit/UIKit.h>
@class XYLiveMultiChatInviteListFriendModel, XYTrackerEventContext;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiChatInviteListFriendView : UIView

// 点击列表Item回调
@property (nonatomic, copy) void(^didTapHandler)(XYLiveMultiChatInviteListFriendModel *listItem, NSIndexPath *indexPath);

// 点击埋点
@property (nonatomic, copy) XYTrackerEventContext *(^didTapTrackerHandler)(XYLiveMultiChatInviteListFriendModel *listItem, NSIndexPath *indexPath);

// 绑定数据源
- (void)bindListItems:(NSArray<XYLiveMultiChatInviteListFriendModel *> *)listItems indexPath:(NSIndexPath *)indexPath;

// 刷新列表
- (void)reloadData;

@end

NS_ASSUME_NONNULL_END
