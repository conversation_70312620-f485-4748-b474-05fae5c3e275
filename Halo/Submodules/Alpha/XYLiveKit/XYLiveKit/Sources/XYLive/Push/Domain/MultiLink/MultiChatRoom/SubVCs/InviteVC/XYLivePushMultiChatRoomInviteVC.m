//
//  XYLivePushMultiChatRoomInviteVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/2/18.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatRoomInviteVC.h"
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLivePushMultiLinkBizTracker.h>
#import <XYLiveKit/XYLiveMultiChatInvitePanelVC.h>
#import <XYLiveKit/XYLivePushMultiChatRoomConsts.h>
#import <XYLiveKit/XYLivePushMultiChatRoomInviteSettingPanelVC.h>

@interface XYLivePushMultiChatRoomInviteVC()<XYLiveMultiLinkListener>

@property (nonatomic, weak) UIViewController *containerVC;
@property (nonatomic, weak) XYLivePushMultiChatRoomCoreModel *coreModel;
@property (nonatomic, weak) id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak) id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, weak) id<XYLiveGroupServiceProtocol> liveGroupService;
@property (nonatomic, copy) NSString *sessionId;

@end

@implementation XYLivePushMultiChatRoomInviteVC

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                          coreModel:(XYLivePushMultiChatRoomCoreModel *)coreModel
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService
                   groupLiveService:(id<XYLiveGroupServiceProtocol>)groupLiveService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _coreModel = coreModel;
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
        _liveGroupService = groupLiveService;
    }
    return self;
}

/// 展示邀请面板
- (void)showInvitePanelWithSource:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString * _Nullable)extraInfo {
    XYLiveMultiChatInvitePanelVC *panelVC = [[XYLiveMultiChatInvitePanelVC alloc] initWithBizType:XYLiveMultiLinkBizTypeChatRoom source:source extraInfo:extraInfo];
    WS
    panelVC.didTapSettingHandler = ^{
        SS
        [self showSettingPanelVC];
    };
    panelVC.moveToIndexHandler = ^NSInteger{
        SS
        return self.coreModel.applyNum <= 0 ? 1 : 0;
    };
    panelVC.liveInfoServiceHandler = ^id<XYLiveInfoServiceProtocol> _Nonnull{
        SS
        return self.liveInfoService;
    };
    panelVC.multiLinkServiceHandler = ^id<XYLiveMultiLinkServiceProtocol> _Nonnull{
        SS
        return self.multiLinkService;
    };
    panelVC.showUserCardHandler = ^(XYLiveUserInfo * _Nonnull userInfo) {
        SS
        self.showUserCardHandler ? self.showUserCardHandler(userInfo) : nil;
    };
    XYLiveNavigationChildController *nav = [[XYLiveNavigationChildController alloc] initWithRootChildController:panelVC];
    nav.portraitHeight = ^CGFloat{
        return kXYLiveMultiChatRoomPanelHeight;
    };
    nav.needReachBottom = YES;
    [nav showWithAnimated:YES onVC:self.containerVC complete:nil];
}

/// 邀请
- (void)inviteWithUserId:(NSString *)userId source:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString * _Nullable)extraInfo {
    [self.coreModel inviteWithUserId:userId source:source extraInfo:extraInfo];
}

#pragma mark - Private

- (void)showSettingPanelVC {
    // 畅聊设置面板
    XYLivePushMultiChatRoomInviteSettingPanelVC *panelVC = [[XYLivePushMultiChatRoomInviteSettingPanelVC alloc] initWithContainerVC:self.containerVC liveInfoService:self.liveInfoService groupLiveService:self.liveGroupService coreModel:self.coreModel];
    XYLiveNavigationChildController *nav = [[XYLiveNavigationChildController alloc] initWithRootChildController:panelVC];
    nav.portraitHeight = ^CGFloat{
        return kXYLiveMultiChatRoomPanelHeight;
    };
    nav.needReachBottom = YES;
    [self.containerVC showPopBottomVC:nav];
}

@end
