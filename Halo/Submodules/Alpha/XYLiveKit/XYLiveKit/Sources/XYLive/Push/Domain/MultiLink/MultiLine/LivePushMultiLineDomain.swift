//
//  PushMultiLineDomain.swift
//  XYLiveKit
//
//  Created by 大远 on 2024/10/25.
//  Copyright © 2024 XingIn. All rights reserved.
//

import Foundation
import XYAlphaFusion

@objcMembers
class LivePushMultiLineDomain: LiveBaseDomain<LivePushMultiLineDependProtocol>, LiveRoomInfoServiceProtocol {
    
    override class func enableDomain() -> Bool {
        return XYAlphaSwitch.enableNewMultiLinkFramework()
    }
    
    // 中间层，用于缓存数据和事件
    private lazy var serviceImpl: XYLivePushMultiLineService = {
        let service = XYLivePushMultiLineService()
        return service
    }()
    
    // 注册服务
    override func doRegisterService(registery: ServiceRegistery) {
        registery.registerService(protocol: XYLivePushMultiLineServiceProtocol.self, service: self.serviceImpl)
    }
    
    // 声明依赖
    override func doInjectService(provider: ServiceProvider) -> LivePushMultiLineDependProtocol {
        return LivePushMultiLineDepend(provider)
    }
    
    override func didLoad() {
        super.didLoad()
        // 注册监听
        self.depend?.liveInfoService()?.registerRoomInfoListener(self)
    }
    
    func onRoomInfoDidUpdate(_ oldRoomInfo: XYLiveRoomInfo?, newRoomInfo: XYLiveRoomInfo?) {
        // 合法性校验
        guard let depend = self.depend, let roomInfo = newRoomInfo else { return }
        // 游戏和畅聊直播间跳过不处理
        if roomInfo.isScreenLive || roomInfo.isLiveChat { return }
        // 初始化容器vc
        let vc = XYLivePushMultiLineController(containerVC: self.hostViewController(), depend: depend)
        add(vc)
        // 绑定服务
        serviceImpl.bindTarget(vc)
    }
}
