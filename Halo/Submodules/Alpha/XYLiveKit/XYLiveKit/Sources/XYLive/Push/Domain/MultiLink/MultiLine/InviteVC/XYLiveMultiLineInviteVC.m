//
//  XYLiveMultiLineInviteVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/26.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineInviteVC.h"
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLiveMultiLinkOnInvitePanelVC.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineConsts.h>
#import <XYLiveCore/XYLiveCore-Swift.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveKit/XYLiveLinkHostInviteeInfo.h>
#import <XYLiveFoundation/XYLiveUserInfo.h>
#import <XYLiveKit/XYLiveMultiLineInviteVM.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYLiveKit/XYLivePushMultiLineBizTracker.h>

@interface XYLiveMultiLineInviteVC()<XYLiveMultiLineInviteDelegate>

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, weak)   XYLiveMultiLinkOnInvitePanelVC *onInviteVC;
@property (nonatomic, strong) NSHashTable<id<XYLiveMultiLineInviteListener>> *listeners;
@property (nonatomic, strong) XYLiveMultiLineInviteVM *viewModel;

@end

@implementation XYLiveMultiLineInviteVC

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    // 注册监听
    [self.multiLinkService registerListener:self.viewModel];
}

// 发起邀请
- (void)inviteWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo source:(XYLiveMultiLineInviteSource)source extraInfo:(NSString *_Nullable)extraInfo {
    [self.viewModel inviteWithInviteeInfo:inviteeInfo source:source extraInfo:extraInfo];
}

// 取消邀请
- (void)cancelInviteWithUserId:(NSString *)targetUserId {
    [self.viewModel cancelInviteWithUserId:targetUserId];
}

// 是否处于邀请中
- (BOOL)isInvitingWithUserId:(NSString *)userId {
    return [self.viewModel isInvitingWithUserId:userId];
}

// 邀请中成员列表
- (NSArray<XYLiveLinkHostInviteeInfo *> *)inviteeInfoList {
    return self.viewModel.inviteeInfoList;
}

// 注册监听
- (void)registerListener:(id<XYLiveMultiLineInviteListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners addObject:listener];
}

// 取消注册监听
- (void)unregisterListener:(id<XYLiveMultiLineInviteListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners removeObject:listener];
}

#pragma mark - XYLiveMultiLineInviteDelegate

- (void)onUpdateInviteState:(XYLiveMultiLinkInviteState)inviteState inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    // 事件分发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLiveMultiLineInviteListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onLineUpdateInviteState:inviteeInfo:)]) {
            [obj onLineUpdateInviteState:inviteState inviteeInfo:inviteeInfo];
        }
    }];
}

- (void)onReceiveInviteMessage:(XYLiveMultiLinkInviteInfo *)inviteInfo {
    // 展示受邀弹窗
    XYLiveMultiLinkOnInvitePanelVC *panelVC = [[XYLiveMultiLinkOnInvitePanelVC alloc] initWithBizType:XYLiveMultiLinkBizTypeLine inviteInfo:inviteInfo];
    [panelVC updateRejectItemTitle:[NSString stringWithFormat:@"拒绝（%@s）", @(inviteInfo.interval)]];
    NSString *targetUserId = inviteInfo.fromUserInfo.userID;
    NSString *targetRoomId = [NSString stringWithFormat:@"%@", @(inviteInfo.fromUserInfo.roomID)];
    WS
    panelVC.didTapRejectHandler = ^{
        SS
        // 埋点上报
        [XYLivePushMultiLineBizTracker eventActionId80520WithRoomId:self.liveInfoService.roomId channelTabType:inviteInfo.userInfoList.count > 1 ? @"3" : @"1" hasGoods:self.liveInfoService.roomInfo.hasGoods];
        // 发起请求
        [self.viewModel rejectInviteWithTargetUserId:targetUserId targetRoomId:targetRoomId];
    };
    panelVC.didTapAcceptHandler = ^{
        SS
        // 埋点上报
        [XYLivePushMultiLineBizTracker eventActionId80521WithRoomId:self.liveInfoService.roomId channelTabType:inviteInfo.userInfoList.count > 1 ? @"3" : @"1" hasGoods:self.liveInfoService.roomInfo.hasGoods];
        [self.viewModel acceptInviteWithTargetUserId:targetUserId targetRoomId:targetRoomId];
    };
    panelVC.didTapUserHandler = ^(XYLiveUserInfo * _Nonnull userInfo) {
        SS
        self.didTapUserHandler ? self.didTapUserHandler(userInfo) : nil;
    };
    panelVC.didTapExtendItemHandler = ^{
        SS
        // 埋点上报
        [XYLivePushMultiLineBizTracker eventActionId80522WithRoomId:self.liveInfoService.roomId hasGoods:self.liveInfoService.roomInfo.hasGoods];
    };
    [panelVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.onInviteVC = panelVC;
    // 曝光埋点上报
    [XYLivePushMultiLineBizTracker eventActionId80529WithRoomId:self.liveInfoService.roomId channelTabType:inviteInfo.userInfoList.count > 1 ? @"3" : @"1" hasGoods:self.liveInfoService.roomInfo.hasGoods];
}

- (void)onTimerTrick:(NSInteger)remainInterval totalInterval:(NSInteger)totalInterval {
    // 更新倒计时
    [self.onInviteVC updateRejectItemTitle:[NSString stringWithFormat:@"拒绝（%@s）", @(remainInterval)]];
}

- (void)onTimerFinish {
    // 关闭弹窗
    [self.containerVC dismissPopBottomVC:self.onInviteVC dismissAnimated:YES dismissComplete:nil];
}

#pragma mark - Lazy

- (NSHashTable<id<XYLiveMultiLineInviteListener>> *)listeners {
    if (_listeners == nil) {
        _listeners = [NSHashTable weakObjectsHashTable];
    }
    return _listeners;
}

- (XYLiveMultiLineInviteVM *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[XYLiveMultiLineInviteVM alloc] initWithLiveInfoService:self.liveInfoService multiLinkService:self.multiLinkService];
        _viewModel.delegate = self;
    }
    return _viewModel;
}

@end
