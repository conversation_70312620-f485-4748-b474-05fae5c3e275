//
//  ConditionLinkMicNumberInputView.swift
//  XYLiveKit
//
//  Created by quhe on 2023/2/22.
//  Copyright © 2023 XingIn. All rights reserved.
//

import UIKit
import XYUITheme
import XYConfigCenter
import RedI18N
import I18N

private let kComplexInputElementsTop: CGFloat = 12
private let kComplexInputElementsPadding: CGFloat = 16
private let kComplexInputEditHeight: CGFloat = 36
private let kComplexInputEditMultipleLineHeight: CGFloat = 72

@objcMembers 
class MultiChatNumberInputView: UIView {
    public var shouldChangeTextCallback: ((_ curText: String, _ range: NSRange) -> Bool)?
    public var closeActionCallback: (() -> Void)?
    public var confirmActionCallback: ((_ content: String) -> Void)?
    var inputHeight: CGFloat {
        return 233
    }
    
    /// Private var
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "付费金额"
        label.textAlignment = .center
        label.textColor = .ReDs.label
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()
    
    private lazy var closeButton: UIButton = {
        let button = UIButton()
        button.backgroundColor = UIColor.clear
        button.setTitleColor(.ReDs.label, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .regular)
        button.setTitle("取消", for: .normal)
        button.addTarget(self, action: #selector(p_closeButtonClicked), for: .touchUpInside)
        return button
    }()
    
    private lazy var confirmButton: UIButton = {
        let button = UIButton()
        button.backgroundColor = UIColor.clear
        button.setTitleColor(.ReDs.red.withAlphaComponent(0.4), for: .normal)
        button.setTitle("确认", for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.addTarget(self, action: #selector(p_confirmButtonClicked), for: .touchUpInside)
        return button
    }()
    
    private lazy var textField: UITextField = {
        let field = UITextField()
        field.backgroundColor = Theme.color.grayLevel6
        field.font = Theme.fontXMedium
        field.placeholder = "输入薯币金额"
        field.text = ""
        field.tintColor = .ReDs.red
        let leftView = UIView(frame: CGRect(x: 0, y: 0, width: 16, height: 0))
        field.leftView = leftView
        field.leftViewMode = .always
        field.keyboardType = .numberPad
        field.returnKeyType = .done
        field.layer.cornerRadius = 12
        field.delegate = self
        field.addTarget(self, action: #selector(textFieldEditingChange(sender:)), for: .editingChanged)
        return field
    }()
    
    private lazy var coinNameLabel: UILabel = {
        let label = UILabel()
        label.text = L.live.live_recharge_coin
        label.font = .systemFont(ofSize: 16, weight: .regular)
        label.textColor = .ReDs.label
        return label
    }()
    
    private lazy var descLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .regular)
        label.textColor = .ReDs.tertiaryLabel
        label.textAlignment = .left
        label.text = "推荐金额:"
        return label
    }()
    
    private lazy var firstRecommendButton: UIButton = {
        let btn = UIButton()
        btn.setTitle("100\(L.live.live_recharge_coin)", for: .normal)
        btn.setTitleColor(.ReDs.tertiaryLabel, for: .normal)
        btn.backgroundColor = .ReDs.secondaryFill
        btn.titleLabel?.font = .systemFont(ofSize: 14, weight: .regular)
        btn.layer.cornerRadius = 6
        btn.clipsToBounds = true
        btn.addTarget(self, action: #selector(p_firstButtonClicked), for: .touchUpInside)
        return btn
    }()
    
    private lazy var secondRecommendButton: UIButton = {
        let btn = UIButton()
        btn.setTitle("500薯币", for: .normal)
        btn.setTitleColor(.ReDs.tertiaryLabel, for: .normal)
        btn.backgroundColor = .ReDs.secondaryFill
        btn.titleLabel?.font = .systemFont(ofSize: 14, weight: .regular)
        btn.layer.cornerRadius = 6
        btn.clipsToBounds = true
        btn.addTarget(self, action: #selector(p_secondButtonClicked), for: .touchUpInside)
        return btn
    }()
    
    private lazy var thirdRecommendButton: UIButton = {
        let btn = UIButton()
        btn.setTitle("1000薯币", for: .normal)
        btn.setTitleColor(.ReDs.tertiaryLabel, for: .normal)
        btn.backgroundColor = .ReDs.secondaryFill
        btn.titleLabel?.font = .systemFont(ofSize: 14, weight: .regular)
        btn.layer.cornerRadius = 6
        btn.clipsToBounds = true
        btn.addTarget(self, action: #selector(p_thirdButtonClicked), for: .touchUpInside)
        return btn
    }()
    
    public var curText: String {
        return textField.text ?? ""
    }
    
    /// Overrides
    init() {
        super.init(frame: CGRect(x: 0, y: 0, width: 0, height: 0))
        p_setupUI()
    }
    
    required public init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func layoutSubviews() {
        let path = UIBezierPath(roundedRect: bounds, byRoundingCorners: [.topLeft, .topRight], cornerRadii: CGSize(width: 12, height: 12))
        let maskLayer: CAShapeLayer = CAShapeLayer()
        maskLayer.path = path.cgPath
        layer.mask = maskLayer
        
        closeButton.frame = CGRect(x: 16,
                                   y: kComplexInputElementsPadding,
                                   width: 35,
                                   height: 20)
        
        
        let confirmButtonWidth: CGFloat = 35
        confirmButton.frame = CGRect(x: xy_width - confirmButtonWidth - kComplexInputElementsPadding,
                                     y: kComplexInputElementsPadding,
                                     width: confirmButtonWidth,
                                     height: 20)
        
        
        let titleWidth: CGFloat = 70
        titleLabel.frame = CGRect(x: (xy_width - titleWidth) / 2,
                                  y: kComplexInputElementsPadding,
                                  width: titleWidth,
                                  height: 20)
        
        textField.frame = CGRect(x: kComplexInputElementsPadding,
                                 y: 58,
                                 width: xy_width - kComplexInputElementsPadding * 2,
                                 height: 44)
        
        let coinNameLabelW: CGFloat = 35
        coinNameLabel.frame = CGRect(x: textField.xy_width - 12 - coinNameLabelW,
                                    y: (textField.xy_height - 20) / 2,
                                    width: coinNameLabelW,
                                    height: 20)
        
        descLabel.frame = CGRect(x: kComplexInputElementsPadding,
                                 y: textField.xy_bottom + 24,
                                 width: xy_width - kComplexInputElementsPadding * 2,
                                 height: 18)
        
        let buttonW = (xy_width - kComplexInputElementsPadding * 2 - kComplexInputElementsTop * 2) / 3
        firstRecommendButton.frame = CGRect(x: kComplexInputElementsPadding,
                                            y: descLabel.xy_bottom + 10,
                                            width: buttonW,
                                            height: 36)
        
        secondRecommendButton.frame = CGRect(x: kComplexInputElementsPadding + buttonW + kComplexInputElementsTop,
                                 y: descLabel.xy_bottom + 10,
                                 width: buttonW,
                                 height: 36)
        
        thirdRecommendButton.frame = CGRect(x: kComplexInputElementsPadding + buttonW * 2 + kComplexInputElementsTop * 2,
                                 y: descLabel.xy_bottom + 10,
                                 width: buttonW,
                                 height: 36)
    }
}

// MARK: - Privates
private
extension MultiChatNumberInputView {
    func p_setupUI() {
        backgroundColor = Theme.color.white
        
        addSubview(titleLabel)
        addSubview(closeButton)
        addSubview(confirmButton)
        addSubview(textField)
        textField.addSubview(coinNameLabel)
        addSubview(descLabel)
        addSubview(firstRecommendButton)
        addSubview(secondRecommendButton)
        addSubview(thirdRecommendButton)
    }
    
    func p_alertLimit() {
        
    }
}

// MARK: - Handlers
extension MultiChatNumberInputView {
    @objc
    func p_confirmButtonClicked() {
        if let text = textField.text, let number = Int(text), number > 0, number <= 30000 {
            confirmActionCallback?(textField.text ?? "")
            closeActionCallback?()
        } else {
            XYAlert.createTextItemWithText(onMiddle: "请输入1-30000薯币")?.show()
        }
    }
    
    @objc
    func p_firstButtonClicked() {
        textField.text = "100"
        resetAllSetNumberButton()
        confirmButtonEnabled()
        firstRecommendButton.setTitleColor(.ReDs.red, for: .normal)
        firstRecommendButton.backgroundColor = .ReDs.red.withAlphaComponent(0.1)
    }
    
    @objc
    func p_secondButtonClicked() {
        textField.text = "500"
        resetAllSetNumberButton()
        confirmButtonEnabled()
        secondRecommendButton.setTitleColor(.ReDs.red, for: .normal)
        secondRecommendButton.backgroundColor = .ReDs.red.withAlphaComponent(0.1)
    }
    
    @objc
    func p_thirdButtonClicked() {
        textField.text = "1000"
        resetAllSetNumberButton()
        confirmButtonEnabled()
        thirdRecommendButton.setTitleColor(.ReDs.red, for: .normal)
        thirdRecommendButton.backgroundColor = .ReDs.red.withAlphaComponent(0.1)
    }
    
    @objc
    func p_closeButtonClicked() {
        closeActionCallback?()
    }
    
    func resetAllSetNumberButton() {
        firstRecommendButton.setTitleColor(.ReDs.tertiaryLabel, for: .normal)
        firstRecommendButton.backgroundColor = .ReDs.secondaryFill
        secondRecommendButton.setTitleColor(.ReDs.tertiaryLabel, for: .normal)
        secondRecommendButton.backgroundColor = .ReDs.secondaryFill
        thirdRecommendButton.setTitleColor(.ReDs.tertiaryLabel, for: .normal)
        thirdRecommendButton.backgroundColor = .ReDs.secondaryFill
    }
    
    func confirmButtonEnabled() {
        confirmButton.setTitleColor(.ReDs.red, for: .normal)
        confirmButton.isUserInteractionEnabled = true
    }
    
    func confirmButtonDisEnabled() {
        confirmButton.setTitleColor(.ReDs.red.withAlphaComponent(0.4), for: .normal)
        confirmButton.isUserInteractionEnabled = false
    }
}

// MARK: - CommonInputBarProtocol
extension MultiChatNumberInputView {
    func triggerKeyboard() {
        textField.becomeFirstResponder()
    }
    
    func resignKeyboard() {
        textField.resignFirstResponder()
    }
}

extension MultiChatNumberInputView: UITextFieldDelegate {
    @objc
    func textFieldEditingChange(sender: UITextField) {
        
        guard let text = sender.text?.trimmingCharacters(in: CharacterSet.whitespaces), !text.isEmpty else {
            confirmButtonDisEnabled()
            return
        }
        resetAllSetNumberButton()
        if text.count == 1 && text.first == "0" {
            sender.text = ""
            XYAlert.createTextItemWithText(onMiddle: "请输入1-30000薯币")?.show()
            return
        }
        if let number = Int(text), number > 0, number <= 30000 {
            confirmButtonEnabled()
        } else {
            sender.text = String(text.prefix(5))
            if let temNumber = Int(String(text.prefix(5))), temNumber > 0 && temNumber <= 30000 {
                confirmButtonEnabled()
            } else {
                confirmButtonDisEnabled()
                XYAlert.createTextItemWithText(onMiddle: "请输入1-30000薯币")?.show()
            }
        }
    }
}
