//
//  XYLiveMultiPKMatchVM.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/30.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiPKMatchServiceProtocol.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>

NS_ASSUME_NONNULL_BEGIN

@protocol XYLiveMultiPKMatchDelegate <NSObject>

// 匹配状态变化
- (void)onUpdateMatchState:(XYLiveMultiLinkMatchState)state;

@end

@interface XYLiveMultiPKMatchVM : NSObject<XYLiveMultiLinkListener>

// 代理
@property (nonatomic, weak) id<XYLiveMultiPKMatchDelegate> delegate;
// 是否匹配中
@property (nonatomic, assign, readonly) BOOL isMatching;

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

// 发起匹配
- (void)startMatchWithSource:(XYLiveMultiPKMatchSource)source extraInfo:(NSString *)extraInfo;

// 取消匹配
- (void)cancelMatch;

// 发起重新匹配
- (void)startReMatchWithExtraInfo:(NSString * _Nullable)extraInfo;

@end

NS_ASSUME_NONNULL_END
