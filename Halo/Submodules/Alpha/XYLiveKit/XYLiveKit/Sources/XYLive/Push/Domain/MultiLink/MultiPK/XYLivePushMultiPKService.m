//
//  XYLivePushMultiPKService.m
//  XYLiveKit
//
//  Created by 大远 on 2025/4/24.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushMultiPKService.h"
#import <XYFoundation/XYFoundation.h>

@interface XYLivePushMultiPKService()

@property (nonatomic, weak) XYLivePushMultiPKController *target;
@property (nonatomic, strong) NSHashTable<id<XYLivePushMultiPKListener>> *cacheListeners;

@end

@implementation XYLivePushMultiPKService

// 绑定服务
- (void)bindTarget:(XYLivePushMultiPKController *)target {
    self.target = target;
    // 粘性事件
    if (self.cacheListeners.count) {
        [self.cacheListeners.allObjects xy_each:^(id<XYLivePushMultiPKListener>  _Nonnull listener) {
            [self.target registerListener:listener];
        }];
        // 清除本地缓存
        [self.cacheListeners removeAllObjects];
    }
}

#pragma mark - XYLivePushMultiPKInviteServiceProtocol

- (void)inviteWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo source:(XYLiveMultiPKInviteSource)source extraInfo:(NSString *)extraInfo {
    [self.target.inviteVC inviteWithInviteeInfo:inviteeInfo source:source extraInfo:extraInfo];
}

- (void)cancelInviteWithUserId:(NSString *)userId {
    [self.target.inviteVC cancelInviteWithUserId:userId];
}

- (BOOL)isInvitingWithUserId:(NSString *)userId {
    return [self.target.inviteVC isInvitingWithUserId:userId];
}

#pragma mark - XYLivePushMultiPKMatchServiceProtocol

- (void)startMatchWithSource:(XYLiveMultiPKMatchSource)source extraInfo:(NSString *)extraInfo {
    [self.target.matchVC startMatchWithSource:source extraInfo:extraInfo];
}

- (void)showMatchPanel {
    [self.target.matchVC showMatchPanel];
}

- (void)cancelMatch {
    [self.target.matchVC cancelMatch];
}

- (BOOL)isMatching {
    return [self.target.matchVC isMatching];
}

#pragma mark - XYLiveMultiPKCoreServiceProtocol

- (BOOL)isPKing {
    return [self.target.coreVC isPKing];
}

- (XYLiveMultiLinkBizType)bizType {
    return [self.target.coreVC bizType];
}

#pragma mark - XYLivePushMultiPKServiceProtocol

- (void)showOptFuncPanelWithSource:(XYLiveMultiPKOptFuncPanelSource)source {
    [self.target showOptFuncPanelWithSource:source];
}

- (void)registerListener:(id<XYLivePushMultiPKListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call", NSStringFromSelector(_cmd));
        return;
    }
    if (self.target) {
        [self.target registerListener:listener];
    } else {
        [self.cacheListeners addObject:listener];
    }
}

- (void)unregisterListener:(id<XYLivePushMultiPKListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call", NSStringFromSelector(_cmd));
        return;
    }
    if (self.target) {
        [self.target unregisterListener:listener];
    } else {
        [self.cacheListeners removeObject:listener];
    }
}

#pragma mark - Lazy

- (NSHashTable<id<XYLivePushMultiPKListener>> *)cacheListeners {
    if (_cacheListeners == nil) {
        _cacheListeners = [NSHashTable weakObjectsHashTable];
    }
    return _cacheListeners;
}

@end
