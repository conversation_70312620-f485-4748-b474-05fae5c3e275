//
//  XYLiveLinkHostInviteListModel.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/20.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostInviteListModel.h"


@implementation XYLiveLinkHostInviteListTextItem

@end

@implementation XYLiveLinkHostInviteListEmptyItem

@end

@implementation XYLiveLinkHostInviteListModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"friendListItems": @"living_friends_list",
        @"recommendListItems": @"recommend_list",
    };
}

+ (NSDictionary *)modelContainerPropertyGenericClass {
    return @{
        @"friendListItems": [XYLiveLinkHostInviteeInfo class],
        @"recommendListItems": [XYLiveLinkHostInviteeInfo class]
    };
}

@end
