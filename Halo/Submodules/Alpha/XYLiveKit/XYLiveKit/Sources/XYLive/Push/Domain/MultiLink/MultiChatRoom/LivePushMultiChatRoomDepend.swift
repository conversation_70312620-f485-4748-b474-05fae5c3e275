//
//  LivePushMultiChatRoomDepend.swift
//  XYLiveKit
//
//  Created by 大远 on 2024/11/1.
//  Copyright © 2024 XingIn. All rights reserved.
//

import Foundation
import XYLiveServiceProtocol

@objc(XYLivePushMultiChatRoomDependProtocol)
public protocol LivePushMultiChatRoomDependProtocol: NSObjectProtocol {
    // 直播间信息服务
    func liveInfoService() -> XYLiveInfoServiceProtocol?
    // 多人互动服务
    func multiLinkService() -> XYLiveMultiLinkServiceProtocol?
    // 直播间适配服务
    func pushAdapterService () -> XYLivePushAdapterServiceProtocol?
    // 个人资料卡
    func userCardService() -> XYLivePushUserCardServiceProtocol?
    // 底部栏服务
    func bottomBarService() -> LivePushBottomBarNodeServiceProtocol?
    // 长链接服务
    func imDistributeService() -> XYLiveIMDistributerServiceProtocol?
    // 团播
    func groupLiveService() -> LiveGroupServiceProtocol?
}

@objc(XYLivePushMultiChatRoomDepend)
@objcMembers
class LivePushMultiChatRoomDepend: NSObject, LivePushMultiChatRoomDependProtocol {
    
    private weak var provider: ServiceProvider?
    
    init(provider: ServiceProvider) {
        self.provider = provider
    }
    
    public func liveInfoService() -> XYLiveInfoServiceProtocol? {
        return provider?.getService(XYLiveInfoServiceProtocol.self)
    }
    
    public func multiLinkService() -> XYLiveMultiLinkServiceProtocol? {
        return provider?.getService(XYLiveMultiLinkServiceProtocol.self)
    }
    
    public func pushAdapterService() -> XYLivePushAdapterServiceProtocol? {
        return provider?.getService(XYLivePushAdapterServiceProtocol.self)
    }
    
    public func userCardService() -> XYLivePushUserCardServiceProtocol? {
        return provider?.getService(XYLivePushUserCardServiceProtocol.self)
    }
    
    public func bottomBarService() -> LivePushBottomBarNodeServiceProtocol? {
        return provider?.getService(LivePushBottomBarNodeServiceProtocol.self)
    }
    
    func imDistributeService() -> XYLiveIMDistributerServiceProtocol? {
        return provider?.getService(XYLiveIMDistributerServiceProtocol.self)
    }
    
    func groupLiveService() -> LiveGroupServiceProtocol? {
        return provider?.getService(LiveGroupServiceProtocol.self)
    }
}
