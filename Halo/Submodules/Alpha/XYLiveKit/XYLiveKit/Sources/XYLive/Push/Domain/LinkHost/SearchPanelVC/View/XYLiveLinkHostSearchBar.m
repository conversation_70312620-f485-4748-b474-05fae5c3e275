//
//  XYLiveLinkHostSearchBar.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/21.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostSearchBar.h"

@implementation XYLiveLinkHostSearchBar

- (instancetype)init{
    if (self = [super init]) {
        self.style = XYSearchBarStyleCenterField;
        self.inset = 0;
        self.showsCancelButton = YES;
        self.showsNavShadow = NO;
    }
    return self;
}

- (CGFloat)searchBarHeight {
    return 36;
}

@end
