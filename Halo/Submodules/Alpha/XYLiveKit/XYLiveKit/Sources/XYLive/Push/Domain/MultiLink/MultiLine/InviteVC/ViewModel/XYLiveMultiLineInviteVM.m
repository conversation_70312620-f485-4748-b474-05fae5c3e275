//
//  XYLiveMultiLineInviteVM.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/30.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineInviteVM.h"
#import <XYLiveKit/XYLiveLinkHostInviteeInfo.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineConsts.h>
#import <XYFoundation/XYFoundation.h>

@interface XYLiveMultiLineInviteVM()

@property (nonatomic, weak) id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak) id<XYLiveMultiLinkServiceProtocol> multiLinkService;

@end

@implementation XYLiveMultiLineInviteVM

/// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
        _inviteeInfoList = @[];
    }
    return self;
}

// 发起邀请
- (void)inviteWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo source:(XYLiveMultiLineInviteSource)source extraInfo:(NSString *_Nullable)extraInfo {
    // 合法性校验
    if (inviteeInfo == nil) { return; }
    // 更新缓存
    [self addItemWithInviteeInfo:inviteeInfo];
    // 更新状态
    [self updateInviteState:XYLiveMultiLinkInviteStateInviting userId:inviteeInfo.userId];
    // 发起请求
    [self.multiLinkService inviteWithBizType:XYLiveMultiLinkBizTypeLine targetUserId:inviteeInfo.userId targetRoomId:inviteeInfo.roomId mediaType:XYLiveMultiLinkMediaTypeVideo bizExtraInfo:nil];
}

// 取消邀请
- (void)cancelInviteWithUserId:(NSString *)targetUserId {
    [self.multiLinkService cancelInviteWithBizType:XYLiveMultiLinkBizTypeLine targetUserId:targetUserId];
}

// 是否处于邀请中
- (BOOL)isInvitingWithUserId:(NSString *)userId {
    return [self.multiLinkService isInvitingWithBizType:XYLiveMultiLinkBizTypeLine targetUserId:userId];
}

// 接受邀请
- (void)acceptInviteWithTargetUserId:(NSString *)targetUserId targetRoomId:(NSString *)targetRoomId {
    [self.multiLinkService acceptInviteWithBizType:XYLiveMultiLinkBizTypeLine targetUserId:targetUserId targetRoomId:targetRoomId bizExtraInfo:nil];
}

// 拒绝邀请
- (void)rejectInviteWithTargetUserId:(NSString *)targetUserId targetRoomId:(NSString *)targetRoomId {
    [self.multiLinkService rejectInviteWithBizType:XYLiveMultiLinkBizTypeLine targetUserId:targetUserId targetRoomId:targetRoomId];
}

#pragma mark - XYLiveLinkHostInviteeInfo

- (void)onInviteResponseWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId error:(NSError *)error {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeLine) { return; }
    // 处理邀请成功相关逻辑
    if (error == nil) {
        // Toast提示
        [XYAlert live_showTextItemWithText:@"已发送邀请"];
    } else {
        // 更新状态
        [self updateInviteState:XYLiveMultiLinkInviteStateIdle userId:targetUserId];
        // 更新缓存
        [self deleteItemWithUserId:targetUserId];
        // Toast提示
        [XYAlert live_showTextItemWithError:error];
    }
}

- (void)onCancelInviteResponseWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId error:(NSError *)error {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeLine) { return; }
    // 处理取消成功
    if (error == nil) {
        // 更新状态
        [self updateInviteState:XYLiveMultiLinkInviteStateIdle userId:targetUserId];
        // 更新缓存
        [self deleteItemWithUserId:targetUserId];
        // Toast提示
        [XYAlert live_showTextItemWithText:@"已撤销邀请"];
    } else {
        // 移除提示
        [[XYAlert sharedInstance] hideLoadingAlertItems];
        // Toast提示
        [XYAlert live_showTextItemWithError:error];
    }
}

- (void)onInviteSuccessWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeLine) { return; }
    // 更新状态
    [self updateInviteState:XYLiveMultiLinkInviteStateInvited userId:targetUserId];
    // 更新缓存
    [self deleteItemWithUserId:targetUserId];
}

- (void)onInviteFailWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId failReason:(XYLiveMultiLinkInviteFailReason)failReason {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeLine) { return; }
    // 更新状态
    [self updateInviteState:XYLiveMultiLinkInviteStateIdle userId:targetUserId];
    // 更新缓存
    [self deleteItemWithUserId:targetUserId];
    // Toast提示
    [XYAlert live_showTextItemWithText:@"对方已拒绝"];
}

- (void)onReceiveInviteMessageWithBizType:(XYLiveMultiLinkBizType)bizType inviteInfo:(XYLiveMultiLinkInviteInfo *)inviteInfo {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeLine) { return; }
    // 事件分发
    if ([self.delegate respondsToSelector:@selector(onReceiveInviteMessage:)]) {
        [self.delegate onReceiveInviteMessage:inviteInfo];
    }
}

- (void)onAcceptInviteResponseWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId error:(NSError *)error {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeLine) { return; }
    // 处理接受接口失败
    if (error) {
        // Toast提示
        [XYAlert live_showTextItemWithError:error];
    }
}

- (void)onRejectInviteResponseWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId error:(NSError *)error {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeLine) { return; }
    // 处理拒绝失败
    if (error) {
        // Toast提示
        [XYAlert live_showTextItemWithError:error];
    }
}

- (void)onInviteCountDownTrickWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId remainTime:(NSInteger)remainTime totalTime:(NSInteger)totalTime {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeLine) { return; }
    // 更新倒计时
    if ([self.delegate respondsToSelector:@selector(onTimerTrick:totalInterval:)]) {
        [self.delegate onTimerTrick:remainTime totalInterval:totalTime];
    }
}

- (void)onInviteCountDownFinishWithBizType:(XYLiveMultiLinkBizType)bizType targetUserId:(NSString *)targetUserId sessionId:(NSString *)sessionId {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeLine) { return; }
    // 倒计时完成
    if ([self.delegate respondsToSelector:@selector(onTimerFinish)]) {
        [self.delegate onTimerFinish];
    }
}

#pragma mark - Private

// 添加邀请
- (void)addItemWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    NSMutableArray *listItemsM = self.inviteeInfoList.mutableCopy;
    [listItemsM addObject:inviteeInfo];
    self.inviteeInfoList = listItemsM.copy;
}

// 删除邀请
- (void)deleteItemWithUserId:(NSString *)userId {
    NSMutableArray *listItemsM = [NSMutableArray array];
    [self.inviteeInfoList enumerateObjectsUsingBlock:^(XYLiveLinkHostInviteeInfo * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (![obj.userId isEqualToString:userId]) {
            [listItemsM addObject:obj];
        }
    }];
    self.inviteeInfoList = listItemsM.copy;
}

// 更新某人邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)inviteState userId:(NSString *)userId {
    // 过滤用户信息
    XYLiveLinkHostInviteeInfo *inviteeInfo = [self.inviteeInfoList xy_match:^BOOL(XYLiveLinkHostInviteeInfo * _Nonnull obj) {
        return [obj.userId isEqualToString:userId];
    }];
    // 合法性校验
    if (inviteeInfo == nil) { return; }
    [XYLogCollector xyLiveLogTag:kXYLiveMultiLineModuleName content:[NSString stringWithFormat:@"update invite state:{state:%@, userId:%@}", @(inviteState), userId]];
    // 事件分发
    if ([self.delegate respondsToSelector:@selector(onUpdateInviteState:inviteeInfo:)]) {
        [self.delegate onUpdateInviteState:inviteState inviteeInfo:inviteeInfo];
    }
}

@end
