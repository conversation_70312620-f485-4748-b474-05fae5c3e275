//
//  XYLiveMultiPKOptFuncVC.h
//  XYLiveKit
//
//  Created by 大远 on 2025/6/6.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveLinkHostServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiPKConsts.h>
#import <XYLiveKit/XYLiveMultiLineOptFuncPanelConsts.h>
@class XYLiveUserInfo;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiPKOptFuncVC : XYViewController

// 点击用户
@property (nonatomic, copy) void(^didTapUserHandler)(XYLiveUserInfo *userInfo);
// 点击结束连线
@property (nonatomic, copy) void(^didTapEndLinkHandler)(void);
// 点击重新匹配
@property (nonatomic, copy) void(^didTapRematchHandler)(void);
// 点击玩法操作
@property (nonatomic, copy) void(^didTapBizOptHandler)(XYLiveMultiLinkBizType bizType, XYLiveMultiLineBizOptType optType);

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService
                    linkHostService:(id<XYLiveLinkHostServiceProtocol>)linkHostService;

// 展示操作面板
- (void)showOptFuncPanelWithSource:(XYLiveMultiPKOptFuncPanelSource)source;

// 隐藏面板
- (void)hideAllPanel;

@end

NS_ASSUME_NONNULL_END
