//
//  XYLivePushMultiChatRoomService.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/31.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatRoomService.h"

@interface XYLivePushMultiChatRoomService()

@property (nonatomic, weak) id<XYLivePushMultiChatRoomServiceProtocol> target;

@end

@implementation XYLivePushMultiChatRoomService

/// 绑定服务
- (void)bindTarget:(id<XYLivePushMultiChatRoomServiceProtocol>)target {
    self.target = target;
}

- (void)showInvitePanelWithSource:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString *)extraInfo {
    [self.target showInvitePanelWithSource:source extraInfo:extraInfo];
}

- (void)inviteWithUserId:(NSString *)userId source:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString *)extraInfo {
    [self.target inviteWithUserId:userId source:source extraInfo:extraInfo];
}

@end
