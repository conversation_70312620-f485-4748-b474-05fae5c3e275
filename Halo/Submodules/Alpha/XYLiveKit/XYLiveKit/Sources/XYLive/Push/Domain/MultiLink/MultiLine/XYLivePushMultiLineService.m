//
//  XYLivePushMultiLineService.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/25.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLivePushMultiLineService.h"
#import <XYFoundation/XYFoundation.h>

@interface XYLivePushMultiLineService()

@property (nonatomic, weak)   XYLivePushMultiLineController *target;
@property (nonatomic, strong) NSHashTable<id<XYLivePushMultiLineListener>> *cacheListeners;

@end

@implementation XYLivePushMultiLineService

// 绑定基础服务
- (void)bindTarget:(XYLivePushMultiLineController *)target {
    self.target = target;
    // 粘性事件
    if (self.cacheListeners.count) {
        [self.cacheListeners.allObjects xy_each:^(id<XYLivePushMultiLineListener>  _Nonnull listener) {
            [self.target registerListener:listener];
        }];
        // 清除本地缓存
        [self.cacheListeners removeAllObjects];
    }
}

#pragma mark - XYLiveMultiLineInviteServiceProtocol

- (void)inviteWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo source:(XYLiveMultiLineInviteSource)source extraInfo:(NSString *)extraInfo {
    [self.target.inviteVC inviteWithInviteeInfo:inviteeInfo source:source extraInfo:extraInfo];
}

- (void)cancelInviteWithUserId:(NSString *)userId {
    [self.target.inviteVC cancelInviteWithUserId:userId];
}

- (BOOL)isInvitingWithUserId:(NSString *)userId {
    return [self.target.inviteVC isInvitingWithUserId:userId];
}

#pragma mark - XYLiveMultiLineMatchServiceProtocol

- (void)startMatchWithSource:(XYLiveMultiLineMatchSource)source extraInfo:(NSString *)extraInfo {
    [self.target.matchVC startMatchWithSource:source extraInfo:extraInfo];
}

- (void)showMatchPanel {
    [self.target.matchVC showMatchPanel];
}

- (void)cancelMatch {
    [self.target.matchVC cancelMatch];
}

- (BOOL)isMatching {
    return [self.target.matchVC isMatching];
}

#pragma mark - XYLiveMultiLineCoreServiceProtocol

- (BOOL)isLining {
    return [self.target.coreVC isLining];
}

- (XYLiveMultiLinkLayoutType)layoutType {
    return [self.target.coreVC layoutType];
}

- (CGRect)renderAreaFrame {
    return [self.target.coreVC renderAreaFrame];
}

#pragma mark - XYLivePushMultiLineServiceProtocol

- (void)showOptFuncPanelWithSource:(XYLiveMultiLineOptFuncPanelSource)source {
    [self.target showOptFuncPanelWithSource:source];
}

- (void)registerListener:(id<XYLivePushMultiLineListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    if (self.target) {
        [self.target registerListener:listener];
    } else {
        [self.cacheListeners addObject:listener];
    }
}

- (void)unregisterListener:(id<XYLivePushMultiLineListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    if (self.target) {
        [self.target unregisterListener:listener];
    } else {
        [self.cacheListeners removeObject:listener];
    }
}

#pragma mark - Lazy

- (NSHashTable<id<XYLivePushMultiLineListener>> *)cacheListeners {
    if (_cacheListeners == nil) {
        _cacheListeners = [NSHashTable weakObjectsHashTable];
    }
    return _cacheListeners;
}

@end
