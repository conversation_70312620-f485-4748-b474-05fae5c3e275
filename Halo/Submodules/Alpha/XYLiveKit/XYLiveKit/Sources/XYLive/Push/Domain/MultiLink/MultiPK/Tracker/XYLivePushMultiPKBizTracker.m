//
//  XYLivePushMultiPKBizTracker.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/30.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushMultiPKBizTracker.h"
#import <XYSessionManager/XYSessionManager.h>

@implementation XYLivePushMultiPKBizTracker

/* 直播开播页/连线中-结束/点击 */
+ (void)eventActionId80517WithRoomId:(NSString *)roomId channelTabName:(NSString *)channelTabName hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._channelTabName(channelTabName)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80517)/* 直播开播页/连线中-结束/点击 */._igoto(1)._mid(47941)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/匹配中-放弃匹配/点击 */
+ (void)eventActionId80519WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._channelTabType(channelTabType)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80519)/* 直播开播页/匹配中-放弃匹配/点击 */._igoto(1)._mid(47943)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/邀请连线-拒绝/点击 */
+ (void)eventActionId80520WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._channelTabType(channelTabType)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80520)/* 直播开播页/邀请连线-拒绝/点击 */._igoto(1)._mid(47944)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/邀请连线-接受/点击 */
+ (void)eventActionId80521WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._channelTabType(channelTabType)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Click)._pid(80521)/* 直播开播页/邀请连线-接受/点击 */._igoto(1)._mid(47945)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

/* 直播开播页/邀请连线面板/曝光 */
+ (void)eventActionId80529WithRoomId:(NSString *)roomId channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods {
    [XYAnalyticsOrganizer._
        .index._channelTabType(channelTabType)
        .page._pins(PageInstance_LiveBroadcastPage)._instanceId(roomId)
        .event._action(NormalizedAction_Impression)._pid(80529)/* 直播开播页/邀请连线面板/曝光 */._igoto(2)._mid(48097)
        .live._liveId(roomId)._anchorId(XYSessionManager.sharedManager.user.userId)._liveType(hasGoods ? @"goods" : @"Interaction")
     send];
}

@end
