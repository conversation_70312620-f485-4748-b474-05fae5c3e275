//
//  PushDispatchMultiLevelUtil.swift
//  XYLiveKit
//
//  Created by 王帅 on 2024/11/8.
//  Copyright © 2024 XingIn. All rights reserved.
//

import Foundation

@objcMembers
public class PushDispatchMultiLevelUtil: NSObject {
    public static func videoPushDispatchSaved() -> Bool {
        !(XYSCKVMigrateTool.defaultUserDefaultTool_v2.string("live_meida_manual_save_definition_video")?.isEmpty ?? true)
    }
    
    public static func videoLevel() -> String? {
        XYSCKVMigrateTool.defaultUserDefaultTool_v2.string("live_meida_manual_save_definition_video")
    }
    
    public static func gamePushDispatchSaved() -> Bool {
        !(XYSCKVMigrateTool.defaultUserDefaultTool_v2.string("live_meida_manual_save_definition_game")?.isEmpty ?? true)
    }
    
    public static func gameLevel() -> String? {
        XYSCKVMigrateTool.defaultUserDefaultTool_v2.string("live_meida_manual_save_definition_game")
    }
    
    public static func setVideoLevel(_ name: String) {
        XYSCKVMigrateTool.defaultUserDefaultTool_v2.set(string: name, "live_meida_manual_save_definition_video")
    }
    
    public static func setGameLevel(_ name: String) {
        XYSCKVMigrateTool.defaultUserDefaultTool_v2.set(string: name, "live_meida_manual_save_definition_game")
    }
}
