//
//  XYLiveLinkHostViewModel.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/27.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLivePushMultiLineServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLivePushMultiPKServiceProtocol.h>
#import <XYLiveKit/XYLiveLinkHostConfigModel.h>
#import <XYLiveKit/XYLiveLinkHostResource.h>
#import <XYLiveKit/XYLiveLinkHostConsts.h>
@class XYLiveLinkHostInviteeInfo;

NS_ASSUME_NONNULL_BEGIN

@protocol XYLiveLinkHostDelegate <NSObject>

// 状态变化
- (void)onLinkUpdateState:(XYLiveLinkHostState)state;

// 邀请状态变化
- (void)onLinkUpdateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo isPK:(BOOL)isPK;

@end

@interface XYLiveLinkHostViewModel : NSObject<XYLivePushMultiLineListener, XYLivePushMultiPKListener>

// 代理
@property (nonatomic, weak)   id<XYLiveLinkHostDelegate> delegate;
// 是否匹配中
@property (nonatomic, assign) BOOL isMatching;
// 是否连接中
@property (nonatomic, assign) BOOL isLinking;
// 资源配置
@property (nonatomic, strong) XYLiveLinkHostResource *resource;
// 功能配置
@property (nonatomic, strong) XYLiveLinkHostConfigModel *configModel;

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLineService:(id<XYLivePushMultiLineServiceProtocol>)multiLineService
                         multiPKService:(id<XYLivePushMultiPKServiceProtocol>)multiPKService;

// 发起匹配
- (void)startMatchWithIsPK:(BOOL)isPK;

// 展示匹配中面板
- (void)showMatchPanel;

// 取消匹配
- (void)cancelMatch;

// 发起邀请
- (void)inviteWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo;

// 取消邀请
- (void)cancelInviteWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo;

// 展示操作面板
- (void)showOptFuncPanel;

// 加载配置
- (void)loadInitConfigWithCompletion:(void(^_Nullable)(XYLiveLinkHostConfigModel *configModel, NSError *error))completion;

// 更新连线配置
- (void)updateLineSwitch:(BOOL)isOpened layoutType:(XYLiveMultiLinkLayoutType)layoutType limitType:(XYLiveMultiLineLimitType)limitType completion:(void(^_Nullable)(NSError *error))completion;

// 更新PK配置
- (void)updatePKSwtich:(BOOL)isOpened limitType:(XYLiveMultiPKLimitType)limitType completion:(void(^_Nullable)(NSError *error))completion;

@end

NS_ASSUME_NONNULL_END
