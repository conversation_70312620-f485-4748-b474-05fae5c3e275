//
//  XYLiveLinkHostBubbleVC.h
//  XYLiveKit
//
//  Created by 大远 on 2025/6/5.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLivePushAdapterServiceProtocol.h>
#import <XYLiveKit/XYLiveLinkHostConsts.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveLinkHostBubbleVC : XYViewController

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                     adapterService:(id<XYLivePushAdapterServiceProtocol>)adapterService;

// 更新连接状态
- (void)updateLinkState:(XYLiveLinkHostState)state;

@end

NS_ASSUME_NONNULL_END
