//
//  XYLiveLinkHostInviteListCell.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/27.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostInviteListCell.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYWebImage/UIImageView+XYWebImage.h>
#import <XYLiveKit/XYLiveLinkHostInvitePanelConsts.h>
#import <Masonry/Masonry.h>

@implementation XYLiveLinkHostInviteListCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        // 设置选中样式
        self.contentView.backgroundColor = [XYLiveTokenColor bg];
        self.backgroundColor = [XYLiveTokenColor bg];
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

- (void)bindListItems:(NSArray *)listItems indexPath:(NSIndexPath *)indexPath {
    self.listItem = [listItems safeObjectAtIndex:indexPath.row];
    self.indexPath = indexPath;
}

- (void)setupSubviews {
}

@end

@implementation XYLiveLinkHostInviteListTextCell

/// 更新数据
- (void)bindListItems:(NSArray *)listItems indexPath:(NSIndexPath *)indexPath {
    [super bindListItems:listItems indexPath:indexPath];
    XYLiveLinkHostInviteListTextItem *textItem = XYSAFE_CAST(self.listItem, XYLiveLinkHostInviteListTextItem);
    self.titleLabel.text = textItem.title;
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    [super setupSubviews];
    // 创建标题
    [self setupTitleLabel];
    
    // 布局
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(kXYLiveLinkHostPanelPadding);
        make.bottom.equalTo(self.contentView);
        make.height.mas_equalTo(18);
    }];
}

- (void)setupTitleLabel {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.font = [UIFont fontWithName:@"PingFangSC-Regular" size:12];
    titleLabel.textColor = [XYLiveTokenColor desc];
    [self.contentView addSubview:titleLabel];
    self.titleLabel = titleLabel;
}

@end

@implementation XYLiveLinkHostInviteListEmptyCell

- (void)bindListItems:(NSArray *)listItems indexPath:(NSIndexPath *)indexPath {
    [super bindListItems:listItems indexPath:indexPath];
    XYLiveLinkHostInviteListEmptyItem *item = XYSAFE_CAST(self.listItem, XYLiveLinkHostInviteListEmptyItem);
    self.emptyView.title =  item.showErrorView ? @"未连接到服务器，刷新一下试试" : @"暂无观众";
    self.emptyView.image = item.showErrorView ? [XYEmptyViewUtil networkImage] : [XYEmptyViewUtil userImage];;
}

- (void)setupSubviews {
    [super setupSubviews];
    // 创建空占位视图
    [self setupEmptyView];
    
    // 布局
    [self.emptyView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
}

#pragma mark - 创建子视图

- (void)setupEmptyView {
    XYLiveMutiLinkEmptyView *emptyView = [[XYLiveMutiLinkEmptyView alloc] init];
    [self.contentView addSubview:emptyView];
    self.emptyView = emptyView;
}

@end

@implementation XYLiveLinkHostInviteListItemCell

/// 更新数据
- (void)bindListItems:(NSArray *)listItems indexPath:(NSIndexPath *)indexPath {
    [super bindListItems:listItems indexPath:indexPath];
    XYLiveLinkHostInviteeInfo *inviteeInfo = XYSAFE_CAST(self.listItem, XYLiveLinkHostInviteeInfo);
    // 更新头像
    [self.avatarImgView xy_setImageWithURL:[NSURL URLWithString:inviteeInfo.avatarUrl ?: @""] placeholderImage:nil];
    // 更新昵称
    self.nickNameLabel.text = inviteeInfo.nickName;
    // 更新标签
    self.relationLabel.text = [XYLiveUserInfo relationTextForHostWithFollowStatus:inviteeInfo.followStatus];
    // 更新副标题
    [self.subTitleView bindListItems:[self convertToSubTitlesWithItem:inviteeInfo]];
    // 更新按钮文案
    [self.inviteBtn setTitle:inviteeInfo.isPK ? @"邀请PK": @"邀请连线" forState:UIControlStateNormal];
    self.inviteBtn.selected = inviteeInfo.isInvited;
}

- (void)setupSubviews {
    [super setupSubviews];
    // 创建头像
    [self setupAvatarImgView];
    // 创建标题
    [self setupNickNameLabel];
    // 创建关系标签
    [self setupRelationLabel];
    // 创建副标题
    [self setupSubTitleView];
    // 创建邀请按钮
    [self setupInviteBtn];
    
    // 布局
    [self.avatarImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(44, 44));
        make.left.mas_equalTo(kXYLiveLinkHostPanelPadding);
    }];
    
    [self.nickNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.avatarImgView.mas_right).offset(8);
        make.top.equalTo(self.avatarImgView).offset(2);
    }];
    
    [self.relationLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nickNameLabel.mas_right).offset(4);
        make.centerY.equalTo(self.nickNameLabel);
        make.right.lessThanOrEqualTo(self.inviteBtn.mas_left).offset(-24);
    }];
    
    [self.subTitleView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.nickNameLabel);
        make.top.equalTo(self.nickNameLabel.mas_bottom).offset(2);
        make.right.lessThanOrEqualTo(self.inviteBtn.mas_left).offset(-24);
    }];
    
    [self.inviteBtn mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(self.contentView);
        make.size.mas_equalTo(CGSizeMake(76, 28));
        make.right.mas_equalTo(-kXYLiveLinkHostPanelPadding);
    }];
}

#pragma mark - 创建子视图

- (void)setupAvatarImgView {
    UIImageView *avatarImgView = [[UIImageView alloc] init];
    avatarImgView.layer.cornerRadius = 22;
    avatarImgView.layer.masksToBounds = YES;
    avatarImgView.layer.borderColor = [XYLiveTokenColor separator].CGColor;
    avatarImgView.layer.borderWidth = 0.5;
    avatarImgView.contentMode = UIViewContentModeScaleAspectFill;
    [self.contentView addSubview:avatarImgView];
    self.avatarImgView = avatarImgView;
}

- (void)setupNickNameLabel {
    UILabel *nickNameLabel = [[UILabel alloc] init];
    nickNameLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:14];
    nickNameLabel.textColor = [XYLiveTokenColor title];
    [nickNameLabel setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [nickNameLabel setContentCompressionResistancePriority:UILayoutPriorityDefaultLow forAxis:UILayoutConstraintAxisHorizontal];
    [self.contentView addSubview:nickNameLabel];
    self.nickNameLabel = nickNameLabel;
}

- (void)setupRelationLabel {
    XYLivePaddingLabel *relationTagLbl = [[XYLivePaddingLabel alloc] init];
    relationTagLbl.textColor = [XYLiveTokenColor paragraph];
    relationTagLbl.font = [UIFont fontWithName:@"PingFangSC-Regular" size:10];
    relationTagLbl.backgroundColor = [XYLiveTokenColor fill1];
    relationTagLbl.layer.cornerRadius = 4;
    relationTagLbl.layer.masksToBounds = YES;
    relationTagLbl.textInsets = UIEdgeInsetsMake(1, 4, 1, 4);
    [relationTagLbl setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [self.contentView addSubview:relationTagLbl];
    self.relationLabel = relationTagLbl;
}

- (void)setupSubTitleView {
    XYLiveLinkHostInviteListSubTitleView *subTitleView = [[XYLiveLinkHostInviteListSubTitleView alloc] init];
    [self.contentView addSubview:subTitleView];
    self.subTitleView = subTitleView;
}

- (void)setupInviteBtn {
    UIButton *inviteBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    inviteBtn.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:13];
    inviteBtn.xy_responseRectInset = UIEdgeInsetsMake(-5, -5, -5, -5);
    [inviteBtn setTitleColor:[XYLiveTokenColor title] forState:UIControlStateNormal];
    inviteBtn.layer.borderWidth = 0.5;
    inviteBtn.layer.borderColor = [XYLiveTokenColor separator2].CGColor;
    inviteBtn.layer.cornerRadius = 14;
    inviteBtn.layer.masksToBounds = YES;
    [inviteBtn setTitle:@"撤销" forState:UIControlStateSelected];
    [inviteBtn addTarget:self action:@selector(didTapInvite:) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:inviteBtn];
    self.inviteBtn = inviteBtn;
}

#pragma mark - Event

- (void)didTapInvite:(UIButton *)sender {
    XYLiveLinkHostInviteeInfo *item = XYSAFE_CAST(self.listItem, XYLiveLinkHostInviteeInfo);
    // 事件回调
    self.didTapInviteHandler ? self.didTapInviteHandler(item, self.indexPath) : nil;
}

#pragma mark - Private

- (NSArray<NSString *> *)convertToSubTitlesWithItem:(XYLiveLinkHostInviteeInfo *)item {
    NSMutableArray *titles = [NSMutableArray array];
    [titles addObject:[NSString stringWithFormat:@"粉丝数%@", [NSString countStringWithCountV2:item.fansNum]]];
    [titles addObject:[NSString stringWithFormat:@"在线人数%@", [NSString countStringWithCountV2:item.onlineViewerNum]]];
    return titles.copy;
}

@end

