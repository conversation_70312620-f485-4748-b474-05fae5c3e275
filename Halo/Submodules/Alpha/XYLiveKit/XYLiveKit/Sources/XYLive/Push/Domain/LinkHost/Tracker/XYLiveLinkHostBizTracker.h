//
//  XYLiveLinkHostBizTracker.h
//  XYLiveKit
//
//  Created by 大远 on 2025/6/30.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYAnalytics/XYAnalytics.h>
@class XYTrackerEventContext;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveLinkHostBizTracker : NSObject

/* 直播开播页/连主播-随机连线/点击 */
+ (void)eventActionId80508WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods;

/* 直播开播页/连主播-随机PK/点击 */
+ (void)eventActionId80509WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods;

/* 直播开播页/连主播-和TA进行PK选项/点击 */
+ (void)eventActionId80510WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods;

/* 直播开播页/连主播-邀请按钮/点击 */
+ (void)eventActionId80511WithRoomId:(NSString *)roomId channelTabName:(NSString *)channelTabName index:(NSInteger)index inviteeUserId:(NSString *)inviteeUserId hasGoods:(BOOL)hasGoods;

/* 直播开播页/连主播-搜索/点击 */
+ (void)eventActionId80512WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods;

/* 直播开播页/连主播-设置/点击 */
+ (void)eventActionId80513WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods;

/* 直播开播页/主播连线面板/曝光 */
+ (void)eventActionId80528WithRoomId:(NSString *)roomId hasGoods:(BOOL)hasGoods;

/* 直播开播页/连线设置-布局/点击 */
+ (void)eventActionId80525WithRoomId:(NSString *)roomId channelTabName:(NSString *)channelTabName hasGoods:(BOOL)hasGoods;

/* 直播开播页/连线设置-接受连线邀请/点击 */
+ (void)eventActionId80526WithRoomId:(NSString *)roomId status:(NSString *)status hasGoods:(BOOL)hasGoods;

/* 直播开播页/连线设置-接受PK邀请/点击 */
+ (void)eventActionId80527WithRoomId:(NSString *)roomId status:(NSString *)status hasGoods:(BOOL)hasGoods;

/* 直播开播页/连线设置-连线范围/点击 */
+ (void)eventActionId80532WithRoomId:(NSString *)roomId channelTabName:(NSString *)channelTabName channelTabType:(NSString *)channelTabType hasGoods:(BOOL)hasGoods;

@end

NS_ASSUME_NONNULL_END
