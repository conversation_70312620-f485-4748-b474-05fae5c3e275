//
//  XYLiveLinkHostInvitePanelVC.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/27.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveRefreshBaseChildViewController.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveKit/XYLiveLinkHostInvitePanelConsts.h>
@class XYLiveLinkHostInviteeInfo;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveLinkHostInvitePanelVC : XYLiveRefreshBaseChildViewController

// 点击邀请
@property (nonatomic, copy) void(^didTapInviteHandler)(XYLiveLinkHostInviteeInfo *inviteeInfo);
// 点击用户
@property (nonatomic, copy) void(^didTapUserHandler)(XYLiveLinkHostInviteeInfo *inviteeInfo);
// 点击匹配
@property (nonatomic, copy) void(^didTapMatchHandler)(BOOL isPK);
// 点击搜索
@property (nonatomic, copy) void(^didTapSearchHandler)(void);
// 点击设置
@property (nonatomic, copy) void(^didTapSettingHandler)(void);

// 初始化
- (instancetype)initWithContianerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo isPK:(BOOL)isPK;

@end

NS_ASSUME_NONNULL_END
