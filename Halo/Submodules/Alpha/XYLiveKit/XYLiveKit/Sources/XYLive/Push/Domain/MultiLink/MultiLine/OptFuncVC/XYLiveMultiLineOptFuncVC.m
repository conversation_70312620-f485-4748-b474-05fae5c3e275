//
//  XYLiveMultiLineOptFuncVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/6/3.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineOptFuncVC.h"
#import <XYLiveKit/XYLiveLinkHostInviteeInfo.h>
#import <XYLiveKit/XYLiveMultiLineOptFuncVM.h>
#import <XYLiveKit/XYLiveMultiLineOptFuncPanelVC.h>
#import <XYLiveKit/XYLiveMultiLineInviteMorePanelVC.h>
#import <XYLiveKit/XYLiveMultiLineSearchPanelVC.h>
#import <XYLiveKit/XYLiveActionSheetViewController.h>
#import <XYLiveKit/XYLiveAlertViewController.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLivePushMultiLineBizTracker.h>

@interface XYLiveMultiLineOptFuncVC ()<XYLiveMultiLinkListener>

@property (nonatomic, weak) UIViewController *containerVC;
@property (nonatomic, weak) id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak) id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, weak) id<XYLiveLinkHostServiceProtocol> linkHostService;
@property (nonatomic, weak) XYLiveMultiLineOptFuncPanelVC *optFuncPanelVC;
@property (nonatomic, weak) XYLiveMultiLineInviteMorePanelVC *inviteMorePanelVC;
@property (nonatomic, weak) XYLiveMultiLineSearchPanelVC *searchPanelVC;
@property (nonatomic, weak) XYLiveActionSheetViewController *sheetVC;
@property (nonatomic, weak) XYLiveAlertViewController *alertVC;

@end

@implementation XYLiveMultiLineOptFuncVC

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService
                    linkHostService:(id<XYLiveLinkHostServiceProtocol>)linkHostService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
        _linkHostService = linkHostService;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    // 注册监听
    [self.multiLinkService registerListener:self];
}

// 展示操作面板
- (void)showOptFuncPanelWithInviteeInfoList:(NSArray<XYLiveLinkHostInviteeInfo *> *)inviteeInfoList
                                     source:(XYLiveMultiLineOptFuncPanelSource)source {
    [XYLogCollector xyLiveLogTag:kXYLiveMultiLineModuleName content:[NSString stringWithFormat:@"show line optFunc panel,{source:%@, inviteeInfoList:%@}", @(source), inviteeInfoList]];
    XYLiveMultiLineOptFuncPanelVC *panelVC = [[XYLiveMultiLineOptFuncPanelVC alloc] initWithContianerVC:self.containerVC liveInfoService:self.liveInfoService multiLinkService:self.multiLinkService inviteeInfoList:inviteeInfoList];
    WS
    panelVC.didTapAddHandler = ^{
        SS
        [self showInviteMorePanel];
    };
    panelVC.didTapSettingHandler = ^{
        SS
        [self.linkHostService showLinkHostSettingPanel];
    };
    panelVC.didTapUserHandler = ^(XYLiveLinkHostInviteeInfo *inviteeInfo) {
        SS
        [self showUserCardWithInviteeInfo:inviteeInfo];
    };
    panelVC.didTapCancelHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull inviteeInfo) {
        SS
        self.didTapCancelHandler ? self.didTapCancelHandler(inviteeInfo) : nil;
    };
    panelVC.didTapCloseHandler = ^{
        SS
        [self showCloseSheetPanel];
    };
    panelVC.didTapOptHandler = ^(XYLiveMultiLinkBizType bizType, XYLiveMultiLineBizOptType optType) {
        SS
        // 发起请求
        [self showSwitchBizAlertWithBizType:bizType];
    };
    XYLiveNavigationChildController *nav = [[XYLiveNavigationChildController alloc] initWithRootChildController:panelVC];
    nav.portraitHeight = ^CGFloat{
        return kXYLiveMultiLineOptFuncPanelH;
    };
    nav.needReachBottom = YES;
    [nav showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.optFuncPanelVC = panelVC;
}

// 隐藏面板
- (void)hideAllPanel {
    [self.inviteMorePanelVC dismissWithAnimated:YES complete:nil];
    [self.searchPanelVC dismissWithAnimated:YES complete:nil];
    [self.sheetVC dismissWithAnimated:YES complete:nil];
    [self.alertVC dismissWithAnimated:YES complete:nil];
    [self.optFuncPanelVC.navigationChildController dismissWithAnimated:YES complete:nil];
}

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    [self.optFuncPanelVC updateInviteState:state inviteeInfo:inviteeInfo];
    [self.inviteMorePanelVC updateInviteState:state inviteeInfo:inviteeInfo];
    [self.searchPanelVC updateInviteState:state inviteeInfo:inviteeInfo];
}

#pragma mark - Private

// 展示邀请更多面板
- (void)showInviteMorePanel {
    XYLiveMultiLineInviteMorePanelVC *panelVC = [[XYLiveMultiLineInviteMorePanelVC alloc] initWithContainerVC:self.containerVC liveInfoService:self.liveInfoService multiLinkService:self.multiLinkService];
    WS
    panelVC.didTapUserHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull inviteeInfo) {
        SS
        [self showUserCardWithInviteeInfo:inviteeInfo];
    };
    panelVC.didTapInviteHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull inviteeInfo) {
        SS
        [self handleInviteOptWithInviteeInfo:inviteeInfo];
    };
    panelVC.didTapSearchHandler = ^{
        SS
        [self showSearchPanel];
    };
    [panelVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.inviteMorePanelVC = panelVC;
}

// 展示搜索面板
- (void)showSearchPanel {
    XYLiveMultiLineSearchPanelVC *panelVC = [[XYLiveMultiLineSearchPanelVC alloc] initWithContainerVC:self.containerVC liveInfoService:self.liveInfoService multiLinkService:self.multiLinkService];
    WS
    panelVC.didTapUserHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull inviteeInfo) {
        SS
        [self showUserCardWithInviteeInfo:inviteeInfo];
    };
    panelVC.didTapInviteHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull inviteeInfo) {
        SS
        [self handleInviteOptWithInviteeInfo:inviteeInfo];
    };
    [panelVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.searchPanelVC = panelVC;
}

// 展示关闭操作面板
- (void)showCloseSheetPanel {
    WS
    XYLivePopupItem *stopItem = XYLivePopupItemMake(@"结束连线", XYLivePopupItemTypeHighlight, ^(NSInteger index) {
        SS
        // 埋点上报
        [XYLivePushMultiLineBizTracker eventActionId80517WithRoomId:self.liveInfoService.roomId channelTabName:@"close" hasGoods:self.liveInfoService.roomInfo.hasGoods];
        // 事件回调
        self.didTapEndLinkHandler ? self.didTapEndLinkHandler() : nil;
    });
    XYLivePopupItem *rematchItem = XYLivePopupItemMake(@"重新匹配", XYLivePopupItemTypeNormal, ^(NSInteger index) {
        SS
        // 埋点上报
        [XYLivePushMultiLineBizTracker eventActionId80517WithRoomId:self.liveInfoService.roomId channelTabName:@"rematch" hasGoods:self.liveInfoService.roomInfo.hasGoods];
        // 事件回调
        self.didTapRematchHandler ? self.didTapRematchHandler() : nil;
    });
    XYLiveActionSheetViewController *sheetVC = [[XYLiveActionSheetViewController alloc] initWithTitle:nil items:@[stopItem, rematchItem]];
    [sheetVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.sheetVC = sheetVC;
}

// 展示个人资料卡
- (void)showUserCardWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    self.didTapUserHandler ? self.didTapUserHandler(inviteeInfo) : nil;
}

// 处理邀请操作
- (void)handleInviteOptWithInviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    // 执行撤销操作
    if (inviteeInfo.isInvited) {
        self.didTapCancelHandler ? self.didTapCancelHandler(inviteeInfo) : nil;
    } else {
        // 执行邀请操作
        self.didTapInviteHandler ? self.didTapInviteHandler(inviteeInfo) : nil;
    }
}

// 展示玩法切换确认弹窗
- (void)showSwitchBizAlertWithBizType:(XYLiveMultiLinkBizType)bizType {
    NSString *title = bizType == XYLiveMultiLinkBizTypePKGift ? @"确认发起礼物PK" : @"确认发起人气PK";
    NSString *msg = bizType == XYLiveMultiLinkBizTypePKGift ? @"选择确定后，将发起礼物PK" : @"选择确定后，将发起人气PK";
    WS
    XYLiveAlertViewController *alertVC = [[XYLiveAlertViewController alloc] initWithTitle:title message:msg cancelButtonTitle:@"取消" confirmButtonTitle:@"确认" handler:^(NSInteger index) {
        SS
        if (index == 1) {
            [self switchBizWithBizType:bizType extraInfo:nil completion:nil];
        }
    }];
    [alertVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.alertVC = alertVC;
}

// 玩法切换
- (void)switchBizWithBizType:(XYLiveMultiLinkBizType)bizType
                   extraInfo:(NSString *_Nullable)extraInfo
                  completion:(void(^_Nullable)(NSError *error))completion {
    WS
    [self.multiLinkService swithchBizWithTargetBizType:bizType bizExtraInfo:extraInfo completion:^(NSError * _Nonnull error) {
        SS
        // Toast提示
        error ? [XYAlert live_showTextItemWithError:error] : nil;
        // 执行回调
        completion ? completion(error) : nil;
    }];
}

#pragma mark - XYLiveMultiLinkListener

- (void)onRtcEnterRoomSuccess:(NSString *)userId {
    [self.inviteMorePanelVC dismissWithAnimated:YES complete:nil];
    [self.searchPanelVC dismissWithAnimated:YES complete:nil];
}

@end
