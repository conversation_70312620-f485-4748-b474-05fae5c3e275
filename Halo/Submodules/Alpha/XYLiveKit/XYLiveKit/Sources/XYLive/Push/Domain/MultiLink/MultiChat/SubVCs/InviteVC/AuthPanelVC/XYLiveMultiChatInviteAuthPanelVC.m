//
//  XYLiveMultiChatInviteAuthPanelVC.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/25.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatInviteAuthPanelVC.h"
#import <XYLiveFoundation/XYLiveFoundation-Swift.h>
#import <XYLiveKit/XYLiveMultiChatInvitePanelVM.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYUITheme/XYThemeManager.h>
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <Masonry/Masonry.h>
#import <XYMessageKit/XYMessageKit-Swift.h>
#import <XYLiveKit/UIViewController+LiveChildViewController.h>
#import <XYUIKit/XYUIKit-Swift.h>

@interface XYLiveMultiChatInviteAuthPanelVC ()

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   XYLivePushMultiChatCoreModel *coreModel;
@property (nonatomic, strong) UIImageView *imageView; // 占位视图
@property (nonatomic, strong) UILabel *titleLabel; // 标题
@property (nonatomic, strong) UIButton *openButton; // 开启按钮

@end

@implementation XYLiveMultiChatInviteAuthPanelVC

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                          coreModel:(XYLivePushMultiChatCoreModel *)coreModel {
    if (self = [super initWithNibName:nil bundle:nil]) {
        _containerVC = containerVC;
        _coreModel = coreModel;
    }
    return self;
}

- (void)setupSubViews {
    [super setupSubViews];
    // 创建占位图片
    [self setupImageView];
    // 创建标题
    [self setupTitleLabel];
    // 创建开启按钮
    [self setupOpenButton];
    
    // 布局
    [self.imageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.contentView);
        make.top.mas_equalTo(53);
        make.size.mas_equalTo(CGSizeMake(166, 147));
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(46);
        make.right.mas_equalTo(-46);
        make.top.equalTo(self.imageView.mas_bottom).offset(20);
        make.height.mas_equalTo(44);
    }];
    
    [self.openButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.contentView);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(18);
        make.size.mas_equalTo(CGSizeMake(160, 48));
    }];
}

#pragma mark - UI

- (void)setupImageView {
    UIImageView *imageView = [[UIImageView alloc] init];
    imageView.contentMode = UIViewContentModeScaleAspectFit;
    [imageView xylive_setImageWithURLWithLight:@"https://picasso-static.xiaohongshu.com/fe-platform/b1e02bc8df2fd1deeb58415920e25e358a4eea81.png" dark:@"https://picasso-static.xiaohongshu.com/fe-platform/5f7d66bc528fc0157989d7505a0b4867259d6bfc.png"];
    [self.contentView addSubview:imageView];
    self.imageView = imageView;
}

- (void)setupTitleLabel {
    UILabel *titleLabel = [[UILabel alloc] init];
    titleLabel.font = [UIFont fontWithName:@"PingFangSC-Regular" size:14];
    titleLabel.textColor = [XYLiveTokenColor desc];
    titleLabel.numberOfLines = 0;
    titleLabel.lineBreakMode = NSLineBreakByWordWrapping;
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineHeightMultiple = 1.12;
    paragraphStyle.alignment = NSTextAlignmentCenter;
    titleLabel.attributedText = [[NSMutableAttributedString alloc] initWithString:@"开启连线功能可以和观众更好的互动\n获得更多流量和关注" attributes:@{ NSParagraphStyleAttributeName: paragraphStyle }];
    [self.contentView addSubview:titleLabel];
    self.titleLabel = titleLabel;
}

- (void)setupOpenButton {
    UIButton *openButton = [UIButton buttonWithType:UIButtonTypeCustom];
    openButton.backgroundColor = XYLiveTokenColor.red;
    [openButton setTitleColor:XYLiveTokenColor.white forState:UIControlStateNormal];
    [openButton setTitle:@"开启观众连线" forState:UIControlStateNormal];
    openButton.titleLabel.font = [UIFont fontWithName:@"PingFangSC-Medium" size:16];
    openButton.layer.cornerRadius = 24;
    openButton.layer.masksToBounds = YES;
    [openButton addTarget:self action:@selector(didTapOpenChat:) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:openButton];
    self.openButton = openButton;
}

#pragma mark - XYLiveNavigationChildControllerProtocol

- (NSString *)XYLiveNav_title {
    return @"观众连线";
}

- (CGFloat)heightWhenPortrait {
    return kXYLiveMultiChatInvitePanelHeight;
}

#pragma mark - Event

// 点击开启连麦
- (void)didTapOpenChat:(UIButton *)sender {
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:@"--Auth-didTapOpen--"];
    // 展示Loading
    [XYAlert live_showLoadingInView:self.contentView];
    // 发起请求
    [self.coreModel updateSwitchStatus:YES completion:^(NSError * _Nonnull error) {
        // 移除Loading
        [[XYAlert sharedInstance] hideLoadingAlertItems];
        // Toast提示
        if (error) {
            [XYAlert live_showTextItemWithError:error];
        } else {
            // 关闭面板
            [self.containerVC dismissPopBottomVC:self.navigationChildController dismissAnimated:YES dismissComplete:nil];
            // 执行回调
            self.openSwitchHandler ? self.openSwitchHandler() : nil;
        }
    }];
}

@end
