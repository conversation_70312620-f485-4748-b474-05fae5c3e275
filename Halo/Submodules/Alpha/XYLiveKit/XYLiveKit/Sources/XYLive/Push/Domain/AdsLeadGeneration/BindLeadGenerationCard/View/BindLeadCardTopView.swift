//
//  BindLeadCardTopView.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/4/29.
//  Copyright © 2025 XingIn. All rights reserved.
//

@objcMembers
@objc(XYBindLeadCardTopView)
class BindLeadCardTopView: UIView {
    
    var closeClosure: (() -> Void)?
    var tipsClosure:(() -> Void)?
    var title: String?
    
    private lazy var backButton: UIButton = {
        let button = UIButton()
        button.addTarget(self, action: #selector(didTapCloseButton(sender:)), for: .touchUpInside)
        button.xypk_setEnlargeEdge(5)
        return button
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .ReDs.title.light()
        label.text = "线索留资"
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupData()
    }
    
    convenience init(title: String) {
        self.init()
        self.title = title
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func setupUI() {
        addSubview(titleLabel)
        addSubview(backButton)
    }
    
    func setupData() {
        titleLabel.font = UIFont(name: "PingFangSC-Medium", size: 16)
        backButton.setImage(Theme.icon.back_left_b.size(20).color(.ReDs.title.light()).image, for: .normal)

        backButton.snp.remakeConstraints { make in
            make.left.equalTo(self).offset(16)
            make.centerY.equalTo(self)
        }
        titleLabel.snp.remakeConstraints { make in
            make.center.equalTo(self)
        }
        
        if let title = self.title {
            self.titleLabel.text = title
        }
    }
    
    func didTapCloseButton(sender: UIButton) {
        closeClosure?()
    }
}
