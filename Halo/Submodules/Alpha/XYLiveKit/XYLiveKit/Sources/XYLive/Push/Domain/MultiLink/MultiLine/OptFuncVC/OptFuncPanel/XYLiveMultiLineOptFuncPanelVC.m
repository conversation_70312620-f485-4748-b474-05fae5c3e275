//
//  XYLiveMultiLineOptFuncPanelVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/21.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineOptFuncPanelVC.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYLiveFoundation/UIImage+XYLiveKit.h>
#import <XYUIKitCore/UIView+XYUIKCPointInside.h>
#import <XYLiveUIKit/XYLivePaddingLabel.h>
#import <XYLiveUIKit/XYLiveBaseButton.h>
#import <XYLiveKit/XYLiveMultiLineOptFuncFriendItemCell.h>
#import <XYLiveKit/XYLiveMultiLineOptFuncListBizItemCell.h>
#import <XYLiveKit/XYLiveMultiLineOptFuncGroupItem.h>
#import <XYLiveKit/XYLiveMultiLineOptFuncVM.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLivePushMultiLineBizTracker.h>
#import <Masonry/Masonry.h>

// 重用标识符
static NSString *const kXYLiveMultiLineOptFuncFriendItemCellIde = @"XYLiveMultiLineOptFuncFriendItemCell";
static NSString *const kXYLiveMultiLineOptFuncListBizItemCellIde = @"XYLiveMultiLineOptFuncListBizItemCell";

@interface XYLiveMultiLineOptFuncPanelVC()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, copy)   NSArray<XYLiveLinkHostInviteeInfo *> *inviteeInfoList;
@property (nonatomic, strong) XYLiveBaseButton *closeBtn;
@property (nonatomic, strong) XYLiveBaseButton *settingBtn;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) XYLiveMultiLineOptFuncVM *viewModel;

@end

@implementation XYLiveMultiLineOptFuncPanelVC

// 初始化
- (instancetype)initWithContianerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService
                    inviteeInfoList:(NSArray<XYLiveLinkHostInviteeInfo *> * _Nullable)inviteeInfoList {
    if (self = [super init]) {
        _containerVC = containerVC;
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
        _inviteeInfoList = inviteeInfoList;
    }
    return self;
}

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo {
    // 更新数据源
    [self.viewModel updateInviteState:state inviteeInfo:inviteeInfo];
    // 刷新列表
    [self.tableView reloadData];
}

#pragma mark - Overrides

- (void)setupSubViews {
    [super setupSubViews];
    self.containerView.backgroundColor = [XYLiveTokenColor bg];
    self.contentView.backgroundColor = [XYLiveTokenColor bg];
    // 创建列表视图
    [self setupTableView];
    
    // 布局
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
}

#pragma mark - 创建子视图

- (void)setupTableView {
    UITableView *tableView = [[UITableView alloc] init];
    tableView.backgroundColor = [UIColor clearColor];
    tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    tableView.showsVerticalScrollIndicator = NO;
    tableView.delegate = self;
    tableView.dataSource = self;
    if (@available(iOS 15.0, *)) {
        tableView.sectionHeaderTopPadding = 0;
    }
    // 注册cell
    [tableView registerClass:XYLiveMultiLineOptFuncFriendItemCell.class forCellReuseIdentifier:kXYLiveMultiLineOptFuncFriendItemCellIde];
    [tableView registerClass:XYLiveMultiLineOptFuncListBizItemCell.class forCellReuseIdentifier:kXYLiveMultiLineOptFuncListBizItemCellIde];
    [self.contentView addSubview:tableView];
    self.tableView = tableView;
}

#pragma mark - UITableViewDelegate, UITableViewDataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.viewModel.dataSource.count;
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    XYLiveMultiLineOptFuncGroupItem *group = [self.viewModel.dataSource safeObjectAtIndex:section];
    return group.items.count;
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    XYLiveMultiLineOptFuncGroupItem *group = [self.viewModel.dataSource safeObjectAtIndex:indexPath.section];
    return group.itemH;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    XYLiveMultiLineOptFuncGroupItem *group = [self.viewModel.dataSource safeObjectAtIndex:indexPath.section];
    XYLiveMultiLineOptFuncListItem *item = [group.items safeObjectAtIndex:indexPath.row];
    // 连线好友
    if ([item isKindOfClass:XYLiveMultiLineOptFuncListFriendItem.class]) {
        XYLiveMultiLineOptFuncFriendItemCell *cell = [tableView dequeueReusableCellWithIdentifier:kXYLiveMultiLineOptFuncFriendItemCellIde forIndexPath:indexPath];
        WS
        cell.didTapAddHandler = ^{
            SS
            // 埋点上报
            [XYLivePushMultiLineBizTracker eventActionId80514WithRoomId:self.liveInfoService.roomId hasGoods:self.liveInfoService.roomInfo.hasGoods];
            // 事件回调
            self.didTapAddHandler ? self.didTapAddHandler() : nil;
        };
        cell.didTapUserHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull inviteeInfo) {
            SS
            // 埋点上报
            [XYLivePushMultiLineBizTracker eventActionId80516WithRoomId:self.liveInfoService.roomId inviteeUserId:inviteeInfo.userId hasGoods:self.liveInfoService.roomInfo.hasGoods];
            // 事件回调
            self.didTapUserHandler ? self.didTapUserHandler(inviteeInfo) : nil;
        };
        cell.didTapCancelHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull inviteeInfo) {
            SS
            // 埋点上报
            [XYLivePushMultiLineBizTracker eventActionId80515WithRoomId:self.liveInfoService.roomId inviteeUserId:inviteeInfo.userId hasGoods:self.liveInfoService.roomInfo.hasGoods];
            // 事件回调
            self.didTapCancelHandler ? self.didTapCancelHandler(inviteeInfo) : nil;
        };
        // 数据绑定
        [cell bindDataSource:self.viewModel.dataSource indexPath:indexPath];
        cell.disableAdd = self.multiLinkService.bizType != XYLiveMultiLinkBizTypeLine;
        return cell;
    } else if ([item isKindOfClass:XYLiveMultiLineOptFuncListBizItem.class]){
        // 玩法
        XYLiveMultiLineOptFuncListBizItemCell *cell  = [tableView dequeueReusableCellWithIdentifier:kXYLiveMultiLineOptFuncListBizItemCellIde forIndexPath:indexPath];
        WS
        cell.didTapOptHandler = ^(XYLiveMultiLinkBizType bizType, XYLiveMultiLineBizOptType optType) {
            SS
            // 埋点上报
            NSString *channelTabName = @"initiate";
            if (optType == XYLiveMultiLineBizOptTypeClose) {
                channelTabName = @"close";
            } else if (optType == XYLiveMultiLineBizOptTypeReOpen) {
                channelTabName = @"rematch";
            }
            [XYLivePushMultiLineBizTracker eventActionId80518WithRoomId:self.liveInfoService.roomId channelTabName:channelTabName channelTabType:bizType == XYLiveMultiLinkBizTypePKGift ? @"1" : @"2" hasGoods:self.liveInfoService.roomInfo.hasGoods];
            // 合法性校验
            if (self.viewModel.bizType == XYLiveMultiLinkBizTypeLine && self.viewModel.linkMemberNum > 2) {
                [XYAlert live_showTextItemWithText:@"多人场景暂不支持PK"];
                return;
            }
            // 事件回调
            self.didTapOptHandler ? self.didTapOptHandler(bizType, optType) : nil;
        };
        // 数据绑定
        [cell bindDataSource:self.viewModel.dataSource indexPath:indexPath];
        return cell;
    }
    return nil;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return 16;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    return [UIView new];
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    XYLiveMultiLineOptFuncGroupItem *group = [self.viewModel.dataSource safeObjectAtIndex:section];
    return group.title.length ? 36 : CGFLOAT_MIN;
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    XYLiveMultiLineOptFuncGroupItem *group = [self.viewModel.dataSource safeObjectAtIndex:section];
    if (!group.title.length) { return nil; }
    XYLivePaddingLabel *titleView = [[XYLivePaddingLabel alloc] init];
    titleView.text = group.title;
    titleView.textColor = [XYLiveTokenColor neutralBlack];
    titleView.font = [UIFont fontWithName:@"PingFangSC-Medium" size:14];
    titleView.textInsets = UIEdgeInsetsMake(0, 16, 0, 0);
    titleView.backgroundColor = [XYLiveTokenColor bg];
    return titleView;
}

#pragma mark - XYLiveNavigationChildControllerProtocol

- (NSString *)XYLiveNav_title {
    return @"连线中";
}

- (NSArray<UIButton *> *)XYLiveNav_rightButtons {
    return @[self.settingBtn, self.closeBtn];
}

- (CGFloat)heightWhenPortrait {
    return kXYLiveMultiLineOptFuncPanelH;
}

- (BOOL)needReachBottom {
    return YES;
}

#pragma mark - Event

- (void)didTapClose:(UIButton *)sender {
    self.didTapCloseHandler ? self.didTapCloseHandler() : nil;
}

- (void)didTapSetting:(UIButton *)sender {
    self.didTapSettingHandler ? self.didTapSettingHandler() : nil;
}

#pragma mark - Lazy

- (XYLiveBaseButton *)closeBtn {
    if (_closeBtn == nil) {
        _closeBtn = [XYLiveBaseButton buttonWithType:UIButtonTypeCustom];
        [_closeBtn setImageWithLightURL:@"https://fe-platform.xhscdn.com/platform/104101l031j1dtvot1406crss5gt00000000000jg36i8u" darkURL:@"https://fe-platform.xhscdn.com/platform/104101l031j1dtvnr1g0mcrss5gt00000000000jul709q"];
        _closeBtn.xy_responseRectInset = UIEdgeInsetsMake(-5, -5, -5, -5);
        [_closeBtn addTarget:self action:@selector(didTapClose:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _closeBtn;
}

- (XYLiveBaseButton *)settingBtn {
    if (_settingBtn == nil) {
        _settingBtn = [XYLiveBaseButton buttonWithType:UIButtonTypeCustom];
        [_settingBtn setImage:[UIImage xy_liveKitBundleImage:@"link_setting"] forState:UIControlStateNormal];
        _settingBtn.xy_responseRectInset = UIEdgeInsetsMake(-5, -5, -5, -5);
        [_settingBtn addTarget:self action:@selector(didTapSetting:) forControlEvents:UIControlEventTouchUpInside];
    }
    return _settingBtn;
}

- (XYLiveMultiLineOptFuncVM *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[XYLiveMultiLineOptFuncVM alloc] initWithLiveInfoService:self.liveInfoService multiLinkService:self.multiLinkService inviteeInfoList:self.inviteeInfoList];
        WS
        _viewModel.onUpdateDataSourceHandler = ^{
            SS
            [self.tableView reloadData];
        };
    }
    return _viewModel;
}

@end
