//
//  XYLiveMultiLineMatchVM.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/30.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineMatchVM.h"
#import <XYLiveServiceProtocol/XYLiveMultiLineConsts.h>
#import <XYLiveKit/XYLiveCommonConst.h>

@interface XYLiveMultiLineMatchVM()

@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, assign) XYLiveMultiLinkMatchState state;

@end

@implementation XYLiveMultiLineMatchVM

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
    }
    return self;
}

// 发起匹配
- (void)startMatchWithSource:(XYLiveMultiLineMatchSource)source extraInfo:(NSString *)extraInfo {
    // 合法性校验
    if (self.isMatching) {
        [XYLogCollector xyLiveLogTag:kXYLiveMultiLineModuleName content:[NSString stringWithFormat:@"start match fail, state is not idle"]];
        return;
    }
    [XYLogCollector xyLiveLogTag:kXYLiveMultiLineModuleName content:[NSString stringWithFormat:@"start match,{source:%@, extraInfo:%@}", @(source), extraInfo]];
    // 发起请求
    [self.multiLinkService matchWithBizType:XYLiveMultiLinkBizTypeLine bizExtraInfo:nil];
}

// 发起重新匹配
- (void)startReMatchWithExtraInfo:(NSString *)extraInfo {
    // 发起请求
    [self.multiLinkService rematchWithBizType:XYLiveMultiLinkBizTypeLine bizExtraInfo:extraInfo completion:^(NSError * _Nonnull error) {
        // Toast提示
        error ? [XYAlert live_showTextItemWithError:error] : nil;
    }];
}

// 取消匹配
- (void)cancelMatch {
    [self.multiLinkService cancelMatchWithBizType:XYLiveMultiLinkBizTypeLine];
}


// 是否匹配中
- (BOOL)isMatching {
    return self.state == XYLiveMultiLinkMatchStateMatching;
}

#pragma mark - XYLiveMultiLinkListener

- (void)onMatchStartWithBizType:(XYLiveMultiLinkBizType)bizType sessionId:(NSString *)sessionId {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeLine) { return; }
    // 状态更新
    [self updateMatchState:XYLiveMultiLinkMatchStateMatching source:@"MATCH"];
}

- (void)onMatchResponseWithBizType:(XYLiveMultiLinkBizType)bizType sessionId:(NSString *)sessionId error:(NSError *)error {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeLine) { return; }
    // 处理匹配失败情况
    if (error) {
        // 状态更新
        [self updateMatchState:XYLiveMultiLinkMatchStateIdle source:@"MATCH"];
        // Toast提示
        [XYAlert live_showTextItemWithError:error];
    }
}

- (void)onCancelMatchResponseWithBizType:(XYLiveMultiLinkBizType)bizType sessionId:(NSString *)sessionId error:(NSError *)error {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeLine) { return; }
    // 取消成功
    if (error == nil) {
        // Toast提示
        [XYAlert live_showTextItemWithText:@"已结束匹配"];
        // 更新状态
        [self updateMatchState:XYLiveMultiLinkMatchStateIdle source:@"CANCEL-MATCH"];
    } else {
        // Toast提示
        [XYAlert live_showTextItemWithError:error];
    }
}

- (void)onMatchSuccessWithBizType:(XYLiveMultiLinkBizType)bizType sessionId:(NSString *)sessionId {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeLine) { return; }
    // 更新状态
    [self updateMatchState:XYLiveMultiLinkMatchStateMatched source:@"MATCH-SUCCESS"];
}

- (void)onMatchFailWithBizType:(XYLiveMultiLinkBizType)bizType sessionId:(NSString *)sessionId failReason:(XYLiveMultiLinkMatchFailReason)failReason failMsg:(nonnull NSString *)failMsg {
    // 合法性校验
    if (bizType != XYLiveMultiLinkBizTypeLine) { return; }
    // 更新状态
    [self updateMatchState:XYLiveMultiLinkMatchStateIdle source:@"MATCH-FAIL"];
    // Toast提示
    if (failMsg.length) {
        [XYAlert live_showTextItemWithText:failMsg];
    }
}

#pragma mark - Private

// 更新匹配状态
- (void)updateMatchState:(XYLiveMultiLinkMatchState)state source:(NSString *)source {
    // 合法性校验
    if (self.state == state) { return; }
    [XYLogCollector xyLiveLogTag:kXYLiveMultiLineModuleName content:[NSString stringWithFormat:@"update match state,{state:%@, source:%@}", @(state), source]];
    self.state = state;
    // 事件分发
    if ([self.delegate respondsToSelector:@selector(onUpdateMatchState:)]) {
        [self.delegate onUpdateMatchState:state];
    }
}

@end
