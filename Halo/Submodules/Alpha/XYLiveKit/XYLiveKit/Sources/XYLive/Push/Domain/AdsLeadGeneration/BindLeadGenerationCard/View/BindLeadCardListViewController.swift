//
//  BindLeadCardConsultViewController.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/4/29.
//  Copyright © 2025 XingIn. All rights reserved.
//

import UIKit
import XYAlphaUtils

typealias LoadMoreAction = () -> Void
typealias BindCardCellDidSelectedAction = (AdsLeadBindCardModel) -> Void
typealias ViewWillAppearAction = () -> Void

enum LeadCardType: Int {
    case unknown = 0    // 未知类型
    case formMsg = 1    // 表单消息
    case consult = 2    // 咨询消息
    
    var description: String {
        switch self {
        case .unknown:
            return "unknown"
        case .formMsg:
            return "form_msg"
        case .consult:
            return "consult"
        }
    }
}

@objcMembers
class BindLeadCardConsultViewController: BindLeadCardListViewController {
    fileprivate override func setupData() {
        self.loadingView.show(in: self.listView)
        self.serviceModel.fetchAdsLeadBindCardList(cardType: 2, roomId: Int64(XYLiveManager.shared().roomInfo.roomID)) { [weak self] listModel, error in
            guard let self = self else {
                return
            }
            
            self.loadingView.hide()
            
            if let _ = error {
                // 错误页面
                self.emptyView.isHidden = true
                self.listView.isHidden = true
                self.errorView.isHidden = false
            } else {
                let selectedCount = listModel?.cardList?.filter { $0.selected == true }.count ?? 0
                if listModel?.cardList?.count ?? 0 > 0 {
                    self.emptyView.isHidden = true
                    self.listView.isHidden = false
                    self.errorView.isHidden = true
                    self.updateData(dataSource: listModel?.cardList)
                    self.limitCount = listModel?.selectLimit ?? 0
                } else {
                    self.emptyView.isHidden = false
                    self.listView.isHidden = true
                    self.errorView.isHidden = true
                }

                self.bottomBarUpdateIfNeededAction?(self.shouldShowAllSelect(), self.emptyView.isHidden, self.errorView.isHidden, .consult)
                // limitCount 为全部卡片数
                self.dataSourceDidFetchedHandler?(selectedCount, self.listView.leadCardDataSource.count)
            }
            
            self.requestDidCompleteAction?()
        }
    }
    
    fileprivate override func loadMoreData() {
        self.serviceModel.fetchAdsLeadBindCardList(cardType: 2, roomId: Int64(XYLiveManager.shared().roomInfo.roomID)) { [weak self] listModel, error in
            guard let self = self else {
                return
            }
            
            guard error == nil else {
                self.listView.endloadMore()
                return
            }
            self.appendData(dataAppend: listModel?.cardList)
            self.listView.endloadMore()
            // 更新已选数目
            let selectedCount = self.listView.leadCardDataSource.filter { $0.selected == true }.count
            // limitCount 为全部卡片数
            self.dataSourceDidFetchedHandler?(selectedCount, self.listView.leadCardDataSource.count)
        }
    }
    
    fileprivate override func getLeadCardType() -> LeadCardType {
        return .consult
    }
}

@objcMembers
class BindLeadCardFormMsgViewController: BindLeadCardListViewController {
    fileprivate override func setupData() {
        self.loadingView.show(in: self.listView)
        self.serviceModel.fetchAdsLeadBindCardList(cardType: 1, roomId: Int64(XYLiveManager.shared().roomInfo.roomID)) { [weak self] listModel, error in
            guard let self = self else {
                return
            }
            self.loadingView.hide()
            
            if let _ = error {
                // 错误页面
                self.emptyView.isHidden = true
                self.listView.isHidden = true
                self.errorView.isHidden = false
            } else {
                
                let selectedCount = listModel?.cardList?.filter { $0.selected == true }.count ?? 0
                
                if listModel?.cardList?.count ?? 0 > 0 {
                    self.emptyView.isHidden = true
                    self.listView.isHidden = false
                    self.errorView.isHidden = true
                    self.updateData(dataSource: listModel?.cardList)
                    self.limitCount = listModel?.selectLimit ?? 0
                } else {
                    self.emptyView.isHidden = false
                    self.listView.isHidden = true
                    self.errorView.isHidden = true
                }
                
                self.bottomBarUpdateIfNeededAction?(self.shouldShowAllSelect(), self.emptyView.isHidden, self.errorView.isHidden, .formMsg)
                // limitCount 为全部卡片数
                self.dataSourceDidFetchedHandler?(selectedCount, self.listView.leadCardDataSource.count)
            }
            
            self.requestDidCompleteAction?()
        }
    }
    
    fileprivate override func loadMoreData() {
        self.serviceModel.fetchAdsLeadBindCardList(cardType: 1, roomId: Int64(XYLiveManager.shared().roomInfo.roomID)) { [weak self] listModel, error in
            guard let self = self else {
                return
            }
            
            guard error == nil else {
                self.listView.endloadMore()
                return
            }
            
            self.appendData(dataAppend: listModel?.cardList)
            self.listView.endloadMore()
            self.limitCount = listModel?.selectLimit ?? 0
            self.bottomBarUpdateIfNeededAction?(self.shouldShowAllSelect(), self.emptyView.isHidden, self.errorView.isHidden, .formMsg)
            // 更新已选数目
            let selectedCount = self.listView.leadCardDataSource.filter { $0.selected == true }.count
            // limitCount 为全部卡片数
            self.dataSourceDidFetchedHandler?(selectedCount, self.listView.leadCardDataSource.count)
        }
    }
    
    fileprivate override func getLeadCardType() -> LeadCardType {
        return .formMsg
    }
}

@objcMembers
class BindLeadCardListViewController: UIViewController {
    
    var limitCount = 0
    var bottomBarUpdateIfNeededAction: ((Bool, Bool, Bool, LeadCardType) -> Void)?
    var dataSourceDidFetchedHandler: ((Int, Int) -> Void)?
    var leadCardDataSource: [AdsLeadBindCardModel] {
        return self.listView.leadCardDataSource
    }
    
    var cellSelectedAction: BindCardCellDidSelectedAction? {
        didSet {
            self.listView.cellSelectedAction = cellSelectedAction
        }
    }
    var viewWillAppearAction: ViewWillAppearAction?
    var requestDidCompleteAction: (() -> Void)?
    
    fileprivate lazy var emptyView = BindAdsLeadsCardEmptyView(type: self.getLeadCardType())
    fileprivate lazy var errorView = {
        let errorView = BindAdsLeadsCardErrorView()
        errorView.refreshAction = { [weak self] in
            self?.setupData()
        }
        return errorView
    }()
    
    fileprivate let serviceModel = AdsBindLeadCardServiceModel()
    
    lazy var listView: BindLeadCardListView = {
        let listView = BindLeadCardListView()
        listView.loadMoreAction = { [weak self] in
            guard let self = self else { return }
            self.loadMoreData()
        }
        
        return listView
    }()
    
    // Loading
    fileprivate lazy var loadingView = XYAlertLoadingView.live_createAlertLoading()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.listView.leadCardType = self.getLeadCardType()
        self.setupUI()
        self.setupData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.viewWillAppearAction?()
        self.listView.contentListView.reloadData()
        self.bottomBarUpdateIfNeededAction?(shouldShowAllSelect(), self.emptyView.isHidden, self.errorView.isHidden, getLeadCardType())
    }
    
    func shouldShowAllSelect() -> Bool {
        
        guard self.limitCount > 0 else {
            return true
        }
        
        let shouldShow = self.limitCount >= self.leadCardDataSource.count
        return shouldShow
    }
    
    fileprivate func setupData() {
        
    }
    
    fileprivate func loadMoreData() {
        
    }
    
    fileprivate func getLeadCardType() -> LeadCardType {
        return .unknown
    }
    
    private func setupUI() {
        self.view.addSubview(errorView)
        self.view.addSubview(emptyView)
        self.view.addSubview(listView)
        listView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        emptyView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        errorView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    fileprivate func updateData(dataSource: [AdsLeadBindCardModel]?) {
        guard let dataSource = dataSource else {
            return
        }
        self.listView.update(leadCardCellModels: dataSource)
    }
    
    func reloadData() {
        self.listView.contentListView.reloadData()
    }
    
    fileprivate func appendData(dataAppend: [AdsLeadBindCardModel]?) {
        guard let dataAppend = dataAppend else {
            return
        }
        self.listView.append(leadCardCellModels: dataAppend)
    }
}

@objcMembers
class BindLeadCardListView: UIView {
    var loadMoreAction: LoadMoreAction?
    var cellSelectedAction: BindCardCellDidSelectedAction?
    var leadCardDataSource =  [AdsLeadBindCardModel]()
    fileprivate var leadCardType: LeadCardType = .unknown
    
    fileprivate lazy var contentListView: UITableView = {
        let listView = UITableView(frame: .zero, style: .plain)
        listView.backgroundColor = .ReDs.background.light()
        listView.delegate = self
        listView.dataSource = self
        listView.register(BindLeadCardCell.self, forCellReuseIdentifier: BindLeadCardCellConstant.bindLeadCardCellReuseIdentifier)
        listView.separatorStyle = .none
        listView.showsVerticalScrollIndicator = false
        listView.xy_addInfinite { [weak self] in
            guard let self = self else { return }
            self.loadMoreAction?()
        }
        
        return listView
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        self.backgroundColor = .ReDs.background.light()
        self.addSubview(self.contentListView)
        self.contentListView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
}

extension BindLeadCardListView {
    func update(leadCardCellModels: [AdsLeadBindCardModel]) {
        self.leadCardDataSource = leadCardCellModels
        self.contentListView.reloadData()
    }
    
    func append(leadCardCellModels: [AdsLeadBindCardModel]) {
        self.leadCardDataSource.append(contentsOf: leadCardCellModels)
        self.contentListView.reloadData()
    }
    
    func update(showsInfiniteScrolling: Bool) {
        self.contentListView.showsInfiniteScrolling = showsInfiniteScrolling
    }
    
    func endloadMore() {
        self.contentListView.endFooterRefresh()
    }
}

extension BindLeadCardListView: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.leadCardDataSource.count
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return BindLeadCardCellConstant.bindLeadCardCellHeight
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if let cell = tableView.dequeueReusableCell(withIdentifier: BindLeadCardCellConstant.bindLeadCardCellReuseIdentifier, for: indexPath) as? BindLeadCardCell,
           let itemData = cellModel(at: indexPath) {
            cell.updateCell(with: itemData)
            return cell
        }
        
        return UITableViewCell()
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        guard let itemData = cellModel(at: indexPath) else {
            return
        }
        
        didSelectCell(data: itemData)
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        guard let itemData = cellModel(at: indexPath) else {
            return
        }
        
        self.trackExposureReport(itemData)
    }
    
    func didSelectCell(data: AdsLeadBindCardModel) {
        // 更新当前选中项的状态
        self.cellSelectedAction?(data)
        self.contentListView.reloadData()
    }
}

extension BindLeadCardListView {
    // 获取对应的 model 对象
    func cellModel(at indexPath: IndexPath) -> AdsLeadBindCardModel? {
        if self.leadCardDataSource.indices.contains(indexPath.row) {
            return self.leadCardDataSource[indexPath.row]
        }
        return nil
    }
}

extension BindLeadCardListView {
    func trackExposureReport(_ cardModel: AdsLeadBindCardModel) {
        XYAnalyticsOrganizer._
            .ads
            .cardId(cardModel.cardId ?? "")
            .page
            .pageInstance(.livePreparePage)
            .event
            .action(.impression)
            .pointId(79754 /* 直播准备页/选卡页-卡片/曝光 */)
            .isGoto(2)
            .moduleId(47364)
            .send()
    }
}
