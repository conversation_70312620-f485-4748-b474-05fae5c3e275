//
//  XYLiveMultiChatInviteListModel.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatInviteListModel.h"

@implementation XYLiveMultiChatInviteListItem
@end

@implementation XYLiveMultiChatInviteListEmptyItem
@end

@implementation XYLiveMultiChatInviteListFriendItem
@end

@implementation XYLiveMultiChatInviteListGroupItem

@end

@implementation XYLiveMultiChatInviteListFriendModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"userId": @"user_id",
        @"nickName": @"nickname",
        @"avatarUrl": @"avatar",
        @"followStatus": @"follow_status"
    };
}

@end

@implementation XYLiveMultiChatInviteListAudienceItem

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"userId": @"user_id",
        @"nickName": @"nickname",
        @"avatarURL": @"avatar",
        @"followStatus": @"follow_status",
        @"fansNum": @"fans_num",
        @"roomId": @"room_id",
        @"sendCoins": @"send_coins",
        @"fansClubInfo": @"fans_group",
    };
}

- (XYLiveUserInfo *)userInfo {
    XYLiveUserInfo *userInfo = [[XYLiveUserInfo alloc] init];
    userInfo.userID = self.userId;
    userInfo.avatar = self.avatarURL;
    userInfo.nickname = self.nickName;
    userInfo.redCoins = self.sendCoins;
    return userInfo;
}

@end

@implementation XYLiveMultiChatInviteListModel

+ (NSDictionary<NSString *,id> *)modelCustomPropertyMapper {
    return @{
        @"friendList": @"friend_list",
        @"audienceList": @"viewer_list"
    };
}

+ (NSDictionary<NSString *,id> *)modelContainerPropertyGenericClass {
    return @{
        @"friendList": XYLiveMultiChatInviteListFriendModel.class,
        @"audienceList": XYLiveMultiChatInviteListAudienceItem.class
    };
}

@end
