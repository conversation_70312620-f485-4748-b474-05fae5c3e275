//
//  XYLiveMultiPKProgressView.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/14.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiPKProgressView.h"
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <Masonry/Masonry.h>
#import <RedI18N/RedI18N-Swift.h>
#import <I18N/I18N-Swift.h>
#import <XYLiveKit/XYLiveMultiPKScoreView.h>
#import <XYLiveKit/XYLiveKit-Swift.h>
#import <XYLiveUIKit/LOTAnimationView+XYLiveUIKit.h>

@interface XYLiveMultiPKProgressView()

@property (nonatomic, strong) XYLiveMultiPKLeftScoreView *leftScoreView;
@property (nonatomic, strong) XYLiveMultiPKRightScoreView *rightScoreView;
@property (nonatomic, strong) LOTAnimationView *lottieView;
@property (nonatomic, assign) BOOL isLoaded;

@end

@implementation XYLiveMultiPKProgressView

- (instancetype)initWithFrame:(CGRect)frame {
    if (self = [super initWithFrame:frame]) {
        // 创建子视图
        [self setupSubviews];
        // 超出裁剪
        self.clipsToBounds = YES;
    }
    return self;
}

#pragma mark - Public

- (void)updateUIWithBizType:(XYLiveMultiLinkBizType)bizType {
    if (bizType == XYLiveMultiLinkBizTypePKGift) {
        self.leftScoreView.titleLabel.text = NSLocalizedString(L.live.live_pk_ourside, @"我方");
        self.leftScoreView.gradientView.gradientLayer.colors = @[
            (__bridge id)[UIColor colorWithRed:255.0 / 255.0 green:36.0 / 255.0 blue:66.0 / 255.0 alpha:1.0].CGColor,
            (__bridge id)[UIColor colorWithRed:255.0 / 255.0 green:102.0 / 255.0 blue:36.0 / 255.0 alpha:1.0].CGColor
        ];
        self.rightScoreView.titleLabel.text = NSLocalizedString(L.live.live_pk_opponentside, @"对方");
        self.rightScoreView.gradientView.gradientLayer.colors = @[
            (__bridge id)[UIColor colorWithRed:66.0 / 255.0 green:187.0 / 255.0 blue:255.0 / 255.0 alpha:1.0].CGColor,
            (__bridge id)[UIColor colorWithRed:56.0 / 255.0 green:108.0 / 255.0 blue:238.0 / 255.0 alpha:1.0].CGColor
        ];
    } else {
        self.leftScoreView.titleLabel.text = NSLocalizedString(L.live.live_pk_like1, @"我方点赞");
        self.leftScoreView.gradientView.gradientLayer.colors = @[
            (__bridge id)Theme.color.hexString(@"#FF0D47").CGColor,
            (__bridge id)Theme.color.hexString(@"#FF8685").CGColor
        ];
        self.rightScoreView.titleLabel.text = NSLocalizedString(L.live.live_pk_like2, @"对方点赞");
        self.rightScoreView.gradientView.gradientLayer.colors = @[
            (__bridge id)Theme.color.hexString(@"#23E9B9").CGColor,
            (__bridge id)Theme.color.hexString(@"#00C1EC").CGColor
        ];
    }
}

// 更新战队信息
- (void)updateLeftTeamInfo:(XYLiveMultiPKTeamInfo *)leftTeamInfo rightTeamInfo:(XYLiveMultiPKTeamInfo *)rightTeamInfo {
    NSInteger leftScore  = leftTeamInfo.score;
    NSInteger rightScore = rightTeamInfo.score;
    // 更新分数
    self.leftScoreView.scoreLabel.text = [NSString stringWithFormat:@"%@",@(leftScore)];
    self.rightScoreView.scoreLabel.text = [NSString stringWithFormat:@"%@",@(rightScore)];
    // 更新进度
    CGFloat ratio = [self calculateProgressWithLeftScore:leftScore rightScore:rightScore];
    CGFloat leftWidth = CGRectGetWidth(self.bounds) * ratio;
    CGFloat rightWidth = CGRectGetWidth(self.bounds) * (1 - ratio);
    CGFloat lottieViewWH = 30;
    CGFloat viewH = CGRectGetHeight(self.bounds);
    // 首次加载视图不触发动画
    if (!self.isLoaded) {
        self.isLoaded = YES;
        self.leftScoreView.frame = CGRectMake(0, 0, leftWidth, viewH);
        self.rightScoreView.frame = CGRectMake(leftWidth, 0, rightWidth, viewH);
        self.lottieView.frame = CGRectMake(leftWidth - lottieViewWH * 0.5, (viewH - lottieViewWH) * 0.5, lottieViewWH, lottieViewWH);
    } else {
        [UIView animateWithDuration:0.25f animations:^{
            self.leftScoreView.frame = CGRectMake(0, 0, leftWidth, viewH);
            self.rightScoreView.frame = CGRectMake(leftWidth, 0, rightWidth, viewH);
            self.lottieView.frame = CGRectMake(leftWidth - lottieViewWH * 0.5, (viewH - lottieViewWH) * 0.5, lottieViewWH, lottieViewWH);
            [self layoutIfNeeded];
        } completion:^(BOOL finished) {
        }];
    }
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 创建左侧分数视图
    [self setupLeftScoreView];
    // 创建右侧分数视图
    [self setupRightScoreView];
    // 创建Lottie动画视图
    [self setupLottieView];
}

- (void)setupLeftScoreView {
    XYLiveMultiPKLeftScoreView *leftScoreView = [[XYLiveMultiPKLeftScoreView alloc] init];
    [self addSubview:leftScoreView];
    self.leftScoreView = leftScoreView;
}

- (void)setupRightScoreView {
    XYLiveMultiPKRightScoreView *rightScoreView = [[XYLiveMultiPKRightScoreView alloc] init];
    [self addSubview:rightScoreView];
    self.rightScoreView = rightScoreView;
}

- (void)setupLottieView {
    LOTAnimationView *lottieView = [[LOTAnimationView alloc] init];
    [lottieView xylive_playWithName:@"pkCutLine" completion:^(BOOL finished) {
        [XYLogCollector xyLiveLogTag:kXYLiveMultiPKModuleName content:[NSString stringWithFormat:@"pk lottie <finished:%d>", finished]];
    }];
    lottieView.loopAnimation = YES;
    [self addSubview:lottieView];
    self.lottieView = lottieView;
}

#pragma mark - Private

- (CGFloat)calculateProgressWithLeftScore:(NSInteger)leftScore rightScore:(NSInteger)rightScore {
    CGFloat ratio = 0.5;
    CGFloat minRatio = 0.22;
    CGFloat maxRatio = 0.78;
    if (leftScore < rightScore) {
        if (leftScore == 0) {
            ratio = minRatio;
        } else {
            ratio = (1.0 * leftScore) / (1.0 * (leftScore + rightScore));
        }
    } else if (leftScore > rightScore) {
        if (rightScore == 0) {
            ratio = maxRatio;
        } else {
            ratio = (1.0 * leftScore) / (1.0 * (leftScore + rightScore));
        }
    }
    // 兜底处理
    ratio = MAX(ratio, minRatio);
    ratio = MIN(ratio, maxRatio);
    return ratio;
}

@end
