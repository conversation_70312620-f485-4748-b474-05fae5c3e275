//
//  XYLiveMultiLineMatchVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/28.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineMatchVC.h"
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineConsts.h>
#import <XYLiveKit/XYLiveAlertViewController.h>
#import <XYLiveKit/XYLiveMultiLineMatchVM.h>
#import "XYLiveMultiLineMatchPanelVC.h"

@interface XYLiveMultiLineMatchVC ()<XYLiveMultiLineMatchDelegate>

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, weak)   XYLiveMultiLineMatchPanelVC *panelVC;
@property (nonatomic, weak)   XYLiveAlertViewController *alertVC;
@property (nonatomic, strong) NSHashTable<id<XYLiveMultiLineMatchListener>> *listeners;
@property (nonatomic, strong) XYLiveMultiLineMatchVM *viewModel;

@end

@implementation XYLiveMultiLineMatchVC

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    // 注册监听
    [self.multiLinkService registerListener:self.viewModel];
}

// 发起匹配
- (void)startMatchWithSource:(XYLiveMultiLineMatchSource)source extraInfo:(NSString * _Nullable)extraInfo {
    // 判断是否处于匹配中
    if ([self isMatching]) {
        // 展示匹配中面板
        [self showMatchPanel];
    }
    [self.viewModel startMatchWithSource:source extraInfo:extraInfo];
}

// 展示匹配中面板
- (void)showMatchPanel {
    XYLiveMultiLineMatchPanelVC *panelVC = [[XYLiveMultiLineMatchPanelVC alloc] initWithContainerVC:self.containerVC liveInfoService:self.liveInfoService];
    WS
    panelVC.didTapCancelHandler = ^{
        SS
        [self cancelMatch];
    };
    [panelVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.panelVC = panelVC;
}

// 发起重新匹配
- (void)startReMatchWithExtraInfo:(NSString * _Nullable)extraInfo {
    WS
    XYLiveAlertViewController *alertVC = [[XYLiveAlertViewController alloc] initWithTitle:@"确认重新匹配" message:@"选择确定后，你将断开当前连线进行重新匹配" cancelButtonTitle:@"取消" confirmButtonTitle:@"确认" handler:^(NSInteger index) {
        SS
        if (index == 1) {
            [self.viewModel startReMatchWithExtraInfo:extraInfo];
        }
    }];
    [alertVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.alertVC = alertVC;
}

// 取消匹配
- (void)cancelMatch {
    [self.viewModel cancelMatch];
}

// 是否匹配中
- (BOOL)isMatching {
    return [self.viewModel isMatching];
}

// 注册监听
- (void)registerListener:(id<XYLiveMultiLineMatchListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners addObject:listener];
}

// 取消注册监听
- (void)unregisterListener:(id<XYLiveMultiLineMatchListener>)listener {
    if (![NSThread isMainThread] || !listener) {
        NSCAssert(NO, @"%@ only support main thread call and listener should not be nil", NSStringFromSelector(_cmd));
        return;
    }
    [self.listeners removeObject:listener];
}

// 关闭弹窗
- (void)hideAllPanel {
    [self.alertVC dismissWithAnimated:YES complete:nil];
    [self.panelVC dismissWithAnimated:YES complete:nil];
}

#pragma mark - XYLiveMultiLineMatchDelegate

- (void)onUpdateMatchState:(XYLiveMultiLinkMatchState)state {
    // 事件分发
    [self.listeners.allObjects enumerateObjectsUsingBlock:^(id<XYLiveMultiLineMatchListener>  _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if ([obj respondsToSelector:@selector(onLineUpdateMatchState:)]) {
            [obj onLineUpdateMatchState:state];
        }
    }];
    // 关闭匹配中面板
    if (state != XYLiveMultiLinkMatchStateMatching) {
        [self hideAllPanel];
    }
}
   
#pragma mark - Lazy

- (NSHashTable<id<XYLiveMultiLineMatchListener>> *)listeners {
    if (_listeners == nil) {
        _listeners = [NSHashTable weakObjectsHashTable];
    }
    return _listeners;
}

- (XYLiveMultiLineMatchVM *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[XYLiveMultiLineMatchVM alloc] initWithLiveInfoService:self.liveInfoService multiLinkService:self.multiLinkService];
        _viewModel.delegate = self;
    }
    return _viewModel;
}

@end
