//
//  XYLivePushRoomVC+CoreInternal.h
//  XYLiveKit
//
//  Created by 周博立 on 2020/8/4.
//  Copyright © 2020 XingIn. All rights reserved.
//
@import XYLivePlayManager;
@import XYLivePusher;
@import XYLiveFoundation;
#import <XYLiveKit/XYLivePushRoomViewController.h>
#import <XYLiveKit/XYLiveBlurBackgroundView.h>
#import <XYLiveKit/XYLivePushDecorateView.h>
#import <XYLiveKit/XYLiveStopPushBackgroundView.h>
#import <XYLiveKit/XYLivePushDecorateViewModel.h>
#import <XYLiveKit/XYLiveShieldWordViewController.h>
#import <XYLiveKit/XYLiveHostGiftListViewController.h>
#import <XYLiveKit/XYLiveShoppingFansListViewController.h>
#import <XYLiveKit/XYLiveTopupViewController.h>
#import <XYLiveKit/XYIMModel.h>
#import "XYLiveAlertView.h"
#import "XYLivePlayingRenderContainerView.h"

@class XYLiveLetterBoxListViewController;
@class XYLiveTextEditView;
@class XYLivePushGoodsOnsaleViewController;
@class XYLivePushCategoryGoodsOnsaleViewController;
@class XYLiveVoiceAnnoncementSettingViewController;
@class XYLiveMorePanelViewController;
@class XYLiveSettingPanelViewController;
@class XYLivePushBGMusicViewController;
@class XYLivePrepCoverSelectViewController;
@class XYLiveIMChatDebugViewController;
//@class XYLivePlayingRenderContainerView;
@class XYLiveGiftDomain;
@class XYFeelBorderDomain;
@class XYLiveEffectDomain;

@interface XYLivePushRoomViewController () {
    __weak XYLiveWebViewController *_webVC;
}

@property (nonatomic, assign) NSInteger pushAudioCheckCountdown;
@property (nonatomic, assign) NSInteger pushReconnChance;
@property (nonatomic, assign) BOOL needInitLivePush;
@property (nonatomic, assign) BOOL hasRetryJoin;

@property (nonatomic, strong) XYLiveRtmpPusher *pusher;
@property (nonatomic, strong) XYLiveKasaPusher *kasaPusher;
@property (nonatomic, strong) XYLiveFilterConfigModel *filter;
@property (nonatomic, strong) XYLivePushDecorateViewModel *decorateVM;
@property (nonatomic, strong) XYLiveRoomInfo *roomInfo;
@property (nonatomic, strong) RTCChannelSession *channelSession;
@property (nonatomic, strong) XYLiveLoginParam *loginParam;
@property (nonatomic, assign) XYLiveRoomBizPushStyle bizPushStyle;

@property (nonatomic, weak) UIImageView *screenshotView;
@property (nonatomic, strong) UIView *cameraRenderingContainerView;
@property (nonatomic, strong) XYLiveBlurBackgroundView *blurBGView;
@property (nonatomic, strong) XYLiveAlertView *liveAlertView;
@property (nonatomic, strong) XYLiveStopPushBackgroundView *stopPushBGView;
@property (nonatomic, strong) XYLiveCustomAlertView *screenCaptureView;
@property (nonatomic, strong) XYLivePushDecorateView *decorateView;

@property (nonatomic, strong) UIView *renderContainerView;
// V8.11，电脑开播主播端拉流
@property (nonatomic, strong) XYLivePlayingRenderContainerView *playerRenderingView;

@property (nonatomic, strong) XYLiveMorePanelViewController *morePanelVC;
@property (nonatomic, strong) XYLiveSettingPanelViewController *settingPanelVC;
@property (nonatomic, strong) XYLiveShieldWordViewController *shieldWordVC;
@property (nonatomic, strong) XYLiveHostGiftListViewController *hostGiftListVC;
@property (nonatomic, weak) XYLiveShoppingFansListViewController *shoppingFansListVC;
@property (nonatomic, strong) XYLiveVoiceAnnoncementSettingViewController *voiceAnnounceSettingVC;
// 在需要时持有当前的alert
@property (nonatomic, strong) XYAlertItem *currentAlertItem;
@property (nonatomic, weak) XYLivePushBGMusicViewController *bgmVC;// from V7.44，推流背景音

// alfred
@property (nonatomic, weak) XYLiveIMChatDebugViewController *imDebugVC;

@property (nonatomic, assign) NSInteger submitRetryCount;
@property (nonatomic, assign) NSInteger uploadCoverRetryCount;

@property (nonatomic, strong) XYLiveGiftDomain *giftDomain;
@property (nonatomic, strong) XYLiveEffectDomain *effectDomain;
@property (nonatomic, strong, nullable) XYLiveFeelBorderDomain *feelBorderDomain;

@end
