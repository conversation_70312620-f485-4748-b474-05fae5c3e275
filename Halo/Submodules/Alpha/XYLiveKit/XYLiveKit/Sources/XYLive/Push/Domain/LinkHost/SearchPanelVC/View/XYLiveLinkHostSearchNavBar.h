//
//  XYLiveLinkHostSearchNavBar.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/21.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveLinkHostSearchNavBar : UIView

// 点击取消事件回调
@property (nonatomic, copy) void(^didTapCancelHandler)(void);
// 点击搜索事件回调
@property (nonatomic, copy) void(^didTapSearchHandler)(NSString *searchText);
// 取消键盘
- (void)resignFirstResponder;

@end

NS_ASSUME_NONNULL_END
