//
//  AdsBindLeadCardServiceModel.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/4/27.
//  Copyright © 2025 XingIn. All rights reserved.
//

import XYModel
import XYAPIRoute

typealias RequestCardListReqCompletion = (AdsLeadBindCardFetchListModel?, Error?) -> Void

@objcMembers
class AdsBindLeadCardServiceModel: NSObject {
    private static let kBindCardRequestPageSize : Int32 = 10
    var pageNum: Int = 1
    // 获取绑定卡片列表
    func fetchAdsLeadBindCardList(cardType: Int64, roomId: Int64, completion: @escaping RequestCardListReqCompletion) {
        let api = XYAPIRouter.shared.buildURL()
        api.path = "/api/edith/ads/social/live/card/list"
        api.method = .WSHTTPMPOST
        api.urlEncoding = WorldSnakeJsonEncoding.jsonEncoding
        api.addingQueryItem("pageNum", value: String(self.pageNum))
        api.addingQueryItem("pageSize", value: String(AdsBindLeadCardServiceModel.kBindCardRequestPageSize))
        api.addingQueryItem("cardType", value: String(cardType))
        api.addingQueryItem("roomId", value: String(roomId))
        // 请求添加卡片接口
        api.data().responseModel(type: AdsLeadBindCardFetchListModel.self) { [weak self] response in
            guard let self = self else {
                return
            }
            switch response.result {
            case .success(let resourceModel):
                if resourceModel.cardList?.count ?? 0 > 0 {
                    self.pageNum += 1
                }
                completion(resourceModel, nil)
            case .failure(let error):
                completion(nil, error)
            }
        }
    }
}

@objcMembers
class AdsAttachLeadCardServiceModel: NSObject {
    // 操作绑定卡片请求
    func requestAdsLeadAttachCardList(roomId: Int64, cradList: [String], completion: @escaping ((Bool, Int64?, String?) -> Void)) {
        let api = XYAPIRouter.shared.buildURL()
        api.path = "/api/edith/ads/social/live/card/bind"
        api.method = .WSHTTPMPOST
        api.urlEncoding = WorldSnakeJsonEncoding.jsonEncoding
        api.addingQueryItem("roomId", value: String(roomId))
        api.addingQueryItem("cardIdList", value: cradList)
        // 请求添加卡片接口
        api.data().responseData { response in
            guard response.error == nil,
                  let data = response.data,
                  let result = try? JSONSerialization.jsonObject(with: data, options: .allowFragments),
                  let json = result as? [String: Any]
            else {
                completion(false, nil, "绑卡失败")
                return
            }
            
            guard let code = json["code"] as? Int, code == 0 else {
                let message = json["msg"] as? String
                completion(false, nil, message)
                return
            }

            let dataBean = json["data"] as? [String: Any]
            let cardCount = dataBean?["succCount"] as? Int64
            // 回调绑定卡片的条数
            completion(true, cardCount, "")
        }
    }
}

@objcMembers
class AdsDisplayLeadCardServiceModel: NSObject {
    // 获取展示卡片列表
    func fetchAdsLeadDisplayCardList(roomId: Int64, cardType: Int64, completion: @escaping RequestCardListReqCompletion) {
        let api = XYAPIRouter.shared.buildURL()
        api.path = "/api/edith/ads/social/live/card/tob/bind/list"
        api.method = .WSHTTPMGET
        api.addingQueryItem("roomId", value: String(roomId))
        api.addingQueryItem("cardType", value: cardType)
        // 请求添加卡片接口
        api.data().responseModel(type: AdsLeadBindCardFetchListModel.self) { response in
            switch response.result {
            case .success(let resourceModel):
                completion(resourceModel, nil)
            case .failure(let error):
                completion(nil, error)
            }
        }
    }
}

@objcMembers
class AdsLeadCardClueAuthServiceModel: NSObject {
    // 请求线索主播权限
    func requestAdsLeadCardAuth(completion: @escaping ((Bool, Error?) -> Void)) {
        let api = XYAPIRouter.shared.buildURL()
        api.path = "/api/edith/ads/social/live/clue/auth/check"
        api.method = .WSHTTPMGET
        // 请求操作卡片接口
        api.data().responseJson { response in
            if case let .success(result) = response.result,
               let val = result as? [String: Any],
               let data = val["data"] as? [String: Any],
               let hasClueAuth = data["hasClueAuth"] as? Bool
            {
                completion(hasClueAuth, nil)
            } else {
                completion(false, response.error)
            }
        }
    }
}

@objcMembers
class AdsFetchLeadCardInfoServiceModel: NSObject {
    // 拉取讲解中的卡片
    func requestAdsLeadCardInfo(roomId: Int64 = 0, completion: @escaping ((Bool, [String: Any]?) -> Void)) {
        let api = XYAPIRouter.shared.buildURL()
        api.path = "/api/edith/ads/social/live/card/pop/info"
        api.method = .WSHTTPMGET
        api.addingQueryItem("roomId", value: String(roomId))
        // 请求操作卡片接口
        api.data().responseData { response in
            guard response.error == nil,
                  let data = response.data,
                  let result = try? JSONSerialization.jsonObject(with: data, options: .allowFragments),
                  let json = result as? [String: Any]
            else {
                completion(false, nil)
                return
            }
            
            guard let code = json["code"] as? Int, code == 0 else {
                let _ = json["msg"] as? String
                completion(false, nil)
                return
            }
            
            guard let cardInfo = json["data"] as? [String: Any] else {
                completion(false, nil)
                return
            }
            
            completion(true, cardInfo)
        }
    }
}

@objcMembers
class AdsOperateLeadCardServiceModel: NSObject {
    // 操作卡片
    func operateAdsLeadCardList(operation: Int64 = 0, roomId: Int64 = 0, cardId: Int64 = 0, completion: @escaping ((Bool, String?) -> Void)) {
        let api = XYAPIRouter.shared.buildURL()
        api.path = "/api/edith/ads/social/live/card/operate"
        api.method = .WSHTTPMPOST
        api.urlEncoding = WorldSnakeJsonEncoding.jsonEncoding
        api.addingQueryItem("roomId", value: String(roomId))
        api.addingQueryItem("cardId", value: cardId)
        api.addingQueryItem("operateType", value: operation)
        // 请求操作卡片接口
        api.data().responseData { response in
            guard response.error == nil,
                  let data = response.data,
                  let result = try? JSONSerialization.jsonObject(with: data, options: .allowFragments),
                  let json = result as? [String: Any]
            else {
                completion(false, "卡片操作失败")
                return
            }
            
            guard let code = json["code"] as? Int, code == 0 else {
                let message = json["msg"] as? String
                completion(false, message)
                return
            }
            
            completion(true, nil)
        }
    }
}

@objcMembers
public class AdsLeadBindCardFetchListModel: NSObject, XYModelProtocol {
    var total = 0
    var selectLimit = 0
    var inLineCnt = 0
    var formSubmitCnt = 0
    var cardList: [AdsLeadBindCardModel]?
    
    public class func modelContainerPropertyGenericClass() -> [String: Any]? {
        [
            "cardList": AdsLeadBindCardModel.self
        ]
    }
}

@objcMembers
public class AdsLeadBindCardModel: NSObject, XYModelProtocol  {
    var cardId: String?
    var cardType: String?
    var cardTag: String?
    var cover: String?
    var title: String?
    var subTitle: String?
    var buttonInfo: AdsLeadBindCardButtonInfo?
    var effectText: String?
    var explaining = false
    var popUping = false
    var selected = false
    var orderIndex = 0
    var priority = 0
    var leftTime = -1
    var explainSeconds = 0
    
    public class func modelCustomPropertyMapper() -> [String: Any]? {
        [
            "cardId": "id",
            "orderIndex": "sort",
            "selected": "isSelected"
        ]
    }
    
    public class func modelContainerPropertyGenericClass() -> [String: Any]? {
        [
            "buttonInfo": AdsLeadBindCardButtonInfo.self,
        ]
    }
}

@objcMembers
public class AdsLeadBindCardButtonInfo: NSObject, XYModelProtocol {
    var buttonName: String?
    var link: String?
}
