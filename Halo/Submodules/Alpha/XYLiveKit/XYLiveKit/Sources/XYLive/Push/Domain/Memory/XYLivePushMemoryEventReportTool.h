//
//  XYLivePushMemoryEventReportTool.h
//  XYLiveKit
//
//  Created by wangzhen<PERSON> on 2024/10/9.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMemoryEventReportTool : NSObject
+ (void)reportEventWithRoomId:(NSString *)roomId
        memLevelStr:(NSString *)memLevel
            message:(NSString *)message;

@end

NS_ASSUME_NONNULL_END
