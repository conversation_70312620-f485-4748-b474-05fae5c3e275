//
//  XYLiveMultiLineOptFuncVM.h
//  XYLiveKit
//
//  Created by 大远 on 2025/6/3.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineInviteServiceProtocol.h>
@class XYLiveLinkHostInviteeInfo, XYLiveMultiLineOptFuncGroupItem;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiLineOptFuncVM : NSObject

// 数据刷新
@property (nonatomic, copy) void(^onUpdateDataSourceHandler)(void);

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService
                        inviteeInfoList:(NSArray<XYLiveLinkHostInviteeInfo *> *)inviteeInfoList;

// 数据源
@property (nonatomic, copy, readonly)   NSArray<XYLiveMultiLineOptFuncGroupItem *> *dataSource;
// 房间人数
@property (nonatomic, assign, readonly) NSInteger linkMemberNum;
// 当前玩法
@property (nonatomic, assign, readonly) XYLiveMultiLinkBizType bizType;

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo;

@end

NS_ASSUME_NONNULL_END
