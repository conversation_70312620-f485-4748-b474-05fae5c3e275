//
//  XYLivePushMultiChatRoomInviteVC.h
//  XYLiveKit
//
//  Created by 大远 on 2025/2/18.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveKit/XYLivePushMultiChatRoomCoreModel.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveKit/XYLivePushMultiChatRoomConsts.h>

@protocol XYLiveGroupServiceProtocol;

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiChatRoomInviteVC : XYViewController

/// 展示个人资料卡
@property (nonatomic, copy) void(^showUserCardHandler)(XYLiveUserInfo *userInfo);

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                          coreModel:(XYLivePushMultiChatRoomCoreModel *)coreModel
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService
                   groupLiveService:(id<XYLiveGroupServiceProtocol>)groupLiveService;

/// 展示邀请面板
/// - Parameters:
///   - source: 来源
///   - extraInfo: 业务扩展参数
- (void)showInvitePanelWithSource:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString * _Nullable)extraInfo;


/// 邀请
/// - Parameters:
///   - userId: 用户id
///   - source: 来源
///   - extraInfo: 业务扩展参数
- (void)inviteWithUserId:(NSString *)userId source:(XYLiveMultiLinkInviteSource)source extraInfo:(NSString * _Nullable)extraInfo;

@end

NS_ASSUME_NONNULL_END
