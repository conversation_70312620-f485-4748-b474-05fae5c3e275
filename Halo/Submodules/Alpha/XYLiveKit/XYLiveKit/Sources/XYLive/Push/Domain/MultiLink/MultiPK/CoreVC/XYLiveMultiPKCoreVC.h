//
//  XYLiveMultiPKCoreVC.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/13.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveIMDistributerServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiPKCoreServiceProtocol.h>
@class XYLiveUserInfo;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiPKCoreVC : XYViewController<XYLiveMultiPKCoreServiceProtocol>

// 点击用户
@property (nonatomic, copy) void(^didTapUserHandler)(XYLiveUserInfo *userInfo);
// 点击重新匹配
@property (nonatomic, copy) void(^didTapReMatchHandler)(void);
// 点赞手势
@property (nonatomic, weak) UITapGestureRecognizer *likeTapGesture;

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                      socketService:(id<XYLiveIMDistributerServiceProtocol>)socketService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

// 结束连线
- (void)stopLink;

// 重开一局
- (void)startPKAgain;

// 结束PK
- (void)stopPK;

// 注册监听
- (void)registerListener:(id<XYLiveMultiPKCoreListener>)listener;

// 取消注册监听
- (void)unregisterListener:(id<XYLiveMultiPKCoreListener>)listener;

@end

NS_ASSUME_NONNULL_END
