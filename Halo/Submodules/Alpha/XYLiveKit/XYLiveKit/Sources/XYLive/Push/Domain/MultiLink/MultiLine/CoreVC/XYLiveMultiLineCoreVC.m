//
//  XYLiveMultiLineCoreVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/16.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineCoreVC.h"
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLiveMultiLineListCell.h>
#import <XYLiveKit/XYLiveMultiLineViewModel.h>
#import <XYLiveKit/XYLiveAlertViewController.h>
#import <XYLiveKit/XYLiveActionSheetViewController.h>
#import <XYLiveKit/XYLiveLinkHostInviteeInfo.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <XYSessionManager/XYSessionManager.h>
#import <XYLiveKit/XYLiveKit-Swift.h>

@interface XYLiveMultiLineCoreVC ()<XYLiveMultiLinkBizStageListener>

@property (nonatomic, weak)   UIViewController *containerVC;
@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveIMDistributerServiceProtocol> socketService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, weak)   XYLiveActionSheetViewController *sheetVC;
@property (nonatomic, weak)   XYLiveAlertViewController *alertVC;
@property (nonatomic, strong) XYLiveMultiLineViewModel *viewModel;

@end

@implementation XYLiveMultiLineCoreVC

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                      socketService:(id<XYLiveIMDistributerServiceProtocol>)socketService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _liveInfoService = liveInfoService;
        _socketService = socketService;
        _multiLinkService = multiLinkService;
    }
    return self;
}

- (void)didLoad {
    [super didLoad];
    // 初始化ViewModel
    [self setupViewModel];
    // 注册监听
    [self.multiLinkService registerUIListener:self forBizType:XYLiveMultiLinkBizTypeLine];
}

// 结束连线
- (void)stopLink {
    WS
    XYLiveAlertViewController *alertVC = [[XYLiveAlertViewController alloc] initWithTitle:@"确认结束连线" message:@"选择确定后，你将断开当前连线" cancelButtonTitle:@"取消" confirmButtonTitle:@"确认" handler:^(NSInteger index) {
        SS
        if (index == 1) {
            [self.viewModel stopLink];
        }
    }];
    [alertVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.alertVC = alertVC;
}

#pragma mark - XYLiveMultiLineCoreServiceProtocol

- (BOOL)isLining {
    return self.viewModel.isLining;
}

- (XYLiveMultiLinkLayoutType)layoutType {
    return self.viewModel.layoutType;
}

- (CGRect)renderAreaFrame {
    return self.viewModel.renderAreaFrame;
}

// 注册监听
- (void)registerListener:(id<XYLiveMultiLineCoreListener>)listener {
    [self.viewModel registerListener:listener];
}

// 取消注册监听
- (void)unregisterListener:(id<XYLiveMultiLineCoreListener>)listener {
    [self.viewModel unregisterListener:listener];
}

#pragma mark - XYLiveMultiLinkBizStageListener

- (XYLiveMultiLinkListViewCell *)overlayListView:(XYLiveMultiLinkListView *)listView cellForReuseId:(NSString *)reuseId windowInfo:(XYLiveMultiLinkWindowInfo *)windowInfo mediaInfo:(XYLiveMultiLinkMediaInfo *)mediaInfo {
    XYLiveMultiLineListCell *cell = [listView dequeueReusableCellWithIdentifier:reuseId];
    if (cell == nil) {
        cell = [[XYLiveMultiLineListCell alloc] initWithLiveInfoService:self.liveInfoService];
        cell.likeTapGesture = self.likeTapGesture;
    }
    return cell;
}

- (void)overlayListView:(XYLiveMultiLinkListView *)listView didSelectedAtWindowInfo:(XYLiveMultiLinkWindowInfo *)windowInfo mediaInfo:(XYLiveMultiLinkMediaInfo *)mediaInfo {
    // 如果点击空麦位，不做处理
    if (windowInfo.isEmptyMic) { return; }
    // 判断是否当前主播操作
    if ([windowInfo.userId isEqualToString:XYSessionManager.sharedManager.user.userId]) {
        // 展示操作面板
        [self showOptSheetPanelWithMediaInfo:mediaInfo];
    } else {
        // 展示个人资料卡
        XYLiveUserInfo *userInfo = windowInfo.userInfo.userInfo;
        userInfo.roomRole = XYLiveRoomRoleHost;
        userInfo.roomID = [windowInfo.userInfo.roomId integerValue];
        [self showUserCardWithUserInfo:userInfo];
    }
}

#pragma mark - Private

// 初始化viewModel
- (void)setupViewModel {
    XYLiveMultiLineViewModel *viewModel = [[XYLiveMultiLineViewModel alloc] initWithLiveInfoService:self.liveInfoService socketService:self.socketService multiLinkService:self.multiLinkService];
    self.viewModel = viewModel;
}

// 展示操作面板
- (void)showOptSheetPanelWithMediaInfo:(XYLiveMultiLinkMediaInfo *)mediaInfo {
    WS
    // 断开连线
    XYLivePopupItem *stopItem = XYLivePopupItemMake(@"断开连线", XYLivePopupItemTypeHighlight, ^(NSInteger index) {
        SS
        [self stopLink];
    });
    // 切换布局
    BOOL isFloatLayout = self.viewModel.layoutType == XYLiveMultiLinkLayoutTypeFloat;
    XYLivePopupItem *layoutItem = XYLivePopupItemMake(isFloatLayout ? @"切换宫格布局" : @"切换小窗布局", XYLivePopupItemTypeNormal, ^(NSInteger index) {
        SS
        self.didTapSwitchLayoutHandler ? self.didTapSwitchLayoutHandler(isFloatLayout ? XYLiveMultiLinkLayoutTypeGrid : XYLiveMultiLinkLayoutTypeFloat) : nil;
    });
    // 关闭麦克风
    XYLivePopupItem *micItem = XYLivePopupItemMake(mediaInfo.isMute ? @"打开麦克风" : @"关闭麦克风", XYLivePopupItemTypeNormal, ^(NSInteger index) {
        SS
        [self.viewModel updateMuteStateWithIsMute:!mediaInfo.isMute bizExtraInfo:nil];
    });
    // 翻转摄像头
    XYLivePopupItem *flipItem = XYLivePopupItemMake(@"翻转摄像头", XYLivePopupItemTypeNormal, ^(NSInteger index) {
        SS
        [self.multiLinkService switchCameraFlipWithCompletion:nil];
    });
    XYLiveActionSheetViewController *sheetVC = [[XYLiveActionSheetViewController alloc] initWithTitle:nil items:@[stopItem, layoutItem, micItem, flipItem]];
    // 埋点上报
    sheetVC.didTapItemAutoTrackHandler = ^XYTrackerEventContext * _Nonnull(XYLivePopupItem * _Nonnull item, NSUInteger index) {
        return [XYLiveLinkTracker auto_eventActionID32864WithChannelTabName:item.title];
    };
    [sheetVC showWithAnimated:YES onVC:self.containerVC complete:nil];
    self.sheetVC = sheetVC;
    // 埋点上报
    [XYLiveLinkTracker eventActionID32861];
}

// 关闭展示中的面板
- (void)hideAllPanel {
    [self.sheetVC dismissWithAnimated:YES complete:nil];
    [self.alertVC dismissWithAnimated:YES complete:nil];
}

// 展示个人资料卡
- (void)showUserCardWithUserInfo:(XYLiveUserInfo *)userInfo {
    self.didTapUserHandler ? self.didTapUserHandler(userInfo) : nil;
}

@end
