//
//  XYLiveMultiLineOptFuncFriendItemCell.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/22.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveMultiLineOptFuncFriendItemCell.h"
#import <XYLiveFoundation/UIImage+XYLiveKit.h>
#import <XYLiveKit/XYLiveMultiLineOptFuncFriendItemView.h>
#import <XYLiveFoundation/XYLiveFoundation-Swift.h>
#import <XYLiveUIKit/XYLiveUIKit-Swift.h>
#import <XYUITheme/XYThemeManager.h>
#import <XYLiveKit/XYLiveCommonConst.h>
#import <Masonry/Masonry.h>

@interface XYLiveMultiLineOptFuncFriendItemCell()

@property (nonatomic, strong) XYLiveMultiLineFuncFriendItemSubView *addItemView;
@property (nonatomic, strong) XYLiveMultiLineOptFuncFriendItemView *listView;
@property (nonatomic, strong) XYLiveMultiLineOptFuncListFriendItem *listItem;

@end

@implementation XYLiveMultiLineOptFuncFriendItemCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        self.selectionStyle = UITableViewCellSelectionStyleNone;
        // 创建子视图
        [self setupSubviews];
    }
    return self;
}

// 数据绑定
- (void)bindDataSource:(NSArray<XYLiveMultiLineOptFuncGroupItem *> *)dataSource indexPath:(NSIndexPath *)indexPath {
    XYLiveMultiLineOptFuncGroupItem *group = dataSource[indexPath.section];
    XYLiveMultiLineOptFuncListFriendItem *listItem = XYSAFE_CAST(group.items[indexPath.row], XYLiveMultiLineOptFuncListFriendItem);
    // 更新数据源
    [self.listView bindListItems:listItem.list];
    // 更新缓存
    self.listItem = listItem;
}

- (void)setDisableAdd:(BOOL)disableAdd {
    _disableAdd = disableAdd;
    [self updateUIInterfaceWithDisable:disableAdd];
}

#pragma mark - 创建子视图

- (void)setupSubviews {
    // 创建添加好友视图
    [self setupAddItemView];
    // 创建好友列表视图
    [self setupListView];
    
    // 布局
    [self.addItemView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(8);
        make.size.mas_equalTo(CGSizeMake(64, 76));
        make.centerY.equalTo(self.contentView);
    }];
    
    [self.listView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.addItemView.mas_right).offset(6);
        make.right.equalTo(self.contentView).offset(-8);
        make.height.mas_equalTo(76);
        make.centerY.equalTo(self.contentView);
    }];
}

- (void)setupAddItemView {
    XYLiveMultiLineFuncFriendItemSubView *addItemView = [[XYLiveMultiLineFuncFriendItemSubView alloc] init];
    addItemView.nickNameLbl.font = [UIFont fontWithName:@"PingFangSC-Medium" size:12];
    addItemView.nickNameLbl.textColor = [XYLiveTokenColor title];
    addItemView.nickNameLbl.text = @"邀请更多";
    [self.contentView addSubview:addItemView];
    self.addItemView = addItemView;
    
    // 添加点击事件
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(didTapAdd:)];
    [addItemView addGestureRecognizer:tapGesture];
}

- (void)setupListView {
    XYLiveMultiLineOptFuncFriendItemView *listView = [[XYLiveMultiLineOptFuncFriendItemView alloc] init];
    WS
    listView.didTapHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull listItem, NSIndexPath * _Nonnull indexPath) {
        SS
        self.didTapUserHandler ? self.didTapUserHandler(listItem) : nil;
    };
    listView.didTapCancelHandler = ^(XYLiveLinkHostInviteeInfo * _Nonnull listItem, NSIndexPath * _Nonnull indexPath) {
        SS
        self.didTapCancelHandler ? self.didTapCancelHandler(listItem) : nil;
    };
    [self.contentView addSubview:listView];
    self.listView = listView;
}

#pragma mark - Event

- (void)didTapAdd:(UITapGestureRecognizer *)tapGesture {
    self.didTapAddHandler ? self.didTapAddHandler() : nil;
}

#pragma mark - Private

- (void)updateUIInterfaceWithDisable:(BOOL)disable {
    self.addItemView.nickNameLbl.textColor = disable ? [XYLiveTokenColor disabled] : [XYLiveTokenColor title];
    if (disable) {
        [self.addItemView.avatarImgView xylive_setImageWithURLWithLight:@"https://fe-platform.xhscdn.com/platform/104101l031jd1ij8o2o0mcrss5gt00000000000hf3ppuo" dark:@"https://fe-platform.xhscdn.com/platform/104101l031jdtut1nj406crss5gt00000000000htkmhi4"];
    } else {
        [self.addItemView.avatarImgView xylive_setImageWithURLWithLight:@"https://fe-platform.xhscdn.com/platform/104101l031iuf9o1dh406crss5gt00000000000hmlum8m" dark:@"https://fe-platform.xhscdn.com/platform/104101l031iuf9nvqhe06crss5gt00000000000gqcsc9s"];
    }
}

#pragma mark - TraitCollection

- (void)traitCollectionDidChange:(UITraitCollection *)previousTraitCollection {
    [super traitCollectionDidChange:previousTraitCollection];
    // 强刷
    if(@available(iOS 13, *)) {
        if ([self.traitCollection hasDifferentColorAppearanceComparedToTraitCollection:previousTraitCollection]) {
            [self updateUIInterfaceWithDisable:self.disableAdd];
        }
    }
}

@end
