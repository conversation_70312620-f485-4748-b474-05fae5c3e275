//
//  XYLiveMultiChatInvitePanelVC.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/22.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatInvitePanelVC.h"
#import <XYSegmentControl/XYSegmentControl.h>
#import <XYLiveKit/XYLiveMultiChatApplyListVC.h>
#import <XYLiveKit/XYLiveMultiChatInviteListVC.h>
#import <XYLiveFoundation/XYLiveFoundation-Swift.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>
#import <XYLiveKit/XYLiveMultiChatInvitePanelVM.h>
#import <XYLiveKit/XYLivePushMultiChatInviteSettingPanelVC.h>
#import <XYLiveKit/XYLivePushMultiChatRoomInviteSettingPanelVC.h>
#import <XYLiveKit/XYLivePushMultiChatNavigationBar.h>
#import <XYLiveKit/XYLiveMultiChatInvitePanelItem.h>
#import <XYLiveKit/XYLivePushMultiLinkBizTracker.h>
#import <Masonry/Masonry.h>
#import <XYLiveKit/XYLiveKit-Swift.h>

@interface XYLiveMultiChatInvitePanelVC ()<
XYPageViewControllerDelegate,
XYPageViewControllerDataSource,
XYSegmentControlDelegate
>

@property (nonatomic, assign) XYLiveMultiLinkBizType bizType;
@property (nonatomic, assign) XYLiveMultiLinkInviteSource source;
@property (nonatomic, copy)   NSString *extraInfo;
@property (nonatomic, strong) XYLivePushMultiChatNavigationBar *navigationBar;
@property (nonatomic, strong) XYSegmentControl *segmentControl;
@property (nonatomic, strong) XYPageViewController *segmentViewController;
@property (nonatomic, copy)   NSArray<XYLiveMultiChatInvitePanelItem *> *listItems;
@property (nonatomic, strong) XYLiveMultiChatApplyListVC *applyListVC;
@property (nonatomic, strong) XYLiveMultiChatInviteListVC *inviteListVC;
@property (nonatomic, strong) XYLiveMultiChatInvitePanelVM *viewModel;
@property (nonatomic, assign) NSInteger initialIndex;
@property (nonatomic, assign) BOOL animated;

@end

@implementation XYLiveMultiChatInvitePanelVC

- (void)dealloc {
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:@"XYLiveMultiChatInvitePanelVC dealloc"];
}

// 初始化
- (instancetype)initWithBizType:(XYLiveMultiLinkBizType)bizType
                         source:(XYLiveMultiLinkInviteSource)source
                      extraInfo:(NSString * _Nullable)extraInfo {
    if (self = [super init]) {
        _bizType = bizType;
        _source = source;
        _extraInfo = extraInfo;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    // 曝光埋点上报
    [XYLivePushMultiLinkBizTracker eventActionId63370WithBizType:self.bizType secondTabName:[self trackerSecondLevelCTLNameWithIndex:self.initialIndex]];
}

- (void)setupSubViews {
    [super setupSubViews];
    // 创建Tab选项卡
    [self setupSegmentControl];
    // 创建Tab选项卡容器
    [self setupSegmentVC];
    
    // 布局
    [self.segmentControl mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.mas_equalTo(4);
        make.centerY.equalTo(self.navigationBar);
        make.height.equalTo(self.navigationBar);
        make.width.equalTo(self.navigationBar).multipliedBy(0.6);
    }];
    
    [self.segmentViewController.view mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.mas_equalTo(UIEdgeInsetsZero);
    }];
    
    // 更新滚动到指定位置
    self.initialIndex = self.moveToIndexHandler ? self.moveToIndexHandler() : 0;
    [self moveToIndex:self.initialIndex animated:self.animated];
}

#pragma mark - UI

- (void)setupSegmentControl {
    XYSegmentControl *segmentControl = [[XYSegmentControl alloc] init];
    segmentControl.segmentPadding = 12;
    segmentControl.selectionIndicatorWidth = 56;
    segmentControl.segmentWidthStyle = XYSegmentControlSegmentWidthStyleFixed;
    segmentControl.titleFont = [UIFont fontWithName:@"PingFangSC-Regular" size:14];
    segmentControl.selectedTitleFont = [UIFont fontWithName:@"PingFangSC-Medium" size:14];
    segmentControl.titleColor = XYLiveTokenColor.desc;
    segmentControl.selectedTitleColor = XYLiveTokenColor.title;
    segmentControl.selectionIndicatorColor = XYLiveTokenColor.red;
    segmentControl.selectionIndicatorAlignment = XYSegmentControlSelectionIndicatorAlignmentTitleCenter;
    segmentControl.delegate = self;
    segmentControl.titles = [self.listItems valueForKeyPath:@"title"];
    [self.navigationBar addSubview:segmentControl];
    self.segmentControl = segmentControl;
}

- (void)setupSegmentVC {
    XYPageViewController *segmentViewController = [[XYPageViewController alloc] init];
    segmentViewController.scrollEnabled = NO;
    segmentViewController.delegate = self;
    segmentViewController.dataSource = self;
    [self addChildViewController:segmentViewController];
    [self.contentView addSubview:segmentViewController.view];
    self.segmentViewController = segmentViewController;
}

#pragma mark - XYLiveNavigationChildControllerProtocol

- (UIView *)XYLiveNav_customHeader {
    return self.navigationBar;
}

- (CGFloat)heightWhenPortrait {
    return kXYLiveMultiChatInvitePanelHeight;
}

- (BOOL)isRoundedTopCorners {
    return NO;
}

#pragma mark - XYSegmentControlDelegate

- (void)segmentControl:(XYSegmentControl *)segmentControl didSelectSegmentAtIndex:(NSUInteger)index {
    [self.segmentViewController showViewControllerAtIndex:index animated:YES];
}

#pragma mark - XYPageViewControllerDelegate / XYPageViewControllerDataSource

- (NSInteger)numberOfControllersInPageViewController:(XYPageViewController *)pageViewController {
    return self.listItems.count;
}

- (UIViewController *)pageViewController:(XYPageViewController *)pageViewController viewControllerAtIndex:(NSInteger)index {
    XYLiveMultiChatInvitePanelItem *item = self.listItems[index];
    return item.vc;
}

#pragma mark - Private

- (void)moveToIndex:(NSInteger)index animated:(BOOL)animated {
    self.initialIndex = index;
    self.animated = animated;
    [self.segmentControl moveToSegmentAtIndex:index animated:animated];
    [self.segmentViewController showViewControllerAtIndex:index animated:animated];
}

- (NSString *)trackerSecondLevelCTLNameWithIndex:(NSInteger)index {
    return index == 0 ? @"申请消息" : @"邀请连线";
}

- (id<XYLiveInfoServiceProtocol>)liveInfoService {
    return self.liveInfoServiceHandler ? self.liveInfoServiceHandler() : nil;
}

- (id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    return self.multiLinkServiceHandler ? self.multiLinkServiceHandler() : nil;
}

#pragma mark - Lazy

- (XYLivePushMultiChatNavigationBar *)navigationBar {
    if (_navigationBar == nil) {
        _navigationBar = [[XYLivePushMultiChatNavigationBar alloc] init];
        WS
        _navigationBar.didTapShareHandler = ^{
            SS
            [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:@"--didTapShare--"];
            [self.viewModel openShareVCWithRoomId:self.liveInfoService.roomId completion:^(XYPMMessageShareToMessageLiveModel * _Nonnull shareModel, NSError * _Nonnull error) {
                // 拉起分享列表
                [XYPMRouteToShareManager routeToShareWithModel:shareModel idList:nil shareDesc:nil source:nil];
            }];
        };
        _navigationBar.didTapShareTrackerHandler = ^XYTrackerEventContext * _Nonnull{
            SS
            return [XYLivePushMultiLinkBizTracker eventActionId63547WithBizType:self.bizType secondTabName:[self trackerSecondLevelCTLNameWithIndex:self.segmentControl.selectedIndex]];
        };
        _navigationBar.didTapSettingHandler = ^{
            SS
            self.didTapSettingHandler ? self.didTapSettingHandler() : nil;
        };
        _navigationBar.didTapSettingTrackerHandler = ^XYTrackerEventContext * _Nonnull{
            SS
            return [XYLivePushMultiLinkBizTracker eventActionId63371WithBizType:self.bizType secondTabName:[self trackerSecondLevelCTLNameWithIndex:self.segmentControl.selectedIndex]];
        };
    }
    return _navigationBar;
}

- (XYLiveMultiChatInvitePanelVM *)viewModel {
    if (_viewModel == nil) {
        _viewModel = [[XYLiveMultiChatInvitePanelVM alloc] init];
    }
    return _viewModel;
}

- (NSArray<XYLiveMultiChatInvitePanelItem *> *)listItems {
    if (_listItems == nil) {
        // 申请列表
        XYLiveMultiChatInvitePanelItem *applyListItem = [[XYLiveMultiChatInvitePanelItem alloc] init];
        applyListItem.title = @"申请消息";
        XYLiveMultiChatApplyListVC *applyListVC = [[XYLiveMultiChatApplyListVC alloc] initWithBizType:self.bizType source:self.source extraInfo:self.extraInfo];
        WS
        applyListVC.showUserCardHandler = ^(XYLiveUserInfo * _Nonnull userInfo) {
            SS
            self.showUserCardHandler ? self.showUserCardHandler(userInfo) : nil;
        };
        applyListVC.liveInfoServiceHandler = ^id<XYLiveInfoServiceProtocol> _Nonnull{
            SS
            return self.liveInfoServiceHandler ? self.liveInfoServiceHandler() : nil;
        };
        applyListVC.multiLinkServiceHandler = ^id<XYLiveMultiLinkServiceProtocol> _Nonnull{
            SS
            return self.multiLinkServiceHandler ? self.multiLinkServiceHandler() : nil;
        };
        applyListVC.hasGoodsHandler = ^BOOL{
            SS
            return self.hasGoodsHandler ? self.hasGoodsHandler() : NO;
        };
        applyListVC.didTapInviteLinkHandler = ^{
            SS
            [self moveToIndex:1 animated:YES];
        };
        applyListItem.vc = applyListVC;

        // 邀请连线
        XYLiveMultiChatInvitePanelItem *inviteListItem = [[XYLiveMultiChatInvitePanelItem alloc] init];
        inviteListItem.title = @"邀请连线";
        XYLiveMultiChatInviteListVC *inviteListVC = [[XYLiveMultiChatInviteListVC alloc] initWithBizType:self.bizType source:self.source extraInfo:self.extraInfo];
        inviteListVC.showUserCardHandler = ^(XYLiveUserInfo * _Nonnull userInfo) {
            SS
            self.showUserCardHandler ? self.showUserCardHandler(userInfo) : nil;
        };
        inviteListVC.liveInfoServiceHandler = ^id<XYLiveInfoServiceProtocol> _Nonnull{
            SS
            return self.liveInfoServiceHandler ? self.liveInfoServiceHandler() : nil;
        };
        inviteListVC.multiLinkServiceHandler = ^id<XYLiveMultiLinkServiceProtocol> _Nonnull{
            SS
            return self.multiLinkServiceHandler ? self.multiLinkServiceHandler() : nil;
        };
        inviteListVC.hasGoodsHandler = ^BOOL{
            SS
            return self.hasGoodsHandler ? self.hasGoodsHandler() : NO;
        };
        inviteListItem.vc = inviteListVC;
        
        _listItems = @[applyListItem, inviteListItem];
    }
    return _listItems;
}

@end
