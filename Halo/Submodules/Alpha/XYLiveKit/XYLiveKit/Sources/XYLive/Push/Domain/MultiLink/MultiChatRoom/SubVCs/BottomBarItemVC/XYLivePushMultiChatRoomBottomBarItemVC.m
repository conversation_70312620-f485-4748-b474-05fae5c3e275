//
//  XYLivePushMultiChatRoomBottomBarItemVC.m
//  XYLiveKit
//
//  Created by 大远 on 2025/2/19.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLivePushMultiChatRoomBottomBarItemVC.h"
#import <XYLiveKit/XYLiveKit-Swift.h>

@interface XYLivePushMultiChatRoomBottomBarItemVC()<XYLivePushMultiChatRoomCoreModelListener>

@property (nonatomic, weak) UIViewController *containerVC;
@property (nonatomic, weak) XYLivePushMultiChatRoomCoreModel *coreModel;
@property (nonatomic, weak) id<XYLivePushBottomBarNodeServiceProtocol> bottomBarService;

@end

@implementation XYLivePushMultiChatRoomBottomBarItemVC

/// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                          coreModel:(XYLivePushMultiChatRoomCoreModel *)coreModel
                   bottomBarService:(id<XYLivePushBottomBarNodeServiceProtocol>)bottomBarService {
    if (self = [super init]) {
        _containerVC = containerVC;
        _coreModel = coreModel;
        _bottomBarService = bottomBarService;
        // 注册监听
        [self registerAllObservers];
    }
    return self;
}

- (void)registerAllObservers {
    // 监听核心Model
    [self.coreModel registerListener:self];
}

#pragma mark - XYLivePushMultiChatRoomCoreModelListener

- (void)onUpdateApplyNum:(NSInteger)applyNum {
    [self.bottomBarService updateLinkMicHandleCountWithCount:applyNum];
}

@end
