//
//  XYLiveMultiLineInviteVM.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/30.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineInviteServiceProtocol.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
@class XYLiveLinkHostInviteeInfo, XYLiveMultiLinkInviteInfo;

NS_ASSUME_NONNULL_BEGIN

@protocol XYLiveMultiLineInviteDelegate <NSObject>

// 邀请状态变化
- (void)onUpdateInviteState:(XYLiveMultiLinkInviteState)inviteState inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo;

// 收到连线邀请
- (void)onReceiveInviteMessage:(XYLiveMultiLinkInviteInfo *)inviteInfo;

// 倒计时回调
- (void)onTimerTrick:(NSInteger)remainInterval totalInterval:(NSInteger)totalInterval;

// 倒计时完成
- (void)onTimerFinish;

@end

@interface XYLiveMultiLineInviteVM : NSObject<XYLiveMultiLineInviteServiceProtocol, XYLiveMultiLinkListener>

// 代理
@property (nonatomic, weak) id<XYLiveMultiLineInviteDelegate> delegate;
// 邀请列表
@property (nonatomic, copy) NSArray<XYLiveLinkHostInviteeInfo *> *inviteeInfoList;

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

// 接受邀请
- (void)acceptInviteWithTargetUserId:(NSString *)targetUserId targetRoomId:(NSString *)targetRoomId;

// 拒绝
- (void)rejectInviteWithTargetUserId:(NSString *)targetUserId targetRoomId:(NSString *)targetRoomId;

@end

NS_ASSUME_NONNULL_END
