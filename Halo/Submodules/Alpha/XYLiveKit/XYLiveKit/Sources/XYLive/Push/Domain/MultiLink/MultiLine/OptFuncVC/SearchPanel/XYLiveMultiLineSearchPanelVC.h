//
//  XYLiveMultiLineSearchPanelVC.h
//  XYLiveKit
//
//  Created by 大远 on 2025/6/3.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveBaseChildViewController.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineInviteServiceProtocol.h>
@class XYLiveLinkHostInviteeInfo;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiLineSearchPanelVC : XYLiveBaseChildViewController

// 点击用户
@property (nonatomic, copy) void(^didTapUserHandler)(XYLiveLinkHostInviteeInfo *inviteeInfo);
// 点击邀请
@property (nonatomic, copy) void(^didTapInviteHandler)(XYLiveLinkHostInviteeInfo *inviteeInfo);

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo;

@end

NS_ASSUME_NONNULL_END
