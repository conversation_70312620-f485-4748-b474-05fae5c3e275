//
//  XYLiveMultiChatInviteListViewModel.m
//  XYLiveKit
//
//  Created by 大远 on 2024/10/21.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLiveMultiChatInviteListViewModel.h"
#import <XYAlphaNetwork/XYAlphaNetwork-Swift.h>
#import <XYLiveKit/XYLivePushMultiChatConsts.h>
#import <XYAlphaShare/XYAlphaShare-Swift.h>
#import <XYAlphaShare/XYAlphaShare.h>
#import <XYAlphaUtils/XYLogCollector+XYLive.h>

@interface XYLiveMultiChatInviteListViewModel()

// 是否缓存好友列表
@property (nonatomic, assign) BOOL hasCacheFriendList;
// 是否缓存好友列表
@property (nonatomic, assign) BOOL hasCacheAudienceList;
// 列表数据
@property (nonatomic, copy)   NSArray<XYLiveMultiChatInviteListGroupItem *> *listItems;

@end

@implementation XYLiveMultiChatInviteListViewModel

// 请求列表数据
- (void)requestListDataWithRoomId:(NSString *)roomId
                       completion:(void(^)(NSArray<XYLiveMultiChatInviteListGroupItem *> *listItems, NSError *error))completion {
    // 请求好友列表数据
    [self requestFriendListDataWithRoomId:roomId completion:completion];
    // 请求房内观众列表数据
    [self requestAudienceListDataWithRoomId:roomId completion:completion];
}

// 请求好友列表数据
- (void)requestFriendListDataWithRoomId:(NSString *)roomId
                             completion:(void(^)(NSArray<XYLiveMultiChatInviteListGroupItem *> *listItems, NSError *error))completion {
    // 由于好友列表加载耗时，故下拉刷新不请求好友列表接口，前提是之前已经缓存过
    if (self.hasCacheFriendList) { return; }
    self.hasCacheFriendList = YES;
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/line/v1/host/invite_friend_list" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMGET;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"room_id"] = roomId;
    params[@"app_id"] = @(1);
    [api addingQueryItems:params.copy];
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"start request friend list api, params:{%@}", params]];
    WS
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"friend list api response, result:{%@}", response.error]];
        [response onSuccess:^(id  _Nonnull value) {
            // 数据转模型
            XYLiveMultiChatInviteListModel *listModel = [XYLiveMultiChatInviteListModel xy_modelWithDictionary:value];
            // 更新缓存标识
            weak_self.hasCacheFriendList = listModel.friendList.count;
            // 配置数据源
            NSMutableArray *listItemsM = (weak_self.listItems ?: @[]).mutableCopy;
            // 刷新数据
            if (listModel.friendList.count) {
                XYLiveMultiChatInviteListGroupItem *group = [[XYLiveMultiChatInviteListGroupItem alloc] init];
                group.title = @"最近添加的好友";
                NSMutableArray *itemsM = [NSMutableArray arrayWithArray:listModel.friendList];
                // mock邀请更多
                XYLiveMultiChatInviteListFriendModel *moreModel = [[XYLiveMultiChatInviteListFriendModel alloc] init];
                moreModel.nickName = @"邀请更多";
                [itemsM addObject:moreModel];
                // 构建Item
                XYLiveMultiChatInviteListFriendItem *item = [[XYLiveMultiChatInviteListFriendItem alloc] init];
                item.listItems = itemsM.copy;
                group.items = @[item];
                
                [listItemsM insertObject:group atIndex:0];
                weak_self.listItems = listItemsM.copy;
            }
            completion ? completion(weak_self.listItems, nil) : nil;
        } onFailure:^(NSError * _Nonnull error) {
            completion ? completion(weak_self.listItems, error) : nil;
        }];
    }];
}

// 请求观众列表数据
- (void)requestAudienceListDataWithRoomId:(NSString *)roomId
                               completion:(void(^)(NSArray<XYLiveMultiChatInviteListGroupItem *> *listItems, NSError *error))completion {
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/line/v1/host/viewer_list" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMGET;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"room_id"] = roomId;
    params[@"app_id"] = @(1);
    [api addingQueryItems:params.copy];
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"start request audience list api, params:{%@}", params]];
    WS
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        SS
        [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"audience list api response, result:{%@}", response.error]];
        // 获取本地缓存
        XYLiveMultiChatInviteListGroupItem *group = [self.listItems xy_match:^BOOL(XYLiveMultiChatInviteListGroupItem * _Nonnull obj) {
            return ![obj.items.firstObject isKindOfClass:XYLiveMultiChatInviteListFriendItem.class];
        }];
        // 配置数据源
        NSMutableArray *listItemsM = (self.listItems ?: @[]).mutableCopy;
        if (group == nil) {
            group = [[XYLiveMultiChatInviteListGroupItem alloc] init];
            group.title = @"房内观众";
            [listItemsM addObject:group];
        }
        [response onSuccess:^(id  _Nonnull value) {
            SS
            // 数据转模型
            XYLiveMultiChatInviteListModel *listModel = [XYLiveMultiChatInviteListModel xy_modelWithDictionary:value];
            // 配置数据源
            if (listModel.audienceList.count) {
                group.items = [listModel.audienceList xy_map:^id _Nonnull(XYLiveMultiChatInviteListAudienceItem * _Nonnull item, NSUInteger index) {
                    item.isInvited = self.didFetchInviteStatusHandler ? self.didFetchInviteStatusHandler(item) : NO;
                    return item;
                }];
            } else {
                XYLiveMultiChatInviteListEmptyItem *item = [[XYLiveMultiChatInviteListEmptyItem alloc] init];
                group.items = @[item];
            }
            self.listItems = listItemsM.copy;
            completion ? completion(weak_self.listItems, nil) : nil;
        } onFailure:^(NSError * _Nonnull error) {
            SS
            // 配置数据源
            XYLiveMultiChatInviteListEmptyItem *item = [[XYLiveMultiChatInviteListEmptyItem alloc] init];
            item.showErrorResponseView = YES;
            group.items = @[item];
            self.listItems = listItemsM.copy;
            completion ? completion(self.listItems, error) : nil;
        }];
    }];
}

/// 邀请好友
- (void)inviteFriendWithRoomId:(NSString *)roomId
                        userId:(NSString *)userId
                    completion:(void(^)(XYPMMessageShareToMessageLiveModel * _Nullable shareModel, XYPMMessageChatUserModel * _Nullable userInfo, NSError * _Nullable error))completion {
    [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"start request invite friend api, params:{roomId:%@, useId:%@}", roomId, userId]];
    XYLiveShareSourceInfo *sourceInfo = [[XYLiveShareSourceInfo alloc] init];
    sourceInfo.roomID = roomId;
    [XYLiveShareNetworking reqAppServLiveShareCompletionInfoWithSoureInfo:sourceInfo succ:^(id  _Nullable value, NSError * _Nullable error) {
        [XYLogCollector xyLiveLogTag:kXYLivePushMultiChatModule content:[NSString stringWithFormat:@"invite friend api response, result:{error:%@}", error]];
        XYLiveShareInfo *sinfo = XYSAFE_CAST(value, XYLiveShareInfo);
        if (sinfo != nil) {
            XYPMMessageShareToMessageLiveModel *model = [[XYPMMessageShareToMessageLiveModel alloc] init];
            model.link = sinfo.link;
            model.image = sinfo.bigImgURL;
            model.username = sinfo.nickName;
            model.avatar = sinfo.avatar;
            model.title = sinfo.desc;
            model.tagName = sinfo.tagName;
            
            // 构建用户模型
            XYPMMessageChatUserModel *userModel = [[XYPMMessageChatUserModel alloc] init];
            userModel.userId = userId;
            completion ? completion(model, userModel, nil) : nil;
        }
    } fail:^(id  _Nullable value, NSError * _Nullable error) {
        completion ? completion(nil, nil, error) : nil;
    }];
}

// 重置邀请状态
- (void)updateInviteStatusWithUserId:(NSString *)userId isInvited:(BOOL)isInvited {
    for (XYLiveMultiChatInviteListGroupItem *group in self.listItems) {
        for (XYLiveMultiChatInviteListItem *item in group.items) {
            if ([item isKindOfClass:XYLiveMultiChatInviteListAudienceItem.class]) {
                XYLiveMultiChatInviteListAudienceItem *audienceItem = XYSAFE_CAST(item, XYLiveMultiChatInviteListAudienceItem);
                if ([audienceItem.userId isEqualToString:userId]) {
                    audienceItem.isInvited = isInvited;
                    break;
                }
            }
        }
    }
}

@end
