//
//  XYLiveLinkHostSearchListVM.m
//  XYLiveKit
//
//  Created by 大远 on 2025/5/21.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import "XYLiveLinkHostSearchListVM.h"
#import <XYAlphaNetwork/XYAlphaNetwork-Swift.h>
#import <XYAPIRoute/XYAPIRoute.h>
#import <XYLiveKit/XYLiveMultiLinkController.h>
#import <XYLiveKit/XYLiveLinkHostSearchListModel.h>
#import <XYLiveKit/XYLiveLinkHostSearchPanelConsts.h>
#import <XYStorageCore/XYStorageCenter.h>
#import <XYLiveKit/XYLiveLinkHostInvitePanelConsts.h>
#import <XYLiveKit/XYLiveLinkHostConsts.h>

@interface XYLiveLinkHostSearchListVM()

@property (nonatomic, weak)   id<XYLiveInfoServiceProtocol> liveInfoService;
@property (nonatomic, weak)   id<XYLiveMultiLinkServiceProtocol> multiLinkService;
@property (nonatomic, copy)   NSArray<XYLiveLinkHostInviteeInfo *> *dataSource;
@property (nonatomic, assign) BOOL isPK;

@end

@implementation XYLiveLinkHostSearchListVM

// 初始化
- (instancetype)initWithLiveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                       multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService {
    if (self = [super init]) {
        _liveInfoService = liveInfoService;
        _multiLinkService = multiLinkService;
        _isPK = [[XYStorageCenter defaultCenter].permanent getBoolForKey:kXYLiveLinkHostInviteListBizKey defaultValue:NO];
    }
    return self;
}

// 发起搜索请求
- (void)requestSearchResultWithKey:(NSString *)key completion:(void(^)(NSError *error))completion {
    XYAPI *api = [XYLiveFoundationAPIRouter routerWithPath:@"/api/sns/red/live/app/line/v1/search_invite_host_list" type:XYLiveDomainTypeLiveRoom];
    api.method = WSHTTPMPOST;
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"app_id"] = @(1);
    params[@"room_id"] = self.liveInfoService.roomId;
    params[@"query"] = key;
    [api addingQueryItems:params.copy];
    [XYLogCollector xyLiveLogTag:kXYLiveLinkHostModuleName content:[NSString stringWithFormat:@"start request search host list api,{key:%@, roomId:%@}", key, self.liveInfoService.roomId]];
    // 重置数据源
    self.dataSource = nil;
    WS
    [api.data responseJson:nil completion:^(WorldSnakeDataResponse<id> * _Nonnull response) {
        [XYLogCollector xyLiveLogTag:kXYLiveLinkHostModuleName content:[NSString stringWithFormat:@"request search host list api reposne,{error:%@}", response.error]];
        SS
        // 处理成功
        if (response.error == nil) {
            // 数据转模型
            XYLiveLinkHostSearchListModel *listModel = [XYLiveLinkHostSearchListModel xy_modelWithDictionary:response.result.value];
            // 配置数据源
            [self configureDataSourceWithModel:listModel];
        }
        // 执行事件回调
        completion ? completion(response.error) : nil;
    }];
}

// 更新邀请状态
- (void)updateInviteState:(XYLiveMultiLinkInviteState)state inviteeInfo:(XYLiveLinkHostInviteeInfo *)inviteeInfo isPK:(BOOL)isPK {
    // 合法性校验
    if (self.isPK != isPK || inviteeInfo == nil) { return; }
    // 更新数据源
    XYLiveLinkHostInviteeInfo *inviteInfo = [self.dataSource xy_match:^BOOL(id  _Nonnull obj) {
        XYLiveLinkHostInviteeInfo *listItem = XYSAFE_CAST(obj, XYLiveLinkHostInviteeInfo);
        return [listItem.userId isEqualToString:inviteeInfo.userId];
    }];
    inviteInfo.isInvited = state != XYLiveMultiLinkInviteStateIdle;
}

// 更新列表状态
- (void)updateListItemStatusWithIsPK:(BOOL)isPK {
    // 更新缓存
    self.isPK = isPK;
    // 更新状态
    [self.dataSource enumerateObjectsUsingBlock:^(XYLiveLinkHostInviteeInfo * _Nonnull listItem, NSUInteger idx, BOOL * _Nonnull stop) {
        listItem.isPK = isPK;
        listItem.isInvited = [self isInvitingWithIsPK:isPK userId:listItem.userId];
    }];
}

#pragma mark - Private

// 配置数据源
- (void)configureDataSourceWithModel:(XYLiveLinkHostSearchListModel *)model {
    NSMutableArray *listItemsM = [NSMutableArray array];
    for (XYLiveLinkHostInviteeInfo *listItem in model.listItems) {
        XYLiveMultiLinkBizType bizType = self.isPK ? XYLiveMultiLinkBizTypePKGift : XYLiveMultiLinkBizTypeLine;
        listItem.isInvited = [self isInvitingWithIsPK:self.isPK userId:listItem.userId];
        listItem.isPK = self.isPK;
        [listItemsM addObject:listItem];
    }
    self.dataSource = listItemsM.copy;
}

// 是否邀请中
- (BOOL)isInvitingWithIsPK:(BOOL)isPK userId:(NSString *)userId {
    XYLiveMultiLinkBizType bizType = isPK ? XYLiveMultiLinkBizTypePKGift : XYLiveMultiLinkBizTypeLine;
    return [self.multiLinkService isInvitingWithBizType:bizType targetUserId:userId];
}

@end
