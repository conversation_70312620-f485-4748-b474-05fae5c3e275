//
//  XYLiveMultiLineCoreVC.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/16.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveServiceProtocol/XYLiveInfoServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveIMDistributerServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLinkServiceProtocol.h>
#import <XYLiveServiceProtocol/XYLiveMultiLineCoreServiceProtocol.h>
@class XYLiveUserInfo;

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveMultiLineCoreVC : XYViewController<XYLiveMultiLineCoreServiceProtocol>

// 点击用户
@property (nonatomic, copy) void(^didTapUserHandler)(XYLiveUserInfo *userInfo);
// 点击切换布局
@property (nonatomic, copy) void(^didTapSwitchLayoutHandler)(XYLiveMultiLinkLayoutType layoutType);
// 点赞手势
@property (nonatomic, weak) UITapGestureRecognizer *likeTapGesture;

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                    liveInfoService:(id<XYLiveInfoServiceProtocol>)liveInfoService
                      socketService:(id<XYLiveIMDistributerServiceProtocol>)socketService
                   multiLinkService:(id<XYLiveMultiLinkServiceProtocol>)multiLinkService;

// 结束连线
- (void)stopLink;

// 关闭面板
- (void)hideAllPanel;

// 注册监听
- (void)registerListener:(id<XYLiveMultiLineCoreListener>)listener;

// 取消注册监听
- (void)unregisterListener:(id<XYLiveMultiLineCoreListener>)listener;

@end

NS_ASSUME_NONNULL_END
