//
//  BindAdsLeadsCardEmptyView.swift
//  XYLiveKit
//
//  Created by 桑丘(王泽) on 2025/5/23.
//  Copyright © 2025 XingIn. All rights reserved.
//

@objcMembers
class BindAdsLeadsCardEmptyView: UIView {
    
    var cardType: LeadCardType?
    private static let kCopyAddressUrl = "http://pro.xiaohongshu.com/new-live-manage/live-leads-card/list"
    private static let kCopyToast = "复制成功"
    
    convenience init(type: LeadCardType) {
        self.init()
        self.cardType = type
        self.setupUI()
    }
    
    private func setupUI() {
        self.backgroundColor = .ReDs.background.light()
        self.addSubview(emptyImage)
        self.addSubview(emptyLabel)
        self.addSubview(emptyBtn)
        
        self.emptyImage.snp.makeConstraints { make in
            make.width.equalTo(80.0)
            make.height.equalTo(80.0)
            make.top.equalToSuperview().offset(125.0)
            make.centerX.equalToSuperview()
        }
        
        self.emptyLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(48.0)
            make.right.equalToSuperview().offset(-48.0)
            make.top.equalTo(self.emptyImage.snp.bottom).offset(16.0)
            make.centerX.equalTo(self.emptyImage)
        }
        
        self.emptyBtn.snp.makeConstraints { make in
            make.width.equalTo(124.0)
            make.height.equalTo(36.0)
            make.top.equalTo(self.emptyLabel.snp.bottom).offset(20.0)
            make.centerX.equalTo(self.emptyLabel)
        }
    }
    
    private lazy var emptyImage = {
        let imageView = UIImageView()
        imageView.image = UIImage.xy_liveKitBundleImage("live_ads_empty_page_icon")
        
        return imageView
    }()
    
    private lazy var emptyLabel = {
        let label = UILabel()
        label.text = AdsLeadGenerationTools.getI18NLanguage(from: "暂无可添加的卡片，请前往「专业号-直播卡片」创建后重试")
        label.textColor = .ReDs.description.light()
        label.font = Theme.fontXMedium
        label.textAlignment = .center
        label.numberOfLines = 0
        
        return label
    }()
    
    private lazy var emptyBtn = {
        let button = UIButton(type: .custom)
        button.setTitle(AdsLeadGenerationTools.getI18NLanguage(from: "复制创建地址"), for: .normal)
        button.setTitleColor(.ReDs.title.light(), for: .normal)
        button.titleLabel?.font = Theme.fontXMedium
        button.titleLabel?.theme.borderColor = .ReDs.separator2.light()
        button.layer.borderWidth = 0.5
        button.layer.cornerRadius = 18.0
        
        button.addTarget(self, action: #selector(emptyBtnDidPressed), for: .touchUpInside)

        return button
    }()
    
    func emptyBtnDidPressed() {
        let addressUrlString = ConfigCenter.shared.justOnceString(forKey: "ads_leads_copy_address", defalut: BindAdsLeadsCardEmptyView.kCopyAddressUrl)
        // 复制到剪切板
        UIPasteboard.general.string = addressUrlString
        AdsLeadGenerationTools.showToast(message: BindAdsLeadsCardEmptyView.kCopyToast)
    }
}
