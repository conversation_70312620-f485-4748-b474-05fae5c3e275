//
//  XYLivePushMultiChatCoreModelListener.h
//  XYLiveKit
//
//  Created by 大远 on 2025/2/17.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol XYLivePushMultiChatCoreModelListener <NSObject>

@optional
/// 开关状态更新
- (void)onUpdateSwitchStatus:(BOOL)isOpened;

/// 申请人数变化
- (void)onUpdateApplyNum:(NSInteger)applyNum;

@end

NS_ASSUME_NONNULL_END
