//
//  XYLivePushMemoryEventReportTool.m
//  XYLiveKit
//
//  Created by wangzhenxing on 2024/10/9.
//  Copyright © 2024 XingIn. All rights reserved.
//

#import "XYLivePushMemoryEventReportTool.h"
@import XYTracker;

@implementation XYLivePushMemoryEventReportTool
+ (void)reportEventWithRoomId:(NSString *)roomId
        memLevelStr:(NSString *)memLevel
            message:(NSString *)message {
    [XYAPMLiteTracker track:^(XYAPMLiteContext * _Nonnull context) {
            context.eventName(@"ios_live_push_mem_event")
            .sampleRate(1)
            .appendEventData(@"roomId", roomId ?:@"")
            .appendEventData(@"memLevel", memLevel ?:@"")
            .appendEventData(@"message", message ?:@"");
    }];
}
@end
