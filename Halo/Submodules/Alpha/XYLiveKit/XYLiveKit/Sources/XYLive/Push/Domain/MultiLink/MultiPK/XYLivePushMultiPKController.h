//
//  XYLivePushMultiPKController.h
//  XYLiveKit
//
//  Created by 大远 on 2025/4/24.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <XYAlphaFusion/XYAlphaFusion.h>
#import <XYLiveKit/XYLiveMultiPKInviteVC.h>
#import <XYLiveKit/XYLiveMultiPKMatchVC.h>
#import <XYLiveKit/XYLiveMultiPKCoreVC.h>
@protocol XYLivePushMultiPKDependProtocol, XYLivePushMultiPKListener;

NS_ASSUME_NONNULL_BEGIN

@interface XYLivePushMultiPKController : XYViewController

// 邀请模块
@property (nonatomic, weak) XYLiveMultiPKInviteVC *inviteVC;
// 匹配模块
@property (nonatomic, weak) XYLiveMultiPKMatchVC *matchVC;
// 核心模块
@property (nonatomic, weak) XYLiveMultiPKCoreVC *coreVC;

// 初始化
- (instancetype)initWithContainerVC:(UIViewController *)containerVC
                             depend:(id<XYLivePushMultiPKDependProtocol>)depend;

// 展示操作面板
- (void)showOptFuncPanelWithSource:(XYLiveMultiPKOptFuncPanelSource)source;

// 注册监听
- (void)registerListener:(id<XYLivePushMultiPKListener>)listener;

// 取消注册
- (void)unregisterListener:(id<XYLivePushMultiPKListener>)listener;

@end

NS_ASSUME_NONNULL_END
