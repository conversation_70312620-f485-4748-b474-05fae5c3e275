//
//  XYLivePushRoomViewController+StreamImage.m
//  XYLiveKit
//
//  Created by 王帅 on 2022/5/10.
//  Copyright © 2022 XingIn. All rights reserved.
//

#import "XYLivePushRoomViewController.h"
#import "XYLivePushRoomVC+RTCInternal.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import <XYLiveKit/XYLiveKit-Swift.h>
@import XYConfigCenter;
@import SDWebImage;


@implementation XYLivePushRoomViewController (StreamImage)
- (void)configHostLeavingImage:(BOOL)isVoiceLive {
    [self.presetImageUtil configBizInfoWithVoiceLive:isVoiceLive screenLive:self.shouldScreenPushing chatLive:self.roomInfo.isLiveChat];
    [self.presetImageUtil configAppInactiveImage];
}

- (void)configInactiveImage:(UIImage * _Nullable)image {
    if (image) {
        NSString *filePath =  [[XYLiveFileManager alphaRootCachesDirectory] stringByAppendingPathComponent:[NSString stringWithFormat:@"rtcMuteImage.png"]];
        if ([UIImagePNGRepresentation(image) writeToFile:filePath atomically:YES]) {
            NSLog(@"[rtc]setVideoMuteImage Success");
        }
        if ([self canApplyRTC]) {
            [self.rtcPusher setVideoMuteImage:image imageUrl:filePath appActiveEffective:NO];
        } else if ([self canApplyKasa]) {
            [self.kasaPusher setMuteImage:image appActive:NO];
        } else {
            [self.pusher setMuteImage:image appActive:NO];
        }
    }
}

@end
