//
//  UIView+HostAnimate.swift
//  XYLiveKit
//
//  Created by 张恒 on 2024/5/28.
//  Copyright © 2024 XingIn. All rights reserved.
//

import UIKit
import XYAlphaUtils
#if RELEASE
#else
import XYLiveFoundation
#endif

// swiftlint:disable line_length
extension Alpha where Base: UIView {
    private static var hostAnimateDegrade: Bool {
        if XYAlphaSwitch.enablePushCustomAnimationFps {
            return false
        }
        let needDegrade = LivePushAnimationDegradeControl.canDegrade
#if RELEASE
        return needDegrade
#else
        return XYLiveOdysseyConfig.hostAnimateDegrade() || needDegrade
#endif
    }
    
    public static func hostAnimate(withDuration duration: TimeInterval, delay: TimeInterval, options: UIView.AnimationOptions = [], animations: @escaping () -> Void, completion: ((Bool) -> Void)? = nil) {
        if hostAnimateDegrade {
            let options: UIView.AnimationOptions = []
            var newOptions = options
            newOptions.insert(.preferredFramesPerSecond30)
            UIView.animate(withDuration: duration, delay: delay, options: newOptions, animations: animations, completion: completion)
        } else {
            UIView.animate(withDuration: duration, delay: delay, options: options, animations: animations, completion: completion)
        }
    }
    
    public static func hostAnimate(withDuration duration: TimeInterval, animations: @escaping () -> Void, completion: ((Bool) -> Void)? = nil) {
        if hostAnimateDegrade {
            UIView.animate(withDuration: duration, delay: 0, options: [.preferredFramesPerSecond30], animations: animations)
        } else {
            UIView.animate(withDuration: duration, animations: animations, completion: completion)
        }
    }
    
    public static func hostAnimate(withDuration duration: TimeInterval, delay: TimeInterval, usingSpringWithDamping dampingRatio: CGFloat, initialSpringVelocity velocity: CGFloat, options: UIView.AnimationOptions = [], animations: @escaping () -> Void, completion: ((Bool) -> Void)? = nil) {
        if hostAnimateDegrade {
            let options: UIView.AnimationOptions = []
            var newOptions = options
            newOptions.insert(.preferredFramesPerSecond30)
            UIView.animate(withDuration: duration, delay: delay, usingSpringWithDamping: dampingRatio, initialSpringVelocity: velocity, options: newOptions, animations: animations, completion: completion)
        } else {
            UIView.animate(withDuration: duration, delay: delay, usingSpringWithDamping: dampingRatio, initialSpringVelocity: velocity, options: options, animations: animations, completion: completion)
        }
    }
}
