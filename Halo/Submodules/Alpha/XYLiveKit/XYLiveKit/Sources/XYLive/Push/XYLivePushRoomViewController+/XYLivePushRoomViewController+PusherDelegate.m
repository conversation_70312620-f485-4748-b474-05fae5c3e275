//
//  XYLivePushRoomViewController+PusherDelegate.m
//  XYLiveKit
//
//  Created by 周博立 on 2020/12/1.
//  Copyright © 2020 XingIn. All rights reserved.
//

#import <XYLiveKit/XYLiveKit-Swift.h>
@import XYLiveUIKit;
#import "XYLivePushRoomViewController.h"
#import "XYLivePushRoomVC+CoreInternal.h"
#import "XYLiveManager.h"
#import "XYTrackLiveBroadcastPage.h"
@import XYLiveFoundation;
@import XYLivePusher;
@import XYLiveCore;

@implementation XYLivePushRoomViewController (PusherDelegate)

- (void)livePusher:(XYLiveRtmpPusher *)pusher didRecvNetStats:(NSDictionary *)param {
    [self reportLoop:param];
    
    BOOL check = XYLiveLinkConfig.enableRegularAudioCheck;
    if (check) {
        if (self.pushAudioCheckCountdown <= 0) {
            self.pushAudioCheckCountdown = pushAudioCheckInterval;
            [XYLiveMediaControl reloadAudioSessionWithReceiver:self sceneType:XYLiveAudioSessionScenePush];
        } else {
            self.pushAudioCheckCountdown --;
        }
    }
}

- (void)livePusherNetworkQuality:(XYLiveRtmpPusher *)pusher withData:(RTCNetworkQualityInfo *__nonnull)qualityInfo {
    dispatch_async(dispatch_get_main_queue(), ^{
        self.decorateView.viewModel.networkQualityScore = qualityInfo.aggregateQualityScore;
//        NSLog(@"rtmp model: qualityInfo : rtt = %@ uploss = %@ socore = %@", @(qualityInfo.rtt), @(qualityInfo.upLoss),@(qualityInfo.aggregateQualityScore));
    });
}

- (void)livePusherDidBeginPush:(XYLiveRtmpPusher *)pusher {
    [self sdk_startLive];// 推流成功后触发start
    [self sdk_submitPushInfo:NO];
    
    [XYLiveManagerSharedInstance updatePushStats:YES];
    [XYLogCollector xyLiveLogTag:@"pusher" content:@"begin"];
    [XYLiveManagerSharedInstance startPushStreamCompletion:^(BOOL success, NSError * _Nullable error) {}];
    [XYTrackLiveBroadcastPage startLiveEvent:@"start_push" isOBS:NO success:YES roomID:self.roomInfo.roomID pushUrl:self.roomInfo.streamURLInfo.livePushURL errorMessage:@""];
    
    [self updateApmRtmpParam];
    [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventPushStart recovery:self.roomInfo.isRecovery encodeInfo:self.pusher.videoEncodeInfo];
    [XYLiveManagerSharedInstance reportCurrentPushType:XYLivePushTypeRtmp completion:^(BOOL success, NSError * _Nullable error) {
        NSString *content = [NSString stringWithFormat:@"submit ptype resp: %@, err: %@", @(success), error];
        [XYLogCollector xyLiveLogTag:@"hotswitch" content:content];
        NSLog(@"[rtmp]%@", content);
        [self.decorateVM.adapter appendDebugInfo:content];
    }];
    [self.decorateVM.adapter appendDebugInfo:[NSString stringWithFormat:@"submit push type: %@", @(XYLivePushTypeRtmp)]];
    
    [XYLiveManagerSharedInstance.consumeService memoryGenerate:XYLiveConsumeMemOptionPushMem];
    [XYLiveManagerSharedInstance.consumeService setContentType:XYLiveManagerSharedInstance.roomInfo.contentType];
    [XYLiveManagerSharedInstance.consumeService reportStartPush];
}

- (void)livePusherWarningWithNetBusy:(XYLiveRtmpPusher *)pusher {
    [XYLogCollector xyLiveLogTag:@"pusher" content:@"net_busy"];
}

- (void)livePusherWarningWithReconnect:(XYLiveRtmpPusher *)pusher {
    [XYLogCollector xyLiveLogTag:@"pusher" content:@"reconn"];
}

- (void)livePusherErrorWithNetDisconnect:(XYLiveRtmpPusher *)pusher {
    [XYLogCollector xyLiveLogTag:@"pusher" content:@"net_disconn"];
    if (XYLiveManagerSharedInstance.isLiveRoomJoined) {
        XYLiveRoomInfo *roomInfo = XYLiveManagerSharedInstance.roomInfo;
        NSString *rtmp = roomInfo.streamURLInfo.livePushURL;
        if (self.pushReconnChance > 0 && rtmp.length) {
            self.pushReconnChance --;
            if ([self canApplyRTC]) {
                //TODO: 洛萨
            } else {
                [self.pusher stopPush];
                NSString *roomID = XYLiveManagerSharedInstance.roomInfo.roomIDStr;
                NSString *userID = XYLiveManagerSharedInstance.userInfo.userID;
                NSDictionary *extra = @{
                    kLogHostID: userID ?: @"",
                    kLogRoomID: roomID ?: @"",
                };
                [self.pusher startPush:rtmp extra:extra];
            }
        } else {
            [XYLogCollector xyLiveLogTag:@"push_room" content:@"failure_to_reopen_toast"];
            __weak typeof(self) weakSelf = self;
            XYAlertAction *OK = [XYAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:^(UIAlertAction *_Nonnull action) {
                [weakSelf.decorateVM notifyManagerToLeaveRoomWithReason:@"pusher_error_confirm"];
            }];
            [[XYAlertCenter createAlertItemWithTitle:@"服务器连接失败了，可尝试重新开播" message:nil actions:@[OK] textFieldConfigs:nil isSystem:YES] show];
        }
    }
}

- (void)livePusherErrorWithErrorCode:(XYLiveRtmpPusher *)pusher errorCode:(NSInteger)errorCode errorMsg:(NSString *)errorMsg {
    if (self.pusher != nil) {
        [self updateApmRtmpParam];
        [xyLiveSharedAPMManager reportLivePushError:(int32_t)errorCode msg:errorMsg extInfo:@""];
        //for New push_event
        [xyLiveSharedAPMManager reportLivePushEventError:XYLivePushEventError recovery:self.roomInfo.isRecovery encodeInfo:self.pusher.videoEncodeInfo errorCode:(int32_t)errorCode errorMsg:errorMsg];
        xyLiveSharedAPMManager.startApmInfo.pushRespTs = [NSDate date].timeIntervalSince1970;
        xyLiveSharedAPMManager.startApmInfo.pushRespCode = errorCode;
        xyLiveSharedAPMManager.startApmInfo.pushRespMsg = errorMsg ?: @"";
        [xyLiveSharedAPMManager reportStartPipelineTracker];
    }
}

- (void)livePusherWarnWithWarnCode:(XYLiveRtmpPusher *)pusher warnCode:(NSInteger)warnCode warnMsg:(NSString *)warnMsg {
    if (self.pusher != nil) {
        [self updateApmRtmpParam];
        [xyLiveSharedAPMManager reportLivePushWarn:(int32_t)warnCode msg:warnMsg extInfo:@""];
        [xyLiveSharedAPMManager reportLivePushEventWarn:XYLivePushEventWarn recovery:self.roomInfo.isRecovery encodeInfo:self.pusher.videoEncodeInfo warnCode:(int32_t)warnCode warnMsg:warnMsg];
    }
}

- (void)reportLoop:(NSDictionary *)param {
    [self updateApmRtmpParam];
  
    RTCApmStatistics *stat = [[RTCApmStatistics alloc] init];
    stat.localStats.resWidth = [param[@"videoWidth"] intValue];
    stat.localStats.resHeight = [param[@"videoHeight"] intValue];
    stat.localStats.fps = [param[@"videoFps"] intValue];
    stat.localStats.ara = [param[@"audioBitrate"] intValue];
    stat.localStats.asr = [param[@"audioSamplerate"] intValue];
    stat.localStats.vra = [param[@"videoBitrate"] intValue];
    stat.upLoss = [param[@"uploss"] intValue];
    stat.sentBytes = [param[@"sentBytes"] longValue];
    stat.rtt = [param[@"rtt"] intValue];
    
    double cameraRate = [param[@"cameraRate"] doubleValue];
    double previewRate = [param[@"previewRate"] doubleValue];
    int ori = [param[@"orientation"] intValue];

    [xyLiveSharedAPMManager reportLivePushLoop:stat cameraRate:cameraRate previewRate:previewRate cameraOrientation: ori];
}

- (void)livePusherCutStreamHotSwitch:(XYLiveRtmpPusher *)pusher {
    [XYLogCollector xyLiveLogTag:@"push_room" content:@"rtmp recv cutStreamHotSwitch callback"];
    //主线程执行
    dispatch_async(dispatch_get_main_queue(), ^{
        // 会涉及View操作 切换到主线程执行
        [self hotSwitchToRtcPusher];
        [xyLiveSharedAPMManager reportLivePushEvent:XYLivePushEventPushRtmpHotSwitchRtc recovery:self.roomInfo.isRecovery encodeInfo:self.pusher.videoEncodeInfo];
    });
    
}

- (void)livePusherPushEvent:(XYLiveRtmpPusher *)pusher eventId:(XYLivePusherEvent)eventId {
    if (self.pusher != nil) {
        [self updateApmRtmpParam];
        XYLivePushEvent event;
        switch(eventId) {
            case XYLivePusherEventSentFirstVideoFrame:
                [self sendSubmitPushType:NO];
                event = XYLivePushEventPushFirstVideoFrame;
                break;
            case XYLivePusherEventSentFirstAudioFrame:
                event = XYLivePushEventPushFirstAudioFrame;
                break;
            case XYLivePusherEventUserVideoAvailable:
                event = XYLivePushEventPushVideo;
                break;
            case XYLivePusherEventUserAudioAvailable:
                event = XYLivePushEventPushAudio;
                break;
            case XYLivePusherEventConnectLost:
                event = XYLivePushEventConnectionLost;
                break;
            case XYLivePusherEventConnectReconnect:
                event = XYLivePushEventConnectionReconnect;
                break;
            case XYLivePusherEventConnectRecovery:
                event = XYLivePushEventConnectionRecovery;
                break;
            case XYLivePusherEventStartPubCDNStream:
                event = XYLivePushEventStartPushCdnStream;
                break;
            case XYLivePusherEventStopPubCDNStream:
                event = XYLivePushEventStopPushCdnStream;
                break;
            case XYLivePusherEventStartPubSRSStream:
                event = XYLivePushEventStartPushSRSStream;
                break;
            case XYLivePusherEventStopPubSRSStream:
                event = XYLivePushEventStopPushSRSStream;
                break;
            case XYLivePusherEventSetMixConfig:
                event = XYLivePushEventSetMixTranscodingConfig;
                break;
            case XYLivePusherHotSwitch:
                event = XYLivePushEventPushRtmpHotSwitchRtc;
                break;
            case XYLivePusherCameraReady:
                event = XYLivePushEventCameraReady;
                break;
            case XYLivePusherMicReady:
                event = XYLivePushEventMicReady;
                break;
            case XYLivePusherVTBEncodedFirstVideo:
                /// 自研推流 在收到VTB 首帧回调 上报success 事件
                event = XYLivePushEventPushSuccess;
                break;
            default:
                event = XYLivePushEventUnknown;
                break;
        }
        [xyLiveSharedAPMManager reportLivePushEvent:event recovery:self.roomInfo.isRecovery encodeInfo:self.pusher.videoEncodeInfo];
    }
}

- (void)livePusherPushCodecChangedEvent:(XYLiveRtmpPusher *)pusher currentCodec:(int)codec {
    if (self.pusher != nil) {
        // 自研推流收到codec变化 submitPushType增量更新codec
        [self sdk_submitPushInfo:YES];
        [self updateApmRtmpParam];
        [xyLiveSharedAPMManager reportLivePushCodecChange:codec recovery:self.roomInfo.isRecovery encodeInfo:self.pusher.videoEncodeInfo];
    }
}

- (void)onUserVoiceVolumeWithLocal:(int)volume {
    
}


@end
