//
//  LivePushMultiChatRoomDomain.swift
//  XYLiveKit
//
//  Created by 大远 on 2024/8/20.
//  Copyright © 2024 XingIn. All rights reserved.
//

import XYAlphaFusion
import XYLiveServiceProtocol
import XYAlphaUtils

@objc(XYLivePushMultiChatRoomDomain)
@objcMembers
class LivePushMultiChatRoomDomain: LiveBaseDomain<LivePushMultiChatRoomDependProtocol>, LiveRoomInfoServiceProtocol {
    
    // 容器vc
    private weak var vc: XYLivePushMultiChatRoomController?
    
    // 中间层，用于缓存数据和事件
    private lazy var serviceImpl: XYLivePushMultiChatRoomService = {
        let service = XYLivePushMultiChatRoomService()
        return service
    }()
    
    // 注册服务
    public override func doRegisterService(registery: ServiceRegistery) {
        registery.registerService(protocol: XYLivePushMultiChatRoomServiceProtocol.self, service: self.serviceImpl)
    }

    // 声明依赖服务
    public override func doInjectService(provider: ServiceProvider) -> LivePushMultiChatRoomDependProtocol {
        return LivePushMultiChatRoomDepend(provider: provider)
    }
    
    public override func didLoad() {
        super.didLoad()
        // 注册监听
        self.depend?.liveInfoService()?.registerRoomInfoListener(self)
    }
    
    public func onRoomInfoDidUpdate(_ oldRoomInfo: XYLiveRoomInfo?, newRoomInfo: XYLiveRoomInfo?) {
        // 非畅聊直播间则直接跳出
        guard let isLiveChat = newRoomInfo?.isLiveChat, isLiveChat, let depend = self.depend else { return }
        
        // 初始化容器vc
        let vc = XYLivePushMultiChatRoomController(containerVC: self.hostViewController(), depend: depend);
        add(vc)
        self.vc = vc
        // 绑定服务
        serviceImpl.bindTarget(vc);
    }
}
