//
//  XYLiveLinkHostSearchListSectionView.h
//  XYLiveKit
//
//  Created by 大远 on 2025/5/21.
//  Copyright © 2025 XingIn. All rights reserved.
//

#import <UIKit/UIKit.h>
#import <XYLiveKit/XYLiveLinkHostInviteListSectionView.h>

NS_ASSUME_NONNULL_BEGIN

@interface XYLiveLinkHostSearchListSectionView : UIView

// 是否选中
@property (nonatomic, assign, getter=isChecked) BOOL checked;
// 点击事件回调
@property (nonatomic, copy) void(^didTapHandler)(BOOL isChecked);

@end

NS_ASSUME_NONNULL_END
