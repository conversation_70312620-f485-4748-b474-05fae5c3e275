Pod::Spec.new do |s|

  s.name         = "XYLiveKit"
  s.version      = "0.0.2"
  s.summary      = "A short description of XYLiveKit."

  s.homepage     = "https://code.devops.xiaohongshu.com/iOS/XYLiveKit"
  s.license      = { :type => "MIT", :file => "LICENSE" }
  s.author       = { "周博立" => "<EMAIL>" }
  s.platform     = :ios, "10.0"

  s.static_framework = true

  s.source       = { :git => "*******************************:iOS/XYLiveKit.git", :tag => "#{s.version}" }

  s.source_files  = "XYLiveKit/XYLiveKit.h", "XYLiveKit/Sources/**/*"
  s.public_header_files = [
    "XYLiveKit/XYLiveKit.h",
    "XYLiveKit/Sources/**/*.h",
  ]
  #这里面放国际化资源，xib等，打包在main bundle
  s.resources = "XYLiveKit/Resources/**.*"
  #这里面放图片资源，独立bundle
  s.resource_bundles = {
    'XYLiveKit' => ['XYLiveKit/Resource_Bundle/XYLiveKit.bundle/**/*',
                    'XYLiveKit/Resource/*']
  }

  #直播基础组件
  s.dependency 'XYLiveFoundation'
  s.dependency 'XYLiveUIKit'
  s.dependency 'XYLivePlayManager'
  s.dependency 'XYLivePusher'
  s.dependency 'XYLiveIMKit'
  s.dependency 'DLNAKit'
  s.dependency 'XYLiveSquare'

  #第三方开源
  s.dependency 'Masonry'
  s.dependency 'KVOController'
  s.dependency 'CocoaLumberjack'
  s.dependency 'lottie-ios'
  s.dependency 'ZipArchive'
  s.dependency 'IQKeyboardManager'
  s.dependency 'RxSwift'
  s.dependency 'RxRelay'

  #业务基础组件
  s.dependency 'XYMetalAnimationKit'
  s.dependency 'XYNoteModels'
  s.dependency 'XYTeenagerModeKit'
  s.dependency 'XYTransitionProtocols'
  s.dependency 'XYPKDownloadKit'
  s.dependency 'XYPostBeautyKit'
  s.dependency 'XYHybridModule'
  s.dependency 'XYHybridUtility'
  s.dependency 'XYChinaUnicomKit'
  s.dependency 'XYLiveChatBoard'
  s.dependency 'XYSessionManager'
  s.dependency 'XYRoutesManager'
  s.dependency 'XYPhotosKit'
  s.dependency 'XYPHBindPhoneManager'
  s.dependency 'XYMixUpload'
  s.dependency 'XYCamera'
  s.dependency 'XYAVKit'
  s.dependency 'XYHorizon'
  s.dependency 'XYCollectionViewAlignedLayout'
  s.dependency 'XYLiveCore'
  s.dependency 'XYAnimationView'
  s.dependency 'XYCustomVolumeBar'
  s.dependency 'XYSearchBar'
  s.dependency 'XYCardMessage'
  s.dependency 'XYMessageRefresh'
  s.dependency 'XYCommercialAPISet'
  s.dependency 'XYLiveServiceProtocol'
  s.dependency 'XYMerchantTroubleShooting'
  s.dependency 'XYScarlet'
  s.dependency 'XYShopExtension'
  #直播SDK
#  s.dependency 'TXIMSDK_iOS'
  s.dependency 'XYDylibLoader'
  s.dependency 'TXLiteAVSDK_Professional'
#  s.dependency 'XYRTCEngineKit'

  #基础组件
  s.dependency 'XYAPIRoute'
  s.dependency 'XYLogCollector'
  s.dependency 'XYAlertCenter'
  s.dependency 'XYUIKit'
  s.dependency 'XYUIKitCore'
  s.dependency 'XYProtoBuffer'
  s.dependency 'XYSharingUIKit'
  s.dependency 'XYPaymentKit'
  s.dependency 'XYAuthFlowKit'
  s.dependency 'XYCommentBar'
  s.dependency 'XYSecurity'
  s.dependency 'XYModel'
  s.dependency 'XYSharingKit'
  s.dependency 'XYAPISet'
  s.dependency 'XYRouter'
  s.dependency 'XYFileManager'
  s.dependency 'XYEyeSauron'
  s.dependency 'XYFoundation'
  s.dependency 'XYWorldSnake'
  s.dependency 'XYUITheme'
  s.dependency 'XYPrivacy'
  s.dependency 'XYDevice'
  s.dependency 'XYModels'
  s.dependency 'XYWebImage'
  s.dependency 'XYPHConfig'
  s.dependency 'XYAnalytics'
  s.dependency 'XYVideoKit'
  s.dependency 'XYAudioSessionCategoryKit'
  s.dependency 'XYTracker'
  s.dependency 'XYNoteCardComponent'
  s.dependency 'XYDeepLink'
  s.dependency 'XYSocketKit'
  s.dependency 'XYNegativeFeedback'
  s.dependency 'XYPageViewController'
  s.dependency 'XYEngagedManager'
  s.dependency 'XYMediaControl'
  s.dependency 'XYCanvas'
  s.dependency 'XYPageFeedModule'
  s.dependency 'XYMatrix'
  s.dependency 'XYMessageKit'
  s.dependency 'XYPBPhotoBrowser'
  s.dependency 'HomeEntities'
  s.dependency 'XYStorageCore_Linker'
  s.dependency 'XYPlayerAPM'
  s.dependency 'XYJankMonitor'
  s.dependency 'XYAlphaUtils'
  s.dependency 'XYAlphaShare'
  s.dependency 'XYAlphaNetwork'
  s.dependency 'XYLiveFeed'
  s.dependency 'RedI18N'
  s.dependency 'I18N'
  s.dependency 'XYMobileLevel'
  s.dependency 'XYLiveRedPacket'
  s.dependency 'XYAlphaGift'
  s.dependency 'XYAlphaWallet'
  s.dependency 'XYSearchKit'
  s.dependency 'XYAlphaLog'
  s.dependency 'XYVision'
  s.dependency 'XYBeehive'
  s.dependency 'XYDeviceKit'
  s.dependency 'XYDPMonitor'
  s.dependency 'XYUserAvatar'
  s.dependency 'XYColdStartConfig'

end
