//
//  XYBasePusherListener.swift
//  XYLiveMediaKit
//
//  Created by Gael on 2022/9/21.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation

public protocol XYBasePusherListener {
    func onAudioVolumeIndication(speakers: [XYLiveMediaAudioVolumeInfo], totalVolume: Int)
    func onLocalAudioStats(stats: LiveMediaLocalAudioStats)
    func onRtcStats(stats: LiveMediaRtcStats)
    func onJoinChannelSuccess(channel: String, uid: Int, elapsed: Int)
    func onError(err: Int)
    func onWarning(warn: Int)
    func onFirstLocalAudioFramePublished(elapsed: Int)
}

public protocol XYBasePusherMusicListener {
    func onAudioMixingStateChanged(state: LiveMediaAudioMixingState, errorCode: LiveMediaAudioMixingReasonType)
    func onAudioEffectFinished(soundId: Int)
}
