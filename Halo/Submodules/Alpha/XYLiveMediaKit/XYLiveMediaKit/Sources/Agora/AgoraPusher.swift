//
//  AgoraPusher.swift
//  XYLiveMediaKit
//
//  Created by zhangzhen<PERSON> on 2022/9/22.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation
import AgoraRtcKit
import XYLiveFoundation

public class AgoraPusher: XYBasePusher {
    private var mediaConfig: XYLiveMediaConfig
    private var appId: String
    private var agora: AgoraRtcEngineKit?
    private var TAG: String = "AgoraPusher"
    var pushListener: XYBasePusherListener?
    var musicListener: XYBasePusherMusicListener?
    
    init(appId: String, config: XYLiveMediaConfig) {
        self.mediaConfig = config
        self.appId = appId
        super.init()
        createAgora()
        sdkVersion = AgoraRtcEngineKit.getSdkVersion()
        XYLogCollector.xyLiveLogTag(TAG, content: "AgoraPusher -- version:\(String(describing: sdkVersion)) appid: \(appId)")
    }
    
    deinit {
        XYLogCollector.xyLiveLogTag(TAG, content: "deinit!!!!")
    }
    
    private func createAgora() {
        let config = AgoraRtcEngineConfig()
        config.appId = appId
        config.audioScenario = .default
//        config.areaCode = GlobalSettings.shared.area
        config.channelProfile = .liveBroadcasting
        agora = AgoraRtcEngineKit.sharedEngine(with: config, delegate: self)
        agora?.setAudioProfile(.musicStandard)
        agora?.setChannelProfile(.liveBroadcasting)
        agora?.enableAudioVolumeIndication(self.mediaConfig.audioVolumeIndication, smooth: self.mediaConfig.audioVolumeSmooth, reportVad: true)
        
    }
    public override func enableVideo() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "enableVideo")
        return Int(agora?.enableVideo() ?? -1)
    }
    
    public override func disableVideo() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "disableVideo")
        return Int(agora?.disableVideo() ?? -1)
    }
    
    public override func enableAudio() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "enableAudio")
        return Int(agora?.enableAudio() ?? -1)
    }
   
    public override func disableAudio() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "disableAudio")
        return Int(agora?.disableAudio() ?? -1)
    }
    
    public override func join(channelId: String, token: String, uid: Int, audioOn: Bool, videoOn: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "join channel:\(channelId), token:\(token), uid:\(uid), audioOn:\(audioOn), videoOn: \(videoOn)")
        let mediaOptions: AgoraRtcChannelMediaOptions = AgoraRtcChannelMediaOptions()
        mediaOptions.audioDelayMs = getAgoraIntOption(option: 1000)
        if audioOn {
            mediaOptions.clientRoleType = getAgoraIntOption(option: getAgoraRole(role: .Anchor).rawValue)
        } else {
            mediaOptions.clientRoleType = getAgoraIntOption(option: getAgoraRole(role: .Audience).rawValue)
        }
        
        if videoOn {
            // 自采集
            mediaOptions.publishCameraTrack = getAgoraBoolOption(option: false)
            mediaOptions.publishCustomVideoTrack = getAgoraBoolOption(option: true)
        } else {
            mediaOptions.publishCameraTrack = getAgoraBoolOption(option: false)
        }
        // 自动订阅远端音视频流
        mediaOptions.autoSubscribeAudio = getAgoraBoolOption(option: true)
        mediaOptions.autoSubscribeVideo = getAgoraBoolOption(option: true)
        return Int(agora?.joinChannel(byToken: token, channelId: channelId, uid: UInt(uid), mediaOptions: mediaOptions, joinSuccess: nil) ?? -1)
    }
    
    public override func leave() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "leave")
        return Int(agora?.leaveChannel(nil) ?? -1)
    }
    
    public override func publishLocal(videoOn: Bool, audioOn: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "publishLocal videoOn:\(videoOn), audioOn\(audioOn)")
        agora?.enableLocalVideo(videoOn)
        return Int(agora?.enableLocalAudio(audioOn) ?? -1)
    }
    
    public override func muteRemote(uid: Int, video: Bool, audio: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "muteRemote[\(uid)] video:\(video), audio\(audio)")
        agora?.muteRemoteVideoStream(UInt(uid), mute: video)
        return Int(agora?.muteRemoteAudioStream(UInt(uid), mute: audio) ?? -1)
    }
    
    public override func muteLocal(video: Bool, audio: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "muteLocal video:\(video), audio\(audio)")
        agora?.muteLocalVideoStream(video)
        return Int(agora?.muteLocalAudioStream(audio) ?? -1)
        
    }
    
    public override func muteAllRemote(video: Bool, audio: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "muteAllRemote video:\(video), audio\(audio)")
        agora?.muteAllRemoteVideoStreams(video)
        return Int(agora?.muteAllRemoteAudioStreams(audio) ?? -1)
    }
    
    public override func setParameters(parameters: String) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "setParameters parameters:\(parameters)")
        return Int(agora?.setParameters(parameters) ?? -1)
    }
    public override func setLogPath(path: String) {
        XYLogCollector.xyLiveLogTag(TAG, content: "setLogPath path:\(path)")
        agora?.setLogFile(path)
    }
    public override func switchRole(role: LiveMediaRoleType) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "switchRole role:\(role)")
        return Int(agora?.setClientRole(getAgoraRole(role: role)) ?? -1)
    }
    
    public override func release() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "release")
//        return Int(agora?.destory ?? -1)
        muteAudioLocal(audio: false)
        return 0
        
    }
    
    public override func reset(videoOn: Bool, audioOn: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "reset videoOn: \(videoOn), audioOn: \(audioOn)")
        let mediaOpt = AgoraRtcChannelMediaOptions()
        let role: LiveMediaRoleType = .Audience
        mediaOpt.channelProfile = getAgoraIntOption(option: role.rawValue)
        if !videoOn {
            mediaOpt.publishCameraTrack = getAgoraBoolOption(option: false)
            mediaOpt.autoSubscribeVideo = getAgoraBoolOption(option: false)
        } else {
            mediaOpt.publishCameraTrack = getAgoraBoolOption(option: false)
            mediaOpt.autoSubscribeVideo = getAgoraBoolOption(option: true)
            
        }
        mediaOpt.autoSubscribeAudio = getAgoraBoolOption(option: true)
        if !audioOn {
            mediaOpt.publishMicrophoneTrack = getAgoraBoolOption(option: false)
        } else {
            mediaOpt.publishMicrophoneTrack = getAgoraBoolOption(option: true)
        }
        
        return Int(agora?.updateChannel(with: mediaOpt) ?? -1)
        
    }
    
    public override func updateMediaConfig(config: XYLiveMediaConfig) {
        XYLogCollector.xyLiveLogTag(TAG, content: "updateMediaConfig config:\(config)")
        self.mediaConfig = config
        agora?.enableAudioVolumeIndication(config.audioVolumeIndication, smooth: config.audioVolumeSmooth, reportVad: false)
    }
    
    public override func updateChannelConfig(videoOn: Bool, audioOn: Bool, role: LiveMediaRoleType) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "updateChannelConfig videoOn:\(videoOn) audioOn:\(audioOn) role:\(role)")
        XYLogCollector.xyLiveLogTag(TAG, content: "reset videoOn: \(videoOn), audioOn: \(audioOn)")
        let mediaOpt = AgoraRtcChannelMediaOptions()
        let role: LiveMediaRoleType = role
        mediaOpt.channelProfile = getAgoraIntOption(option: role.rawValue)
        if !videoOn {
            mediaOpt.publishCameraTrack = getAgoraBoolOption(option: false)
            mediaOpt.autoSubscribeVideo = getAgoraBoolOption(option: false)
        } else {
            mediaOpt.publishCameraTrack = getAgoraBoolOption(option: false)
            mediaOpt.autoSubscribeVideo = getAgoraBoolOption(option: true)
            
        }
        
        mediaOpt.autoSubscribeAudio = getAgoraBoolOption(option: true)
        mediaOpt.publishMicrophoneTrack = getAgoraBoolOption(option: audioOn)
        
        return Int(agora?.updateChannel(with: mediaOpt) ?? -1)
    }
    public override func setRecordVolume(volume: Int) {
        XYLogCollector.xyLiveLogTag(TAG, content: "setRecordVolume volume:\(volume)")
        agora?.adjustRecordingSignalVolume(volume)
    }
   
    public override func setBasePushListener(listener: XYBasePusherListener) {
        self.pushListener = listener
    }
    public override func setBasePushMusicListener(listener: XYBasePusherMusicListener) {
        self.musicListener = listener
    }
    
    public override func publishVideoLocal(videoOn: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "publishVideoLocal videoOn:\(videoOn)")
        return Int(agora?.enableLocalVideo(videoOn) ?? -1)
    }
    public override func publicAudioLocal(audioOn: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "publicAudioLocal audioOn:\(audioOn)")
        return Int(agora?.enableLocalAudio(audioOn) ?? -1)
    }
    public override func muteVideoRemote(uid: Int, video: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "muteVideoRemote video:\(video) uid: \(uid)")
        return Int(agora?.muteRemoteVideoStream(UInt(uid), mute: video) ?? -1)
    }
    public override func muteAudioRemote(uid: Int, audio: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "muteAudioRemote audio:\(audio) uid: \(uid)")
        return Int(agora?.muteRemoteAudioStream(UInt(uid), mute: audio) ?? -1)
    }
    public override func muteVideoLocal(video: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "muteVideoLocal video:\(video)")
        return Int(agora?.muteLocalVideoStream(video) ?? -1)
    }
    public override func muteAudioLocal(audio: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "muteAudioLocal audio:\(audio)")
        return Int(agora?.muteLocalAudioStream(audio) ?? -1)
    }
    public override func muteVideoAllRemote(video: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "muteVideoAllRemote video:\(video)")
        return Int(agora?.muteAllRemoteVideoStreams(video) ?? -1)
    }
    public override func muteAudioAllRemote(audio: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "muteAudioAllRemote audio:\(audio)")
        return Int(agora?.muteAllRemoteAudioStreams(audio) ?? -1)
    }
    
    public override func preloadEffect(soundId: Int, filePath: String) {
        XYLogCollector.xyLiveLogTag(TAG, content: "preloadEffect filePath:\(filePath) soundId: \(soundId)")
        agora?.preloadEffect(Int32(soundId), filePath: filePath)
    }
    public override func playEffect(params: LiveMediaEffectParams) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "playEffect params:\(params)")
        return Int(agora?.playEffect(Int32(params.soundId), filePath: params.filePath, loopCount: params.loopCount, pitch: params.pitch, pan: params.pan, gain: Int(params.gain), publish: params.publish) ?? -1)
    }
    public override func startPlayMusic(params: LiveMediaMusicParams) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "startPlayMusic params:\(params)")
        if params.musicId < 0 && params.pos < 0{
            return Int(agora?.startAudioMixing(params.filePath, loopback: params.loopback, cycle: params.cycle) ?? -1)
        } else {
            return Int(agora?.startAudioMixing(params.filePath, loopback: params.loopback, cycle: params.cycle, startPos: Int(params.pos)) ?? -1)
        }
    }
    public override func startAudioMixing(params: LiveMediaMusicParams) {
        XYLogCollector.xyLiveLogTag(TAG, content: "startAudioMixing params:\(params)")
        agora?.startAudioMixing(params.filePath, loopback: params.loopback, cycle: params.cycle, startPos: Int(params.pos))
    }
    public override func stopAudioMixing() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "stopAudioMixing")
        return Int(agora?.stopAudioMixing() ?? -1)
    }
    public override func pauseAudioMixing() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "pauseAudioMixing")
        return Int(agora?.pauseAudioMixing() ?? -1)
    }
    public override func resumeAudioMixing() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "resumeAudioMixing")
        return Int(agora?.resumeAudioMixing() ?? -1)
    }
    // 调整音乐文件播放音量(影响本地和远端)
    public override func adjustAudioMixingVolume(volume: Int) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "adjustAudioMixingVolume volume:\(volume)")
        return Int(agora?.adjustAudioMixingVolume(volume) ?? -1)
    }
    // 调整音乐文件本地播放音量(只影响本地)
    public override func adjustAudioMixingPlayoutVolume(volume: Int) {
        XYLogCollector.xyLiveLogTag(TAG, content: "adjustAudioMixingPlayoutVolume volume:\(volume)")
        agora?.adjustAudioMixingVolume(volume)
    }
    // 调整音乐文件远端播放音量(只影响远端)
    public override func adjustAudioMixingPublishVolume(volume: Int) {
        XYLogCollector.xyLiveLogTag(TAG, content: "adjustAudioMixingPublishVolume volume:\(volume)")
        agora?.adjustAudioMixingPublishVolume(volume)
    }
    public override func getAudioMixingPlayoutVolume() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "getAudioMixingPlayoutVolume")
        return Int(agora?.getAudioMixingPlayoutVolume() ?? -1)
    }
    public override func getAudioMixingPublishVolume() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "getAudioMixingPublishVolume")
        return Int(agora?.getAudioMixingPublishVolume() ?? -1)
    }
    public override func getAudioMixingDuration() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "getAudioMixingDuration")
        return Int(agora?.getAudioMixingDuration() ?? -1)
    }
    public override func getAudioMixingCurrentPosition() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "getAudioMixingCurrentPosition")
        return Int(agora?.getAudioMixingCurrentPosition() ?? -1)
    }
    public override func setAudioMixingPosition(pos: Int) {
        XYLogCollector.xyLiveLogTag(TAG, content: "setAudioMixingPosition pos:\(pos)")
        agora?.setAudioMixingPosition(pos)
    }
    public override func getEffectsVolume() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "getEffectsVolume")
        return Int(agora?.getEffectsVolume() ?? -1)
    }
    
    
}
