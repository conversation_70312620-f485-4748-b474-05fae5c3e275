//
//  AgoraPusher+optional.swift
//  XYLiveMediaKit
//
//  Created by z<PERSON>zheng on 2022/9/22.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation
import AgoraRtcKit

extension AgoraPusher {
    func getAgoraRole(role: LiveMediaRoleType) -> AgoraClientRole {
        return AgoraClientRole(rawValue: role.rawValue) ?? .broadcaster
    }
    
    func getAgoraBoolOption(option: Bool) -> AgoraRtcBoolOptional {
        return AgoraRtcBoolOptional.of(option)
    }
    
    func getAgoraIntOption(option: Int) -> AgoraRtcIntOptional {
        return AgoraRtcIntOptional.of(Int32(option))
    }
    

}
