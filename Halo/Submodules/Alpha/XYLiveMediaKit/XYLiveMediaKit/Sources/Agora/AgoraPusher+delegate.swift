//
//  AgoraPusher+delegate.swift
//  XYLiveMediaKit
//
//  Created by z<PERSON>zheng on 2022/9/22.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation
import AgoraRtcKit
import XYLiveFoundation

extension AgoraPusher: AgoraRtcEngineDelegate {
    
    public func rtcEngine(_ engine: AgoraRtcEngineKit, didOccurWarning warningCode: AgoraWarningCode) {
        pushListener?.onWarning(warn: warningCode.rawValue)
    }
    public func rtcEngine(_ engine: AgoraRtcEngineKit, didOccurError errorCode: AgoraErrorCode) {
        pushListener?.onError(err: errorCode.rawValue)
    }
    
    public func rtcEngine(_ engine: AgoraRtcEngineKit, didJoinChannel channel: String, withUid uid: UInt, elapsed: Int) {
        pushListener?.onJoinChannelSuccess(channel: channel, uid: Int(uid), elapsed: elapsed)
    }
    
    public func rtcEngine(_ engine: AgoraRtcEngineKit, didJoinedOfUid uid: UInt, elapsed: Int) {
        
    }
    
    public func rtcEngine(_ engine: AgoraRtcEngineKit, didOfflineOfUid uid: UInt, reason: AgoraUserOfflineReason) {
    
        
    }
    
    public func rtcEngine(_ engine: AgoraRtcEngineKit, reportRtcStats stats: AgoraChannelStats) {
        let rtcStats = LiveMediaRtcStats()
        rtcStats.totalDuration = Int(stats.duration)
        rtcStats.txBytes = Int(stats.txBytes)
        rtcStats.rxBytes = Int(stats.rxBytes)
        rtcStats.txKBitRate = Int(stats.txKBitrate)
        rtcStats.txAudioBytes = Int(stats.txAudioBytes)
        rtcStats.rxAudioBytes = Int(stats.rxAudioBytes)
        rtcStats.txVideoBytes = Int(stats.txVideoBytes)
        rtcStats.rxVideoBytes = Int(stats.rxVideoBytes)
        rtcStats.rxKBitRate = Int(stats.rxKBitrate)
        rtcStats.txAudioKBitRate = Int(stats.txAudioKBitrate)
        rtcStats.rxAudioKBitRate = Int(stats.rxAudioKBitrate)
        rtcStats.txVideoKBitRate = Int(stats.txVideoKBitrate)
        rtcStats.rxVideoKBitRate = Int(stats.rxVideoKBitrate)
        rtcStats.lastmileDelay = Int(stats.lastmileDelay)
        rtcStats.cpuTotalUsage = stats.cpuTotalUsage
        rtcStats.gatewayRtt = stats.gatewayRtt
        rtcStats.cpuAppUsage = stats.cpuAppUsage
        rtcStats.users = Int(stats.userCount)
        rtcStats.connectTimeMs = stats.connectTimeMs
        rtcStats.txPacketLossRate = stats.txPacketLossRate
        rtcStats.rxPacketLossRate = stats.rxPacketLossRate
        rtcStats.memoryAppUsageRatio = stats.memoryAppUsageRatio
        rtcStats.memoryTotalUsageRatio = stats.memoryTotalUsageRatio
        rtcStats.memoryAppUsageInKbytes = stats.memoryAppUsageInKbytes
        self.pushListener?.onRtcStats(stats: rtcStats)
    }
    
    public func rtcEngine(_ engine: AgoraRtcEngineKit, localAudioStats stats: AgoraRtcLocalAudioStats) {
        let audioStats: LiveMediaLocalAudioStats = LiveMediaLocalAudioStats()
        audioStats.numChannels = Int(stats.numChannels)
        audioStats.sentSampleRate = Int(stats.sentSampleRate)
        audioStats.sentBitrate = Int(stats.sentBitrate)
        audioStats.internalCodec = Int(stats.internalCodec)
        audioStats.txPacketLossRate = Int(stats.txPacketLossRate)
        audioStats.audioDeviceDelay = Int(stats.audioDeviceDelay)
        self.pushListener?.onLocalAudioStats(stats: audioStats)
    }
    
    public func rtcEngine(_ engine: AgoraRtcEngineKit, remoteVideoStats stats: AgoraRtcRemoteVideoStats) {
    
    }
    
    public func rtcEngine(_ engine: AgoraRtcEngineKit, remoteAudioStats stats: AgoraRtcRemoteAudioStats) {
    
    }
    
    public func rtcEngine(_ engine: AgoraRtcEngineKit, firstLocalAudioFramePublished elapsed:Int) {
        XYLogCollector.xyLiveLogTag("AgoraPusher+delegate", content: "firstLocalPublished elapsed:\(elapsed)")
        self.pushListener?.onFirstLocalAudioFramePublished(elapsed: elapsed)
    }
    
    

    public func rtcEngine(_ engine: AgoraRtcEngineKit, reportAudioVolumeIndicationOfSpeakers speakers: [AgoraRtcAudioVolumeInfo], totalVolume: Int) {
        var mediaSpeakers = [XYLiveMediaAudioVolumeInfo]()
        for speaker in speakers {
            mediaSpeakers.append(covertVolumeInfo(volumeInfo: speaker))
        }
        self.pushListener?.onAudioVolumeIndication(speakers: mediaSpeakers, totalVolume: totalVolume)
    }
    
    public func rtcEngine(_ engine: AgoraRtcEngineKit, audioMixingStateChanged state: AgoraAudioMixingStateType, reasonCode: AgoraAudioMixingReasonCode) {
        self.musicListener?.onAudioMixingStateChanged(state: covertLiveMediaMixingState(state: state), errorCode: covertLiveMediaMixingReason(reason: reasonCode))
    }
    
    public func rtcEngine(_ engine: AgoraRtcEngineKit, didAudioEffectFinish soundId: Int) {
        self.musicListener?.onAudioEffectFinished(soundId: soundId)
    }
  

    private func covertVolumeInfo(volumeInfo: AgoraRtcAudioVolumeInfo) -> XYLiveMediaAudioVolumeInfo {
        let mediaVolumeInfo = XYLiveMediaAudioVolumeInfo()
        mediaVolumeInfo.volume = Int(volumeInfo.volume)
        mediaVolumeInfo.uid = Int(volumeInfo.uid)
        mediaVolumeInfo.vad = Int(volumeInfo.vad)
        mediaVolumeInfo.voicePitch = volumeInfo.voicePitch
        return mediaVolumeInfo
    }
    
    private func covertLiveMediaMixingState(state: AgoraAudioMixingStateType) -> LiveMediaAudioMixingState {
        var res: LiveMediaAudioMixingState = .Unknown
        switch state {
        case .paused:
            res = .Paused
        case .playing:
            res = .Playing
        case .stopped:
            res = .Stopped
        case .failed:
            res = .Failed
        @unknown default:
            break
        }
        return res
    }
    
    private func covertLiveMediaMixingReason(reason: AgoraAudioMixingReasonCode) -> LiveMediaAudioMixingReasonType {
        var res: LiveMediaAudioMixingReasonType = .Unknown
        switch reason {
        case .typeOk:
            res = .Ok
        case .canNotOpen:
            res = .CanNotOpen
        case .tooFrequentlyCall:
            res = .TooFrequentlyCall
        case .interruptedEOF:
            res = .InterruptedEOF
        case .oneLoopCompleted:
            res = .OneLoopCompleted
        case .allLoopsCompleted:
            res = .AllLoopsCompleted
        case .stoppedByUser:
            res = .StoppedByUser
        @unknown default:
            break
        }
        return res
    }
    
}
