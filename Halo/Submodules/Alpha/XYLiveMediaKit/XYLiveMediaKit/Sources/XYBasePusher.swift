//
//  XYBasePusher.swift
//  XYLiveMediaKit
//
//  Created by <PERSON><PERSON> on 2022/9/21.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation

public class XYBasePusher: NSObject, XYBasePusherInterface, XYBasePusherStreamInterface, XYBasePusherMusicInterface {
    
    private static let TAG = "XYBasePusher"
    
    public var sdkVersion: String?
     
    public func join(channelId: String, token: String, uid: Int, audioOn: Bool, videoOn: Bool) -> Int { return 0 }
    public func leave() -> Int { return 0 }
    public func publishLocal(videoOn: Bool, audioOn: Bool) -> Int { return 0 }
    public func subscribeRemote(uid: Int, videoOn: Bool, audioOn: Bool) -> Int { return 0 }
    public func muteRemote(uid: Int, video: Bool, audio: Bool) -> Int { return 0 }
    public func muteLocal(video: Bool, audio: Bool) -> Int { return 0 }
    public func muteAllRemote(video: Bool, audio: Bool) -> Int { return 0 }

    public func setParameters(parameters: String) -> Int { return 0 }
    public func setLogPath(path: String) {}
    public func switchRole(role: LiveMediaRoleType) -> Int { return 0 }
    
    public func release() -> Int { return 0 }
    public func reset(videoOn: Bool, audioOn: Bool) -> Int { return 0 }
    public func updateMediaConfig(config: XYLiveMediaConfig) { }
    public func updateChannelConfig(videoOn: Bool, audioOn: Bool, role: LiveMediaRoleType) -> Int { return 0}
    public func setRecordVolume(volume: Int) {}
    public func enableVideo() -> Int { return 0 }
    public func disableVideo() -> Int { return 0 }
    public func enableAudio() -> Int { return 0 }
    public func disableAudio() -> Int { return 0 }
    public func setBasePushListener(listener: XYBasePusherListener) {}
    public func setBasePushMusicListener(listener: XYBasePusherMusicListener) {}
    
    public func publishVideoLocal(videoOn: Bool) -> Int { return 0 }
    public func publicAudioLocal(audioOn: Bool) -> Int { return 0 }
    public func muteVideoRemote(uid: Int, video: Bool) -> Int { return 0 }
    public func muteAudioRemote(uid: Int, audio: Bool) -> Int { return 0 }
    public func muteVideoLocal(video: Bool) -> Int { return 0 }
    public func muteAudioLocal(audio: Bool) -> Int { return 0 }
    public func muteVideoAllRemote(video: Bool) -> Int { return 0 }
    public func muteAudioAllRemote(audio: Bool) -> Int { return 0 }
    
    public func preloadEffect(soundId: Int, filePath: String) {}
    public func playEffect(params: LiveMediaEffectParams) -> Int { return 0 }
    public func startPlayMusic(params: LiveMediaMusicParams) -> Int { return 0 }
    public func startAudioMixing(params: LiveMediaMusicParams) {}
    public func stopAudioMixing() -> Int { return 0 }
    public func pauseAudioMixing() -> Int { return 0 }
    public func resumeAudioMixing() -> Int { return 0 }

    // 调整音乐文件播放音量(影响本地和远端)
    public func adjustAudioMixingVolume(volume: Int) -> Int { return 0 }
    // 调整音乐文件本地播放音量(只影响本地)
    public func adjustAudioMixingPlayoutVolume(volume: Int) {}
    // 调整音乐文件远端播放音量(只影响远端)
    public func adjustAudioMixingPublishVolume(volume: Int) {}
    public func getAudioMixingPlayoutVolume() -> Int { return 0 }
    public func getAudioMixingPublishVolume() -> Int { return 0 }
    public func getAudioMixingDuration() -> Int { return 0 }
    public func getAudioMixingCurrentPosition() -> Int { return 0 }
    public func setAudioMixingPosition(pos: Int) {}
    public func getEffectsVolume() -> Int { return 0 }
}
