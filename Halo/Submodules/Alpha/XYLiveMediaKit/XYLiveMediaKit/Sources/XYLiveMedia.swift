//
//  XYLiveMedia.swift
//  XYLiveMediaKit
//
//  Created by <PERSON><PERSON> on 2022/9/21.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation
import XYLiveFoundation

public class XYLiveMedia: NSObject, XYBasePusherListener, XYBasePusherMusicListener {
    private static let TAG = "XYLiveMedia"
    private let currentPushType: LiveMediaPushType = .Agora
    private var xyMediaPusher: XYBasePusher?
    var xyMediaListener: XYLiveMediaListener?
    var xyMediaMusicListener: XYLiveMediaMusicListener?
    private var mediaConfig: XYLiveMediaConfig?
    private var TAG = "XYLiveMedia"
    private var currentUserMode: LiveMediaUserMode = LiveMediaUserMode()
    private var loopBundle: LiveMediaBundle = LiveMediaBundle()
    private var currentRole: LiveMediaRoleType = .Audience
    private var apmTracker: XYLiveMediaApmTracker?
    private var extendInfo: [String: String] = [String: String]()
    private var currentStatus: MediaStatus = .NONE
    
    public init(appId: String, config: XYLiveMediaConfig) {
        XYLogCollector.xyLiveLogTag(TAG, content: "init")
        super.init()
        mediaConfig = config
        xyMediaPusher = createPusher(pushType: currentPushType, appId: appId)
        xyMediaPusher?.setBasePushListener(listener: self)
        xyMediaPusher?.setBasePushMusicListener(listener: self)
        updateUserMode()
        // 初始化apmTracker
        apmTracker = XYLiveMediaApmTracker(userMode: currentUserMode)
        extendInfo["bizType"] = String(config.bizType.rawValue)
        
    }
    
    deinit {
        XYLogCollector.xyLiveLogTag(TAG, content: "deinit!!!")
    }
    
    private func createPusher(pushType: LiveMediaPushType, appId: String) -> XYBasePusher? {
        guard let config = mediaConfig else { return nil }
        if pushType == .Agora {
            return AgoraPusher(appId: appId, config: config)
        } else {
            return nil
        }
    }
    
    private func updateUserMode() {
        currentUserMode.pushSdkType = currentPushType
        if let config = mediaConfig {
            currentUserMode.userId = config.currentUserId
            currentUserMode.emceeId = config.currentUserId
            currentUserMode.emceeName = config.currentUserName
        }
        // apmTracker update
        apmTracker?.updateUserMode(mode: currentUserMode)
        // apmTracker init sdkVersion
        if let sdkVersion = xyMediaPusher?.sdkVersion {
            apmTracker?.initParams(sdkVersion: sdkVersion)
        }
    }
    
    
    @discardableResult
    public func join(channelId: String, token: String, uid: Int, config: XYLiveMediaConfig, audioOn: Bool, videoOn: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        self.mediaConfig = config
        currentUserMode.roomId = channelId
        updateUserMode()
        let res = xyMediaPusher?.join(channelId: channelId, token: token, uid: uid, audioOn: audioOn, videoOn: videoOn) ?? -1
        apmTracker?.onPusherEvent(event: .ALPHA_PUSHER_ROOM_CREATE, extendInfo: self.extendInfo, extraCode: 0, extraMsg: "")
        return res
    }
    @discardableResult
    public func leave() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return xyMediaPusher?.leave() ?? -1
    }
    @discardableResult
    public func publishLocal(videoOn: Bool, audioOn: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return xyMediaPusher?.publishLocal(videoOn: videoOn, audioOn: audioOn) ?? -1
    }
    @discardableResult
    public func subscribeRemote(uid: Int, videoOn: Bool, audioOn: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return xyMediaPusher?.subscribeRemote(uid: uid, videoOn: videoOn, audioOn: audioOn) ?? -1
    }
    @discardableResult
    public func muteRemote(uid: Int, video: Bool, audio: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return xyMediaPusher?.muteRemote(uid: uid, video: video, audio: audio) ?? -1
    }
    @discardableResult
    public func muteLocal(video: Bool, audio: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return xyMediaPusher?.muteLocal(video: video, audio: audio) ?? -1
    }
    @discardableResult
    public func muteAllRemote(video: Bool, audio: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return xyMediaPusher?.muteAllRemote(video: video, audio: audio) ?? -1
    }
    @discardableResult
    public func setParameters(parameters: String) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return xyMediaPusher?.setParameters(parameters: parameters) ?? -1
    }
    public func setLogPath(path: String) {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        xyMediaPusher?.setLogPath(path: path)
    }
    @discardableResult
    public func switchRole(role: LiveMediaRoleType) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function) role: \(role)")
        currentRole = role
        return xyMediaPusher?.switchRole(role: role) ?? -1
    }
    @discardableResult
    public func release() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        setStatus(status: .STOP)
        setStatus(status: .NONE)
        return xyMediaPusher?.release() ?? -1
    }
    public func setRecordVolume(volume: Int) {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        xyMediaPusher?.setRecordVolume(volume: volume)
    }
    @discardableResult
    public func enableVideo() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return xyMediaPusher?.enableVideo() ?? -1
    }
    @discardableResult
    public func disableVideo() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return xyMediaPusher?.disableVideo() ?? -1
    }
    @discardableResult
    public func enableAudio() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return xyMediaPusher?.enableAudio() ?? -1
    }
    @discardableResult
    public func disableAudio() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return xyMediaPusher?.disableAudio() ?? -1
    }
    @discardableResult
    public func reset(videoOn: Bool, audioOn: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return xyMediaPusher?.reset(videoOn: videoOn, audioOn: audioOn) ?? -1
    }
    @discardableResult
    public func updateChannelConfig(videoOn: Bool, audioOn: Bool, role: LiveMediaRoleType) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function) videoOn: \(videoOn) audioOn: \(audioOn) role: \(role) currentRole: \(currentRole)")
        if audioOn {
            setStatus(status: .PUBLISH)
        } else {
            setStatus(status: .STOP)
        }
        currentRole = role
        return xyMediaPusher?.updateChannelConfig(videoOn: videoOn, audioOn: audioOn, role: role) ?? -1
    }
    
    public func updateMediaConfig(config: XYLiveMediaConfig) {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        xyMediaPusher?.updateMediaConfig(config: config)
    }
    
    public func setListener(listener: XYLiveMediaListener) {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        self.xyMediaListener = listener
    }
    public func setMusicListener(listener: XYLiveMediaMusicListener) {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        self.xyMediaMusicListener = listener
    }
    @discardableResult
    public func publishVideoLocal(videoOn: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.publishVideoLocal(videoOn: videoOn) ?? -1
    }
    @discardableResult
    public func publichAudioLocal(audioOn: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        if audioOn {
            setStatus(status: .PUBLISH)
        } else {
            setStatus(status: .STOP)
        }
        return self.xyMediaPusher?.publicAudioLocal(audioOn: audioOn) ?? -1
    }
    @discardableResult
    public func muteVideoRemote(uid: Int, video: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.muteVideoRemote(uid: uid, video: video) ?? -1
    }
    @discardableResult
    public func muteAudioRemote(uid: Int, audio: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.muteAudioRemote(uid: uid, audio: audio) ?? -1
    }
    @discardableResult
    public func muteVideoLocal(video: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.muteVideoLocal(video: video) ?? -1
    }
    @discardableResult
    public func muteAudioLocal(audio: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.muteAudioLocal(audio: audio) ?? -1
    }
    @discardableResult
    public func muteVideoAllRemote(video: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.muteVideoAllRemote(video: video) ?? -1
    }
    @discardableResult
    public func muteAudioAllRemote(audio: Bool) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.muteAudioAllRemote(audio: audio) ?? -1
    }
    
    public func preloadEffect(soundId: Int, filePath: String) {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        self.xyMediaPusher?.preloadEffect(soundId: soundId, filePath: filePath)
    }
    @discardableResult
    public func playEffect(params: LiveMediaEffectParams) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.playEffect(params: params) ?? -1
    }
    public func startPlayMusic(params: LiveMediaMusicParams) -> Int{
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.startPlayMusic(params: params) ?? -1
    }
    public func startAudioMixing(params: LiveMediaMusicParams) {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        self.xyMediaPusher?.startAudioMixing(params: params)
    }
    public func stopAudioMixing() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.stopAudioMixing() ?? -1
    }
    public func pauseAudioMixing() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.pauseAudioMixing() ?? -1
    }
    public func resumeAudioMixing() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.resumeAudioMixing() ?? -1
    }

    // 调整音乐文件播放音量(影响本地和远端)
    public func adjustAudioMixingVolume(volume: Int) -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.adjustAudioMixingVolume(volume: volume) ?? -1
    }
    // 调整音乐文件本地播放音量(只影响本地)
    public func adjustAudioMixingPlayoutVolume(volume: Int) {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        self.xyMediaPusher?.adjustAudioMixingPlayoutVolume(volume: volume)
    }
    // 调整音乐文件远端播放音量(只影响远端)
    public func adjustAudioMixingPublishVolume(volume: Int) {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        self.xyMediaPusher?.adjustAudioMixingPublishVolume(volume: volume)
    }
    @discardableResult
    public func getAudioMixingPlayoutVolume() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.getAudioMixingPlayoutVolume() ?? -1
    }
    @discardableResult
    public func getAudioMixingPublishVolume() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.getAudioMixingPublishVolume() ?? -1
    }
    @discardableResult
    public func getAudioMixingDuration() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.getAudioMixingDuration() ?? -1
    }
    @discardableResult
    public func getAudioMixingCurrentPosition() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.getAudioMixingCurrentPosition() ?? -1
    }
    public func setAudioMixingPosition(pos: Int) {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        self.xyMediaPusher?.setAudioMixingPosition(pos: pos)
    }
    @discardableResult
    public func getEffectsVolume() -> Int {
        XYLogCollector.xyLiveLogTag(TAG, content: "\(#function)")
        return self.xyMediaPusher?.getEffectsVolume() ?? -1
    }
    
    private func setStatus(status: MediaStatus) {
        if status == currentStatus {
            return
        }
        currentStatus = status
        switch currentStatus {
        case .PUBLISH:
            apmTracker?.onPusherEvent(event: .ALPHA_PUSHER_PUSH_START, extendInfo: self.extendInfo, extraCode: 0, extraMsg: "")
        case .STOP:
            if currentRole == .Anchor {
                apmTracker?.onPusherEvent(event: .ALPHA_PUSHER_ROOM_STOP, extendInfo: self.extendInfo, extraCode: 0, extraMsg: "")
            }
        case .NONE:
            break
        case .JOINING:
            break
        case .JOINED:
            break
        }
    }
    
    
}


extension XYLiveMedia {
    
    public func onAudioVolumeIndication(speakers: [XYLiveMediaAudioVolumeInfo], totalVolume: Int) {
        xyMediaListener?.onAudioVolumeIndication(speakers: speakers, totalVolume: totalVolume)
    }
    public func onAudioMixingStateChanged(state: LiveMediaAudioMixingState, errorCode: LiveMediaAudioMixingReasonType) {
        xyMediaMusicListener?.onAudioMixingStateChanged(state: state, errorCode: errorCode)
    }
    
    public func onAudioEffectFinished(soundId: Int) {
        xyMediaMusicListener?.onAudioEffectFinished(soundId: soundId)
    }
    
    public func onLocalAudioStats(stats: LiveMediaLocalAudioStats) {
        if currentRole != .Anchor {
            return
        }
        loopBundle.localAudioSamplerate = stats.sentSampleRate
        loopBundle.localAudioBitrate = stats.sentBitrate
    }
    
    public func onRtcStats(stats: LiveMediaRtcStats) {
        if currentRole != .Anchor {
            return
        }
        loopBundle.rtt = stats.lastmileDelay
        loopBundle.upLoss = stats.txPacketLossRate
        loopBundle.downLoss = stats.rxPacketLossRate
        loopBundle.sentBytes = UInt64(stats.txBytes)
        loopBundle.receivedBytes = UInt64(stats.rxBytes)
        
        apmTracker?.onPusherLoop(bundle: loopBundle, extendInfo: extendInfo)
    }
    
    public func onJoinChannelSuccess(channel: String, uid: Int, elapsed: Int) {
        
    }
    
    public func onError(err: Int) {
        if currentRole == .Anchor {
            apmTracker?.onPusherEvent(event: .ALPHA_PUSHER_ERROR, extendInfo: self.extendInfo, extraCode: 0, extraMsg: "")
            apmTracker?.onPusherError(errorCode: err, errorMsg: "", extendInfo: self.extendInfo)
        }
    }
    
    public func onWarning(warn: Int) {
        if currentRole == .Audience {
            apmTracker?.onPusherEvent(event: .ALPHA_PUSHER_WARN, extendInfo: self.extendInfo, extraCode: 0, extraMsg: "")
            apmTracker?.onPusherWarn(warnCode: warn, warnMsg: "", extendInfo: self.extendInfo)
        }
    }
    
    public func onFirstLocalAudioFramePublished(elapsed: Int) {
        apmTracker?.onPusherEvent(event: .ALPHA_PUSHER_PUSH_SUCCESS, extendInfo: self.extendInfo, extraCode: 0, extraMsg: "")
    }
    
}
