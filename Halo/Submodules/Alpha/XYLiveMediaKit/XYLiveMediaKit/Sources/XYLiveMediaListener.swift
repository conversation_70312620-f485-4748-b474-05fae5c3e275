//
//  XYLiveMediaListener.swift
//  XYLiveMediaKit
//
//  Created by Gael on 2022/9/21.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation

public protocol XYLiveMediaListener {
    func onAudioVolumeIndication(speakers: [XYLiveMediaAudioVolumeInfo], totalVolume: Int)
}

public protocol XYLiveMediaMusicListener {
    func onAudioMixingStateChanged(state: LiveMediaAudioMixingState, errorCode: LiveMediaAudioMixingReasonType)
    func onAudioEffectFinished(soundId: Int)
}
