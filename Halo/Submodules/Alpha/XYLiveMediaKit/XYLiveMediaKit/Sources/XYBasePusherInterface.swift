//
//  XYBasePusherInterface.swift
//  XYLiveMediaKit
//
//  Created by <PERSON><PERSON> on 2022/9/21.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation

public protocol XYBasePusherInterface {
    func join(channelId: String, token: String, uid: Int, audioOn: Bool, videoOn: Bool) -> Int
    func leave() -> Int
    func publishLocal(videoOn: Bool, audioOn: Bool) -> Int
    func subscribeRemote(uid: Int, videoOn: Bool, audioOn: Bool) -> Int
    func muteRemote(uid: Int, video: Bool, audio: Bool) -> Int
    func muteLocal(video: Bool, audio: Bool) -> Int
    func muteAllRemote(video: Bool, audio: Bool) -> Int

    func setParameters(parameters: String) -> Int
    func setLogPath(path: String)
    func switchRole(role: LiveMediaRoleType) -> Int

    func release() -> Int
    func setRecordVolume(volume: Int)
    func enableVideo() -> Int
    func disableVideo() -> Int
    func enableAudio() -> Int
    func disableAudio() ->Int
    func setBasePushListener(listener: XYBasePusherListener)
    func setBasePushMusicListener(listener: XYBasePusherMusicListener)
    func reset(videoOn: Bool, audioOn: Bool) -> Int
    func updateChannelConfig(videoOn: Bool, audioOn: Bool, role: LiveMediaRoleType) -> Int
    func updateMediaConfig(config: XYLiveMediaConfig)
}
public protocol XYBasePusherStreamInterface {
    func publishVideoLocal(videoOn: Bool) -> Int
    func publicAudioLocal(audioOn: Bool) -> Int
    func muteVideoRemote(uid: Int, video: Bool) -> Int
    func muteAudioRemote(uid: Int, audio: Bool) -> Int
    func muteVideoLocal(video: Bool) -> Int
    func muteAudioLocal(audio: Bool) -> Int
    func muteVideoAllRemote(video: Bool) -> Int
    func muteAudioAllRemote(audio: Bool) -> Int
}

public protocol XYBasePusherMusicInterface {
    func preloadEffect(soundId: Int, filePath: String)
    func playEffect(params: LiveMediaEffectParams) -> Int
    func startPlayMusic(params: LiveMediaMusicParams) -> Int
    func startAudioMixing(params: LiveMediaMusicParams)
    func stopAudioMixing() -> Int
    func pauseAudioMixing() -> Int
    func resumeAudioMixing() -> Int

    // 调整音乐文件播放音量(影响本地和远端)
    func adjustAudioMixingVolume(volume: Int) -> Int
    // 调整音乐文件本地播放音量(只影响本地)
    func adjustAudioMixingPlayoutVolume(volume: Int)
    // 调整音乐文件远端播放音量(只影响远端)
    func adjustAudioMixingPublishVolume(volume: Int)
    func getAudioMixingPlayoutVolume() -> Int
    func getAudioMixingPublishVolume() -> Int
    func getAudioMixingDuration() -> Int
    func getAudioMixingCurrentPosition() -> Int
    func setAudioMixingPosition(pos: Int)
    func getEffectsVolume() -> Int
}
