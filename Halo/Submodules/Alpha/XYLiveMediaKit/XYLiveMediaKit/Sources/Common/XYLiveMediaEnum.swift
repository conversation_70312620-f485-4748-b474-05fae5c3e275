//
//  LiveMediaEnum.swift
//  XYLiveMediaKit
//
//  Created by <PERSON><PERSON> on 2022/9/21.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation

public enum LiveMediaPushType: String {
    case Rtmp_push = "rtmp_pusher"
    case Trtc = "trtc"
    case Agora = "agora"
    case Custom_rtmp = "custom_rtmp"
}


public enum LiveMediaRoleType: Int {
    case Anchor = 1
    case Audience = 2
}

public enum LiveMediaAudioMixingState: Int {
    /**
     * 710: The audio mixing file is playing.
     */
    case Playing = 710
    /**
     * 711: The audio mixing file pauses playing.
     */
    case Paused = 711
    /**
     * 713: The audio mixing file stops playing.
     */
    case Stopped = 713
    /**
     * 714: The audio mixing file playback fails. See `XYRTCBridgeAudioMixingReasonType` for details.
     */
    case Failed = 714
    
    case Unknown = -1
}

public enum LiveMediaAudioMixingReasonType: Int {
    /**
     * 701: The SDK cannot open the audio file.
     */
    case CanNotOpen = 701
    /**
     * 702: The SDK opens the audio mixing file too frequently. Ensure that the time interval between calling {@link XYRTCBridgeRtcEngineKit.startAudioMixing:loopback:replace:cycle: startAudioMixing} is more than 100 ms.
     */
    case TooFrequentlyCall = 702
    /**
     * 703: The audio mixing file playback is interrupted.
     */
    case InterruptedEOF = 703
    /**
     * 721: The audio mixing file is played once.
     */
    case OneLoopCompleted = 721
    
    /** 723: The audio mixing file is all played out. */
    case AllLoopsCompleted = 723
    
    /** 724: The audio mixing file stopped by user */
    case StoppedByUser = 724
    /**
     * 0: No error.
     */
    case Ok = 0
    
    case Unknown = -1
}

public enum LiveMediaBizType: Int {
    case VIDEOLIVE = 0
    case AUDIOLIVE = 1
    case ROOMLIVE = 2
}

public enum MediaStatus: Int {
    case NONE = 0
    case JOINING = 1
    case JOINED = 2
    case PUBLISH = 3
    case STOP = 4
}
