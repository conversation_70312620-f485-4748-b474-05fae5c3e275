//
//  XYLiveMediaConfig.swift
//  XYLiveMediaKit
//
//  Created by Gael on 2022/9/21.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation
import AgoraRtcKit

public class XYLiveMediaConfig: NSObject {
    public var audioVolumeIndication: Int = 1000  // 音量回调间隔
    public var audioVolumeSmooth: Int = 3 // 平滑系数
    public var currentUserName: String = "" //当前用户名
    public var currentUserId: String = "" //当前用户UserId
    public var bizType: LiveMediaBizType = .ROOMLIVE // 业务线
}

public class LiveMediaMusicParams: NSObject {
    public var musicId: Int = -1
    public var filePath: String = ""
    public var cycle: Int = 0
    public var pos: Int64 = -1
    public var loopback: Bool = false
}

public class LiveMediaEffectParams: NSObject {
    public var soundId: Int = 0
    public var filePath: String = ""
    public var loopCount: Int = 0
    // 音效的音调，取值范围为 [0.5,2.0]。默认值为 1.0，表示原始音调。取值越小，则音调越低
    public var pitch: Double = 0.0
    // 音效的空间位置。取值范围为 [-1.0,1.0] -1.0：音效出现在左边 0.0：音效出现在正前方 1.0：音效出现在右边
    public var pan: Double = 0.0
    // 音效的音量 [0.0,100.0]
    public var gain: Double = 0.0
    public var publish: Bool = false
}


public class XYLiveMediaAudioVolumeInfo: NSObject {
    public var uid: Int = 0
    public var volume: Int = 0
    public var vad: Int = 0
    public var voicePitch: Double = 0.0
}

public class LiveMediaUserMode: NSObject {
    public var role: LiveMediaRoleType = .Anchor
    public var uid: Int = 0
    public var roomId: String = ""
    public var linkType: Int = 0
    public var linkId: String = ""
    public var pushSdkType: LiveMediaPushType = .Trtc
    public var userId: String = ""
    public var emceeId: String = ""
    public var emceeName: String = ""
}

public class LiveMediaBundle: NSObject {
    public var cameraFps: Int = 0
    public var previewFps: Int = 0
    public var localVideoWidth: Int = 0
    public var localVideoHeight: Int = 0
    public var localVideoFps: Int = 0
    public var localVideoBitrate: Int = 0
    public var localAudioSamplerate: Int = 0
    public var localAudioBitrate: Int = 0
    public var remoteId: String = "0"
    public var remoteVideoFps: Int = 0
    public var remoteVideoBitrate: Int = 0
    public var remoteAudioSamplerate: Int = 0
    public var remoteAudioBitrate: Int = 0
    public var finalLoss: Int = 0
    public var jitterbufferDelay: Int = 0
    public var rtt: Int = 0
    public var upLoss: Int = 0
    public var downLoss: Int = 0
    public var sentBytes: UInt64 = 0
    public var receivedBytes: UInt64 = 0
    public var cameraOrientation: Int = 0
    public var video_codec: Int = 0
}

public class LiveMediaLocalAudioStats: NSObject {
    public var numChannels: Int = 0
    public var sentSampleRate: Int = 0
    public var sentBitrate: Int = 0
    public var internalCodec: Int = 0
    public var txPacketLossRate: Int = 0
    public var audioDeviceDelay: Int = 0
}
public class LiveMediaRtcStats: NSObject {
    public var totalDuration: Int = 0
    public var txBytes: Int = 0
    public var rxBytes: Int = 0
    public var txKBitRate: Int = 0
    public var txAudioBytes: Int = 0
    public var rxAudioBytes: Int = 0
    public var txVideoBytes: Int = 0
    public var rxVideoBytes: Int = 0
    public var rxKBitRate: Int = 0
    public var txAudioKBitRate: Int = 0
    public var rxAudioKBitRate: Int = 0
    public var txVideoKBitRate: Int = 0
    public var rxVideoKBitRate: Int = 0
    public var lastmileDelay: Int = 0
    public var cpuTotalUsage: Double = 0.0
    public var gatewayRtt: Int = 0
    public var cpuAppUsage: Double = 0.0
    public var users: Int = 0
    public var connectTimeMs: Int = 0
    public var txPacketLossRate: Int = 0
    public var rxPacketLossRate: Int = 0
    public var memoryAppUsageRatio: Double = 0.0
    public var memoryTotalUsageRatio: Double = 0.0
    public var memoryAppUsageInKbytes: Int = 0
}
