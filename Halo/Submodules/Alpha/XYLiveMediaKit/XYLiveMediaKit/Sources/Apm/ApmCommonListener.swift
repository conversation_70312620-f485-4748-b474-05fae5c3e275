//
//  ApmCommonListener.swift
//  XYLiveMediaKit
//
//  Created by zhangzhen<PERSON> on 2022/10/2.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation
public protocol ApmCommonListener {
    /**
     * params
     */
    func initParams(sdkVersion: String)
    
    /**
     * 循环上报推流状态
     * @param bundle
     */
    func onPusherLoop(bundle: LiveMediaBundle, extendInfo: [String: String])
    
    /**
     * 实时上报推流行为
     * @param String
     */
    func onPusherEvent(event: XYLiveMediaPusherEvent, extendInfo: [String: String], extraCode: Int, extraMsg: String)
    
    /**
     * 更新实时上报部分字段
     * @param String
     */
    func updatePushEventParams(bundle: [String: String])
    
    /**
     * call pushUrl api
     */
    func onCallStartPush(pushUrl: String)
    
    /**
     * call isRecoveryLive api
     */
    func onCallStartLive(isRecoveryLive: Bool)
    
    /**
     * 推流器告警信息上报
     */
    func onPusherWarn(warnCode: Int, warnMsg: String?, extendInfo: [String: String])
    
    /**
     * 推流器错误信息上报
     */
    func onPusherError(errorCode: Int, errorMsg: String?, extendInfo: [String: String])
}
