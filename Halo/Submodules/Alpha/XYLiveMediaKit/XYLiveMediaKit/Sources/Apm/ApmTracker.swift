//
//  ApmTracker.swift
//  XYLiveMediaKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2022/10/2.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation
import XYLiveFoundation
import XYTracker

class ApmTracker: NSObject {
    static var TAG = "liveMedia_apm"
    
    class func onLivePushEvent(params: [String: String]) {
        XYLogCollector.xyLiveLogTag(TAG, content: "event: \(String(describing: params["event"])) onLivePushEvent: \(params)")
        XYTracker.track { (context: XYAPMContext) in
            _ = context.livePushEvent
            .sampleRate(1.0)
            .pusherName(params["pusherName"] ?? "")
            .pusherVersion(params["pusherVersion"] ?? "")
            .pushURL(params["pushUrl"] ?? "")
            .pushType(covertStrToInt32(src: params["pushType"] ?? "0"))
            .localRole(covertStrToInt32(src: params["localRole"] ?? "0"))
            .hostId(params["hostId"] ?? "0")
            .roomId(params["roomId"] ?? "0")
            .hostName(params["hostName"] ?? "")
            .userId(params["userId"] ?? "")
            .linkId(params["linkId"] ?? "")
            .cpuCount(covertStrToInt32(src: params["cpuCount"] ?? "0"))
            .cpuUsage(covertStrToDouble(src: params["cpuUsage"] ?? "0"))
            .memTotal(covertStrToInt64(src: params["memTotal"] ?? "0.0"))
            .memUsed(covertStrToInt64(src: params["memUsed"] ?? "0"))
            .memFree(covertStrToInt64(src: params["memFree"] ?? "0"))
            .memActive(covertStrToInt64(src: params["memActive"] ?? "0"))
            .memInactive(covertStrToInt64(src: params["memInactive"] ?? "0"))
            .memCurrent(covertStrToInt64(src: params["memCurrent"] ?? "0"))
            .thermalState(covertStrToInt32(src: params["thermalState"] ?? "0"))
            .extendInfo(params["extendInfo"] ?? "")
            .event(covertStrToInt32(src: params["event"] ?? "0"))
            .isRecovery(covertStrToInt32(src: params["isRecovery"] ?? "0"))
            .vencWidth(covertStrToInt32(src: params["vencWidth"] ?? "0"))
            .vencHeight(covertStrToInt32(src: params["vencHeight"] ?? "0"))
            .vencFps(covertStrToInt32(src: params["vencFps"] ?? "0"))
            .vencBitrate(covertStrToInt32(src: params["vencBitrate"] ?? "0"))
            .vencMinBitrate(covertStrToInt32(src: params["vencMinBitrate"] ?? "0"))
            .videoPreference(covertStrToInt32(src: params["videoPreference"] ?? "0"))
            .code(covertStrToInt64(src: params["eventCode"] ?? "0"))
            .msg(params["eventMsg"] ?? "")
            .videoCodec(covertStrToInt32(src: params["video_codec"] ?? "0"))
        }
    }
    
    class func onLivePushLoop(params: [String: String]) {
        XYLogCollector.xyLiveLogTag(TAG, content: "onLivePushLoop: \(params)")
        XYTracker.track { (context: XYAPMContext) in
            _ = context.livePushLoop
                .sampleRate(1.0)
                .pusherName(params["pusherName"] ?? "")
                .pusherVersion(params["pusherVersion"] ?? "")
                .pushURL(params["pushUrl"] ?? "")
                .pushType(covertStrToInt32(src: params["pushType"] ?? "0"))
                .localRole(covertStrToInt32(src: params["localRole"] ?? "0"))
                .hostId(params["hostId"] ?? "0")
                .roomId(params["roomId"] ?? "0")
                .hostName(params["hostName"] ?? "")
                .userId(params["userId"] ?? "")
                .linkId(params["linkId"] ?? "")
                .cpuCount(covertStrToInt32(src: params["cpuCount"] ?? "0"))
                .cpuUsage(covertStrToDouble(src: params["cpuUsage"] ?? "0"))
                .memTotal(covertStrToInt64(src: params["memTotal"] ?? "0.0"))
                .memUsed(covertStrToInt64(src: params["memUsed"] ?? "0"))
                .memFree(covertStrToInt64(src: params["memFree"] ?? "0"))
                .memActive(covertStrToInt64(src: params["memActive"] ?? "0"))
                .memInactive(covertStrToInt64(src: params["memInactive"] ?? "0"))
                .memCurrent(covertStrToInt64(src: params["memCurrent"] ?? "0"))
                .thermalState(covertStrToInt32(src: params["thermalState"] ?? "0"))
                .extendInfo(params["extendInfo"] ?? "")
                .cameraFps(covertStrToInt32(src: params["cameraFps"] ?? "0"))
                .previewFps(covertStrToInt32(src: params["previewFps"] ?? "0"))
                .localVideoWidth(covertStrToInt32(src: params["localVideoWidth"] ?? "0"))
                .localVideoHeight(covertStrToInt32(src: params["localVideoHeight"] ?? "0"))
                .localVideoFps(covertStrToInt32(src: params["localVideoFps"] ?? "0"))
                .localVideoBitrate(covertStrToInt32(src: params["localVideoBitrate"] ?? "0"))
                .localAudioSamplerate(covertStrToInt32(src: params["localAudioSamplerate"] ?? "0"))
                .localAudioBitrate(covertStrToInt32(src: params["localAudioBitrate"] ?? "0"))
                .remoteId(params["remoteId"] ?? "")
                .remoteVideoFps(covertStrToInt32(src: params["remoteVideoFps"] ?? "0"))
                .remoteVideoBitrate(covertStrToInt32(src: params["remoteVideoBitrate"] ?? "0"))
                .remoteAudioSamplerate(covertStrToInt32(src: params["remoteAudioSamplerate"] ?? "0"))
                .remoteAudioBitrate(covertStrToInt32(src: params["remoteAudioBitrate"] ?? "0"))
                .finalLoss(covertStrToInt32(src: params["finalLoss"] ?? "0"))
                .jitterbufferDelay(covertStrToInt32(src: params["jitterbufferDelay"] ?? "0"))
                .rtt(covertStrToInt32(src: params["rtt"] ?? "0"))
                .upLoss(covertStrToInt32(src: params["upLoss"] ?? "0"))
                .downLoss(covertStrToInt32(src: params["downLoss"] ?? "0"))
                .sentBytes(covertStrToInt64(src: params["sentBytes"] ?? "0"))
                .receivedBytes(covertStrToInt64(src: params["receivedBytes"] ?? "0"))
                .cameraOrientation(covertStrToInt32(src: params["cameraOrientation"] ?? "0"))
                .videoCodec(covertStrToInt32(src: params["video_codec"] ?? "0"))
        }
    }
    class func onLivePushWarn(params: [String: String]) {
        XYLogCollector.xyLiveLogTag(TAG, content: "onLivePushWarn: \(params)")
        XYTracker.track { (context: XYAPMContext) in
            _ = context.livePushWarn
            .sampleRate(1.0)
            .pusherName(params["pusherName"] ?? "")
            .pusherVersion(params["pusherVersion"] ?? "")
            .pushURL(params["pushUrl"] ?? "")
            .pushType(covertStrToInt32(src: params["pushType"] ?? "0"))
            .localRole(covertStrToInt32(src: params["localRole"] ?? "0"))
            .hostId(params["hostId"] ?? "0")
            .roomId(params["roomId"] ?? "0")
            .hostName(params["hostName"] ?? "")
            .userId(params["userId"] ?? "")
            .linkId(params["linkId"] ?? "")
            .cpuCount(covertStrToInt32(src: params["cpuCount"] ?? "0"))
            .cpuUsage(covertStrToDouble(src: params["cpuUsage"] ?? "0"))
            .memTotal(covertStrToInt64(src: params["memTotal"] ?? "0.0"))
            .memUsed(covertStrToInt64(src: params["memUsed"] ?? "0"))
            .memFree(covertStrToInt64(src: params["memFree"] ?? "0"))
            .memActive(covertStrToInt64(src: params["memActive"] ?? "0"))
            .memInactive(covertStrToInt64(src: params["memInactive"] ?? "0"))
            .memCurrent(covertStrToInt64(src: params["memCurrent"] ?? "0"))
            .thermalState(covertStrToInt32(src: params["thermalState"] ?? "0"))
            .warnCode(covertStrToInt32(src: params["warnCode"] ?? "0"))
            .warnMsg(params["warnMsg"] ?? "")
            .warnExtinfo(params["extendInfo"] ?? "");
        }
    }
    
    class func onLivePushError(params: [String: String]) {
        XYTracker.track { (context: XYAPMContext) in
            _ = context.livePushError
            .sampleRate(1.0)
            .pusherName(params["pusherName"] ?? "")
            .pusherVersion(params["pusherVersion"] ?? "")
            .pushURL(params["pushUrl"] ?? "")
            .pushType(covertStrToInt32(src: params["pushType"] ?? "0"))
            .localRole(covertStrToInt32(src: params["localRole"] ?? "0"))
            .hostId(params["hostId"] ?? "0")
            .roomId(params["roomId"] ?? "0")
            .hostName(params["hostName"] ?? "")
            .userId(params["userId"] ?? "")
            .linkId(params["linkId"] ?? "")
            .cpuCount(covertStrToInt32(src: params["cpuCount"] ?? "0"))
            .cpuUsage(covertStrToDouble(src: params["cpuUsage"] ?? "0"))
            .memTotal(covertStrToInt64(src: params["memTotal"] ?? "0.0"))
            .memUsed(covertStrToInt64(src: params["memUsed"] ?? "0"))
            .memFree(covertStrToInt64(src: params["memFree"] ?? "0"))
            .memActive(covertStrToInt64(src: params["memActive"] ?? "0"))
            .memInactive(covertStrToInt64(src: params["memInactive"] ?? "0"))
            .memCurrent(covertStrToInt64(src: params["memCurrent"] ?? "0"))
            .thermalState(covertStrToInt32(src: params["thermalState"] ?? "0"))
            .errorCode(covertStrToInt32(src: params["errorCode"] ?? "0"))
            .errorMsg(params["errorMsg"] ?? "")
            .errorExtinfo(params["extendInfo"] ?? "");
        }
    }
}

func covertStrToInt64(src: String) -> Int64 {
    return (src as NSString).longLongValue
}
func covertStrToDouble(src: String) -> Double {
    return (src as NSString).doubleValue
}
func covertStrToInt32(src: String) -> Int32 {
    return (src as NSString).intValue
}

func covertStrToInt(src: String) -> Int {
    return (src as NSString).integerValue
}
