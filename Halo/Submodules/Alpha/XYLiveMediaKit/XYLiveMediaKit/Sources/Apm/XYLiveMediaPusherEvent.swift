//
//  XYLiveMediaPusherEvent.swift
//  XYLiveMediaKit
//
//  Created by z<PERSON><PERSON><PERSON> on 2022/10/2.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation

public enum XYLiveMediaPusherEvent: Int {
    case ALPHA_PUSHER_ROOM_CREATE = 0
    case ALPHA_PUSHER_PUSH_START = 1
    case ALPHA_PUSHER_PUSH_SUCCESS = 2
    case ALPHA_PUSHER_ROOM_STOP = 3
    case ALPHA_PUSHER_LINK_START = 4
    case ALPHA_PUSHER_LINK_SUCCESS = 5
    case ALPHA_PUSHER_LINK_STOP = 6
    case ALPHA_PUSHER_CODEC_CHANGE = 7
    case ALPHA_PUSHER_PUSH_OBS = 999
    case ALPHA_PUSHER_WARN = 10001
    case ALPHA_PUSHER_ERROR = 10002
    case ALPHA_PUSHER_PUSH_VIDEO = 2001
    case ALPHA_PUSHER_PUSH_AUDIO = 2002
    case ALPHA_PUSHER_PUSH_FIRST_VIDEO_FRAME = 2003
    case ALPHA_PUSHER_PUSH_FIRST_AUDIO_FRAME = 2004
    case ALPHA_PUSHER_HOT_SWITCH = 2005
    case ALPHA_PUSHER_CONNECT_LOST = 3001
    case ALPHA_PUSHER_CONNECT_RECONNECT = 3002
    case ALPHA_PUSHER_CONNECT_RECOVERY = 3003
    case ALPHA_PUSHER_CAMERA_READY = 4001
    case ALPHA_PUSHER_MIC_READY = 4002
    case ALPHA_PUSHER_START_PUSH_CDN_STREAM = 5001
    case ALPHA_PUSHER_STOP_PUSH_CDN_STREAM = 5002
    case ALPHA_PUSHER_START_PUSH_SRS_STREAM = 5003
    case ALPHA_PUSHER_STOP_PUSH_SRS_STREAM = 5004
    case ALPHA_PUSHER_SET_MIX_CONFIG = 5005
}
