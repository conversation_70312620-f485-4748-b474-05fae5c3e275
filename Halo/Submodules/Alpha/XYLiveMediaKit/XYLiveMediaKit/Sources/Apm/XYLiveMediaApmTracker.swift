//
//  XYLiveMediaApmTracker.swift
//  XYLiveMediaKit
//
//  Created by z<PERSON><PERSON><PERSON> on 2022/10/2.
//  Copyright © 2022 XingIn. All rights reserved.
//

import Foundation
import XYLiveFoundation
import XYDevice
import XYEyeSauron

public class XYLiveMediaApmTracker: NSObject, ApmCommonListener {
    
    private var sdkVersion: String = ""
    private var pusherLoopStep: Int = 0
    private var apmPushUrl: String = ""
    private var vencWidth: String = "0"
    private var vencHeight: String = "0"
    private var vencFps: String = "0"
    private var vencVra: String = "0"
    private var cameraOrientation: Int = 0
    private var isRecoveryLive: Bool = false
    private var codec: Int = 0
    private let TAG = "XYLiveMediaApmTracker"
    private var userMode: LiveMediaUserMode = LiveMediaUserMode()
    
    public init(userMode: LiveMediaUserMode) {
        XYLogCollector.xyLiveLogTag(TAG, content: "init")
        self.userMode = userMode
        super.init()
    }
    
    public func updateUserMode(mode: LiveMediaUserMode) {
        XYLogCollector.xyLiveLogTag(TAG, content: "updateUserMode mode: \(mode)")
        userMode = mode
    }
    
    public func initParams(sdkVersion: String) {
        self.sdkVersion = sdkVersion
    }
    
    public func onPusherLoop(bundle: LiveMediaBundle, extendInfo: [String: String]) {
        if pusherLoopStep <= 0 {
            pusherLoopStep = 10
            var params = buildPusherCommonParams()
            if let str = dicValueString(extendInfo) {
                params["extendInfo"] = str
            }
            let pushLoopParams = appendPushLoopParams(bundle: bundle)
            params.merge(pushLoopParams) { keyValue, _ -> String in
                return keyValue
            }
            ApmTracker.onLivePushLoop(params: params)
        } else {
            pusherLoopStep -= 1
        }
    }
    public func onPusherEvent(event: XYLiveMediaPusherEvent, extendInfo: [String: String], extraCode: Int, extraMsg: String) {
        var params = buildPusherCommonParams()
        if let str = dicValueString(extendInfo) {
            params["extendInfo"] = str
        }
        params["event"] = String(event.rawValue)
        if event == .ALPHA_PUSHER_WARN {
            params["eventCode"] = String(extraCode)
            params["eventMsg"] = extraMsg
        }
        if event == .ALPHA_PUSHER_ERROR {
            params["eventCode"] = String(extraCode)
            params["eventMsg"] = String(extraMsg)
        }
        let pushEventParam = appendPushEventParams()
        params.merge(pushEventParam) { keyValue, _ -> String in
            return keyValue
        }
        ApmTracker.onLivePushEvent(params: params)
    }
    
    public func updatePushEventParams(bundle: [String: String]) {
        self.vencWidth = bundle["vencWidth"] ?? "0"
        self.vencHeight = bundle["vencHeight"] ?? "0"
        self.vencFps = bundle["vencFps"] ?? "0"
        self.vencVra = bundle["vencVra"] ?? "0"
        self.cameraOrientation = Int(covertStrToInt(src: bundle["cameraOrientation"] ?? "0"))
        self.codec = Int(covertStrToInt(src: bundle["video_codec"] ?? "0"))
    }
    
    public func onCallStartPush(pushUrl: String) {
        self.apmPushUrl = pushUrl
    }
    
    public func onCallStartLive(isRecoveryLive: Bool) {
        self.isRecoveryLive = isRecoveryLive
    }
    
    public func onPusherWarn(warnCode: Int, warnMsg: String?, extendInfo: [String: String]) {
        var params = buildPusherCommonParams()
        if let str = dicValueString(extendInfo) {
            params["extendInfo"] = str
        }
        params["warnCode"] = String(warnCode)
        params["warnMsg"] = warnMsg ?? ""
        ApmTracker.onLivePushWarn(params: params)
    }
    
    public func onPusherError(errorCode: Int, errorMsg: String?, extendInfo: [String: String]) {
        var params = buildPusherCommonParams()
        if let str = dicValueString(extendInfo) {
            params["extendInfo"] = str
        }
        params["errorCode"] = String(errorCode)
        params["errorMsg"] = errorMsg ?? ""
        ApmTracker.onLivePushError(params: params)
    }
    
    
    private func buildPusherCommonParams() -> [String: String] {
        var params = [String: String]()
        switch userMode.pushSdkType {
        case .Agora:
            params["pusherName"] = "agora"
        case .Trtc:
            params["pusherName"] = "trtc"
        case .Custom_rtmp:
            params["pusherName"] = "customRtmp"
        default:
            params["pusherName"] = "unKnow"
        }
       
        params["pusherVersion"] = sdkVersion
        params["pushUrl"] = apmPushUrl
        params["pushType"] = String(userMode.linkType)
        params["localRole"] = (userMode.role == .Anchor) ? "0" : "1"
        params["hostId"] = userMode.emceeId
        params["roomId"] = userMode.roomId
        params["hostName"] = userMode.emceeName
        params["userId"] = userMode.userId
        params["linkId"] = userMode.linkId
        let cpuParams = getCpuInfo()
        let memParams = getMemInfo()
        params["cpuCount"] = cpuParams["cpuCount"]
        params["cpuUsage"] = cpuParams["cpuUsage"]
  

        params["memTotal"] = memParams["memTotal"]
        params["memUsed"] = memParams["memUsed"]
        params["memFree"] = memParams["memFree"]
        params["memActive"] = memParams["memActive"]
        params["memInactive"] = memParams["memInactive"]
        params["memCurrent"] = memParams["memCurrent"]
        params["thermalState"] = "0"
        return params
    }
    

    private func appendPushEventParams() -> [String: String] {
        var params = [String: String]()
        params["isRecovery"] = isRecoveryLive ? "1" : "0"
        params["vencWidth"] = vencWidth
        params["vencHeight"] = vencHeight
        params["vencFps"] = vencFps
        params["vencBitrate"] = vencVra
        params["vencMinBitrate"] = "0"
        params["videoPreference"] = "1"
        params["video_codec"] = String(codec)
        return params
    }
    
    
    private func appendPushLoopParams(bundle: LiveMediaBundle) -> [String: String] {
        var params = [String: String]()
        params["cameraFps"] = String(bundle.cameraFps)
        params["previewFps"] = "0"
        params["localVideoWidth"] = String(bundle.localVideoWidth)
        params["localVideoHeight"] = String(bundle.localVideoHeight)
        params["localVideoFps"] = String(bundle.localVideoFps)
        params["localVideoBitrate"] = String(bundle.localVideoBitrate)
        params["localAudioSamplerate"] = String(bundle.localAudioSamplerate)
        params["localAudioBitrate"] = String(bundle.localAudioBitrate)
        params["remoteId"] = bundle.remoteId
        params["remoteVideoFps"] = String(bundle.remoteVideoFps)
        params["remoteVideoBitrate"] = String(bundle.remoteVideoBitrate)
        params["remoteAudioSamplerate"] = String(bundle.remoteAudioSamplerate)
        params["remoteAudioBitrate"] = String(bundle.remoteAudioBitrate)
        params["finalLoss"] = String(bundle.finalLoss)
        params["jitterbufferDelay"] = String(bundle.jitterbufferDelay)
        params["rtt"] = String(bundle.rtt)
        params["upLoss"] = String(bundle.upLoss)
        params["downLoss"] = String(bundle.downLoss)
        params["sentBytes"] = String(bundle.sentBytes)
        params["receivedBytes"] = String(bundle.receivedBytes)
        params["cameraOrientation"] = String(bundle.cameraOrientation)
        params["video_codec"] = String(bundle.video_codec)
        return params
    }
    
    private func getCpuInfo() -> [String: String] {
        return [
            "cpuUsage": String(XYDevice.cpuUsage),
            "cpuCount": String(XYDevice.cpuCount)
        ]
    }
    
    private func getMemInfo() -> [String: String] {
        return [
            "memTotal": String(XYDevice.memoryTotal),
            "memUsed": String(XYDevice.memoryUsed),
            "memFree": String(XYDevice.memoryFree),
            "memActive": String(XYDevice.memoryActive),
            "memInactive": String(XYDevice.memoryInactive),
            "memCurrent": String(XYEyeSauronRAMUsed().usedRam)
        ]
    }
    
    private func dicValueString(_ dic: [String: Any]) -> String? {
        if let data = try? JSONSerialization.data(withJSONObject: dic, options: []) {
            return String(data: data, encoding: String.Encoding.utf8)
        }
        return nil
    }

}
